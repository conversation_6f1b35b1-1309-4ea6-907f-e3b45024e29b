{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Screenfull\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBzY3JlZW5mdWxsIGZyb20gJ3NjcmVlbmZ1bGwnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NjcmVlbmZ1bGwnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLmRlc3Ryb3koKQogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2soKSB7CiAgICAgIGlmICghc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogJ+S9oOeahOa1j+iniOWZqOS4jeaUr+aMgeWFqOWxjycsIHR5cGU6ICd3YXJuaW5nJyB9KQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIHNjcmVlbmZ1bGwudG9nZ2xlKCkKICAgIH0sCiAgICBjaGFuZ2UoKSB7CiAgICAgIHRoaXMuaXNGdWxsc2NyZWVuID0gc2NyZWVuZnVsbC5pc0Z1bGxzY3JlZW4KICAgIH0sCiAgICBpbml0KCkgewogICAgICBpZiAoc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsKICAgICAgICBzY3JlZW5mdWxsLm9uKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSkKICAgICAgfQogICAgfSwKICAgIGRlc3Ryb3koKSB7CiAgICAgIGlmIChzY3JlZW5mdWxsLmlzRW5hYmxlZCkgewogICAgICAgIHNjcmVlbmZ1bGwub2ZmKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSkKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Screenfull", "sourcesContent": ["<template>\n  <div>\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'Screenfull',\n  data() {\n    return {\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.destroy()\n  },\n  methods: {\n    click() {\n      if (!screenfull.isEnabled) {\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\n        return false\n      }\n      screenfull.toggle()\n    },\n    change() {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    init() {\n      if (screenfull.isEnabled) {\n        screenfull.on('change', this.change)\n      }\n    },\n    destroy() {\n      if (screenfull.isEnabled) {\n        screenfull.off('change', this.change)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.screenfull-svg {\n  display: inline-block;\n  cursor: pointer;\n  fill: #5a5e66;;\n  width: 20px;\n  height: 20px;\n  vertical-align: 10px;\n}\n</style>\n"]}]}