{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\second.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\second.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["second.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "second.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n\t<el-form size=\"small\">\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\n\t\t\t\t秒，允许的通配符[, - * /]\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\n\t\t\t\t周期从\n\t\t\t\t<el-input-number v-model='cycle01' :min=\"0\" :max=\"58\" /> -\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : 1\" :max=\"59\" /> 秒\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\n\t\t\t\t从\n\t\t\t\t<el-input-number v-model='average01' :min=\"0\" :max=\"58\" /> 秒开始，每\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"59 - average01 || 0\" /> 秒执行一次\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\n\t\t\t\t指定\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\n\t\t\t\t\t<el-option v-for=\"item in 60\" :key=\"item\" :value=\"item-1\">{{item-1}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\t</el-form>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tradioValue: 1,\n\t\t\tcycle01: 1,\n\t\t\tcycle02: 2,\n\t\t\taverage01: 0,\n\t\t\taverage02: 1,\n\t\t\tcheckboxList: [],\n\t\t\tcheckNum: this.$options.propsData.check\n\t\t}\n\t},\n\tname: 'crontab-second',\n\tprops: ['check', 'radioParent'],\n\tmethods: {\n\t\t// 单选按钮值变化时\n\t\tradioChange() {\n\t\t\tswitch (this.radioValue) {\n\t\t\t\tcase 1:\n\t\t\t\t\tthis.$emit('update', 'second', '*', 'second');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tthis.$emit('update', 'second', this.cycleTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tthis.$emit('update', 'second', this.averageTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tthis.$emit('update', 'second', this.checkboxString);\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 周期两个值变化时\n\t\tcycleChange() {\n\t\t\tif (this.radioValue == '2') {\n\t\t\t\tthis.$emit('update', 'second', this.cycleTotal);\n\t\t\t}\n\t\t},\n\t\t// 平均两个值变化时\n\t\taverageChange() {\n\t\t\tif (this.radioValue == '3') {\n\t\t\t\tthis.$emit('update', 'second', this.averageTotal);\n\t\t\t}\n\t\t},\n\t\t// checkbox值变化时\n\t\tcheckboxChange() {\n\t\t\tif (this.radioValue == '4') {\n\t\t\t\tthis.$emit('update', 'second', this.checkboxString);\n\t\t\t}\n\t\t}\n\t},\n\twatch: {\n\t\t'radioValue': 'radioChange',\n\t\t'cycleTotal': 'cycleChange',\n\t\t'averageTotal': 'averageChange',\n\t\t'checkboxString': 'checkboxChange',\n\t\tradioParent() {\n\t\t\tthis.radioValue = this.radioParent\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 计算两个周期值\n\t\tcycleTotal: function () {\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, 0, 58)\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 59)\n\t\t\treturn cycle01 + '-' + cycle02;\n\t\t},\n\t\t// 计算平均用到的值\n\t\taverageTotal: function () {\n\t\t\tconst average01 = this.checkNum(this.average01, 0, 58)\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 59 - average01 || 0)\n\t\t\treturn average01 + '/' + average02;\n\t\t},\n\t\t// 计算勾选的checkbox值合集\n\t\tcheckboxString: function () {\n\t\t\tlet str = this.checkboxList.join();\n\t\t\treturn str == '' ? '*' : str;\n\t\t}\n\t}\n}\n</script>\n"]}]}