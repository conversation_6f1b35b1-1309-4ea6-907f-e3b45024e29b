{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\profile\\userInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\profile\\userInfo.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userInfo.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n    <el-form-item label=\"用户昵称\" prop=\"nickName\">\n      <el-input v-model=\"form.nickName\" maxlength=\"30\" />\n    </el-form-item> \n    <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n      <el-input v-model=\"form.phonenumber\" maxlength=\"11\" />\n    </el-form-item>\n    <el-form-item label=\"邮箱\" prop=\"email\">\n      <el-input v-model=\"form.email\" maxlength=\"50\" />\n    </el-form-item>\n    <el-form-item label=\"性别\">\n      <el-radio-group v-model=\"form.sex\">\n        <el-radio label=\"0\">男</el-radio>\n        <el-radio label=\"1\">女</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item>\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\n    </el-form-item>\n  </el-form>\n</template>\n\n<script>\nimport { updateUserProfile } from \"@/api/system/user\";\n\nexport default {\n  props: {\n    user: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      form: {},\n      // 表单校验\n      rules: {\n        nickName: [\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"邮箱地址不能为空\", trigger: \"blur\" },\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phonenumber: [\n          { required: true, message: \"手机号码不能为空\", trigger: \"blur\" },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  watch: {\n    user: {\n      handler(user) {\n        if (user) {\n          this.form = { nickName: user.nickName, phonenumber: user.phonenumber, email: user.email, sex: user.sex };\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    submit() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          updateUserProfile(this.form).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.user.phonenumber = this.form.phonenumber;\n            this.user.email = this.form.email;\n          });\n        }\n      });\n    },\n    close() {\n      this.$tab.closePage();\n    }\n  }\n};\n</script>\n"]}]}