{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n  <div>\n    <el-tabs type=\"border-card\">\n      <el-tab-pane label=\"秒\" v-if=\"shouldHide('second')\">\n        <CrontabSecond\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronsecond\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"分钟\" v-if=\"shouldHide('min')\">\n        <CrontabMin\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronmin\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"小时\" v-if=\"shouldHide('hour')\">\n        <CrontabHour\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronhour\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"日\" v-if=\"shouldHide('day')\">\n        <CrontabDay\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronday\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"月\" v-if=\"shouldHide('month')\">\n        <CrontabMonth\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronmonth\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"周\" v-if=\"shouldHide('week')\">\n        <CrontabWeek\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronweek\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"年\" v-if=\"shouldHide('year')\">\n        <CrontabYear\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronyear\"\n        />\n      </el-tab-pane>\n    </el-tabs>\n\n    <div class=\"popup-main\">\n      <div class=\"popup-result\">\n        <p class=\"title\">时间表达式</p>\n        <table>\n          <thead>\n            <th v-for=\"item of tabTitles\" width=\"40\" :key=\"item\">{{item}}</th>\n            <th>Cron 表达式</th>\n          </thead>\n          <tbody>\n            <td>\n              <span>{{crontabValueObj.second}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.min}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.hour}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.day}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.month}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.week}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.year}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueString}}</span>\n            </td>\n          </tbody>\n        </table>\n      </div>\n      <CrontabResult :ex=\"crontabValueString\"></CrontabResult>\n\n      <div class=\"pop_btn\">\n        <el-button size=\"small\" type=\"primary\" @click=\"submitFill\">确定</el-button>\n        <el-button size=\"small\" type=\"warning\" @click=\"clearCron\">重置</el-button>\n        <el-button size=\"small\" @click=\"hidePopup\">取消</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CrontabSecond from \"./second.vue\";\nimport CrontabMin from \"./min.vue\";\nimport CrontabHour from \"./hour.vue\";\nimport CrontabDay from \"./day.vue\";\nimport CrontabMonth from \"./month.vue\";\nimport CrontabWeek from \"./week.vue\";\nimport CrontabYear from \"./year.vue\";\nimport CrontabResult from \"./result.vue\";\n\nexport default {\n  data() {\n    return {\n      tabTitles: [\"秒\", \"分钟\", \"小时\", \"日\", \"月\", \"周\", \"年\"],\n      tabActive: 0,\n      myindex: 0,\n      crontabValueObj: {\n        second: \"*\",\n        min: \"*\",\n        hour: \"*\",\n        day: \"*\",\n        month: \"*\",\n        week: \"?\",\n        year: \"\",\n      },\n    };\n  },\n  name: \"vcrontab\",\n  props: [\"expression\", \"hideComponent\"],\n  methods: {\n    shouldHide(key) {\n      if (this.hideComponent && this.hideComponent.includes(key)) return false;\n      return true;\n    },\n    resolveExp() {\n      // 反解析 表达式\n      if (this.expression) {\n        let arr = this.expression.split(\" \");\n        if (arr.length >= 6) {\n          //6 位以上是合法表达式\n          let obj = {\n            second: arr[0],\n            min: arr[1],\n            hour: arr[2],\n            day: arr[3],\n            month: arr[4],\n            week: arr[5],\n            year: arr[6] ? arr[6] : \"\",\n          };\n          this.crontabValueObj = {\n            ...obj,\n          };\n          for (let i in obj) {\n            if (obj[i]) this.changeRadio(i, obj[i]);\n          }\n        }\n      } else {\n        // 没有传入的表达式 则还原\n        this.clearCron();\n      }\n    },\n    // tab切换值\n    tabCheck(index) {\n      this.tabActive = index;\n    },\n    // 由子组件触发，更改表达式组成的字段值\n    updateCrontabValue(name, value, from) {\n      \"updateCrontabValue\", name, value, from;\n      this.crontabValueObj[name] = value;\n      if (from && from !== name) {\n        console.log(`来自组件 ${from} 改变了 ${name} ${value}`);\n        this.changeRadio(name, value);\n      }\n    },\n    // 赋值到组件\n    changeRadio(name, value) {\n      let arr = [\"second\", \"min\", \"hour\", \"month\"],\n        refName = \"cron\" + name,\n        insValue;\n\n      if (!this.$refs[refName]) return;\n\n      if (arr.includes(name)) {\n        if (value === \"*\") {\n          insValue = 1;\n        } else if (value.indexOf(\"-\") > -1) {\n          let indexArr = value.split(\"-\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].cycle01 = 0)\n            : (this.$refs[refName].cycle01 = indexArr[0]);\n          this.$refs[refName].cycle02 = indexArr[1];\n          insValue = 2;\n        } else if (value.indexOf(\"/\") > -1) {\n          let indexArr = value.split(\"/\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].average01 = 0)\n            : (this.$refs[refName].average01 = indexArr[0]);\n          this.$refs[refName].average02 = indexArr[1];\n          insValue = 3;\n        } else {\n          insValue = 4;\n          this.$refs[refName].checkboxList = value.split(\",\");\n        }\n      } else if (name == \"day\") {\n        if (value === \"*\") {\n          insValue = 1;\n        } else if (value == \"?\") {\n          insValue = 2;\n        } else if (value.indexOf(\"-\") > -1) {\n          let indexArr = value.split(\"-\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].cycle01 = 0)\n            : (this.$refs[refName].cycle01 = indexArr[0]);\n          this.$refs[refName].cycle02 = indexArr[1];\n          insValue = 3;\n        } else if (value.indexOf(\"/\") > -1) {\n          let indexArr = value.split(\"/\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].average01 = 0)\n            : (this.$refs[refName].average01 = indexArr[0]);\n          this.$refs[refName].average02 = indexArr[1];\n          insValue = 4;\n        } else if (value.indexOf(\"W\") > -1) {\n          let indexArr = value.split(\"W\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].workday = 0)\n            : (this.$refs[refName].workday = indexArr[0]);\n          insValue = 5;\n        } else if (value === \"L\") {\n          insValue = 6;\n        } else {\n          this.$refs[refName].checkboxList = value.split(\",\");\n          insValue = 7;\n        }\n      } else if (name == \"week\") {\n        if (value === \"*\") {\n          insValue = 1;\n        } else if (value == \"?\") {\n          insValue = 2;\n        } else if (value.indexOf(\"-\") > -1) {\n          let indexArr = value.split(\"-\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].cycle01 = 0)\n            : (this.$refs[refName].cycle01 = indexArr[0]);\n          this.$refs[refName].cycle02 = indexArr[1];\n          insValue = 3;\n        } else if (value.indexOf(\"#\") > -1) {\n          let indexArr = value.split(\"#\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].average01 = 1)\n            : (this.$refs[refName].average01 = indexArr[0]);\n          this.$refs[refName].average02 = indexArr[1];\n          insValue = 4;\n        } else if (value.indexOf(\"L\") > -1) {\n          let indexArr = value.split(\"L\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].weekday = 1)\n            : (this.$refs[refName].weekday = indexArr[0]);\n          insValue = 5;\n        } else {\n          this.$refs[refName].checkboxList = value.split(\",\");\n          insValue = 6;\n        }\n      } else if (name == \"year\") {\n        if (value == \"\") {\n          insValue = 1;\n        } else if (value == \"*\") {\n          insValue = 2;\n        } else if (value.indexOf(\"-\") > -1) {\n          insValue = 3;\n        } else if (value.indexOf(\"/\") > -1) {\n          insValue = 4;\n        } else {\n          this.$refs[refName].checkboxList = value.split(\",\");\n          insValue = 5;\n        }\n      }\n      this.$refs[refName].radioValue = insValue;\n    },\n    // 表单选项的子组件校验数字格式（通过-props传递）\n    checkNumber(value, minLimit, maxLimit) {\n      // 检查必须为整数\n      value = Math.floor(value);\n      if (value < minLimit) {\n        value = minLimit;\n      } else if (value > maxLimit) {\n        value = maxLimit;\n      }\n      return value;\n    },\n    // 隐藏弹窗\n    hidePopup() {\n      this.$emit(\"hide\");\n    },\n    // 填充表达式\n    submitFill() {\n      this.$emit(\"fill\", this.crontabValueString);\n      this.hidePopup();\n    },\n    clearCron() {\n      // 还原选择项\n      (\"准备还原\");\n      this.crontabValueObj = {\n        second: \"*\",\n        min: \"*\",\n        hour: \"*\",\n        day: \"*\",\n        month: \"*\",\n        week: \"?\",\n        year: \"\",\n      };\n      for (let j in this.crontabValueObj) {\n        this.changeRadio(j, this.crontabValueObj[j]);\n      }\n    },\n  },\n  computed: {\n    crontabValueString: function() {\n      let obj = this.crontabValueObj;\n      let str =\n        obj.second +\n        \" \" +\n        obj.min +\n        \" \" +\n        obj.hour +\n        \" \" +\n        obj.day +\n        \" \" +\n        obj.month +\n        \" \" +\n        obj.week +\n        (obj.year == \"\" ? \"\" : \" \" + obj.year);\n      return str;\n    },\n  },\n  components: {\n    CrontabSecond,\n    CrontabMin,\n    CrontabHour,\n    CrontabDay,\n    CrontabMonth,\n    CrontabWeek,\n    CrontabYear,\n    CrontabResult,\n  },\n  watch: {\n    expression: \"resolveExp\",\n    hideComponent(value) {\n      // 隐藏部分组件\n    },\n  },\n  mounted: function() {\n    this.resolveExp();\n  },\n};\n</script>\n<style scoped>\n.pop_btn {\n  text-align: center;\n  margin-top: 20px;\n}\n.popup-main {\n  position: relative;\n  margin: 10px auto;\n  background: #fff;\n  border-radius: 5px;\n  font-size: 12px;\n  overflow: hidden;\n}\n.popup-title {\n  overflow: hidden;\n  line-height: 34px;\n  padding-top: 6px;\n  background: #f2f2f2;\n}\n.popup-result {\n  box-sizing: border-box;\n  line-height: 24px;\n  margin: 25px auto;\n  padding: 15px 10px 10px;\n  border: 1px solid #ccc;\n  position: relative;\n}\n.popup-result .title {\n  position: absolute;\n  top: -28px;\n  left: 50%;\n  width: 140px;\n  font-size: 14px;\n  margin-left: -70px;\n  text-align: center;\n  line-height: 30px;\n  background: #fff;\n}\n.popup-result table {\n  text-align: center;\n  width: 100%;\n  margin: 0 auto;\n}\n.popup-result table span {\n  display: block;\n  width: 100%;\n  font-family: arial;\n  line-height: 30px;\n  height: 30px;\n  white-space: nowrap;\n  overflow: hidden;\n  border: 1px solid #e8e8e8;\n}\n.popup-result-scroll {\n  font-size: 12px;\n  line-height: 24px;\n  height: 10em;\n  overflow-y: auto;\n}\n</style>\n"]}]}