<template>
  <div class="user-management-container">
    <div class="user-management-header">
      <div class="title">用户管理</div>
      <div class="actions">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">添加用户</el-button>
      </div>
    </div>

    <div class="user-management-content">
      <el-table
        :data="userList"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
      >
        <el-table-column
          prop="userId"
          label="用户ID"
          width="100"
          align="center"
        />
        <el-table-column
          prop="userName"
          label="用户名称"
          width="150"
          align="center"
        />
        <el-table-column
          prop="phoneNumber"
          label="手机号"
          width="150"
          align="center"
        />
        <el-table-column
          prop="email"
          label="邮箱"
          width="180"
          align="center"
        />
        <el-table-column
          prop="maxVisits"
          label="访问权限"
          width="100"
          align="center"
        />
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next, jumper"
          :total="total"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加用户对话框 -->
    <el-dialog title="添加用户" :visible.sync="dialogVisible" width="500px" center class="user-dialog">
      <el-form ref="userForm" :model="userForm" :rules="userFormRules" label-width="100px">
        <el-form-item label="用户名称" prop="userName">
          <div class="required-mark">*</div>
          <el-input v-model="userForm.userName" placeholder="请输入用户名/手机号/邮箱等登录名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <div class="required-mark">*</div>
          <el-input v-model="userForm.phoneNumber" placeholder="输入手机号码" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <div class="required-mark">*</div>
          <el-input v-model="userForm.email" placeholder="输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <div class="required-mark">*</div>
          <el-input v-model="userForm.password" type="password" placeholder="8-16 密码必须同时包含数字、大小写字母和符号" show-password />
        </el-form-item>
        <el-form-item label="最大访问量" prop="maxVisits">
          <el-input v-model="userForm.maxVisits" placeholder="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="userForm.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" class="confirm-btn">确定</el-button>
        <el-button @click="dialogVisible = false" class="cancel-btn">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "UserManagement",
  data() {
    return {
      // 用户列表
      userList: [
        {
          userId: 1,
          userName: "admin",
          phoneNumber: "13800138000",
          email: "<EMAIL>",
          password: "******",
          maxVisits: 1000,
          role: "管理员",
          department: "技术部",
          status: 1
        },
        {
          userId: 2,
          userName: "user1",
          phoneNumber: "13800138001",
          email: "<EMAIL>",
          password: "******",
          maxVisits: 500,
          role: "普通用户",
          department: "市场部",
          status: 1
        },
        {
          userId: 3,
          userName: "user2",
          phoneNumber: "13800138002",
          email: "<EMAIL>",
          password: "******",
          maxVisits: 300,
          role: "普通用户",
          department: "销售部",
          status: 0
        }
      ],
      // 分页相关
      total: 3,
      currentPage: 1,
      pageSize: 10,
      // 对话框相关
      dialogVisible: false,
      dialogTitle: "添加用户",
      userForm: {
        userId: null,
        userName: "",
        phoneNumber: "",
        email: "",
        password: "",
        maxVisits: 0,
        role: "",
        department: "",
        status: 1
      },
      userFormRules: {
        userName: [
          { required: true, message: "请输入用户名称", trigger: "blur" },
          { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
        ],
        phoneNumber: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        email: [
          { required: true, message: "请输入邮箱", trigger: "blur" },
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 8, max: 16, message: "长度在 8 到 16 个字符", trigger: "blur" },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,16}$/,
            message: "密码必须同时包含数字、大小写字母和符号",
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      // 实际应用中这里需要调用接口获取对应页的数据
    },
    // 添加用户
    handleAdd() {
      this.dialogTitle = "添加用户";
      this.userForm = {
        userId: null,
        userName: "",
        phoneNumber: "",
        email: "",
        password: "",
        maxVisits: 0,
        role: "",
        department: "",
        status: 1
      };
      this.dialogVisible = true;
    },
    // 编辑用户
    handleEdit(row) {
      this.dialogTitle = "编辑用户";
      this.userForm = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },
    // 删除用户
    handleDelete(row) {
      this.$confirm(`确定要删除用户"${row.userName}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 实际应用中这里需要调用接口删除用户
        this.userList = this.userList.filter(item => item.userId !== row.userId);
        this.total = this.userList.length;
        this.$message.success("删除成功");
      }).catch(() => {
        this.$message.info("已取消删除");
      });
    },
    // 修改用户状态
    handleStatusChange(row) {
      // 实际应用中这里需要调用接口修改用户状态
      this.$message.success(`用户"${row.userName}"状态已${row.status === 1 ? '启用' : '禁用'}`);
    },
    // 提交表单
    submitForm() {
      this.$refs.userForm.validate(valid => {
        if (valid) {
          if (this.userForm.userId) {
            // 编辑用户
            const index = this.userList.findIndex(item => item.userId === this.userForm.userId);
            if (index !== -1) {
              this.userList.splice(index, 1, this.userForm);
              this.$message.success("修改成功");
            }
          } else {
            // 添加用户
            this.userForm.userId = this.userList.length + 1;
            this.userList.push(this.userForm);
            this.total = this.userList.length;
            this.$message.success("添加成功");
          }
          this.dialogVisible = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.user-management-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .title {
    font-size: 18px;
    font-weight: bold;
  }
}

.user-management-content {
  .el-table {
    margin-bottom: 20px;
  }

  .delete-btn {
    color: #f56c6c;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  text-align: center;

  .confirm-btn {
    background-color: #409EFF;
    border-color: #409EFF;
    margin-right: 10px;
  }

  .cancel-btn {
    color: #606266;
    background-color: #fff;
    border-color: #dcdfe6;
  }
}

.user-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
    }

    .el-dialog__headerbtn {
      top: 15px;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-form-item {
    position: relative;
    margin-bottom: 20px;

    .required-mark {
      position: absolute;
      left: -10px;
      top: 10px;
      color: #f56c6c;
      font-size: 14px;
    }

    .el-form-item__label {
      color: #606266;
      font-weight: normal;
    }

    .el-input__inner {
      border-radius: 3px;
    }
  }
}
</style>
