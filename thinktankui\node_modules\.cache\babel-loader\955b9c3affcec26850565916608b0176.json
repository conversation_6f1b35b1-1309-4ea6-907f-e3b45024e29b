{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "version", "methods", "goTarget", "href", "window", "open"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container home\">\n    <el-row :gutter=\"20\">\n      <el-col :sm=\"24\" :lg=\"24\">\n        <blockquote class=\"text-warning\" style=\"font-size: 14px\">\n          领取阿里云通用云产品1888优惠券\n          <br />\n          <el-link\n            href=\"https://www.aliyun.com/minisite/goods?userCode=brki8iof\"\n            type=\"primary\"\n            target=\"_blank\"\n            >https://www.aliyun.com/minisite/goods?userCode=brki8iof</el-link\n          >\n          <br />\n          领取腾讯云通用云产品2860优惠券\n          <br />\n          <el-link\n            href=\"https://cloud.tencent.com/redirect.php?redirect=1025&cps_key=********************************&from=console\"\n            type=\"primary\"\n            target=\"_blank\"\n            >https://cloud.tencent.com/redirect.php?redirect=1025&cps_key=********************************&from=console</el-link\n          >\n          <br />\n          阿里云服务器折扣区\n          <el-link href=\"http://aly.ruoyi.vip\" type=\"primary\" target=\"_blank\"\n            >>☛☛点我进入☚☚</el-link\n          >\n          &nbsp;&nbsp;&nbsp; 腾讯云服务器秒杀区\n          <el-link href=\"http://txy.ruoyi.vip\" type=\"primary\" target=\"_blank\"\n            >>☛☛点我进入☚☚</el-link\n          ><br />\n          <h4 class=\"text-danger\">\n            云产品通用红包，可叠加官网常规优惠使用。(仅限新用户)\n          </h4>\n        </blockquote>\n\n        <hr />\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"20\">\n      <el-col :sm=\"24\" :lg=\"12\" style=\"padding-left: 20px\">\n        <h2>若依后台管理框架</h2>\n        <p>\n          一直想做一款后台管理系统，看了很多优秀的开源项目但是发现没有合适自己的。于是利用空闲休息时间开始自己写一套后台系统。如此有了若依管理系统，她可以用于所有的Web应用程序，如网站管理后台，网站会员中心，CMS，CRM，OA等等，当然，您也可以对她进行深度定制，以做出更强系统。所有前端后台代码封装过后十分精简易上手，出错概率低。同时支持移动客户端访问。系统会陆续更新一些实用功能。\n        </p>\n        <p>\n          <b>当前版本:</b> <span>v{{ version }}</span>\n        </p>\n        <p>\n          <el-tag type=\"danger\">&yen;免费开源</el-tag>\n        </p>\n        <p>\n          <el-button\n            type=\"primary\"\n            size=\"mini\"\n            icon=\"el-icon-cloudy\"\n            plain\n            @click=\"goTarget('https://gitee.com/y_project/RuoYi-Vue')\"\n            >访问码云</el-button\n          >\n          <el-button\n            size=\"mini\"\n            icon=\"el-icon-s-home\"\n            plain\n            @click=\"goTarget('http://ruoyi.vip')\"\n            >访问主页</el-button\n          >\n        </p>\n      </el-col>\n\n      <el-col :sm=\"24\" :lg=\"12\" style=\"padding-left: 50px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <h2>技术选型</h2>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"6\">\n            <h4>后端技术</h4>\n            <ul>\n              <li>SpringBoot</li>\n              <li>Spring Security</li>\n              <li>JWT</li>\n              <li>MyBatis</li>\n              <li>Druid</li>\n              <li>Fastjson</li>\n              <li>...</li>\n            </ul>\n          </el-col>\n          <el-col :span=\"6\">\n            <h4>前端技术</h4>\n            <ul>\n              <li>Vue</li>\n              <li>Vuex</li>\n              <li>Element-ui</li>\n              <li>Axios</li>\n              <li>Sass</li>\n              <li>Quill</li>\n              <li>...</li>\n            </ul>\n          </el-col>\n        </el-row>\n      </el-col>\n    </el-row>\n    <el-divider />\n    <el-row :gutter=\"20\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"8\">\n        <el-card class=\"update-log\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>联系信息</span>\n          </div>\n          <div class=\"body\">\n            <p>\n              <i class=\"el-icon-s-promotion\"></i> 官网：<el-link\n                href=\"http://www.ruoyi.vip\"\n                target=\"_blank\"\n                >http://www.ruoyi.vip</el-link\n              >\n            </p>\n            <p>\n              <i class=\"el-icon-user-solid\"></i> QQ群：<s> 满937441 </s> <s> 满887144332 </s>\n              <s> 满180251782 </s> <s> 满104180207 </s> <s> 满186866453 </s> <s> 满201396349 </s>\n              <s> 满101456076 </s> <s> 满101539465 </s> <s> 满264312783 </s> <s> 满167385320 </s> \n              <s> 满104748341 </s> <s> 满160110482 </s> <s> 满170801498 </s> <s> 满108482800 </s> \n              <s> 满101046199 </s> <s> 满136919097 </s> <s> 满143961921 </s> <s> 满174951577 </s> <a href=\"http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=Lj0nHRAPD5CZv1jTOciuxXVloBJLS2Lp&authKey=Q2RxC6%2Ffxney9yOGBY0sDJxFhX9b7o1FRY1bsESmkbcZ4PFt6Vx92FpVo9O1u9p4&noverify=0&group_code=161281055\" target=\"_blank\">161281055</a>\n            </p>\n            <p>\n              <i class=\"el-icon-chat-dot-round\"></i> 微信：<a\n                href=\"javascript:;\"\n                >/ *若依</a\n              >\n            </p>\n            <p>\n              <i class=\"el-icon-money\"></i> 支付宝：<a\n                href=\"javascript:;\"\n                class=\"支付宝信息\"\n                >/ *若依</a\n              >\n            </p>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"8\">\n        <el-card class=\"update-log\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>更新日志</span>\n          </div>\n          <el-collapse accordion>\n            <el-collapse-item title=\"v3.8.7 - 2023-12-08\">\n              <ol>\n                <li>操作日志记录部门名称</li>\n                <li>全局数据存储用户编号</li>\n                <li>新增编程式判断资源访问权限</li>\n                <li>操作日志列表新增IP地址查询</li>\n                <li>定时任务新增页去除状态选项</li>\n                <li>代码生成支持选择前端模板类型</li>\n                <li>显隐列组件支持复选框弹出类型</li>\n                <li>通用排序属性orderBy参数限制长度</li>\n                <li>Excel自定义数据处理器增加单元格/工作簿对象</li>\n                <li>升级oshi到最新版本6.4.8</li>\n                <li>升级druid到最新版本1.2.20</li>\n                <li>升级fastjson到最新版2.0.43</li>\n                <li>升级pagehelper到最新版1.4.7</li>\n                <li>升级commons.io到最新版本2.13.0</li>\n                <li>升级element-ui到最新版本2.15.14</li>\n                <li>修复五级路由缓存无效问题</li>\n                <li>修复外链带端口出现的异常</li>\n                <li>修复树模板父级编码变量错误</li>\n                <li>修复字典表详情页面搜索问题</li>\n                <li>修复内链iframe没有传递参数问题</li>\n                <li>修复自定义字典样式不生效的问题</li>\n                <li>修复字典缓存删除方法参数错误问题</li>\n                <li>修复Excel导入数据临时文件无法删除问题</li>\n                <li>修复未登录带参数访问成功后参数丢失问题</li>\n                <li>修复HeaderSearch组件跳转query参数丢失问题</li>\n                <li>修复代码生成导入后必填项与数据库不匹配问题</li>\n                <li>修复Excels导入时无法获取到dictType字典值问题</li>\n                <li>优化下载zip方法新增遮罩层</li>\n                <li>优化头像上传参数新增文件名称</li>\n                <li>优化字典标签支持自定义分隔符</li>\n                <li>优化菜单管理类型为按钮状态可选</li>\n                <li>优化前端防重复提交数据大小限制</li>\n                <li>优化TopNav菜单没有图标svg不显示</li>\n                <li>优化数字金额大写转换精度丢失问题</li>\n                <li>优化富文本Editor组件检验图片格式</li>\n                <li>优化页签在Firefox浏览器被遮挡的问题</li>\n                <li>优化个人中心/基本资料修改时数据显示问题</li>\n                <li>优化缓存监控图表支持跟随屏幕大小自适应调整</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.6 - 2023-06-30\">\n              <ol>\n                <li>支持登录IP黑名单限制</li>\n                <li>新增监控页面图标显示</li>\n                <li>操作日志新增消耗时间属性</li>\n                <li>屏蔽定时任务bean违规的字符</li>\n                <li>日志管理使用索引提升查询性能</li>\n                <li>日志注解支持排除指定的请求参数</li>\n                <li>支持自定义隐藏属性列过滤子对象</li>\n                <li>升级oshi到最新版本6.4.3</li>\n                <li>升级druid到最新版本1.2.16</li>\n                <li>升级fastjson到最新版2.0.34</li>\n                <li>升级spring-boot到最新版本2.5.15</li>\n                <li>升级element-ui到最新版本2.15.13</li>\n                <li>移除apache/commons-fileupload依赖</li>\n                <li>修复页面切换时布局错乱的问题</li>\n                <li>修复匿名注解Anonymous空指针问题</li>\n                <li>修复路由跳转被阻止时内部产生报错信息问题</li>\n                <li>修复isMatchedIp的参数判断产生空指针的问题</li>\n                <li>修复用户多角色数据权限可能出现权限抬升的情况</li>\n                <li>修复开启TopNav后一级菜单路由参数设置无效问题</li>\n                <li>修复DictTag组件value没有匹配的值时则展示value</li>\n                <li>优化文件下载出现的异常</li>\n                <li>优化选择图标组件高亮回显</li>\n                <li>优化弹窗后导航栏偏移的问题</li>\n                <li>优化修改密码日志存储明文问题</li>\n                <li>优化页签栏关闭其他出现的异常问题</li>\n                <li>优化页签关闭左侧选项排除首页选项</li>\n                <li>优化关闭当前tab页跳转最右侧tab页</li>\n                <li>优化缓存列表清除操作提示不变的问题</li>\n                <li>优化字符未使用下划线不进行驼峰式处理</li>\n                <li>优化用户导入更新时需获取用户编号问题</li>\n                <li>优化侧边栏的平台标题与VUE_APP_TITLE保持同步</li>\n                <li>优化导出Excel时设置dictType属性重复查缓存问题</li>\n                <li>连接池Druid支持新的配置connectTimeout和socketTimeout</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.5 - 2023-01-01\">\n              <ol>\n                <li>定时任务违规的字符</li>\n                <li>重置时取消部门选中</li>\n                <li>新增返回警告消息提示</li>\n                <li>忽略不必要的属性数据返回</li>\n                <li>修改参数键名时移除前缓存配置</li>\n                <li>导入更新用户数据前校验数据权限</li>\n                <li>兼容Excel下拉框内容过多无法显示的问题</li>\n                <li>升级echarts到最新版本5.4.0</li>\n                <li>升级core-js到最新版本3.25.3</li>\n                <li>升级oshi到最新版本6.4.0</li>\n                <li>升级kaptcha到最新版2.3.3</li>\n                <li>升级druid到最新版本1.2.15</li>\n                <li>升级fastjson到最新版2.0.20</li>\n                <li>升级pagehelper到最新版1.4.6</li>\n                <li>优化弹窗内容过多展示不全问题</li>\n                <li>优化swagger-ui静态资源使用缓存</li>\n                <li>开启TopNav没有子菜单隐藏侧边栏</li>\n                <li>删除fuse无效选项maxPatternLength</li>\n                <li>优化导出对象的子列表为空会出现[]问题</li>\n                <li>优化编辑头像时透明部分会变成黑色问题</li>\n                <li>优化小屏幕上修改头像界面布局错位的问题</li>\n                <li>修复代码生成勾选属性无效问题</li>\n                <li>修复文件上传组件格式验证问题</li>\n                <li>修复回显数据字典数组异常问题</li>\n                <li>修复sheet超出最大行数异常问题</li>\n                <li>修复Log注解GET请求记录不到参数问题</li>\n                <li>修复调度日志点击多次数据不变化的问题</li>\n                <li>修复主题颜色在Drawer组件不会加载问题</li>\n                <li>修复文件名包含特殊字符的文件无法下载问题</li>\n                <li>修复table中更多按钮切换主题色未生效修复问题</li>\n                <li>修复某些特性的环境生成代码变乱码TXT文件问题</li>\n                <li>修复代码生成图片/文件/单选时选择必填无法校验问题</li>\n                <li>修复某些特性的情况用户编辑对话框中角色和部门无法修改问题</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.4 - 2022-09-26\">\n              <ol>\n                <li>数据逻辑删除不进行唯一验证</li>\n                <li>Excel注解支持导出对象的子列表方法</li>\n                <li>Excel注解支持自定义隐藏属性列</li>\n                <li>Excel注解支持backgroundColor属性设置背景色</li>\n                <li>支持配置密码最大错误次数/锁定时间</li>\n                <li>登录日志新增解锁账户功能</li>\n                <li>通用下载方法新增config配置选项</li>\n                <li>支持多权限字符匹配角色数据权限</li>\n                <li>页面内嵌iframe切换tab不刷新数据</li>\n                <li>操作日志记录支持排除敏感属性字段</li>\n                <li>修复多文件上传报错出现的异常问题</li>\n                <li>修复图片预览组件src属性为null值控制台报错问题</li>\n                <li>升级oshi到最新版本6.2.2</li>\n                <li>升级fastjson到最新版2.0.14</li>\n                <li>升级pagehelper到最新版1.4.3</li>\n                <li>升级core-js到最新版本3.25.2</li>\n                <li>升级element-ui到最新版本2.15.10</li>\n                <li>优化任务过期不执行调度</li>\n                <li>优化字典数据使用store存取</li>\n                <li>优化修改资料头像被覆盖的问题</li>\n                <li>优化修改用户登录账号重复验证</li>\n                <li>优化代码生成同步后值NULL问题</li>\n                <li>优化定时任务支持执行父类方法</li>\n                <li>优化用户个人信息接口防止修改部门</li>\n                <li>优化布局设置使用el-drawer抽屉显示</li>\n                <li>优化没有权限的用户编辑部门缺少数据</li>\n                <li>优化日志注解记录限制请求地址的长度</li>\n                <li>优化excel/scale属性导出单元格数值类型</li>\n                <li>优化日志操作中重置按钮时重复查询的问题</li>\n                <li>优化多个相同角色数据导致权限SQL重复问题</li>\n                <li>优化表格上右侧工具条（搜索按钮显隐&右侧样式凸出）</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.3 - 2022-06-27\">\n              <ol>\n                <li>新增缓存列表菜单功能</li>\n                <li>代码生成树表新增(展开/折叠)</li>\n                <li>Excel注解支持color字体颜色</li>\n                <li>新增Anonymous匿名访问不鉴权注解</li>\n                <li>用户头像上传限制只能为图片格式</li>\n                <li>接口使用泛型使其看到响应属性字段</li>\n                <li>检查定时任务bean所在包名是否为白名单配置</li>\n                <li>添加页签openPage支持传递参数</li>\n                <li>用户缓存信息添加部门ancestors祖级列表</li>\n                <li>升级element-ui到最新版本2.15.8</li>\n                <li>升级oshi到最新版本6.1.6</li>\n                <li>升级druid到最新版本1.2.11</li>\n                <li>升级fastjson到最新版2.0.8</li>\n                <li>升级spring-boot到最新版本2.5.14</li>\n                <li>降级jsencrypt版本兼容IE浏览器</li>\n                <li>删除多余的salt字段</li>\n                <li>新增获取不带后缀文件名称方法</li>\n                <li>新增获取配置文件中的属性值方法</li>\n                <li>新增内容编码/解码方便插件集成使用</li>\n                <li>字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）</li>\n                <li>优化设置分页参数默认值</li>\n                <li>优化对空字符串参数处理的过滤</li>\n                <li>优化显示顺序orderNum类型为整型</li>\n                <li>优化表单构建按钮不显示正则校验</li>\n                <li>优化字典数据回显样式下拉框显示值</li>\n                <li>优化R响应成功状态码与全局保持一致</li>\n                <li>优化druid开启wall过滤器出现的异常问题</li>\n                <li>优化用户管理左侧树型组件增加选中高亮保持</li>\n                <li>优化新增用户与角色信息&用户与岗位信息逻辑</li>\n                <li>优化默认不启用压缩文件缓存防止node_modules过大</li>\n                <li>修复字典数据显示不全问题</li>\n                <li>修复操作日志查询类型条件为0时会查到所有数据</li>\n                <li>修复Excel注解prompt/combo同时使用不生效问题</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.2 - 2022-04-01\">\n              <ol>\n                <li>前端支持设置是否需要防止数据重复提交</li>\n                <li>开启TopNav没有子菜单情况隐藏侧边栏</li>\n                <li>侧边栏菜单名称过长悬停显示标题</li>\n                <li>用户访问控制时校验数据权限，防止越权</li>\n                <li>导出Excel时屏蔽公式，防止CSV注入风险</li>\n                <li>组件ImagePreview支持多图预览显示</li>\n                <li>组件ImageUpload支持多图同时选择上传</li>\n                <li>组件FileUpload支持多文件同时选择上传</li>\n                <li>服务监控新增运行参数信息显示</li>\n                <li>定时任务目标字符串过滤特殊字符</li>\n                <li>定时任务目标字符串验证包名白名单</li>\n                <li>代码生成列表图片支持预览</li>\n                <li>代码生成编辑修改打开新页签</li>\n                <li>代码生成新增Java类型Boolean</li>\n                <li>代码生成子表支持日期/字典配置</li>\n                <li>代码生成同步保留必填/类型选项</li>\n                <li>升级oshi到最新版本6.1.2</li>\n                <li>升级fastjson到最新版1.2.80</li>\n                <li>升级pagehelper到最新版1.4.1</li>\n                <li>升级spring-boot到最新版本2.5.11</li>\n                <li>升级spring-boot-mybatis到最新版2.2.2</li>\n                <li>添加遗漏的分页参数合理化属性</li>\n                <li>修改npm即将过期的注册源地址</li>\n                <li>修复分页组件请求两次问题</li>\n                <li>修复通用文件下载接口跨域问题</li>\n                <li>修复Xss注解字段值为空时的异常问题</li>\n                <li>修复选项卡点击右键刷新丢失参数问题</li>\n                <li>修复表单清除元素位置未垂直居中问题</li>\n                <li>修复服务监控中运行参数显示条件错误</li>\n                <li>修复导入Excel时字典字段类型为Long转义为空问题</li>\n                <li>修复登录超时刷新页面跳转登录页面还提示重新登录问题</li>\n                <li>优化加载字典缓存数据</li>\n                <li>优化IP地址获取到多个的问题</li>\n                <li>优化任务队列满时任务拒绝策略</li>\n                <li>优化文件上传兼容Weblogic环境</li>\n                <li>优化定时任务默认保存到内存中执行</li>\n                <li>优化部门修改缩放后出现的错位问题</li>\n                <li>优化Excel格式化不同类型的日期对象</li>\n                <li>优化菜单表关键字导致的插件报错问题</li>\n                <li>优化Oracle用户头像列为空时不显示问题</li>\n                <li>优化页面若未匹配到字典标签则返回原字典值</li>\n                <li>优化修复登录失效后多次请求提示多次弹窗问题</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.1 - 2022-01-01\">\n              <ol>\n                <li>新增Vue3前端代码生成模板</li>\n                <li>新增图片预览组件</li>\n                <li>新增压缩插件实现打包Gzip</li>\n                <li>自定义xss校验注解实现</li>\n                <li>自定义文字复制剪贴指令</li>\n                <li>代码生成预览支持复制内容</li>\n                <li>路由支持单独配置菜单或角色权限</li>\n                <li>用户管理部门查询选择节点后分页参数初始</li>\n                <li>修复用户分配角色属性错误</li>\n                <li>修复打包后字体图标偶现的乱码问题</li>\n                <li>修复菜单管理重置表单出现的错误</li>\n                <li>修复版本差异导致的懒加载报错问题</li>\n                <li>修复Cron组件中周回显问题</li>\n                <li>修复定时任务多参数逗号分隔的问题</li>\n                <li>修复根据ID查询列表可能出现的主键溢出问题</li>\n                <li>修复tomcat配置参数已过期问题</li>\n                <li>升级clipboard到最新版本2.0.8</li>\n                <li>升级oshi到最新版本v5.8.6</li>\n                <li>升级fastjson到最新版1.2.79</li>\n                <li>升级spring-boot到最新版本2.5.8</li>\n                <li>升级log4j2到2.17.1，防止漏洞风险</li>\n                <li>优化下载解析blob异常提示</li>\n                <li>优化代码生成字典组重复问题</li>\n                <li>优化查询用户的角色组&岗位组代码</li>\n                <li>优化定时任务cron表达式小时设置24</li>\n                <li>优化用户导入提示溢出则显示滚动条</li>\n                <li>优化防重复提交标识组合为(key+url+header)</li>\n                <li>优化分页方法设置成通用方便灵活调用</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.8.0 - 2021-12-01\">\n              <ol>\n                <li>新增配套并同步的Vue3前端版本</li>\n                <li>新增通用方法简化模态/缓存/下载/权限/页签使用</li>\n                <li>优化导出数据/使用通用下载方法</li>\n                <li>Excel注解支持自定义数据处理器</li>\n                <li>Excel注解支持导入导出标题信息</li>\n                <li>Excel导入支持@Excels注解</li>\n                <li>新增组件data-dict，简化数据字典使用</li>\n                <li>新增Jaxb依赖，防止jdk8以上出现的兼容错误</li>\n                <li>生产环境使用路由懒加载提升页面响应速度</li>\n                <li>修复五级以上菜单出现的404问题</li>\n                <li>防重提交注解支持配置间隔时间/提示消息</li>\n                <li>日志注解新增是否保存响应参数</li>\n                <li>任务屏蔽违规字符&参数忽略双引号中的逗号</li>\n                <li>升级SpringBoot到最新版本2.5.6</li>\n                <li>升级pagehelper到最新版1.4.0</li>\n                <li>升级spring-boot-mybatis到最新版2.2.0</li>\n                <li>升级oshi到最新版本v5.8.2</li>\n                <li>升级druid到最新版1.2.8</li>\n                <li>升级velocity到最新版本2.3</li>\n                <li>升级fastjson到最新版1.2.78</li>\n                <li>升级axios到最新版本0.24.0</li>\n                <li>升级dart-sass到版本1.32.13</li>\n                <li>升级core-js到最新版本3.19.1</li>\n                <li>升级jsencrypt到最新版本3.2.1</li>\n                <li>升级js-cookie到最新版本3.0.1</li>\n                <li>升级file-saver到最新版本2.0.5</li>\n                <li>升级sass-loader到最新版本10.1.1</li>\n                <li>升级element-ui到最新版本2.15.6</li>\n                <li>新增sendGet无参请求方法</li>\n                <li>禁用el-tag组件的渐变动画</li>\n                <li>代码生成点击预览重置激活tab</li>\n                <li>AjaxResult重写put方法，以方便链式调用</li>\n                <li>优化登录/验证码请求headers不设置token</li>\n                <li>优化用户个人信息接口防止修改用户名</li>\n                <li>优化Cron表达式生成器关闭时销毁避免缓存</li>\n                <li>优化注册成功提示消息类型success</li>\n                <li>优化aop语法，使用spring自动注入注解</li>\n                <li>优化记录登录信息，移除不必要的修改</li>\n                <li>优化mybatis全局默认的执行器</li>\n                <li>优化Excel导入图片可能出现的异常</li>\n                <li>修复代码生成模板主子表删除缺少事务</li>\n                <li>修复日志记录可能出现的转换异常</li>\n                <li>修复代码生成复选框字典遗漏问题</li>\n                <li>修复关闭xss功能导致可重复读RepeatableFilter失效</li>\n                <li>修复字符串无法被反转义问题</li>\n                <li>修复后端主子表代码模板方法名生成错误问题</li>\n                <li>修复xss过滤后格式出现的异常</li>\n                <li>修复swagger没有指定dataTypeClass导致启动出现warn日志</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.7.0 - 2021-09-13\">\n              <ol>\n                <li>参数管理支持配置验证码开关</li>\n                <li>新增是否开启用户注册功能</li>\n                <li>定时任务支持在线生成cron表达式</li>\n                <li>菜单管理支持配置路由参数</li>\n                <li>支持自定义注解实现接口限流</li>\n                <li>Excel注解支持Image图片导入</li>\n                <li>自定义弹层溢出滚动样式</li>\n                <li>自定义可拖动弹窗宽度指令</li>\n                <li>自定义可拖动弹窗高度指令</li>\n                <li>修复任意账户越权问题</li>\n                <li>修改时检查用户数据权限范围</li>\n                <li>修复保存配置主题颜色失效问题</li>\n                <li>新增暗色菜单风格主题</li>\n                <li>菜单&部门新增展开/折叠功能</li>\n                <li>页签新增关闭左侧&添加图标</li>\n                <li>顶部菜单排除隐藏的默认路由</li>\n                <li>顶部菜单同步系统主题样式</li>\n                <li>跳转路由高亮相对应的菜单栏</li>\n                <li>代码生成主子表多选行数据</li>\n                <li>日期范围支持添加多组</li>\n                <li>升级element-ui到最新版本2.15.5</li>\n                <li>升级oshi到最新版本v5.8.0</li>\n                <li>升级commons.io到最新版本v2.11.0</li>\n                <li>定时任务屏蔽ldap远程调用</li>\n                <li>定时任务屏蔽http(s)远程调用</li>\n                <li>补充定时任务表字段注释</li>\n                <li>定时任务对检查异常进行事务回滚</li>\n                <li>启用父部门状态排除顶级节点</li>\n                <li>富文本新增上传文件大小限制</li>\n                <li>默认首页使用keep-alive缓存</li>\n                <li>修改代码生成字典回显样式</li>\n                <li>自定义分页合理化传入参数</li>\n                <li>修复字典组件值为整形不显示问题</li>\n                <li>修复定时任务日志执行状态显示</li>\n                <li>角色&菜单新增字段属性提示信息</li>\n                <li>修复角色分配用户页面参数类型错误提醒</li>\n                <li>优化布局设置动画特效</li>\n                <li>优化异常处理信息</li>\n                <li>优化错误token导致的解析异常</li>\n                <li>密码框新增显示切换密码图标</li>\n                <li>定时任务新增更多操作</li>\n                <li>更多操作按钮添加权限控制</li>\n                <li>导入用户样式优化</li>\n                <li>提取通用方法到基类控制器</li>\n                <li>优化使用权限工具获取用户信息</li>\n                <li>优化用户不能删除自己</li>\n                <li>优化XSS跨站脚本过滤</li>\n                <li>优化代码生成模板</li>\n                <li>验证码默认20s超时</li>\n                <li>BLOB下载时清除URL对象引用</li>\n                <li>代码生成导入表按创建时间排序</li>\n                <li>修复代码生成页面数据编辑保存之后总是跳转第一页的问题</li>\n                <li>修复带safari浏览器无法格式化utc日期格式yyyy-MM-dd'T'HH:mm:ss.SSS问题</li>\n                <li>多图上传组件移除多余的api地址&验证失败导致图片删除问题&无法删除相应图片修复</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.6.0 - 2021-07-12\">\n              <ol>\n                <li>角色管理新增分配用户功能</li>\n                <li>用户管理新增分配角色功能</li>\n                <li>日志列表支持排序操作</li>\n                <li>优化参数&字典缓存操作</li>\n                <li>系统布局配置支持动态标题开关</li>\n                <li>菜单路由配置支持内链访问</li>\n                <li>默认访问后端首页新增提示语</li>\n                <li>富文本默认上传返回url类型</li>\n                <li>新增自定义弹窗拖拽指令</li>\n                <li>全局注册常用通用组件</li>\n                <li>全局挂载字典标签组件</li>\n                <li>ImageUpload组件支持多图片上传</li>\n                <li>FileUpload组件支持多文件上传</li>\n                <li>文件上传组件添加数量限制属性</li>\n                <li>富文本编辑组件添加类型属性</li>\n                <li>富文本组件工具栏配置视频</li>\n                <li>封装通用iframe组件</li>\n                <li>限制超级管理员不允许操作</li>\n                <li>用户信息长度校验限制</li>\n                <li>分页组件新增pagerCount属性</li>\n                <li>添加bat脚本执行应用</li>\n                <li>升级oshi到最新版本v5.7.4</li>\n                <li>升级element-ui到最新版本2.15.2</li>\n                <li>升级pagehelper到最新版1.3.1</li>\n                <li>升级commons.io到最新版本v2.10.0</li>\n                <li>升级commons.fileupload到最新版本v1.4</li>\n                <li>升级swagger到最新版本v3.0.0</li>\n                <li>修复关闭confirm提示框控制台报错问题</li>\n                <li>修复存在的SQL注入漏洞问题</li>\n                <li>定时任务屏蔽rmi远程调用</li>\n                <li>修复用户搜索分页变量错误</li>\n                <li>修复导出角色数据范围翻译缺少仅本人</li>\n                <li>修复表单构建选择下拉选择控制台报错问题</li>\n                <li>优化图片工具类读取文件</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.5.0 - 2021-05-25\">\n              <ol>\n                <li>新增菜单导航显示风格TopNav（false为左侧导航菜单，true为顶部导航菜单）</li>\n                <li>布局设置支持保存&重置配置</li>\n                <li>修复树表数据显示不全&加载慢问题</li>\n                <li>新增IE浏览器版本过低提示页面</li>\n                <li>用户登录后记录最后登录IP&时间</li>\n                <li>页面导出按钮点击之后添加遮罩</li>\n                <li>富文本编辑器支持自定义上传地址</li>\n                <li>富文本编辑组件新增readOnly属性</li>\n                <li>页签TagsView新增关闭右侧功能</li>\n                <li>显隐列组件加载初始默认隐藏列</li>\n                <li>关闭头像上传窗口还原默认图片</li>\n                <li>个人信息添加手机&邮箱重复验证</li>\n                <li>代码生成模板导出按钮点击后添加遮罩</li>\n                <li>代码生成模板树表操作列添加新增按钮</li>\n                <li>代码生成模板修复主子表字段重名问题</li>\n                <li>升级fastjson到最新版1.2.76</li>\n                <li>升级druid到最新版本v1.2.6</li>\n                <li>升级mybatis到最新版3.5.6 阻止远程代码执行漏洞</li>\n                <li>升级oshi到最新版本v5.6.0</li>\n                <li>velocity剔除commons-collections版本，防止3.2.1版本的反序列化漏洞</li>\n                <li>数据监控页默认账户密码防止越权访问</li>\n                <li>修复firefox下表单构建拖拽会新打卡一个选项卡</li>\n                <li>修正后端导入表权限标识</li>\n                <li>修正前端操作日志&登录日志权限标识</li>\n                <li>设置Redis配置HashKey序列化</li>\n                <li>删除操作日志记录信息</li>\n                <li>上传媒体类型添加视频格式</li>\n                <li>修复请求形参未传值记录日志异常问题</li>\n                <li>优化xss校验json请求条件</li>\n                <li>树级结构更新子节点使用replaceFirst</li>\n                <li>优化ExcelUtil空值处理</li>\n                <li>日志记录过滤BindingResult对象，防止异常</li>\n                <li>修改主题后mini类型按钮无效问题</li>\n                <li>优化通用下载完成后删除节点</li>\n                <li>通用Controller添加响应返回消息</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.4.0 - 2021-02-22\">\n              <ol>\n                <li>代码生成模板支持主子表</li>\n                <li>表格右侧工具栏组件支持显隐列</li>\n                <li>图片组件添加预览&移除功能</li>\n                <li>Excel注解支持Image图片导出</li>\n                <li>操作按钮组调整为朴素按钮样式</li>\n                <li>代码生成支持文件上传组件</li>\n                <li>代码生成日期控件区分范围</li>\n                <li>代码生成数据库文本类型生成表单文本域</li>\n                <li>用户手机邮箱&菜单组件修改允许空字符串</li>\n                <li>升级SpringBoot到最新版本2.2.13 提升启动速度</li>\n                <li>升级druid到最新版本v1.2.4</li>\n                <li>升级fastjson到最新版1.2.75</li>\n                <li>升级element-ui到最新版本2.15.0</li>\n                <li>修复IE11浏览器报错问题</li>\n                <li>优化多级菜单之间切换无法缓存的问题</li>\n                <li>修复四级菜单无法显示问题</li>\n                <li>修正侧边栏静态路由丢失问题</li>\n                <li>修复角色管理-编辑角色-功能权限显示异常</li>\n                <li>配置文件新增redis数据库索引属性</li>\n                <li>权限工具类增加admin判断</li>\n                <li>角色非自定义权限范围清空选择值</li>\n                <li>修复导入数据为负浮点数时丢失精度问题</li>\n                <li>移除path-to-regexp正则匹配插件</li>\n                <li>修复生成树表代码异常</li>\n                <li>修改ip字段长度防止ipv6地址长度不够</li>\n                <li>防止get请求参数值为false或0等特殊值会导致无法正确的传参</li>\n                <li>登录后push添加catch防止出现检查错误</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.3.0 - 2020-12-14\">\n              <ol>\n                <li>新增缓存监控功能</li>\n                <li>支持主题风格配置</li>\n                <li>修复多级菜单之间切换无法缓存的问题</li>\n                <li>多级菜单自动配置组件</li>\n                <li>代码生成预览支持高亮显示</li>\n                <li>支持Get请求映射Params参数</li>\n                <li>删除用户和角色解绑关联</li>\n                <li>去除用户手机邮箱部门必填验证</li>\n                <li>Excel支持注解align对齐方式</li>\n                <li>Excel支持导入Boolean型数据</li>\n                <li>优化头像样式，鼠标移入悬停遮罩</li>\n                <li>代码生成预览提供滚动机制</li>\n                <li>代码生成删除多余的数字float类型</li>\n                <li>修正转换字符串的目标字符集属性</li>\n                <li>回显数据字典防止空值报错</li>\n                <li>日志记录增加过滤多文件场景</li>\n                <li>修改缓存Set方法可能导致嵌套的问题</li>\n                <li>移除前端一些多余的依赖</li>\n                <li>防止安全扫描YUI出现的风险提示</li>\n                <li>修改node-sass为dart-sass</li>\n                <li>升级SpringBoot到最新版本2.1.18</li>\n                <li>升级poi到最新版本4.1.2</li>\n                <li>升级oshi到最新版本v5.3.6</li>\n                <li>升级bitwalker到最新版本1.21</li>\n                <li>升级axios到最新版本0.21.0</li>\n                <li>升级element-ui到最新版本2.14.1</li>\n                <li>升级vue到最新版本2.6.12</li>\n                <li>升级vuex到最新版本3.6.0</li>\n                <li>升级vue-cli到版本4.5.9</li>\n                <li>升级vue-router到最新版本3.4.9</li>\n                <li>升级vue-cli到最新版本4.4.6</li>\n                <li>升级vue-cropper到最新版本0.5.5</li>\n                <li>升级clipboard到最新版本2.0.6</li>\n                <li>升级core-js到最新版本3.8.1</li>\n                <li>升级echarts到最新版本4.9.0</li>\n                <li>升级file-saver到最新版本2.0.4</li>\n                <li>升级fuse.js到最新版本6.4.3</li>\n                <li>升级js-beautify到最新版本1.13.0</li>\n                <li>升级js-cookie到最新版本2.2.1</li>\n                <li>升级path-to-regexp到最新版本6.2.0</li>\n                <li>升级quill到最新版本1.3.7</li>\n                <li>升级screenfull到最新版本5.0.2</li>\n                <li>升级sortablejs到最新版本1.10.2</li>\n                <li>升级vuedraggable到最新版本2.24.3</li>\n                <li>升级chalk到最新版本4.1.0</li>\n                <li>升级eslint到最新版本7.15.0</li>\n                <li>升级eslint-plugin-vue到最新版本7.2.0</li>\n                <li>升级lint-staged到最新版本10.5.3</li>\n                <li>升级runjs到最新版本4.4.2</li>\n                <li>升级sass-loader到最新版本10.1.0</li>\n                <li>升级script-ext-html-webpack-plugin到最新版本2.1.5</li>\n                <li>升级svg-sprite-loader到最新版本5.1.1</li>\n                <li>升级vue-template-compiler到最新版本2.6.12</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.2.1 - 2020-11-18\">\n              <ol>\n                <li>阻止任意文件下载漏洞</li>\n                <li>代码生成支持上传控件</li>\n                <li>新增图片上传组件</li>\n                <li>调整默认首页</li>\n                <li>升级druid到最新版本v1.2.2</li>\n                <li>mapperLocations配置支持分隔符</li>\n                <li>权限信息调整</li>\n                <li>调整sql默认时间</li>\n                <li>解决代码生成没有bit类型的问题</li>\n                <li>升级pagehelper到最新版1.3.0</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v3.2.0 - 2020-10-10\">\n              <ol>\n                <li>升级springboot版本到2.1.17 提升安全性</li>\n                <li>升级oshi到最新版本v5.2.5</li>\n                <li>升级druid到最新版本v1.2.1</li>\n                <li>升级jjwt到版本0.9.1</li>\n                <li>升级fastjson到最新版1.2.74</li>\n                <li>修改sass为node-sass，避免el-icon图标乱码</li>\n                <li>代码生成支持同步数据库</li>\n                <li>代码生成支持富文本控件</li>\n                <li>代码生成页面时不忽略remark属性</li>\n                <li>代码生成添加select必填选项</li>\n                <li>Excel导出类型NUMERIC支持精度浮点类型</li>\n                <li>Excel导出targetAttr优化获取值，防止get方法不规范</li>\n                <li>Excel注解支持自动统计数据总和</li>\n                <li>Excel注解支持设置BigDecimal精度&舍入规则</li>\n                <li>菜单&数据权限新增（展开/折叠 全选/全不选 父子联动）</li>\n                <li>允许用户分配到部门父节点</li>\n                <li>菜单新增是否缓存keep-alive</li>\n                <li>表格操作列间距调整</li>\n                <li>限制系统内置参数不允许删除</li>\n                <li>富文本组件优化，支持自定义高度&图片冲突问题</li>\n                <li>富文本工具栏样式对齐</li>\n                <li>导入excel整形值校验优化</li>\n                <li>修复页签关闭所有时固定标签路由不刷新问题</li>\n                <li>表单构建布局型组件新增按钮</li>\n                <li>左侧菜单文字过长显示省略号</li>\n                <li>修正根节点为子部门时，树状结构显示问题</li>\n                <li>修正调用目标字符串最大长度</li>\n                <li>修正菜单提示信息错误</li>\n                <li>修正定时任务执行一次权限标识</li>\n                <li>修正数据库字符串类型nvarchar</li>\n                <li>优化递归子节点</li>\n                <li>优化数据权限判断</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n\n            <el-collapse-item title=\"v3.1.0 - 2020-08-13\">\n              <ol>\n                <li>表格工具栏右侧添加刷新&显隐查询组件</li>\n                <li>后端支持CORS跨域请求</li>\n                <li>代码生成支持选择上级菜单</li>\n                <li>代码生成支持自定义路径</li>\n                <li>代码生成支持复选框</li>\n                <li>Excel导出导入支持dictType字典类型</li>\n                <li>Excel支持分割字符串组内容</li>\n                <li>验证码类型支持（数组计算、字符验证）</li>\n                <li>升级vue-cli版本到4.4.4</li>\n                <li>修改 node-sass 为 dart-sass</li>\n                <li>表单类型为Integer/Long设置整形默认值</li>\n                <li>代码生成器默认mapper路径与默认mapperScan路径不一致</li>\n                <li>优化防重复提交拦截器</li>\n                <li>优化上级菜单不能选择自己</li>\n                <li>修复角色的权限分配后，未实时生效问题</li>\n                <li>修复在线用户日志记录类型</li>\n                <li>修复富文本空格和缩进保存后不生效问题</li>\n                <li>修复在线用户判断逻辑</li>\n                <li>唯一限制条件只返回单条数据</li>\n                <li>添加获取当前的环境配置方法</li>\n                <li>超时登录后页面跳转到首页</li>\n                <li>全局异常状态汉化拦截处理</li>\n                <li>HTML过滤器改为将html转义</li>\n                <li>检查字符支持小数点&降级改成异常提醒</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n\n            <el-collapse-item title=\"v3.0.0 - 2020-07-20\">\n              <ol>\n                <li>单应用调整为多模块项目</li>\n                <li>升级element-ui版本到2.13.2</li>\n                <li>删除babel，提高编译速度。</li>\n                <li>新增菜单默认主类目</li>\n                <li>编码文件名修改为uuid方式</li>\n                <li>定时任务cron表达式验证</li>\n                <li>角色权限修改时已有权限未自动勾选异常修复</li>\n                <li>防止切换权限用户后登录出现404</li>\n                <li>Excel支持sort导出排序</li>\n                <li>创建用户不允许选择超级管理员角色</li>\n                <li>修复代码生成导入表结构出现异常页面不提醒问题</li>\n                <li>修复代码生成点击多次表修改数据不变化的问题</li>\n                <li>修复头像上传成功二次打开无法改变裁剪框大小和位置问题</li>\n                <li>修复布局为small者mini用户表单显示错位问题</li>\n                <li>修复热部署导致的强换异常问题</li>\n                <li>修改用户管理复选框宽度，防止部分浏览器出现省略号</li>\n                <li>IpUtils工具，清除Xss特殊字符，防止Xff注入攻击</li>\n                <li>生成domain 如果是浮点型 统一用BigDecimal</li>\n                <li>定时任务调整label-width，防止部署出现错位</li>\n                <li>调整表头固定列默认样式</li>\n                <li>代码生成模板调整，字段为String并且必填则加空串条件</li>\n                <li>代码生成字典Integer/Long使用parseInt</li>\n                <li>\n                  修复dict_sort不可update为0的问题&查询返回增加dict_sort升序排序\n                </li>\n                <li>修正岗位导出权限注解</li>\n                <li>禁止加密密文返回前端</li>\n                <li>修复代码生成页面中的查询条件创建时间未生效的问题</li>\n                <li>修复首页搜索菜单外链无法点击跳转问题</li>\n                <li>修复菜单管理选择图标，backspace删除时不过滤数据</li>\n                <li>用户管理部门分支节点不可检查&显示计数</li>\n                <li>数据范围过滤属性调整</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n\n            <el-collapse-item title=\"v2.3.0 - 2020-06-01\">\n              <ol>\n                <li>升级fastjson到最新版1.2.70 修复高危安全漏洞</li>\n                <li>dev启动默认打开浏览器</li>\n                <li>vue-cli使用默认source-map</li>\n                <li>slidebar eslint报错优化</li>\n                <li>当tags-view滚动关闭右键菜单</li>\n                <li>字典管理添加缓存读取</li>\n                <li>参数管理支持缓存操作</li>\n                <li>支持一级菜单（和主页同级）在main区域显示</li>\n                <li>限制外链地址必须以http(s)开头</li>\n                <li>tagview & sidebar 主题颜色与element ui(全局)同步</li>\n                <li>修改数据源类型优先级，先根据方法，再根据类</li>\n                <li>支持是否需要设置token属性，自定义返回码消息。</li>\n                <li>swagger请求前缀加入配置。</li>\n                <li>登录地点设置内容过长则隐藏显示</li>\n                <li>修复定时任务执行一次按钮后不提示消息问题</li>\n                <li>修改上级部门（选择项排除本身和下级）</li>\n                <li>通用http发送方法增加参数 contentType 编码类型</li>\n                <li>更换IP地址查询接口</li>\n                <li>修复页签变量undefined</li>\n                <li>添加校验部门包含未停用的子部门</li>\n                <li>修改定时任务详情下次执行时间日期显示错误</li>\n                <li>角色管理查询设置默认排序字段</li>\n                <li>swagger添加enable参数控制是否启用</li>\n                <li>只对json类型请求构建可重复读取inputStream的request</li>\n                <li>修改代码生成字典字段int类型没有自动选中问题</li>\n                <li>vuex用户名取值修正</li>\n                <li>表格树模板去掉多余的)</li>\n                <li>代码生成序号修正</li>\n                <li>全屏情况下不调整上外边距</li>\n                <li>代码生成Date字段添加默认格式</li>\n                <li>用户管理角色选择权限控制</li>\n                <li>修复路由懒加载报错问题</li>\n                <li>模板sql.vm添加菜单状态</li>\n                <li>设置用户名称不能修改</li>\n                <li>dialog添加append-to-body属性，防止ie遮罩</li>\n                <li>菜单区分状态和显示隐藏功能</li>\n                <li>升级fastjson到最新版1.2.68 修复安全加固</li>\n                <li>修复代码生成如果选择字典类型缺失逗号问题</li>\n                <li>登录请求params更换为data，防止暴露url</li>\n                <li>日志返回时间格式处理</li>\n                <li>添加handle控制允许拖动的元素</li>\n                <li>布局设置点击扩大范围</li>\n                <li>代码生成列属性排序查询</li>\n                <li>代码生成列支持拖动排序</li>\n                <li>修复时间格式不支持ios问题</li>\n                <li>表单构建添加父级class，防止冲突</li>\n                <li>定时任务并发属性修正</li>\n                <li>角色禁用&菜单隐藏不查询权限</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n\n            <el-collapse-item title=\"v2.2.0 - 2020-03-18\">\n              <ol>\n                <li>系统监控新增定时任务功能</li>\n                <li>添加一个打包Web工程bat</li>\n                <li>修复页签鼠标滚轮按下的时候，可以关闭不可关闭的tag</li>\n                <li>修复点击退出登录有时会无提示问题</li>\n                <li>修复防重复提交注解无效问题</li>\n                <li>修复通知公告批量删除异常问题</li>\n                <li>添加菜单时路由地址必填限制</li>\n                <li>代码生成字段描述可编辑</li>\n                <li>修复用户修改个人信息导致缓存不过期问题</li>\n                <li>个人信息创建时间获取正确属性值</li>\n                <li>操作日志详细显示正确类型</li>\n                <li>导入表单击行数据时选中对应的复选框</li>\n                <li>批量替换表前缀逻辑调整</li>\n                <li>固定重定向路径表达式</li>\n                <li>升级element-ui版本到2.13.0</li>\n                <li>操作日志排序调整</li>\n                <li>修复charts切换侧边栏或者缩放窗口显示bug</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n\n            <el-collapse-item title=\"v2.1.0 - 2020-02-24\">\n              <ol>\n                <li>新增表单构建</li>\n                <li>代码生成支持树表结构</li>\n                <li>新增用户导入</li>\n                <li>修复动态加载路由页面刷新问题</li>\n                <li>修复地址开关无效问题</li>\n                <li>汉化错误提示页面</li>\n                <li>代码生成已知问题修改</li>\n                <li>修复多数据源下配置关闭出现异常处理</li>\n                <li>添加HTML过滤器，用于去除XSS漏洞隐患</li>\n                <li>修复上传头像控制台出现异常</li>\n                <li>修改用户管理分页不正确的问题</li>\n                <li>修复验证码记录提示错误</li>\n                <li>修复request.js缺少Message引用</li>\n                <li>修复表格时间为空出现的异常</li>\n                <li>添加Jackson日期反序列化时区配置</li>\n                <li>调整根据用户权限加载菜单数据树形结构</li>\n                <li>调整成功登录不恢复按钮，防止多次点击</li>\n                <li>修改用户个人资料同步缓存信息</li>\n                <li>修复页面同时出现el-upload和Editor不显示处理</li>\n                <li>修复在角色管理页修改菜单权限偶尔未选中问题</li>\n                <li>配置文件新增redis密码属性</li>\n                <li>设置mybatis全局的配置文件</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n\n            <el-collapse-item title=\"v2.0.0 - 2019-12-02\">\n              <ol>\n                <li>新增代码生成</li>\n                <li>新增@RepeatSubmit注解，防止重复提交</li>\n                <li>新增菜单主目录添加/删除操作</li>\n                <li>日志记录过滤特殊对象，防止转换异常</li>\n                <li>修改代码生成路由脚本错误</li>\n                <li>用户上传头像实时同步缓存，无需重新登录</li>\n                <li>调整切换页签后不重新加载数据</li>\n                <li>添加jsencrypt实现参数的前端加密</li>\n                <li>系统退出删除用户缓存记录</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v1.1.0 - 2019-11-11\">\n              <ol>\n                <li>新增在线用户管理</li>\n                <li>新增按钮组功能实现（批量删除、导出、清空）</li>\n                <li>新增查询条件重置按钮</li>\n                <li>新增Swagger全局Token配置</li>\n                <li>新增后端参数校验</li>\n                <li>修复字典管理页面的日期查询异常</li>\n                <li>修改时间函数命名防止冲突</li>\n                <li>去除菜单上级校验，默认为顶级</li>\n                <li>修复用户密码无法修改问题</li>\n                <li>修复菜单类型为按钮时不显示权限标识</li>\n                <li>其他细节优化</li>\n              </ol>\n            </el-collapse-item>\n            <el-collapse-item title=\"v1.0.0 - 2019-10-08\">\n              <ol>\n                <li>若依前后端分离系统正式发布</li>\n              </ol>\n            </el-collapse-item>\n          </el-collapse>\n        </el-card>\n      </el-col>\n      <!-- <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"8\">\n        <el-card class=\"update-log\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>捐赠支持</span>\n          </div>\n          <div class=\"body\">\n            <img\n              src=\"@/assets/images/pay.png\"\n              alt=\"donate\"\n              width=\"100%\"\n            />\n            <span style=\"display: inline-block; height: 30px; line-height: 30px\"\n              >你可以请作者喝杯咖啡表示鼓励</span\n            >\n          </div>\n        </el-card>\n      </el-col> -->\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Index\",\n  data() {\n    return {\n      // 版本号\n      version: \"3.8.7\"\n    };\n  },\n  methods: {\n    goTarget(href) {\n      window.open(href, \"_blank\");\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.home {\n  blockquote {\n    padding: 10px 20px;\n    margin: 0 0 20px;\n    font-size: 17.5px;\n    border-left: 5px solid #eee;\n  }\n  hr {\n    margin-top: 20px;\n    margin-bottom: 20px;\n    border: 0;\n    border-top: 1px solid #eee;\n  }\n  .col-item {\n    margin-bottom: 20px;\n  }\n\n  ul {\n    padding: 0;\n    margin: 0;\n  }\n\n  font-family: \"open sans\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n  font-size: 13px;\n  color: #676a6c;\n  overflow-x: hidden;\n\n  ul {\n    list-style-type: none;\n  }\n\n  h4 {\n    margin-top: 0px;\n  }\n\n  h2 {\n    margin-top: 10px;\n    font-size: 26px;\n    font-weight: 100;\n  }\n\n  p {\n    margin-top: 10px;\n\n    b {\n      font-weight: 700;\n    }\n  }\n\n  .update-log {\n    ol {\n      display: block;\n      list-style-type: decimal;\n      margin-block-start: 1em;\n      margin-block-end: 1em;\n      margin-inline-start: 0;\n      margin-inline-end: 0;\n      padding-inline-start: 40px;\n    }\n  }\n}\n</style>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA09BA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}