{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\BarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\BarChart.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BarChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nconst animationDuration = 6000\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\n          }\n        },\n        grid: {\n          top: 10,\n          left: '2%',\n          right: '2%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: [{\n          type: 'category',\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n          axisTick: {\n            alignWithLabel: true\n          }\n        }],\n        yAxis: [{\n          type: 'value',\n          axisTick: {\n            show: false\n          }\n        }],\n        series: [{\n          name: 'pageA',\n          type: 'bar',\n          stack: 'vistors',\n          barWidth: '60%',\n          data: [79, 52, 200, 334, 390, 330, 220],\n          animationDuration\n        }, {\n          name: 'pageB',\n          type: 'bar',\n          stack: 'vistors',\n          barWidth: '60%',\n          data: [80, 52, 200, 334, 390, 330, 220],\n          animationDuration\n        }, {\n          name: 'pageC',\n          type: 'bar',\n          stack: 'vistors',\n          barWidth: '60%',\n          data: [30, 52, 200, 334, 390, 330, 220],\n          animationDuration\n        }]\n      })\n    }\n  }\n}\n</script>\n"]}]}