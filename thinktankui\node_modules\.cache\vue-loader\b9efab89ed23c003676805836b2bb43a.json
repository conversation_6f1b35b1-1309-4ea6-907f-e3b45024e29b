{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\AppMain.vue?vue&type=style&index=1&id=078753dd&lang=scss", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\AppMain.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8vIGZpeCBjc3Mgc3R5bGUgYnVnIGluIG9wZW4gZWwtZGlhbG9nCi5lbC1wb3B1cC1wYXJlbnQtLWhpZGRlbiB7CiAgLmZpeGVkLWhlYWRlciB7CiAgICBwYWRkaW5nLXJpZ2h0OiA2cHg7CiAgfQp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogNnB4OwogIGhlaWdodDogNnB4Owp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjFmMWYxOwp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjYzBjMGMwOwogIGJvcmRlci1yYWRpdXM6IDNweDsKfQo="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <keep-alive :include=\"cachedViews\">\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\n      </keep-alive>\n    </transition>\n    <iframe-toggle />\n  </section>\n</template>\n\n<script>\nimport iframeToggle from \"./IframeToggle/index\"\n\nexport default {\n  name: 'AppMain',\n  components: { iframeToggle },\n  computed: {\n    cachedViews() {\n      return this.$store.state.tagsView.cachedViews\n    },\n    key() {\n      return this.$route.path\n    }\n  },\n  watch: {\n    $route() {\n      this.addIframe()\n    }\n  },\n  mounted() {\n    this.addIframe()\n  },\n  methods: {\n    addIframe() {\n      const {name} = this.$route\n      if (name && this.$route.meta.link) {\n        this.$store.dispatch('tagsView/addIframeView', this.$route)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-main {\n  /* 50= navbar  50  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.fixed-header + .app-main {\n  padding-top: 50px;\n}\n\n.hasTagsView {\n  .app-main {\n    /* 84 = navbar + tags-view = 50 + 34 */\n    min-height: calc(100vh - 84px);\n  }\n\n  .fixed-header + .app-main {\n    padding-top: 84px;\n  }\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 6px;\n  }\n}\n\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background-color: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: #c0c0c0;\n  border-radius: 3px;\n}\n</style>\n"]}]}