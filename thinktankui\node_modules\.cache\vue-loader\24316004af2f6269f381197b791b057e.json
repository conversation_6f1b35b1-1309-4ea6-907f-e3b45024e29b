{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWVudUl0ZW0nLAogIGZ1bmN0aW9uYWw6IHRydWUsCiAgcHJvcHM6IHsKICAgIGljb246IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHRpdGxlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIHJlbmRlcihoLCBjb250ZXh0KSB7CiAgICBjb25zdCB7IGljb24sIHRpdGxlIH0gPSBjb250ZXh0LnByb3BzCiAgICBjb25zdCB2bm9kZXMgPSBbXQoKICAgIGlmIChpY29uKSB7CiAgICAgIHZub2Rlcy5wdXNoKDxzdmctaWNvbiBpY29uLWNsYXNzPXtpY29ufS8+KQogICAgfQoKICAgIGlmICh0aXRsZSkgewogICAgICBpZiAodGl0bGUubGVuZ3RoID4gNSkgewogICAgICAgIHZub2Rlcy5wdXNoKDxzcGFuIHNsb3Q9J3RpdGxlJyB0aXRsZT17KHRpdGxlKX0+eyh0aXRsZSl9PC9zcGFuPikKICAgICAgfSBlbHNlIHsKICAgICAgICB2bm9kZXMucHVzaCg8c3BhbiBzbG90PSd0aXRsZSc+eyh0aXRsZSl9PC9zcGFuPikKICAgICAgfQogICAgfQogICAgcmV0dXJuIHZub2RlcwogIH0KfQo="}, {"version": 3, "sources": ["Item.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Item.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<script>\nexport default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render(h, context) {\n    const { icon, title } = context.props\n    const vnodes = []\n\n    if (icon) {\n      vnodes.push(<svg-icon icon-class={icon}/>)\n    }\n\n    if (title) {\n      if (title.length > 5) {\n        vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)\n      } else {\n        vnodes.push(<span slot='title'>{(title)}</span>)\n      }\n    }\n    return vnodes\n  }\n}\n</script>\n"]}]}