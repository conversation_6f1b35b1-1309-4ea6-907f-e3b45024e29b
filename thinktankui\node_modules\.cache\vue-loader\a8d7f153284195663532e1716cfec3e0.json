{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\source-monitoring\\index.vue?vue&type=style&index=0&id=53d7ffda&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\source-monitoring\\index.vue", "mtime": 1747909704000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmMmY1OwogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsKfQoKLnBhZ2UtaGVhZGVyIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICBoMiB7CiAgICBtYXJnaW46IDA7CiAgICBmb250LXNpemU6IDIwcHg7CiAgICBmb250LXdlaWdodDogNTAwOwogIH0KfQoKLm1haW4tY29udGVudCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTUwcHgpOwp9Cgouc291cmNlLXRhYnMgewogIHBhZGRpbmc6IDIwcHg7CgogIC50YWItY29udGVudCB7CiAgICBtYXJnaW4tdG9wOiAyMHB4OwogIH0KfQoKLmZpbHRlci1zZWN0aW9uIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAuZmlsdGVyLXJvdyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CgogICAgLmZpbHRlci1sZWZ0IHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZ2FwOiAxMHB4OwogICAgfQoKICAgIC5maWx0ZXItcmlnaHQgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBnYXA6IDEwcHg7CgogICAgICAuc3RhdHVzLXRleHQgewogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzYwNjI2NjsKICAgICAgfQogICAgfQogIH0KCiAgLnNlYXJjaC1yb3cgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CgogICAgLnNlYXJjaC1sZWZ0IHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZ2FwOiAxMHB4OwogICAgfQoKICAgIC5zZWFyY2gtcmlnaHQgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgICAgLnRvdGFsLXRleHQgewogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzYwNjI2NjsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgICAgIH0KICAgIH0KICB9Cn0KCi50YWJsZS1zZWN0aW9uIHsKICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1OwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLmVtcHR5LXN0YXRlIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNjBweCAwOwogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTRweDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/source-monitoring", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <h2>信源监测</h2>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 标签页导航 -->\n      <el-tabs v-model=\"activeTab\" class=\"source-tabs\">\n        <el-tab-pane label=\"信源人员\" name=\"personnel\">\n          <div class=\"tab-content\">\n            <!-- 搜索和筛选区域 -->\n            <div class=\"filter-section\">\n              <div class=\"filter-row\">\n                <div class=\"filter-left\">\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\">接收人设置</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-warning\">预警设置</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-key\">关键词设置</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-star-off\">信源设置</el-button>\n                </div>\n                <div class=\"filter-right\">\n                  <span class=\"status-text\">预警开关</span>\n                  <el-switch v-model=\"monitorStatus\" active-color=\"#13ce66\"></el-switch>\n                </div>\n              </div>\n\n              <div class=\"search-row\">\n                <div class=\"search-left\">\n                  <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-download\">下载</el-button>\n                </div>\n                <div class=\"search-right\">\n                  <span class=\"total-text\">共0条记录</span>\n                  <el-select v-model=\"pageSize\" size=\"small\" style=\"width: 80px; margin: 0 10px;\">\n                    <el-option label=\"7条\" value=\"7\"></el-option>\n                    <el-option label=\"10条\" value=\"10\"></el-option>\n                    <el-option label=\"20条\" value=\"20\"></el-option>\n                  </el-select>\n                  <el-date-picker\n                    v-model=\"dateRange\"\n                    type=\"datetimerange\"\n                    range-separator=\"~\"\n                    start-placeholder=\"2023/04/23 00:00:00\"\n                    end-placeholder=\"2023/04/25 20:47:46\"\n                    size=\"small\"\n                    style=\"width: 350px; margin-right: 10px;\"\n                  ></el-date-picker>\n                  <el-select v-model=\"sortBy\" size=\"small\" style=\"width: 100px; margin-right: 10px;\">\n                    <el-option label=\"全部\" value=\"all\"></el-option>\n                  </el-select>\n                  <el-input\n                    v-model=\"searchKeyword\"\n                    placeholder=\"请输入关键词搜索\"\n                    size=\"small\"\n                    style=\"width: 200px;\"\n                    suffix-icon=\"el-icon-search\"\n                  ></el-input>\n                </div>\n              </div>\n            </div>\n\n            <!-- 表格区域 -->\n            <div class=\"table-section\">\n              <el-table\n                :data=\"tableData\"\n                style=\"width: 100%\"\n                height=\"400\"\n                empty-text=\"暂无数据\"\n              >\n                <el-table-column prop=\"media\" label=\"媒体\" width=\"120\"></el-table-column>\n                <el-table-column prop=\"platform\" label=\"平台\" width=\"100\"></el-table-column>\n                <el-table-column prop=\"publisher\" label=\"发布人\" width=\"120\"></el-table-column>\n                <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\"></el-table-column>\n                <el-table-column prop=\"content\" label=\"内容\" min-width=\"200\"></el-table-column>\n                <el-table-column prop=\"readCount\" label=\"阅读量\" width=\"100\"></el-table-column>\n                <el-table-column label=\"操作\" width=\"120\">\n                  <template slot-scope=\"scope\">\n                    <el-button type=\"text\" size=\"small\">查看</el-button>\n                    <el-button type=\"text\" size=\"small\">编辑</el-button>\n                  </template>\n                </el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"信源媒体\" name=\"media\">\n          <div class=\"tab-content\">\n            <div class=\"empty-state\">\n              <p>暂无数据</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"媒体监控\" name=\"monitoring\">\n          <div class=\"tab-content\">\n            <div class=\"empty-state\">\n              <p>暂无数据</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"媒体反馈\" name=\"feedback\">\n          <div class=\"tab-content\">\n            <div class=\"empty-state\">\n              <p>暂无数据</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SourceMonitoring',\n  data() {\n    return {\n      activeTab: 'personnel',\n      monitorStatus: true,\n      pageSize: '7',\n      dateRange: [],\n      sortBy: 'all',\n      searchKeyword: '',\n      tableData: [] // 暂无数据，所以为空数组\n    }\n  },\n  methods: {\n    // 这里可以添加各种方法\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: calc(100vh - 84px);\n}\n\n.page-header {\n  margin-bottom: 20px;\n\n  h2 {\n    margin: 0;\n    font-size: 20px;\n    font-weight: 500;\n  }\n}\n\n.main-content {\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  min-height: calc(100vh - 150px);\n}\n\n.source-tabs {\n  padding: 20px;\n\n  .tab-content {\n    margin-top: 20px;\n  }\n}\n\n.filter-section {\n  margin-bottom: 20px;\n\n  .filter-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .filter-left {\n      display: flex;\n      gap: 10px;\n    }\n\n    .filter-right {\n      display: flex;\n      align-items: center;\n      gap: 10px;\n\n      .status-text {\n        font-size: 14px;\n        color: #606266;\n      }\n    }\n  }\n\n  .search-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .search-left {\n      display: flex;\n      gap: 10px;\n    }\n\n    .search-right {\n      display: flex;\n      align-items: center;\n\n      .total-text {\n        font-size: 14px;\n        color: #606266;\n        margin-right: 15px;\n      }\n    }\n  }\n}\n\n.table-section {\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 0;\n  color: #909399;\n  font-size: 14px;\n}\n</style>\n"]}]}