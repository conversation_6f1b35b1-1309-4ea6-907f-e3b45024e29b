{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\meta-search\\index.vue?vue&type=style&index=0&id=102ebc49&scoped=true&lang=css", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\meta-search\\index.vue", "mtime": 1748099318318}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm1ldGEtc2VhcmNoLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOwogIG1pbi1oZWlnaHQ6IDEwMHZoOwp9Cgouc2VhcmNoLWhlYWRlciB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHBhZGRpbmc6IDIwcHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOwp9Cgouc2VhcmNoLXRhYnMgewogIGRpc3BsYXk6IGZsZXg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnRhYiB7CiAgcGFkZGluZzogOHB4IDE2cHg7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICBib3JkZXItYm90dG9tOiAycHggc29saWQgdHJhbnNwYXJlbnQ7Cn0KCi50YWIuYWN0aXZlIHsKICBjb2xvcjogIzQwOUVGRjsKICBib3JkZXItYm90dG9tOiAycHggc29saWQgIzQwOUVGRjsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLnNlYXJjaC1ib3ggewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLnNlYXJjaC1pbnB1dCB7CiAgZmxleDogMTsKICBoZWlnaHQ6IDQwcHg7CiAgcGFkZGluZzogMCAxNXB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIG1hcmdpbi1yaWdodDogMTBweDsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5zZWFyY2gtYnRuIHsKICBoZWlnaHQ6IDQwcHg7Cn0KCi5zZWFyY2gtcmVzdWx0cyB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9Cgouc2VhcmNoLWVuZ2luZXMgewogIGRpc3BsYXk6IGZsZXg7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHBhZGRpbmc6IDE1cHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOwp9CgouZW5naW5lLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiA4cHggMTZweDsKICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2OwogIGJhY2tncm91bmQ6ICNmZmY7Cn0KCi5lbmdpbmUtaXRlbTpob3ZlciB7CiAgYm9yZGVyLWNvbG9yOiAjYzZlMmZmOwp9CgouZW5naW5lLWl0ZW0uYWN0aXZlIHsKICBiYWNrZ3JvdW5kOiAjZWNmNWZmOwogIGNvbG9yOiAjNDA5RUZGOwogIGJvcmRlci1jb2xvcjogIzQwOUVGRjsKfQoKLmNoZWNrYm94IHsKICB3aWR0aDogMTZweDsKICBoZWlnaHQ6IDE2cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2RjZGZlNjsKICBib3JkZXItcmFkaXVzOiAycHg7CiAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGNvbG9yOiAjZmZmOwogIGJhY2tncm91bmQtY29sb3I6ICM0MDlFRkY7Cn0KCi5lbmdpbmUtaWNvbiB7CiAgd2lkdGg6IDE2cHg7CiAgaGVpZ2h0OiAxNnB4OwogIG1hcmdpbi1yaWdodDogOHB4Owp9CgoucmVzdWx0cy1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAyMHB4Owp9CgoucmVzdWx0LWNvbHVtbiB7CiAgZmxleDogMTsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoucmVzdWx0LWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDE1cHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7CiAgYmFja2dyb3VuZDogI2Y1ZjdmYTsKfQoKLnJlc3VsdC1pY29uIHsKICB3aWR0aDogMTZweDsKICBoZWlnaHQ6IDE2cHg7CiAgbWFyZ2luLXJpZ2h0OiA4cHg7Cn0KCi5yZXN1bHQtdGl0bGUgewogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjMzAzMTMzOwp9CgoucmVzdWx0LWxpc3QgewogIHBhZGRpbmc6IDE1cHg7Cn0KCi5yZXN1bHQtaXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBwYWRkaW5nLWJvdHRvbTogMTVweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsKfQoKLnJlc3VsdC1pdGVtOmxhc3QtY2hpbGQgewogIG1hcmdpbi1ib3R0b206IDA7CiAgcGFkZGluZy1ib3R0b206IDA7CiAgYm9yZGVyLWJvdHRvbTogbm9uZTsKfQoKLml0ZW0tdGl0bGUgewogIG1hcmdpbjogMCAwIDhweCAwOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLml0ZW0tdGl0bGUgYSB7CiAgY29sb3I6ICMwMzY2ZDY7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwp9CgouaXRlbS10aXRsZSBhOmhvdmVyIHsKICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsKfQoKLml0ZW0tdXJsIHsKICBjb2xvcjogIzY3YzIzYTsKICBmb250LXNpemU6IDEycHg7CiAgbWFyZ2luLWJvdHRvbTogOHB4Owp9CgouaXRlbS1kZXNjIHsKICBjb2xvcjogIzYwNjI2NjsKICBmb250LXNpemU6IDE0cHg7CiAgbGluZS1oZWlnaHQ6IDEuNTsKfQoKLyog5YWo5paH5qOA57Si5qC35byPICovCi5mdWxsdGV4dC1yZXN1bHRzIHsKICBtYXJnaW4tdG9wOiAyMHB4Owp9CgouZmlsdGVyLXNlY3Rpb24gewogIGJhY2tncm91bmQ6IHdoaXRlOwogIHBhZGRpbmc6IDIwcHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLmZpbHRlci1yb3cgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouZmlsdGVyLXJvdzpsYXN0LWNoaWxkIHsKICBtYXJnaW4tYm90dG9tOiAwOwp9CgouZmlsdGVyLWxhYmVsIHsKICB3aWR0aDogODBweDsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5maWx0ZXItb3B0aW9ucyB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LXdyYXA6IHdyYXA7CiAgZ2FwOiAxMHB4Owp9CgouY291bnQgewogIGNvbG9yOiAjOTk5OwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLnJlc3VsdC1zdGF0cyB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLmFjdGlvbi1idXR0b25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogMTBweDsKfQoKLnJlc3VsdHMtbGlzdCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLnJlc3VsdHMtbGlzdCAucmVzdWx0LWl0ZW0gewogIHBhZGRpbmc6IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7Cn0KCi5yZXN1bHRzLWxpc3QgLnJlc3VsdC1pdGVtOmxhc3QtY2hpbGQgewogIGJvcmRlci1ib3R0b206IG5vbmU7Cn0KCi5yZXN1bHQtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgoucmVzdWx0LXRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgY29sb3I6ICMzMzM7CiAgbWFyZ2luOiAwOwogIGxpbmUtaGVpZ2h0OiAxLjQ7CiAgZmxleDogMTsKICBtYXJnaW4tcmlnaHQ6IDIwcHg7Cn0KCi5yZXN1bHQtbWV0YSB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LXdyYXA6IHdyYXA7CiAgZ2FwOiAxNXB4OwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjOTk5Owp9CgoubWV0YS1pdGVtIHsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwp9CgoucmVzdWx0LWNvbnRlbnQgewogIGNvbG9yOiAjNjY2OwogIGxpbmUtaGVpZ2h0OiAxLjY7CiAgZm9udC1zaXplOiAxNHB4Owp9CgoucGFnaW5hdGlvbi1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ZA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/meta-search", "sourcesContent": ["<template>\n  <div class=\"meta-search-container\">\n    <!-- 顶部搜索区域 -->\n    <div class=\"search-header\">\n      <div class=\"search-tabs\">\n        <div\n          class=\"tab\"\n          :class=\"{ active: activeTab === 'fulltext' }\"\n          @click=\"switchTab('fulltext')\"\n        >\n          全文检索\n        </div>\n        <div\n          class=\"tab\"\n          :class=\"{ active: activeTab === 'meta' }\"\n          @click=\"switchTab('meta')\"\n        >\n          元搜索\n        </div>\n      </div>\n\n      <div class=\"search-box\">\n        <input\n          type=\"text\"\n          class=\"search-input\"\n          v-model=\"searchKeyword\"\n          placeholder=\"请输入搜索关键词\"\n          @keyup.enter=\"handleSearch\"\n        />\n        <el-button type=\"primary\" class=\"search-btn\" @click=\"handleSearch\">搜索</el-button>\n      </div>\n    </div>\n\n    <!-- 全文检索结果 -->\n    <div v-if=\"activeTab === 'fulltext' && hasSearched\" class=\"fulltext-results\">\n      <!-- 筛选条件区域 -->\n      <div class=\"filter-section\">\n        <!-- 时间筛选 -->\n        <div class=\"filter-row\">\n          <span class=\"filter-label\">时间范围:</span>\n          <div class=\"filter-options\">\n            <el-button\n              v-for=\"time in timeOptions\"\n              :key=\"time.value\"\n              :type=\"selectedTime === time.value ? 'primary' : ''\"\n              size=\"small\"\n              @click=\"selectTime(time.value)\"\n            >\n              {{ time.label }}\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 平台筛选 -->\n        <div class=\"filter-row\">\n          <span class=\"filter-label\">平台类型:</span>\n          <div class=\"filter-options\">\n            <el-button\n              v-for=\"platform in platformOptions\"\n              :key=\"platform.value\"\n              :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\n              size=\"small\"\n              @click=\"selectPlatform(platform.value)\"\n            >\n              {{ platform.label }}\n              <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 情感筛选 -->\n        <div class=\"filter-row\">\n          <span class=\"filter-label\">情感类型:</span>\n          <div class=\"filter-options\">\n            <el-button\n              v-for=\"emotion in emotionOptions\"\n              :key=\"emotion.value\"\n              :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\n              size=\"small\"\n              @click=\"selectEmotion(emotion.value)\"\n            >\n              {{ emotion.label }}\n              <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 结果统计 -->\n      <div class=\"result-stats\">\n        <span>共{{ totalResults }}条结果</span>\n        <div class=\"action-buttons\">\n          <el-button size=\"small\">导出</el-button>\n          <el-button type=\"primary\" size=\"small\">分析</el-button>\n        </div>\n      </div>\n\n      <!-- 搜索结果列表 -->\n      <div class=\"results-list\">\n        <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\n          <div class=\"result-header\">\n            <h3 class=\"result-title\">{{ item.title }}</h3>\n            <div class=\"result-actions\">\n              <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\n            </div>\n          </div>\n\n          <div class=\"result-meta\">\n            <span class=\"meta-item\">{{ item.source }}</span>\n            <span class=\"meta-item\">{{ item.publishTime }}</span>\n            <span class=\"meta-item\">{{ item.author }}</span>\n            <span class=\"meta-item\">{{ item.platform }}</span>\n            <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\n            <span class=\"meta-item\">{{ item.location }}</span>\n            <span class=\"meta-item\">{{ item.category }}</span>\n          </div>\n\n          <div class=\"result-content\">\n            {{ item.content }}\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"prev, pager, next\"\n          :total=\"totalResults\"\n          :current-page.sync=\"currentPage\"\n          :page-size=\"pageSize\"\n          @current-change=\"handlePageChange\"\n        ></el-pagination>\n      </div>\n    </div>\n\n    <!-- 元搜索结果 -->\n    <div v-if=\"activeTab === 'meta' && hasSearched\" class=\"meta-results\">\n      <!-- 搜索引擎选项卡 -->\n      <div class=\"search-engines\">\n        <div\n          v-for=\"engine in searchEngines\"\n          :key=\"engine.id\"\n          class=\"engine-item\"\n          :class=\"{ active: engine.id === activeEngine }\"\n          @click=\"toggleEngine(engine.id)\"\n        >\n          <div class=\"checkbox\">\n            <i class=\"el-icon-check\" v-if=\"selectedEngines.includes(engine.id)\"></i>\n          </div>\n          <img :src=\"engine.icon\" :alt=\"engine.name\" class=\"engine-icon\" />\n          <span class=\"engine-name\">{{ engine.name }}</span>\n        </div>\n      </div>\n\n      <!-- 搜索结果展示区 -->\n      <div class=\"results-container\">\n        <!-- 左侧搜索结果 -->\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('bing')\">\n          <div class=\"result-header\">\n            <img src=\"https://www.bing.com/favicon.ico\" alt=\"Microsoft Bing\" class=\"result-icon\" />\n            <span class=\"result-title\">Microsoft Bing</span>\n          </div>\n          <div class=\"result-list\">\n            <div class=\"result-item\" v-for=\"(item, index) in bingResults\" :key=\"'bing-'+index\">\n              <h3 class=\"item-title\">\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\n              </h3>\n              <div class=\"item-url\">{{ item.url }}</div>\n              <div class=\"item-desc\">{{ item.description }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 中间搜索结果 -->\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('baidu')\">\n          <div class=\"result-header\">\n            <img src=\"https://www.baidu.com/favicon.ico\" alt=\"百度搜索\" class=\"result-icon\" />\n            <span class=\"result-title\">百度搜索</span>\n          </div>\n          <div class=\"result-list\">\n            <div class=\"result-item\" v-for=\"(item, index) in baiduResults\" :key=\"'baidu-'+index\">\n              <h3 class=\"item-title\">\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\n              </h3>\n              <div class=\"item-url\">{{ item.url }}</div>\n              <div class=\"item-desc\">{{ item.description }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 右侧搜索结果 -->\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('360')\">\n          <div class=\"result-header\">\n            <img src=\"https://www.so.com/favicon.ico\" alt=\"360搜索\" class=\"result-icon\" />\n            <span class=\"result-title\">360搜索</span>\n          </div>\n          <div class=\"result-list\">\n            <div class=\"result-item\" v-for=\"(item, index) in so360Results\" :key=\"'360-'+index\">\n              <h3 class=\"item-title\">\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\n              </h3>\n              <div class=\"item-url\">{{ item.url }}</div>\n              <div class=\"item-desc\">{{ item.description }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'MetaSearch',\n  data() {\n    return {\n      activeTab: 'fulltext', // 默认显示全文检索\n      searchKeyword: '方太',\n      hasSearched: true,\n\n      // 全文检索相关数据\n      selectedTime: '24h',\n      selectedPlatform: 'all',\n      selectedEmotion: 'all',\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 10000,\n\n      timeOptions: [\n        { label: '24小时', value: '24h' },\n        { label: '一周', value: '1w' },\n        { label: '半年', value: '6m' },\n        { label: '一年', value: '1y' },\n        { label: '自定义', value: 'custom' }\n      ],\n\n      platformOptions: [\n        { label: '全部', value: 'all', count: 10540 },\n        { label: '微信', value: 'wechat', count: 1847 },\n        { label: '微博', value: 'weibo', count: 2008 },\n        { label: '客户端', value: 'app', count: 1748 },\n        { label: '论坛', value: 'forum', count: 673 }\n      ],\n\n      emotionOptions: [\n        { label: '全部', value: 'all' },\n        { label: '正面', value: 'positive' },\n        { label: '负面', value: 'negative' },\n        { label: '中性', value: 'neutral' }\n      ],\n\n      searchResults: [\n        {\n          title: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...',\n          source: '新华网',\n          publishTime: '2022-06-29 20:07:04',\n          author: '77人讨论',\n          platform: '平台来源',\n          readCount: '无',\n          location: '无所在地',\n          category: '新闻',\n          content: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...'\n        },\n        {\n          title: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文',\n          source: '中大论文发表',\n          publishTime: '2022-06-29 20:07:04',\n          author: '77人讨论',\n          platform: '平台来源',\n          readCount: '无',\n          location: '无所在地',\n          category: '论文',\n          content: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...'\n        },\n        {\n          title: '转发微博#中#大学生，人情世故。',\n          source: '微博',\n          publishTime: '2022-06-29 20:07:04',\n          author: '77人讨论',\n          platform: '微博',\n          readCount: '1000',\n          location: '北京',\n          category: '社交媒体',\n          content: '转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...'\n        }\n      ],\n\n      // 元搜索相关数据\n      activeEngine: 'bing',\n      selectedEngines: ['bing', 'baidu', '360'],\n      searchEngines: [\n        { id: 'bing', name: 'Microsoft Bing', icon: 'https://www.bing.com/favicon.ico' },\n        { id: 'baidu', name: '百度搜索', icon: 'https://www.baidu.com/favicon.ico' },\n        { id: '360', name: '360搜索', icon: 'https://www.so.com/favicon.ico' }\n      ],\n      bingResults: [\n        {\n          title: '方太官网_高端全场景厨电',\n          url: 'https://www.fotile.com',\n          link: 'https://www.fotile.com',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '【方太集团官网】',\n          url: 'https://www.fotile.com/about',\n          link: 'https://www.fotile.com/about',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '不平凡',\n          url: 'https://www.fotile.com/product',\n          link: 'https://www.fotile.com/product',\n          description: '方太高端厨电，专注高端厨房电器20年，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        }\n      ],\n      baiduResults: [\n        {\n          title: '方太官网_高端全场景厨电',\n          url: 'https://www.fotile.com',\n          link: 'https://www.fotile.com',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '方太厨电 高端集成厨房电器品牌',\n          url: 'https://www.fotile.com/product',\n          link: 'https://www.fotile.com/product',\n          description: '全国服务热线：400-315-0000 方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\n        },\n        {\n          title: '方太 - 百度百科',\n          url: 'https://baike.baidu.com/item/方太/1830',\n          link: 'https://baike.baidu.com/item/方太/1830',\n          description: '方太，是中国高端厨电领导品牌，创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\n        }\n      ],\n      so360Results: [\n        {\n          title: '方太官网_高端全场景厨电',\n          url: 'https://www.fotile.com',\n          link: 'https://www.fotile.com',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '方太厨电旗舰店-天猫',\n          url: 'https://fotile.tmall.com',\n          link: 'https://fotile.tmall.com',\n          description: '方太厨电旗舰店,提供方太油烟机,方太燃气灶,方太消毒柜,方太洗碗机,方太蒸箱,方太烤箱,方太微波炉,方太水槽洗碗机等产品。天猫正品保障,提供...'\n        },\n        {\n          title: '方太集团有限公司',\n          url: 'https://www.fotile.com/about',\n          link: 'https://www.fotile.com/about',\n          description: '方太集团有限公司创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\n        }\n      ]\n    }\n  },\n  methods: {\n    // 标签页切换\n    switchTab(tab) {\n      this.activeTab = tab\n    },\n\n    // 搜索功能\n    handleSearch() {\n      this.hasSearched = true\n      // 实际项目中这里应该调用API获取搜索结果\n      console.log('搜索关键词:', this.searchKeyword)\n      this.$message.success(`搜索: ${this.searchKeyword}`)\n    },\n\n    // 全文检索筛选方法\n    selectTime(value) {\n      this.selectedTime = value\n      this.handleSearch()\n    },\n\n    selectPlatform(value) {\n      this.selectedPlatform = value\n      this.handleSearch()\n    },\n\n    selectEmotion(value) {\n      this.selectedEmotion = value\n      this.handleSearch()\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page\n      // 加载对应页面数据\n    },\n\n    // 元搜索引擎切换\n    toggleEngine(engineId) {\n      // 切换选中状态\n      if (this.selectedEngines.includes(engineId)) {\n        // 如果已经选中，且不是最后一个选中的引擎，则取消选中\n        if (this.selectedEngines.length > 1) {\n          this.selectedEngines = this.selectedEngines.filter(id => id !== engineId)\n        }\n      } else {\n        // 如果未选中，则添加到选中列表\n        this.selectedEngines.push(engineId)\n      }\n\n      // 设置当前活动引擎\n      this.activeEngine = engineId\n    }\n  }\n}\n</script>\n\n<style scoped>\n.meta-search-container {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n.search-header {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.search-tabs {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.tab {\n  padding: 8px 16px;\n  margin-right: 10px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n}\n\n.tab.active {\n  color: #409EFF;\n  border-bottom: 2px solid #409EFF;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  flex: 1;\n  height: 40px;\n  padding: 0 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  margin-right: 10px;\n  font-size: 14px;\n}\n\n.search-btn {\n  height: 40px;\n}\n\n.search-results {\n  display: flex;\n  flex-direction: column;\n}\n\n.search-engines {\n  display: flex;\n  background: white;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.engine-item {\n  display: flex;\n  align-items: center;\n  padding: 8px 16px;\n  margin-right: 15px;\n  cursor: pointer;\n  border-radius: 4px;\n  transition: all 0.3s;\n  border: 1px solid #dcdfe6;\n  background: #fff;\n}\n\n.engine-item:hover {\n  border-color: #c6e2ff;\n}\n\n.engine-item.active {\n  background: #ecf5ff;\n  color: #409EFF;\n  border-color: #409EFF;\n}\n\n.checkbox {\n  width: 16px;\n  height: 16px;\n  border: 1px solid #dcdfe6;\n  border-radius: 2px;\n  margin-right: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  background-color: #409EFF;\n}\n\n.engine-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n}\n\n.results-container {\n  display: flex;\n  gap: 20px;\n}\n\n.result-column {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.result-header {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  background: #f5f7fa;\n}\n\n.result-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n}\n\n.result-title {\n  font-weight: bold;\n  color: #303133;\n}\n\n.result-list {\n  padding: 15px;\n}\n\n.result-item {\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.result-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.item-title {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n}\n\n.item-title a {\n  color: #0366d6;\n  text-decoration: none;\n}\n\n.item-title a:hover {\n  text-decoration: underline;\n}\n\n.item-url {\n  color: #67c23a;\n  font-size: 12px;\n  margin-bottom: 8px;\n}\n\n.item-desc {\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* 全文检索样式 */\n.fulltext-results {\n  margin-top: 20px;\n}\n\n.filter-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.count {\n  color: #999;\n  font-size: 12px;\n}\n\n.result-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 15px 20px;\n  margin-bottom: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.results-list {\n  background: white;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.results-list .result-item {\n  padding: 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.results-list .result-item:last-child {\n  border-bottom: none;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.result-title {\n  font-size: 16px;\n  color: #333;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n  margin-right: 20px;\n}\n\n.result-meta {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: #999;\n}\n\n.meta-item {\n  white-space: nowrap;\n}\n\n.result-content {\n  color: #666;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n</style>\n"]}]}