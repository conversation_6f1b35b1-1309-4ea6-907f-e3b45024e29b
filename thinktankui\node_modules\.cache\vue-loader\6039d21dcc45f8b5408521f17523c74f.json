{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\hot-events\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\hot-events\\index.vue", "mtime": 1748098912535}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnSG90RXZlbnRzJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlVGFiOiAnbWFwJywKICAgICAgaG90RXZlbnRzTGlzdDogWwogICAgICAgIHsgdGl0bGU6ICfmiJHlhpvlnKjlj7Dmtbfov5vooYzlrp7miJjljJbmvJTnu4PvvIzlsZXnjrDnu7TmiqTlm73lrrbkuLvmnYPlhrPlv4MnLCBoZWF0OiAnMjAxOTYnIH0sCiAgICAgICAgeyB0aXRsZTogJ+mprOS6keeOsOi6q++8jOS9leaXtuW9kuadpe+8nycsIGhlYXQ6ICcxOTY0MCcgfSwKICAgICAgICB7IHRpdGxlOiAn5YWo5Zu955ar5oOF77yIMjAyM+W5tDTmnIgyOeaXpe+8iScsIGhlYXQ6ICcxMjg0MCcgfSwKICAgICAgICB7IHRpdGxlOiAn5L+E5Yab5omT5Ye75LmM5YWL5YWw55uu5qCH77yM5LmM5pa556ew6YGt5Y+X6YeN5aSn5o2f5aSxJywgaGVhdDogJzExNDgwJyB9LAogICAgICAgIHsgdGl0bGU6ICfku4rlpKnkuIDlrpropoHph43ngrnlhbPms6jnmoQnLCBoZWF0OiAnMTExMDAnIH0sCiAgICAgICAgeyB0aXRsZTogJ+WIsOW6leaYr+S7gOS5iOWOn+WboOWRou+8gScsIGhlYXQ6ICc5OTUwJyB9LAogICAgICAgIHsgdGl0bGU6ICfkuIDmnaHlvq7ljZrlvJXlj5HkuIrljYPkuIfnvZHlj4vng63orq4nLCBoZWF0OiAnOTc0MCcgfSwKICAgICAgICB7IHRpdGxlOiAn5paw77yM5L+E5LmM5Yay56qB5pyA5paw6L+b5bGV77ya5LmM5YWL5YWw56ew5Ye76YCA5L+E5Yab5LiA5qyh5pS75Ye7JywgaGVhdDogJzk0MTAnIH0sCiAgICAgICAgeyB0aXRsZTogJ+iuqeaIkeS7rOWFseWQjOWFs+azqOi/meS4quS6i+S7tu+8jOS6i+WFs+avj+S4quS6uicsIGhlYXQ6ICc4NTUwJyB9LAogICAgICAgIHsgdGl0bGU6ICflm73pmYXlhpvkuovkuJPlrrbliIbmnpDlvZPliY3lsYDlir/vvIznp7DlsYDlir/ku43lnKjlj5HlsZUnLCBoZWF0OiAnNzk4MCcgfQogICAgICBdCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVUYWJDbGljayh0YWIpIHsKICAgICAgY29uc29sZS5sb2coJ+WIh+aNouagh+etvumhtTonLCB0YWIubmFtZSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/hot-events", "sourcesContent": ["<template>\n  <div class=\"hot-events-container\">\n    <!-- 顶部标签页 -->\n    <div class=\"tabs-header\">\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n        <el-tab-pane label=\"热点地图\" name=\"map\"></el-tab-pane>\n        <el-tab-pane label=\"三方热点\" name=\"third-party\"></el-tab-pane>\n        <el-tab-pane label=\"七日热点\" name=\"seven-days\"></el-tab-pane>\n        <el-tab-pane label=\"平台\" name=\"platform\"></el-tab-pane>\n        <el-tab-pane label=\"主题\" name=\"theme\"></el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 左侧地图区域 -->\n      <div class=\"map-section\">\n        <div class=\"map-container\">\n          <div class=\"china-map\">\n            <div class=\"map-placeholder\">\n              <i class=\"el-icon-location\"></i>\n              <p>中国热点地图</p>\n              <p class=\"map-note\">地图功能开发中...</p>\n            </div>\n          </div>\n          <!-- 地图图例 -->\n          <div class=\"map-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background: #8B0000;\"></span>\n              <span>≥ 500000</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background: #CD5C5C;\"></span>\n              <span>100000 - 500000</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background: #F08080;\"></span>\n              <span>50000 - 100000</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background: #FFA07A;\"></span>\n              <span>10000 - 50000</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background: #FFE4E1;\"></span>\n              <span>0 - 10000</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 右侧热点列表 -->\n      <div class=\"hotlist-section\">\n        <div class=\"hotlist-header\">\n          <h3>昨日热点事件TOP10</h3>\n        </div>\n        <div class=\"hotlist-content\">\n          <div\n            v-for=\"(item, index) in hotEventsList\"\n            :key=\"index\"\n            class=\"hot-item\"\n            :class=\"{ 'top-three': index < 3 }\"\n          >\n            <div class=\"rank-number\">{{ index + 1 }}</div>\n            <div class=\"event-content\">\n              <div class=\"event-title\">{{ item.title }}</div>\n              <div class=\"event-heat\">{{ item.heat }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部详情区域 -->\n    <div class=\"details-section\">\n      <div class=\"detail-card\">\n        <div class=\"card-header\">\n          <h4>全国热点</h4>\n        </div>\n        <div class=\"card-content\">\n          <div class=\"event-item\">\n            <div class=\"event-icon\">热</div>\n            <div class=\"event-info\">\n              <div class=\"event-title\">商务部回应欧盟对华人造板反倾销立案调查：中方对此表示强烈不满和坚决反对</div>\n              <div class=\"event-time\">2023-04-29</div>\n            </div>\n          </div>\n          <div class=\"event-description\">\n            针对欧盟对华人造板反倾销立案调查，中方表示强烈不满和坚决反对。商务部表示，中国人造板产业发展健康，出口产品质量优良，价格公平合理。\n          </div>\n        </div>\n      </div>\n\n      <div class=\"detail-card\">\n        <div class=\"card-header\">\n          <h4>北京市热点</h4>\n          <el-button type=\"text\" size=\"small\">查看详情</el-button>\n        </div>\n        <div class=\"card-content\">\n          <div class=\"event-item\">\n            <div class=\"event-icon\">京</div>\n            <div class=\"event-info\">\n              <div class=\"event-title\">北京海淀法院：\"老赖\"被限，法院强制执行，追回欠款</div>\n              <div class=\"event-time\">2023-04-29</div>\n            </div>\n          </div>\n          <div class=\"event-description\">\n            北京海淀法院近日成功执行一起债务纠纷案件，通过限制被执行人消费等强制措施，成功追回欠款，维护了当事人合法权益。\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HotEvents',\n  data() {\n    return {\n      activeTab: 'map',\n      hotEventsList: [\n        { title: '我军在台海进行实战化演练，展现维护国家主权决心', heat: '20196' },\n        { title: '马云现身，何时归来？', heat: '19640' },\n        { title: '全国疫情（2023年4月29日）', heat: '12840' },\n        { title: '俄军打击乌克兰目标，乌方称遭受重大损失', heat: '11480' },\n        { title: '今天一定要重点关注的', heat: '11100' },\n        { title: '到底是什么原因呢！', heat: '9950' },\n        { title: '一条微博引发上千万网友热议', heat: '9740' },\n        { title: '新，俄乌冲突最新进展：乌克兰称击退俄军一次攻击', heat: '9410' },\n        { title: '让我们共同关注这个事件，事关每个人', heat: '8550' },\n        { title: '国际军事专家分析当前局势，称局势仍在发展', heat: '7980' }\n      ]\n    }\n  },\n  methods: {\n    handleTabClick(tab) {\n      console.log('切换标签页:', tab.name)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.hot-events-container {\n  padding: 20px;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.tabs-header {\n  background: white;\n  border-radius: 8px;\n  padding: 0 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.main-content {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.map-section {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.map-container {\n  position: relative;\n  height: 400px;\n}\n\n.china-map {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px dashed #ddd;\n}\n\n.map-placeholder {\n  text-align: center;\n  color: #666;\n  font-size: 16px;\n  line-height: 1.5;\n}\n\n.map-placeholder i {\n  font-size: 48px;\n  color: #409EFF;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.map-placeholder p {\n  margin: 5px 0;\n}\n\n.map-note {\n  font-size: 14px;\n  color: #999;\n}\n\n.map-legend {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  background: rgba(255,255,255,0.9);\n  padding: 10px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 5px;\n}\n\n.legend-color {\n  width: 16px;\n  height: 12px;\n  margin-right: 8px;\n  border-radius: 2px;\n}\n\n.hotlist-section {\n  width: 350px;\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.hotlist-header h3 {\n  margin: 0 0 20px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.hot-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.hot-item:last-child {\n  border-bottom: none;\n}\n\n.rank-number {\n  width: 24px;\n  height: 24px;\n  background: #f0f0f0;\n  color: #666;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.top-three .rank-number {\n  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);\n  color: white;\n}\n\n.event-content {\n  flex: 1;\n}\n\n.event-title {\n  font-size: 13px;\n  color: #333;\n  line-height: 1.4;\n  margin-bottom: 4px;\n}\n\n.event-heat {\n  font-size: 12px;\n  color: #ff6b6b;\n  font-weight: bold;\n}\n\n.details-section {\n  display: flex;\n  gap: 20px;\n}\n\n.detail-card {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.card-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.event-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.event-icon {\n  width: 32px;\n  height: 32px;\n  background: #ff6b6b;\n  color: white;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.event-info {\n  flex: 1;\n}\n\n.event-info .event-title {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.event-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.event-description {\n  font-size: 13px;\n  color: #666;\n  line-height: 1.5;\n  margin-top: 10px;\n}\n</style>\n"]}]}