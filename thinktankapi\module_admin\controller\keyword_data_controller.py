from datetime import datetime
from fastapi import API<PERSON><PERSON>er, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON><PERSON><PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.keyword_data_vo import (
    DeleteKeywordDataModel, 
    KeywordDataModel, 
    KeywordDataPageQueryModel
)
from module_admin.service.keyword_data_service import KeywordDataService
from module_admin.service.login_service import LoginService
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


keywordDataController = APIRouter(prefix='/admin/keywordData', dependencies=[Depends(LoginService.get_current_user)])


@keywordDataController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:list'))]
)
async def get_keyword_data_list(
    request: Request,
    keyword_data_page_query: KeywordDataPageQueryModel = Depends(KeywordDataPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取关键词数据列表
    """
    # 获取分页数据
    keyword_data_page_query_result = await KeywordDataService.get_keyword_data_list_services(query_db, keyword_data_page_query, is_page=True)
    logger.info('获取关键词数据列表成功')

    return ResponseUtil.success(model_content=keyword_data_page_query_result)


@keywordDataController.get('/statistics')
async def get_type_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取类型统计数据
    """
    statistics_result = await KeywordDataService.get_type_statistics_services(query_db)
    logger.info('获取类型统计数据成功')

    return ResponseUtil.success(data=statistics_result)


@keywordDataController.post('', dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:add'))])
@ValidateFields(validate_model='add_keyword_data')
@Log(title='关键词数据', business_type=BusinessType.INSERT)
async def add_keyword_data(
    request: Request,
    add_keyword_data: KeywordDataModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    新增关键词数据
    """
    add_keyword_data.createtime = datetime.now()
    add_keyword_data_result = await KeywordDataService.add_keyword_data_services(query_db, add_keyword_data)
    logger.info(add_keyword_data_result.message)

    return ResponseUtil.success(msg=add_keyword_data_result.message)


@keywordDataController.put('', dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:edit'))])
@ValidateFields(validate_model='edit_keyword_data')
@Log(title='关键词数据', business_type=BusinessType.UPDATE)
async def edit_keyword_data(
    request: Request,
    edit_keyword_data: KeywordDataModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    编辑关键词数据
    """
    edit_keyword_data_result = await KeywordDataService.edit_keyword_data_services(query_db, edit_keyword_data)
    logger.info(edit_keyword_data_result.message)

    return ResponseUtil.success(msg=edit_keyword_data_result.message)


@keywordDataController.delete('', dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:remove'))])
@ValidateFields(validate_model='delete_keyword_data')
@Log(title='关键词数据', business_type=BusinessType.DELETE)
async def delete_keyword_data(
    request: Request,
    delete_keyword_data: DeleteKeywordDataModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除关键词数据
    """
    delete_keyword_data_result = await KeywordDataService.delete_keyword_data_services(query_db, delete_keyword_data)
    logger.info(delete_keyword_data_result.message)

    return ResponseUtil.success(msg=delete_keyword_data_result.message)


@keywordDataController.get(
    '/{keyword_data_id}', response_model=KeywordDataModel, dependencies=[Depends(CheckUserInterfaceAuth('admin:keywordData:query'))]
)
async def query_detail_keyword_data(request: Request, keyword_data_id: int, query_db: AsyncSession = Depends(get_db)):
    """
    获取关键词数据详情
    """
    keyword_data_detail_result = await KeywordDataService.keyword_data_detail_services(query_db, keyword_data_id)
    logger.info(f'获取keyword_data_id为{keyword_data_id}的信息成功')

    return ResponseUtil.success(data=keyword_data_detail_result)
