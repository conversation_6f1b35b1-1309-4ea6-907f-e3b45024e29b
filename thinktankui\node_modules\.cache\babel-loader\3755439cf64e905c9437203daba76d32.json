{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue", "mtime": 1749115113523}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_news", "require", "name", "data", "loading", "newsDetail", "imagePreviewVisible", "previewImageUrl", "created", "getNewsDetail", "watch", "$route", "methods", "_this", "newsId", "params", "id", "then", "response", "title", "document", "catch", "$message", "error", "goBack", "$router", "go", "goToNews", "push", "path", "concat", "formatTime", "time", "parseTime", "formatContent", "content", "replace", "getSentimentText", "sentiment", "sentimentMap", "positive", "neutral", "negative", "getInfoAttributeText", "attribute", "attributeMap", "official", "media", "user", "competitor", "industry", "policy", "previewImage", "imageUrl", "handleImageError", "event", "target", "style", "display"], "sources": ["src/views/news/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"news-detail-container\">\n    <div v-loading=\"loading\" class=\"news-detail-content\">\n      <!-- 返回按钮 -->\n      <div class=\"back-section\">\n        <el-button \n          type=\"text\" \n          icon=\"el-icon-arrow-left\" \n          @click=\"goBack\"\n          class=\"back-btn\"\n        >\n          返回新闻列表\n        </el-button>\n      </div>\n\n      <!-- 新闻详情 -->\n      <div v-if=\"newsDetail\" class=\"news-article\">\n        <!-- 新闻头部 -->\n        <div class=\"news-header\">\n          <h1 class=\"news-title\">{{ newsDetail.title }}</h1>\n          <div class=\"news-meta\">\n            <div class=\"meta-left\">\n              <span class=\"news-source\">{{ newsDetail.sourceName }}</span>\n              <span class=\"news-time\">{{ formatTime(newsDetail.publishTime) }}</span>\n              <span class=\"sentiment-tag\" :class=\"'sentiment-' + newsDetail.sentiment\">\n                {{ getSentimentText(newsDetail.sentiment) }}\n              </span>\n            </div>\n            <div class=\"meta-right\">\n              <span class=\"news-stats\">\n                <i class=\"el-icon-view\"></i> {{ newsDetail.viewsCount }}\n              </span>\n              <span class=\"news-stats\">\n                <i class=\"el-icon-chat-line-square\"></i> {{ newsDetail.commentsCount }}\n              </span>\n              <span class=\"news-stats\">\n                <i class=\"el-icon-share\"></i> {{ newsDetail.sharesCount }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 新闻摘要 -->\n        <div v-if=\"newsDetail.summary\" class=\"news-summary\">\n          <h3>摘要</h3>\n          <p>{{ newsDetail.summary }}</p>\n        </div>\n\n        <!-- 新闻内容 -->\n        <div class=\"news-content\">\n          <div class=\"content-text\" v-html=\"formatContent(newsDetail.content)\"></div>\n          \n          <!-- 新闻图片 -->\n          <div v-if=\"newsDetail.images && newsDetail.images.length > 0\" class=\"news-images\">\n            <div class=\"images-grid\">\n              <img \n                v-for=\"(img, index) in newsDetail.images\" \n                :key=\"index\" \n                :src=\"img\" \n                class=\"news-image\"\n                @click=\"previewImage(img)\"\n                @error=\"handleImageError\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 新闻标签 -->\n        <div v-if=\"newsDetail.keywords && newsDetail.keywords.length > 0\" class=\"news-tags\">\n          <h4>相关标签</h4>\n          <el-tag \n            v-for=\"keyword in newsDetail.keywords\" \n            :key=\"keyword\" \n            size=\"small\"\n            class=\"keyword-tag\"\n          >\n            {{ keyword }}\n          </el-tag>\n        </div>\n\n        <!-- 新闻信息 -->\n        <div class=\"news-info\">\n          <div class=\"info-item\">\n            <span class=\"info-label\">来源链接：</span>\n            <a v-if=\"newsDetail.sourceUrl\" :href=\"newsDetail.sourceUrl\" target=\"_blank\" class=\"source-link\">\n              查看原文\n            </a>\n            <span v-else>暂无</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">信息属性：</span>\n            <span>{{ getInfoAttributeText(newsDetail.infoAttribute) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">关联实体：</span>\n            <span>{{ newsDetail.entityName || '暂无' }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 导航区域 -->\n      <div v-if=\"newsDetail\" class=\"news-navigation\">\n        <div class=\"nav-item prev-news\" v-if=\"newsDetail.prevNews\">\n          <span class=\"nav-label\">上一篇：</span>\n          <a @click=\"goToNews(newsDetail.prevNews.id)\" class=\"nav-link\">\n            {{ newsDetail.prevNews.title }}\n          </a>\n        </div>\n        <div class=\"nav-item next-news\" v-if=\"newsDetail.nextNews\">\n          <span class=\"nav-label\">下一篇：</span>\n          <a @click=\"goToNews(newsDetail.nextNews.id)\" class=\"nav-link\">\n            {{ newsDetail.nextNews.title }}\n          </a>\n        </div>\n      </div>\n\n      <!-- 相关新闻 -->\n      <div v-if=\"newsDetail && newsDetail.relatedNews && newsDetail.relatedNews.length > 0\" class=\"related-news\">\n        <h3>相关新闻</h3>\n        <div class=\"related-list\">\n          <div \n            v-for=\"related in newsDetail.relatedNews\" \n            :key=\"related.id\"\n            class=\"related-item\"\n            @click=\"goToNews(related.id)\"\n          >\n            <div class=\"related-content\">\n              <h4 class=\"related-title\">{{ related.title }}</h4>\n              <div class=\"related-meta\">\n                <span>{{ related.sourceName }}</span>\n                <span>{{ formatTime(related.publishTime) }}</span>\n              </div>\n            </div>\n            <div v-if=\"related.images && related.images.length > 0\" class=\"related-image\">\n              <img :src=\"related.images[0]\" @error=\"handleImageError\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 图片预览 -->\n    <el-dialog\n      title=\"图片预览\"\n      :visible.sync=\"imagePreviewVisible\"\n      width=\"80%\"\n      center\n    >\n      <div class=\"image-preview\">\n        <img :src=\"previewImageUrl\" style=\"width: 100%; height: auto;\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getNewsDetail } from '@/api/news'\n\nexport default {\n  name: 'NewsDetail',\n  data() {\n    return {\n      loading: true,\n      newsDetail: null,\n      imagePreviewVisible: false,\n      previewImageUrl: ''\n    }\n  },\n  created() {\n    this.getNewsDetail()\n  },\n  watch: {\n    '$route'() {\n      this.getNewsDetail()\n    }\n  },\n  methods: {\n    /** 获取新闻详情 */\n    getNewsDetail() {\n      this.loading = true\n      const newsId = this.$route.params.id\n      \n      getNewsDetail(newsId).then(response => {\n        this.newsDetail = response.data\n        this.loading = false\n        \n        // 更新页面标题\n        if (this.newsDetail.title) {\n          document.title = this.newsDetail.title\n        }\n      }).catch(() => {\n        this.loading = false\n        this.$message.error('获取新闻详情失败')\n      })\n    },\n    /** 返回上一页 */\n    goBack() {\n      this.$router.go(-1)\n    },\n    /** 跳转到指定新闻 */\n    goToNews(newsId) {\n      this.$router.push({ path: `/news/detail/${newsId}` })\n    },\n    /** 格式化时间 */\n    formatTime(time) {\n      if (!time) return ''\n      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')\n    },\n    /** 格式化内容 */\n    formatContent(content) {\n      if (!content) return ''\n      // 简单的换行处理\n      return content.replace(/\\n/g, '<br>')\n    },\n    /** 获取情感文本 */\n    getSentimentText(sentiment) {\n      const sentimentMap = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      }\n      return sentimentMap[sentiment] || '未知'\n    },\n    /** 获取信息属性文本 */\n    getInfoAttributeText(attribute) {\n      const attributeMap = {\n        official: '官方发布',\n        media: '媒体报道',\n        user: '用户评价',\n        competitor: '竞品信息',\n        industry: '行业动态',\n        policy: '政策法规'\n      }\n      return attributeMap[attribute] || '未知'\n    },\n    /** 预览图片 */\n    previewImage(imageUrl) {\n      this.previewImageUrl = imageUrl\n      this.imagePreviewVisible = true\n    },\n    /** 图片加载错误处理 */\n    handleImageError(event) {\n      event.target.style.display = 'none'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.news-detail-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.back-section {\n  margin-bottom: 20px;\n  \n  .back-btn {\n    font-size: 14px;\n    color: #409eff;\n  }\n}\n\n.news-article {\n  background: #fff;\n  padding: 30px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.news-header {\n  border-bottom: 1px solid #ebeef5;\n  padding-bottom: 20px;\n  margin-bottom: 20px;\n  \n  .news-title {\n    font-size: 28px;\n    font-weight: bold;\n    color: #303133;\n    line-height: 1.4;\n    margin: 0 0 15px 0;\n  }\n  \n  .news-meta {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    \n    .meta-left, .meta-right {\n      display: flex;\n      align-items: center;\n      \n      span {\n        margin-right: 15px;\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    .sentiment-tag {\n      padding: 4px 12px;\n      border-radius: 16px;\n      font-size: 12px;\n      \n      &.sentiment-positive {\n        background: #f0f9ff;\n        color: #67c23a;\n      }\n      \n      &.sentiment-neutral {\n        background: #f4f4f5;\n        color: #909399;\n      }\n      \n      &.sentiment-negative {\n        background: #fef0f0;\n        color: #f56c6c;\n      }\n    }\n    \n    .news-stats {\n      i {\n        margin-right: 4px;\n      }\n    }\n  }\n}\n\n.news-summary {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n  \n  h3 {\n    margin: 0 0 10px 0;\n    color: #303133;\n    font-size: 16px;\n  }\n  \n  p {\n    margin: 0;\n    color: #606266;\n    line-height: 1.6;\n  }\n}\n\n.news-content {\n  .content-text {\n    font-size: 16px;\n    line-height: 1.8;\n    color: #303133;\n    margin-bottom: 20px;\n  }\n  \n  .news-images {\n    margin: 20px 0;\n    \n    .images-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n      \n      .news-image {\n        width: 100%;\n        height: 200px;\n        object-fit: cover;\n        border-radius: 6px;\n        cursor: pointer;\n        transition: transform 0.3s;\n        \n        &:hover {\n          transform: scale(1.05);\n        }\n      }\n    }\n  }\n}\n\n.news-tags {\n  margin: 20px 0;\n  \n  h4 {\n    margin: 0 0 10px 0;\n    color: #303133;\n    font-size: 14px;\n  }\n  \n  .keyword-tag {\n    margin-right: 8px;\n    margin-bottom: 8px;\n  }\n}\n\n.news-info {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 6px;\n  margin-top: 20px;\n  \n  .info-item {\n    margin-bottom: 8px;\n    font-size: 14px;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .info-label {\n      color: #909399;\n      margin-right: 8px;\n    }\n    \n    .source-link {\n      color: #409eff;\n      text-decoration: none;\n      \n      &:hover {\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n.news-navigation {\n  background: #fff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n  \n  .nav-item {\n    margin-bottom: 10px;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .nav-label {\n      color: #909399;\n      font-size: 14px;\n    }\n    \n    .nav-link {\n      color: #409eff;\n      cursor: pointer;\n      text-decoration: none;\n      \n      &:hover {\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n.related-news {\n  background: #fff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n  \n  h3 {\n    margin: 0 0 15px 0;\n    color: #303133;\n    font-size: 18px;\n  }\n  \n  .related-list {\n    .related-item {\n      display: flex;\n      padding: 15px 0;\n      border-bottom: 1px solid #ebeef5;\n      cursor: pointer;\n      transition: background-color 0.3s;\n      \n      &:hover {\n        background-color: #f5f7fa;\n      }\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .related-content {\n        flex: 1;\n        \n        .related-title {\n          margin: 0 0 8px 0;\n          color: #303133;\n          font-size: 16px;\n          font-weight: 500;\n        }\n        \n        .related-meta {\n          font-size: 13px;\n          color: #909399;\n          \n          span {\n            margin-right: 15px;\n          }\n        }\n      }\n      \n      .related-image {\n        width: 80px;\n        height: 60px;\n        margin-left: 15px;\n        \n        img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          border-radius: 4px;\n        }\n      }\n    }\n  }\n}\n\n.image-preview {\n  text-align: center;\n}\n</style>\n"], "mappings": ";;;;;;;;;AA4JA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,mBAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,KAAA;IACA,mBAAAC,OAAA;MACA,KAAAF,aAAA;IACA;EACA;EACAG,OAAA;IACA,aACAH,aAAA,WAAAA,cAAA;MAAA,IAAAI,KAAA;MACA,KAAAT,OAAA;MACA,IAAAU,MAAA,QAAAH,MAAA,CAAAI,MAAA,CAAAC,EAAA;MAEA,IAAAP,mBAAA,EAAAK,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAR,UAAA,GAAAa,QAAA,CAAAf,IAAA;QACAU,KAAA,CAAAT,OAAA;;QAEA;QACA,IAAAS,KAAA,CAAAR,UAAA,CAAAc,KAAA;UACAC,QAAA,CAAAD,KAAA,GAAAN,KAAA,CAAAR,UAAA,CAAAc,KAAA;QACA;MACA,GAAAE,KAAA;QACAR,KAAA,CAAAT,OAAA;QACAS,KAAA,CAAAS,QAAA,CAAAC,KAAA;MACA;IACA;IACA,YACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACA,cACAC,QAAA,WAAAA,SAAAb,MAAA;MACA,KAAAW,OAAA,CAAAG,IAAA;QAAAC,IAAA,kBAAAC,MAAA,CAAAhB,MAAA;MAAA;IACA;IACA,YACAiB,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,YAAAC,SAAA,CAAAD,IAAA;IACA;IACA,YACAE,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAAA,OAAA;MACA;MACA,OAAAA,OAAA,CAAAC,OAAA;IACA;IACA,aACAC,gBAAA,WAAAA,iBAAAC,SAAA;MACA,IAAAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,OAAAH,YAAA,CAAAD,SAAA;IACA;IACA,eACAK,oBAAA,WAAAA,qBAAAC,SAAA;MACA,IAAAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,IAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,OAAAN,YAAA,CAAAD,SAAA;IACA;IACA,WACAQ,YAAA,WAAAA,aAAAC,QAAA;MACA,KAAA9C,eAAA,GAAA8C,QAAA;MACA,KAAA/C,mBAAA;IACA;IACA,eACAgD,gBAAA,WAAAA,iBAAAC,KAAA;MACAA,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}