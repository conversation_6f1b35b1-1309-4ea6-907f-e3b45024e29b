{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\SizeSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\SizeSelect\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaXplT3B0aW9uczogW3sKICAgICAgICBsYWJlbDogJ0RlZmF1bHQnLAogICAgICAgIHZhbHVlOiAnZGVmYXVsdCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnTWVkaXVtJywKICAgICAgICB2YWx1ZTogJ21lZGl1bScKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnU21hbGwnLAogICAgICAgIHZhbHVlOiAnc21hbGwnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ01pbmknLAogICAgICAgIHZhbHVlOiAnbWluaScKICAgICAgfV0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgc2l6ZTogZnVuY3Rpb24gc2l6ZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuc2l6ZTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNldFNpemU6IGZ1bmN0aW9uIGhhbmRsZVNldFNpemUoc2l6ZSkgewogICAgICB0aGlzLiRFTEVNRU5ULnNpemUgPSBzaXplOwogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL3NldFNpemUnLCBzaXplKTsKICAgICAgdGhpcy5yZWZyZXNoVmlldygpOwogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiAnU3dpdGNoIFNpemUgU3VjY2VzcycsCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgIH0pOwogICAgfSwKICAgIHJlZnJlc2hWaWV3OiBmdW5jdGlvbiByZWZyZXNoVmlldygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgLy8gSW4gb3JkZXIgdG8gbWFrZSB0aGUgY2FjaGVkIHBhZ2UgcmUtcmVuZGVyZWQKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbEFsbENhY2hlZFZpZXdzJywgdGhpcy4kcm91dGUpOwogICAgICB2YXIgZnVsbFBhdGggPSB0aGlzLiRyb3V0ZS5mdWxsUGF0aDsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLiRyb3V0ZXIucmVwbGFjZSh7CiAgICAgICAgICBwYXRoOiAnL3JlZGlyZWN0JyArIGZ1bGxQYXRoCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["data", "sizeOptions", "label", "value", "computed", "size", "$store", "getters", "methods", "handleSetSize", "$ELEMENT", "dispatch", "refresh<PERSON>iew", "$message", "message", "type", "_this", "$route", "fullPath", "$nextTick", "$router", "replace", "path"], "sources": ["src/components/SizeSelect/index.vue"], "sourcesContent": ["<template>\n  <el-dropdown trigger=\"click\" @command=\"handleSetSize\">\n    <div>\n      <svg-icon class-name=\"size-icon\" icon-class=\"size\" />\n    </div>\n    <el-dropdown-menu slot=\"dropdown\">\n      <el-dropdown-item v-for=\"item of sizeOptions\" :key=\"item.value\" :disabled=\"size===item.value\" :command=\"item.value\">\n        {{ item.label }}\n      </el-dropdown-item>\n    </el-dropdown-menu>\n  </el-dropdown>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      sizeOptions: [\n        { label: 'Default', value: 'default' },\n        { label: 'Medium', value: 'medium' },\n        { label: 'Small', value: 'small' },\n        { label: 'Mini', value: 'mini' }\n      ]\n    }\n  },\n  computed: {\n    size() {\n      return this.$store.getters.size\n    }\n  },\n  methods: {\n    handleSetSize(size) {\n      this.$ELEMENT.size = size\n      this.$store.dispatch('app/setSize', size)\n      this.refreshView()\n      this.$message({\n        message: 'Switch Size Success',\n        type: 'success'\n      })\n    },\n    refreshView() {\n      // In order to make the cached page re-rendered\n      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)\n\n      const { fullPath } = this.$route\n\n      this.$nextTick(() => {\n        this.$router.replace({\n          path: '/redirect' + fullPath\n        })\n      })\n    }\n  }\n\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;iCAcA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACAC,IAAA,WAAAA,KAAA;MACA,YAAAC,MAAA,CAAAC,OAAA,CAAAF,IAAA;IACA;EACA;EACAG,OAAA;IACAC,aAAA,WAAAA,cAAAJ,IAAA;MACA,KAAAK,QAAA,CAAAL,IAAA,GAAAA,IAAA;MACA,KAAAC,MAAA,CAAAK,QAAA,gBAAAN,IAAA;MACA,KAAAO,WAAA;MACA,KAAAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IACAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,KAAA;MACA;MACA,KAAAV,MAAA,CAAAK,QAAA,oCAAAM,MAAA;MAEA,IAAAC,QAAA,QAAAD,MAAA,CAAAC,QAAA;MAEA,KAAAC,SAAA;QACAH,KAAA,CAAAI,OAAA,CAAAC,OAAA;UACAC,IAAA,gBAAAJ;QACA;MACA;IACA;EACA;AAEA", "ignoreList": []}]}