{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\gen\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\gen\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_gen", "require", "_importTable", "_interopRequireDefault", "_createTable", "_highlight", "hljs", "registerLanguage", "_default", "exports", "default", "name", "components", "importTable", "createTable", "data", "loading", "uniqueId", "ids", "tableNames", "single", "multiple", "showSearch", "total", "tableList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "tableName", "undefined", "tableComment", "preview", "open", "title", "activeName", "created", "getList", "activated", "time", "$route", "query", "t", "Number", "methods", "_this", "listTable", "addDateRange", "then", "response", "rows", "handleQuery", "handleGenTable", "row", "_this2", "$modal", "msgError", "genType", "genCode", "msgSuccess", "gen<PERSON><PERSON>", "$download", "zip", "handleSynchDb", "_this3", "confirm", "synchDb", "catch", "openImportTable", "$refs", "import", "show", "openCreateTable", "create", "reset<PERSON><PERSON>y", "resetForm", "handlePreview", "_this4", "previewTable", "tableId", "highlightedCode", "code", "key", "vmName", "substring", "lastIndexOf", "indexOf", "language", "length", "result", "highlight", "value", "clipboardSuccess", "handleSelectionChange", "selection", "map", "item", "handleEditTable", "params", "$tab", "openPage", "handleDelete", "_this5", "tableIds", "delTable"], "sources": ["src/views/tool/gen/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"表名称\" prop=\"tableName\">\n        <el-input\n          v-model=\"queryParams.tableName\"\n          placeholder=\"请输入表名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\n        <el-input\n          v-model=\"queryParams.tableComment\"\n          placeholder=\"请输入表描述\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleGenTable\"\n          v-hasPermi=\"['tool:gen:code']\"\n        >生成</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"openCreateTable\"\n          v-hasRole=\"['admin']\"\n        >创建</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload\"\n          size=\"mini\"\n          @click=\"openImportTable\"\n          v-hasPermi=\"['tool:gen:import']\"\n        >导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleEditTable\"\n          v-hasPermi=\"['tool:gen:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['tool:gen:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" align=\"center\" width=\"55\"></el-table-column>\n      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"表名称\"\n        align=\"center\"\n        prop=\"tableName\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column\n        label=\"表描述\"\n        align=\"center\"\n        prop=\"tableComment\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column\n        label=\"实体\"\n        align=\"center\"\n        prop=\"className\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-view\"\n            @click=\"handlePreview(scope.row)\"\n            v-hasPermi=\"['tool:gen:preview']\"\n          >预览</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEditTable(scope.row)\"\n            v-hasPermi=\"['tool:gen:edit']\"\n          >编辑</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['tool:gen:remove']\"\n          >删除</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-refresh\"\n            @click=\"handleSynchDb(scope.row)\"\n            v-hasPermi=\"['tool:gen:edit']\"\n          >同步</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-download\"\n            @click=\"handleGenTable(scope.row)\"\n            v-hasPermi=\"['tool:gen:code']\"\n          >生成代码</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n    <!-- 预览界面 -->\n    <el-dialog :title=\"preview.title\" :visible.sync=\"preview.open\" width=\"80%\" top=\"5vh\" append-to-body class=\"scrollbar\">\n      <el-tabs v-model=\"preview.activeName\">\n        <el-tab-pane\n          v-for=\"(value, key) in preview.data\"\n          :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\n          :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\n          :key=\"key\"\n        >\n          <el-link :underline=\"false\" icon=\"el-icon-document-copy\" v-clipboard:copy=\"value\" v-clipboard:success=\"clipboardSuccess\" style=\"float:right\">复制</el-link>\n          <pre><code class=\"hljs\" v-html=\"highlightedCode(value, key)\"></code></pre>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n    <import-table ref=\"import\" @ok=\"handleQuery\" />\n    <create-table ref=\"create\" @ok=\"handleQuery\" />\n  </div>\n</template>\n\n<script>\nimport { listTable, previewTable, delTable, genCode, synchDb } from \"@/api/tool/gen\";\nimport importTable from \"./importTable\";\nimport createTable from \"./createTable\";\nimport hljs from \"highlight.js/lib/highlight\";\nimport \"highlight.js/styles/github-gist.css\";\nhljs.registerLanguage(\"py\", require(\"highlight.js/lib/languages/python\"));\nhljs.registerLanguage(\"html\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"vue\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"javascript\", require(\"highlight.js/lib/languages/javascript\"));\nhljs.registerLanguage(\"sql\", require(\"highlight.js/lib/languages/sql\"));\n\nexport default {\n  name: \"Gen\",\n  components: { importTable, createTable },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 唯一标识符\n      uniqueId: \"\",\n      // 选中数组\n      ids: [],\n      // 选中表数组\n      tableNames: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表数据\n      tableList: [],\n      // 日期范围\n      dateRange: \"\",\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        tableName: undefined,\n        tableComment: undefined\n      },\n      // 预览参数\n      preview: {\n        open: false,\n        title: \"代码预览\",\n        data: {},\n        activeName: \"do.py\"\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  activated() {\n    const time = this.$route.query.t;\n    if (time != null && time != this.uniqueId) {\n      this.uniqueId = time;\n      this.queryParams.pageNum = Number(this.$route.query.pageNum);\n      this.getList();\n    }\n  },\n  methods: {\n    /** 查询表集合 */\n    getList() {\n      this.loading = true;\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.tableList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 生成代码操作 */\n    handleGenTable(row) {\n      const tableNames = row.tableName || this.tableNames;\n      if (tableNames == \"\") {\n        this.$modal.msgError(\"请选择要生成的数据\");\n        return;\n      }\n      if(row.genType === \"1\") {\n        genCode(row.tableName).then(response => {\n          this.$modal.msgSuccess(\"成功生成到自定义路径：\" + row.genPath);\n        });\n      } else {\n        this.$download.zip(\"/tool/gen/batchGenCode?tables=\" + tableNames, \"vfadmin.zip\");\n      }\n    },\n    /** 同步数据库操作 */\n    handleSynchDb(row) {\n      const tableName = row.tableName;\n      this.$modal.confirm('确认要强制同步\"' + tableName + '\"表结构吗？').then(function() {\n        return synchDb(tableName);\n      }).then(() => {\n        this.$modal.msgSuccess(\"同步成功\");\n      }).catch(() => {});\n    },\n    /** 打开导入表弹窗 */\n    openImportTable() {\n      this.$refs.import.show();\n    },\n    /** 打开创建表弹窗 */\n    openCreateTable() {\n      this.$refs.create.show();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 预览按钮 */\n    handlePreview(row) {\n      previewTable(row.tableId).then(response => {\n        this.preview.data = response.data;\n        this.preview.open = true;\n        this.preview.activeName = \"do.py\";\n      });\n    },\n    /** 高亮显示 */\n    highlightedCode(code, key) {\n      const vmName = key.substring(key.lastIndexOf(\"/\") + 1, key.indexOf(\".jinja2\"));\n      var language = vmName.substring(vmName.indexOf(\".\") + 1, vmName.length);\n      const result = hljs.highlight(language, code || \"\", true);\n      return result.value || '&nbsp;';\n    },\n    /** 复制代码成功 */\n    clipboardSuccess() {\n      this.$modal.msgSuccess(\"复制成功\");\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.tableId);\n      this.tableNames = selection.map(item => item.tableName);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 修改按钮操作 */\n    handleEditTable(row) {\n      const tableId = row.tableId || this.ids[0];\n      const tableName = row.tableName || this.tableNames[0];\n      const params = { pageNum: this.queryParams.pageNum };\n      this.$tab.openPage(\"修改[\" + tableName + \"]生成配置\", '/tool/gen-edit/index/' + tableId, params);\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const tableIds = row.tableId || this.ids;\n      this.$modal.confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项？').then(function() {\n        return delTable(tableIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AA8LA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAF,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACAK,kBAAA,CAAAC,gBAAA,OAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,SAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,QAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,eAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,QAAAN,OAAA;AAAA,IAAAO,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,QAAA;MACA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,YAAA,EAAAD;MACA;MACA;MACAE,OAAA;QACAC,IAAA;QACAC,KAAA;QACAnB,IAAA;QACAoB,UAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,CAAA;IACA,IAAAH,IAAA,YAAAA,IAAA,SAAAtB,QAAA;MACA,KAAAA,QAAA,GAAAsB,IAAA;MACA,KAAAb,WAAA,CAAAC,OAAA,GAAAgB,MAAA,MAAAH,MAAA,CAAAC,KAAA,CAAAd,OAAA;MACA,KAAAU,OAAA;IACA;EACA;EACAO,OAAA;IACA,YACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,cAAA,OAAAC,YAAA,MAAArB,WAAA,OAAAD,SAAA,GAAAuB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAArB,SAAA,GAAAyB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAtB,KAAA,GAAA0B,QAAA,CAAA1B,KAAA;QACAsB,KAAA,CAAA7B,OAAA;MACA,CACA;IACA;IACA,aACAmC,WAAA,WAAAA,YAAA;MACA,KAAAzB,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACAe,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAnC,UAAA,GAAAkC,GAAA,CAAAxB,SAAA,SAAAV,UAAA;MACA,IAAAA,UAAA;QACA,KAAAoC,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAH,GAAA,CAAAI,OAAA;QACA,IAAAC,YAAA,EAAAL,GAAA,CAAAxB,SAAA,EAAAmB,IAAA,WAAAC,QAAA;UACAK,MAAA,CAAAC,MAAA,CAAAI,UAAA,iBAAAN,GAAA,CAAAO,OAAA;QACA;MACA;QACA,KAAAC,SAAA,CAAAC,GAAA,oCAAA3C,UAAA;MACA;IACA;IACA,cACA4C,aAAA,WAAAA,cAAAV,GAAA;MAAA,IAAAW,MAAA;MACA,IAAAnC,SAAA,GAAAwB,GAAA,CAAAxB,SAAA;MACA,KAAA0B,MAAA,CAAAU,OAAA,cAAApC,SAAA,aAAAmB,IAAA;QACA,WAAAkB,YAAA,EAAArC,SAAA;MACA,GAAAmB,IAAA;QACAgB,MAAA,CAAAT,MAAA,CAAAI,UAAA;MACA,GAAAQ,KAAA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,IAAA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA;MACA,KAAAH,KAAA,CAAAI,MAAA,CAAAF,IAAA;IACA;IACA,aACAG,UAAA,WAAAA,WAAA;MACA,KAAAjD,SAAA;MACA,KAAAkD,SAAA;MACA,KAAAxB,WAAA;IACA;IACA,WACAyB,aAAA,WAAAA,cAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,iBAAA,EAAAzB,GAAA,CAAA0B,OAAA,EAAA/B,IAAA,WAAAC,QAAA;QACA4B,MAAA,CAAA7C,OAAA,CAAAjB,IAAA,GAAAkC,QAAA,CAAAlC,IAAA;QACA8D,MAAA,CAAA7C,OAAA,CAAAC,IAAA;QACA4C,MAAA,CAAA7C,OAAA,CAAAG,UAAA;MACA;IACA;IACA,WACA6C,eAAA,WAAAA,gBAAAC,IAAA,EAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,SAAA,CAAAF,GAAA,CAAAG,WAAA,WAAAH,GAAA,CAAAI,OAAA;MACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAC,SAAA,CAAAD,MAAA,CAAAG,OAAA,WAAAH,MAAA,CAAAK,MAAA;MACA,IAAAC,MAAA,GAAAnF,kBAAA,CAAAoF,SAAA,CAAAH,QAAA,EAAAN,IAAA;MACA,OAAAQ,MAAA,CAAAE,KAAA;IACA;IACA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAArC,MAAA,CAAAI,UAAA;IACA;IACA;IACAkC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5E,GAAA,GAAA4E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,OAAA;MAAA;MACA,KAAA5D,UAAA,GAAA2E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAnE,SAAA;MAAA;MACA,KAAAT,MAAA,GAAA0E,SAAA,CAAAN,MAAA;MACA,KAAAnE,QAAA,IAAAyE,SAAA,CAAAN,MAAA;IACA;IACA,aACAS,eAAA,WAAAA,gBAAA5C,GAAA;MACA,IAAA0B,OAAA,GAAA1B,GAAA,CAAA0B,OAAA,SAAA7D,GAAA;MACA,IAAAW,SAAA,GAAAwB,GAAA,CAAAxB,SAAA,SAAAV,UAAA;MACA,IAAA+E,MAAA;QAAAvE,OAAA,OAAAD,WAAA,CAAAC;MAAA;MACA,KAAAwE,IAAA,CAAAC,QAAA,SAAAvE,SAAA,sCAAAkD,OAAA,EAAAmB,MAAA;IACA;IACA,aACAG,YAAA,WAAAA,aAAAhD,GAAA;MAAA,IAAAiD,MAAA;MACA,IAAAC,QAAA,GAAAlD,GAAA,CAAA0B,OAAA,SAAA7D,GAAA;MACA,KAAAqC,MAAA,CAAAU,OAAA,iBAAAsC,QAAA,aAAAvD,IAAA;QACA,WAAAwD,aAAA,EAAAD,QAAA;MACA,GAAAvD,IAAA;QACAsD,MAAA,CAAAjE,OAAA;QACAiE,MAAA,CAAA/C,MAAA,CAAAI,UAAA;MACA,GAAAQ,KAAA;IACA;EACA;AACA", "ignoreList": []}]}