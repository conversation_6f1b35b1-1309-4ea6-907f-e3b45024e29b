{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\opinion-overview\\index.vue?vue&type=style&index=0&id=22c4c981&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\opinion-overview\\index.vue", "mtime": 1748441392737}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm9waW5pb24tb3ZlcnZpZXcgewogIGRpc3BsYXk6IGZsZXg7CiAgaGVpZ2h0OiAxMDB2aDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Owp9CgoubGVmdC1zaWRlYmFyIHsKICB3aWR0aDogMjgwcHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZThlOGU4OwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKCiAgLnNpZGViYXItaGVhZGVyIHsKICAgIHBhZGRpbmc6IDE2cHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKICB9CgogIC5zaWRlYmFyLXNlYXJjaCB7CiAgICBwYWRkaW5nOiAxNnB4OwogIH0KCiAgLnNpZGViYXItbWVudSB7CiAgICBmbGV4OiAxOwogICAgcGFkZGluZzogMCAxNnB4OwoKICAgIC5zZWN0aW9uLXRpdGxlIHsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogIzY2NjsKICAgICAgbWFyZ2luLWJvdHRvbTogOHB4OwogICAgfQoKICAgIC5zaWRlYmFyLW1lbnUtbGlzdCB7CiAgICAgIGJvcmRlcjogbm9uZTsKCiAgICAgIC5lbC1tZW51LWl0ZW0gewogICAgICAgIGhlaWdodDogNDBweDsKICAgICAgICBsaW5lLWhlaWdodDogNDBweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwoKICAgICAgICAmLmFjdGl2ZS1pdGVtIHsKICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmY7CiAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICB9CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi5yaWdodC1jb250ZW50IHsKICBmbGV4OiAxOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKCiAgLnRvcC10YWJzIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKCiAgICAuZWwtdGFicyB7CiAgICAgIC5lbC10YWJzX19oZWFkZXIgewogICAgICAgIG1hcmdpbjogMDsKCiAgICAgICAgLmVsLXRhYnNfX25hdi13cmFwIHsKICAgICAgICAgIHBhZGRpbmc6IDAgMjRweDsKICAgICAgICB9CgogICAgICAgIC5lbC10YWJzX19pdGVtIHsKICAgICAgICAgIGhlaWdodDogNTBweDsKICAgICAgICAgIGxpbmUtaGVpZ2h0OiA1MHB4OwogICAgICAgICAgZm9udC1zaXplOiAxNHB4OwoKICAgICAgICAgICYuaXMtYWN0aXZlIHsKICAgICAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgICAgICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICMxODkwZmY7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQoKICAubWFpbi1jb250ZW50IHsKICAgIGZsZXg6IDE7CiAgICBwYWRkaW5nOiAyNHB4OwogICAgb3ZlcmZsb3cteTogYXV0bzsKICB9Cn0KCi5zZWN0aW9uLWNhcmQgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHBhZGRpbmc6IDI0cHg7CiAgbWFyZ2luLWJvdHRvbTogMjRweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwoKICAuY2FyZC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAyNHB4OwoKICAgIGgzIHsKICAgICAgbWFyZ2luOiAwOwogICAgICBmb250LXNpemU6IDE4cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICB9CgogICAgLnN0YXRzIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZ2FwOiAyNHB4OwoKICAgICAgLnN0YXQtaXRlbSB7CiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwoKICAgICAgICAubGFiZWwgewogICAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgICBjb2xvcjogIzY2NjsKICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsKICAgICAgICB9CgogICAgICAgIC52YWx1ZSB7CiAgICAgICAgICBkaXNwbGF5OiBibG9jazsKICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDsKICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICAgICAgfQoKICAgICAgICAmLnBvc2l0aXZlIC52YWx1ZSB7CiAgICAgICAgICBjb2xvcjogIzUyYzQxYTsKICAgICAgICB9CgogICAgICAgICYubmV1dHJhbCAudmFsdWUgewogICAgICAgICAgY29sb3I6ICNmYWFkMTQ7CiAgICAgICAgfQoKICAgICAgICAmLm5lZ2F0aXZlIC52YWx1ZSB7CiAgICAgICAgICBjb2xvcjogI2ZmNGQ0ZjsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi5ib3R0b20tc2VjdGlvbiB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDI0cHg7CgogIC5sZWZ0LWNoYXJ0cyB7CiAgICBmbGV4OiAyOwogIH0KCiAgLnJpZ2h0LXN0YXRzIHsKICAgIGZsZXg6IDE7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIGdhcDogMjRweDsKICB9CgogIC5sZWZ0LWFydGljbGVzIHsKICAgIGZsZXg6IDE7CiAgfQoKICAucmlnaHQtYW5ub3VuY2VtZW50cyB7CiAgICBmbGV4OiAxOwogIH0KfQoKLmNoYXJ0LWNhcmQgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHBhZGRpbmc6IDI0cHg7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKCiAgLmNhcmQtaGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogMTZweDsKCiAgICBoMyB7CiAgICAgIG1hcmdpbjogMDsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBmb250LXdlaWdodDogNjAwOwogICAgfQogIH0KfQoKLnN0YXRzLWNhcmQgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHBhZGRpbmc6IDI0cHg7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKCiAgLnRvdGFsLWNvdW50IHsKICAgIGZvbnQtc2l6ZTogMzJweDsKICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBtYXJnaW46IDI0cHggMDsKICAgIGxldHRlci1zcGFjaW5nOiAycHg7CiAgfQoKICAucGxhdGZvcm0tc3RhdHMgewogICAgLnBsYXRmb3JtLWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBwYWRkaW5nOiAxMnB4IDA7CiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwoKICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICBib3JkZXItYm90dG9tOiBub25lOwogICAgICB9CgogICAgICAucGxhdGZvcm0taWNvbiB7CiAgICAgICAgd2lkdGg6IDMycHg7CiAgICAgICAgaGVpZ2h0OiAzMnB4OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7CiAgICAgIH0KCiAgICAgICYud2VpYm8gLnBsYXRmb3JtLWljb24gewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMxODkwZmY7CiAgICAgIH0KCiAgICAgICYud2VjaGF0IC5wbGF0Zm9ybS1pY29uIHsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNTJjNDFhOwogICAgICB9CgogICAgICAmLndlaWJvLXJlZCAucGxhdGZvcm0taWNvbiB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZjsKICAgICAgfQoKICAgICAgJi54aWFvaG9uZ3NodSAucGxhdGZvcm0taWNvbiB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ViMmY5NjsKICAgICAgfQoKICAgICAgJi5hcHAgLnBsYXRmb3JtLWljb24gewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMxM2MyYzI7CiAgICAgIH0KCiAgICAgICYudG91dGlhbyAucGxhdGZvcm0taWNvbiB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZhOGMxNjsKICAgICAgfQoKICAgICAgJi5kb3V5aW4gLnBsYXRmb3JtLWljb24gewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM3MjJlZDE7CiAgICAgIH0KCiAgICAgICYubmV3cyAucGxhdGZvcm0taWNvbiB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZhYWQxNDsKICAgICAgfQoKICAgICAgJi5mb3J1bSAucGxhdGZvcm0taWNvbiB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2EwZDkxMTsKICAgICAgfQoKICAgICAgLnBsYXRmb3JtLWluZm8gewogICAgICAgIGZsZXg6IDE7CgogICAgICAgIC5wbGF0Zm9ybS1uYW1lIHsKICAgICAgICAgIGRpc3BsYXk6IGJsb2NrOwogICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgfQoKICAgICAgICAucGxhdGZvcm0tY291bnQgewogICAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgICBmb250LXNpemU6IDE4cHg7CiAgICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgfQoKICAgICAgICAucGxhdGZvcm0tY2hhbmdlIHsKICAgICAgICAgIGRpc3BsYXk6IGJsb2NrOwogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgY29sb3I6ICM2NjY7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9CgovLyDng63pl6jmlofnq6DmoLflvI8KLmFydGljbGUtY2FyZCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgcGFkZGluZzogMjRweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwoKICAuY2FyZC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwoKICAgIGgzIHsKICAgICAgbWFyZ2luOiAwOwogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICB9CiAgfQoKICAuYXJ0aWNsZS1saXN0IHsKICAgIC5hcnRpY2xlLWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBwYWRkaW5nOiAxMnB4IDA7CiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwoKICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICBib3JkZXItYm90dG9tOiBub25lOwogICAgICB9CgogICAgICAuYXJ0aWNsZS1pY29uIHsKICAgICAgICB3aWR0aDogMzJweDsKICAgICAgICBoZWlnaHQ6IDMycHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICBjb2xvcjogI2ZmZjsKICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgIG1hcmdpbi1yaWdodDogMTJweDsKICAgICAgICBmb250LXNpemU6IDE0cHg7CgogICAgICAgICYubmVnYXRpdmUgewogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZjsKICAgICAgICB9CgogICAgICAgICYubmV1dHJhbCB7CiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFhZDE0OwogICAgICAgIH0KCiAgICAgICAgJi5wb3NpdGl2ZSB7CiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNTJjNDFhOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLmFydGljbGUtY29udGVudCB7CiAgICAgICAgZmxleDogMTsKCiAgICAgICAgLmFydGljbGUtdGl0bGUgewogICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7CiAgICAgICAgICBsaW5lLWhlaWdodDogMS40OwogICAgICAgIH0KCiAgICAgICAgLmFydGljbGUtbWV0YSB7CiAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICBjb2xvcjogIzY2NjsKCiAgICAgICAgICAuYXJ0aWNsZS1zb3VyY2UgewogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9CgovLyDmnIDmlrDlhazlkYrmoLflvI8KLmFubm91bmNlbWVudC1jYXJkIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBwYWRkaW5nOiAyNHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CgogIC5jYXJkLWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIG1hcmdpbi1ib3R0b206IDE2cHg7CgogICAgaDMgewogICAgICBtYXJnaW46IDA7CiAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgIH0KICB9CgogIC5hbm5vdW5jZW1lbnQtbGlzdCB7CiAgICAuYW5ub3VuY2VtZW50LWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBwYWRkaW5nOiAxMnB4IDA7CiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwoKICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICBib3JkZXItYm90dG9tOiBub25lOwogICAgICB9CgogICAgICAuYW5ub3VuY2VtZW50LWluZGljYXRvciB7CiAgICAgICAgd2lkdGg6IDhweDsKICAgICAgICBoZWlnaHQ6IDhweDsKICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4OwoKICAgICAgICAmLmhpZ2ggewogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZjsKICAgICAgICB9CgogICAgICAgICYubWVkaXVtIHsKICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmYWFkMTQ7CiAgICAgICAgfQoKICAgICAgICAmLmxvdyB7CiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNTJjNDFhOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLmFubm91bmNlbWVudC1jb250ZW50IHsKICAgICAgICBmbGV4OiAxOwoKICAgICAgICAuYW5ub3VuY2VtZW50LXRpdGxlIHsKICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgIGNvbG9yOiAjMzMzOwogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4OwogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsKICAgICAgICB9CgogICAgICAgIC5hbm5vdW5jZW1lbnQtdGltZSB7CiAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICBjb2xvcjogIzY2NjsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA64BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-overview", "sourcesContent": ["<template>\n  <div class=\"opinion-overview\">\n    <!-- 左侧导航栏 -->\n    <div class=\"left-sidebar\">\n      <div class=\"sidebar-header\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\n      </div>\n\n      <div class=\"sidebar-search\">\n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索方案\"\n          size=\"small\"\n          prefix-icon=\"el-icon-search\">\n        </el-input>\n      </div>\n\n      <div class=\"sidebar-menu\">\n        <div class=\"menu-section\">\n          <div class=\"section-title\">已有方案</div>\n          <el-menu\n            :default-active=\"activeMenuItem\"\n            class=\"sidebar-menu-list\"\n            @select=\"handleMenuSelect\">\n            <el-menu-item index=\"基础(1)\">\n              <i class=\"el-icon-s-custom\"></i>\n              <span>基础(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"品牌(1)\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span>品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"方太\" class=\"active-item\">\n              <i class=\"el-icon-star-off\"></i>\n              <span>方太</span>\n            </el-menu-item>\n            <el-menu-item index=\"人物(0)\">\n              <i class=\"el-icon-user\"></i>\n              <span>人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"机构(0)\">\n              <i class=\"el-icon-office-building\"></i>\n              <span>机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"产品(0)\">\n              <i class=\"el-icon-goods\"></i>\n              <span>产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"事件(0)\">\n              <i class=\"el-icon-warning\"></i>\n              <span>事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"话题(0)\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              <span>话题(0)</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧内容区 -->\n    <div class=\"right-content\">\n      <!-- 顶部导航标签栏 -->\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <!-- 根据activeTab显示不同内容 -->\n        <div v-if=\"activeTab === 'opinion-monitor'\">\n          <!-- 舆情监测内容 -->\n\n          <!-- 舆情趋势图表 -->\n          <div class=\"section-card\">\n            <div class=\"chart-container\">\n              <div id=\"trend-chart\" style=\"width: 100%; height: 400px;\"></div>\n            </div>\n          </div>\n\n          <!-- 热门文章和最新公告 -->\n          <div class=\"bottom-section\">\n            <div class=\"left-articles\">\n              <div class=\"article-card\">\n                <div class=\"card-header\">\n                  <h3><i class=\"el-icon-document\" style=\"color: #1890ff; margin-right: 8px;\"></i>热门文章</h3>\n                </div>\n                <div class=\"article-list\">\n                  <div class=\"article-item\" v-for=\"(article, index) in hotArticles\" :key=\"index\">\n                    <div class=\"article-icon\" :class=\"article.type\">{{ article.icon }}</div>\n                    <div class=\"article-content\">\n                      <div class=\"article-title\">{{ article.title }}</div>\n                      <div class=\"article-meta\">\n                        <span class=\"article-source\">{{ article.source }}</span>\n                        <span class=\"article-author\">{{ article.author }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"right-announcements\">\n              <div class=\"announcement-card\">\n                <div class=\"card-header\">\n                  <h3><i class=\"el-icon-bell\" style=\"color: #52c41a; margin-right: 8px;\"></i>最新公告</h3>\n                </div>\n                <div class=\"announcement-list\">\n                  <div class=\"announcement-item\" v-for=\"(announcement, index) in announcements\" :key=\"index\">\n                    <div class=\"announcement-indicator\" :class=\"announcement.level\"></div>\n                    <div class=\"announcement-content\">\n                      <div class=\"announcement-title\">{{ announcement.title }}</div>\n                      <div class=\"announcement-time\">{{ announcement.time }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else-if=\"activeTab === 'info-summary'\">\n          <!-- 信息汇总内容 -->\n          <!-- 近30天舆情发布地区 -->\n          <div class=\"section-card\">\n            <div class=\"card-header\">\n              <h3><i class=\"el-icon-location\" style=\"color: #409EFF; margin-right: 8px;\"></i>近30天舆情发布地区</h3>\n              <div class=\"stats\">\n                <div class=\"stat-item positive\">\n                  <span class=\"label\">正面舆情</span>\n                  <span class=\"value\">111930</span>\n                </div>\n                <div class=\"stat-item neutral\">\n                  <span class=\"label\">中性舆情</span>\n                  <span class=\"value\">1118</span>\n                </div>\n                <div class=\"stat-item negative\">\n                  <span class=\"label\">负面舆情</span>\n                  <span class=\"value\">444</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"map-container\">\n              <div id=\"china-map\" style=\"width: 100%; height: 400px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center; color: #999;\">\n                <div>地图组件加载中...</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 今日舆情总量和平台分析 -->\n          <div class=\"bottom-section\">\n          <div class=\"left-charts\">\n            <!-- 近30天平台分析 -->\n            <div class=\"chart-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-pie-chart\" style=\"color: #52C41A; margin-right: 8px;\"></i>近30天平台分析</h3>\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\n              </div>\n              <div id=\"platform-chart\" style=\"width: 100%; height: 300px;\"></div>\n            </div>\n          </div>\n\n          <div class=\"right-stats\">\n            <!-- 今日舆情总量 -->\n            <div class=\"stats-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-data-line\" style=\"color: #FA8C16; margin-right: 8px;\"></i>今日舆情总量</h3>\n              </div>\n              <div class=\"total-count\">0 0 0,0 0 4,6 8 1</div>\n              <div class=\"platform-stats\">\n                <div class=\"platform-item weibo\">\n                  <div class=\"platform-icon\">微</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">微博</span>\n                    <span class=\"platform-count\">534</span>\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item wechat\">\n                  <div class=\"platform-icon\">微</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">微信</span>\n                    <span class=\"platform-count\">1483</span>\n                    <span class=\"platform-change\">今日新增 15.2%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item weibo-red\">\n                  <div class=\"platform-icon\">微</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">微博</span>\n                    <span class=\"platform-count\">279</span>\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item xiaohongshu\">\n                  <div class=\"platform-icon\">小</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">小红书</span>\n                    <span class=\"platform-count\">129</span>\n                    <span class=\"platform-change\">今日新增 3.2%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item app\">\n                  <div class=\"platform-icon\">A</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">APP</span>\n                    <span class=\"platform-count\">764</span>\n                    <span class=\"platform-change\">今日新增 -1.8%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item toutiao\">\n                  <div class=\"platform-icon\">头</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">头条</span>\n                    <span class=\"platform-count\">1455</span>\n                    <span class=\"platform-change\">今日新增 4.5%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item douyin\">\n                  <div class=\"platform-icon\">抖</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">抖音</span>\n                    <span class=\"platform-count\">23</span>\n                    <span class=\"platform-change\">今日新增 100%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item news\">\n                  <div class=\"platform-icon\">新</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">新闻</span>\n                    <span class=\"platform-count\">2</span>\n                    <span class=\"platform-change\">今日新增 100%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item forum\">\n                  <div class=\"platform-icon\">论</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">论坛</span>\n                    <span class=\"platform-count\">12</span>\n                    <span class=\"platform-change\">今日新增 -2.8%</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 近30天情感属性 -->\n            <div class=\"chart-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-sunny\" style=\"color: #722ED1; margin-right: 8px;\"></i>近30天情感属性</h3>\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\n              </div>\n              <div id=\"sentiment-chart\" style=\"width: 100%; height: 200px;\"></div>\n            </div>\n          </div>\n        </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'OpinionOverview',\n  data() {\n    return {\n      activeMenuItem: '方太',\n      searchText: '',\n      activeTab: 'opinion-monitor', // 默认激活舆情监测标签\n      originalTopNav: undefined, // 存储原始的topNav状态\n      // 图表实例\n      mapChart: null,\n      platformChart: null,\n      sentimentChart: null,\n      trendChart: null, // 趋势图表实例\n      // 热门文章数据\n      hotArticles: [\n        {\n          icon: '红',\n          type: 'negative',\n          title: '某品牌产品质量问题引发消费者不满',\n          source: '新浪财经',\n          author: '财经记者'\n        },\n        {\n          icon: '黄',\n          type: 'neutral',\n          title: '市场分析：家电行业发展趋势',\n          source: '中国经济网',\n          author: '市场分析师'\n        },\n        {\n          icon: '绿',\n          type: 'positive',\n          title: '创新技术推动行业发展',\n          source: '科技日报',\n          author: '科技记者'\n        },\n        {\n          icon: '红',\n          type: 'negative',\n          title: '消费者投诉处理不当引发关注',\n          source: '消费者报',\n          author: '消费维权'\n        },\n        {\n          icon: '绿',\n          type: 'positive',\n          title: '企业社会责任获得认可',\n          source: '人民日报',\n          author: '社会记者'\n        }\n      ],\n      // 最新公告数据\n      announcements: [\n        {\n          level: 'high',\n          title: '舆情监测系统升级通知',\n          time: '2023-04-20 10:30:00'\n        },\n        {\n          level: 'medium',\n          title: '五一假期监测安排',\n          time: '2023-04-19 16:45:00'\n        },\n        {\n          level: 'low',\n          title: '数据统计报告已生成',\n          time: '2023-04-19 14:20:00'\n        },\n        {\n          level: 'high',\n          title: '重要舆情预警提醒',\n          time: '2023-04-19 09:15:00'\n        },\n        {\n          level: 'medium',\n          title: '系统维护完成通知',\n          time: '2023-04-18 18:30:00'\n        }\n      ],\n      // 地图数据\n      mapData: [\n        {name: '北京', value: 15000},\n        {name: '上海', value: 12000},\n        {name: '广东', value: 18000},\n        {name: '浙江', value: 8000},\n        {name: '江苏', value: 9000},\n        {name: '山东', value: 7000},\n        {name: '四川', value: 6000},\n        {name: '湖北', value: 5000}\n      ],\n      // 平台数据\n      platformData: [\n        {name: '微博', value: 35.2, color: '#ff6b6b'},\n        {name: '微信', value: 28.6, color: '#51cf66'},\n        {name: '抖音', value: 18.4, color: '#339af0'},\n        {name: '今日头条', value: 12.8, color: '#ffd43b'},\n        {name: '其他', value: 5.0, color: '#868e96'}\n      ],\n      // 情感数据\n      sentimentData: [\n        {name: '正面', value: 65.2, color: '#52c41a'},\n        {name: '中性', value: 28.3, color: '#faad14'},\n        {name: '负面', value: 6.5, color: '#ff4d4f'}\n      ]\n    }\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n\n    this.$nextTick(() => {\n      // 根据当前激活的标签初始化对应的图表\n      if (this.activeTab === 'opinion-monitor') {\n        this.initTrendChart()\n      } else if (this.activeTab === 'info-summary') {\n        this.initCharts()\n      }\n    })\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n\n    // 销毁图表实例\n    if (this.mapChart) {\n      this.mapChart.dispose()\n    }\n    if (this.platformChart) {\n      this.platformChart.dispose()\n    }\n    if (this.sentimentChart) {\n      this.sentimentChart.dispose()\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose()\n    }\n  },\n  methods: {\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('Menu selected:', index)\n      // 这里可以根据选择的方案加载不同的数据\n    },\n    handleTabClick(tab) {\n      console.log('Tab clicked:', tab.name)\n      // 根据不同标签加载不同内容\n      this.$nextTick(() => {\n        if (tab.name === 'opinion-monitor') {\n          this.initTrendChart()\n        } else if (tab.name === 'info-summary') {\n          this.initCharts()\n        }\n      })\n    },\n    initCharts() {\n      // 初始化图表\n      this.initMap()\n      this.initPlatformChart()\n      this.initSentimentChart()\n    },\n    initTrendChart() {\n      // 初始化舆情趋势图表\n      const chartContainer = document.getElementById('trend-chart')\n      if (!chartContainer) return\n\n      this.trendChart = echarts.init(chartContainer)\n\n      // 生成30天的日期数据\n      const dates = []\n      const today = new Date()\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(today)\n        date.setDate(date.getDate() - i)\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\n      }\n\n      // 模拟各平台的数据\n      const weiboData = this.generateRandomData(30, 1500, 2500)\n      const wechatData = this.generateRandomData(30, 1200, 2000)\n      const douyinData = this.generateRandomData(30, 800, 1500)\n      const toutiaData = this.generateRandomData(30, 600, 1200)\n      const otherData = this.generateRandomData(30, 300, 800)\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            label: {\n              backgroundColor: '#6a7985'\n            }\n          },\n          formatter: function(params) {\n            let result = params[0].name + '<br/>'\n            params.forEach(param => {\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\n            })\n            return result\n          }\n        },\n        legend: {\n          data: ['微博', '微信', '抖音', '头条', '其他'],\n          top: 20,\n          textStyle: {\n            fontSize: 12\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          top: '15%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: dates,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#f0f0f0'\n            }\n          }\n        },\n        series: [\n          {\n            name: '微博',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#1890ff',\n              width: 2\n            },\n            itemStyle: {\n              color: '#1890ff'\n            },\n            data: weiboData\n          },\n          {\n            name: '微信',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#52c41a',\n              width: 2\n            },\n            itemStyle: {\n              color: '#52c41a'\n            },\n            data: wechatData\n          },\n          {\n            name: '抖音',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#722ed1',\n              width: 2\n            },\n            itemStyle: {\n              color: '#722ed1'\n            },\n            data: douyinData\n          },\n          {\n            name: '头条',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#fa8c16',\n              width: 2\n            },\n            itemStyle: {\n              color: '#fa8c16'\n            },\n            data: toutiaData\n          },\n          {\n            name: '其他',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#a0d911',\n              width: 2\n            },\n            itemStyle: {\n              color: '#a0d911'\n            },\n            data: otherData\n          }\n        ]\n      }\n\n      this.trendChart.setOption(option)\n    },\n    initMap() {\n      // 暂时跳过地图初始化，避免缺少地图数据导致的错误\n      console.log('地图初始化已跳过，需要引入中国地图数据')\n    },\n    initPlatformChart() {\n      // 初始化平台分析面积图\n      const chartContainer = document.getElementById('platform-chart')\n      if (!chartContainer) return\n\n      this.platformChart = echarts.init(chartContainer)\n\n      // 生成30天的日期数据\n      const dates = []\n      const today = new Date()\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(today)\n        date.setDate(date.getDate() - i)\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\n      }\n\n      // 模拟各平台的数据\n      const weiboData = this.generateRandomData(30, 1500, 2000)\n      const wechatData = this.generateRandomData(30, 1200, 1800)\n      const douyinData = this.generateRandomData(30, 800, 1200)\n      const toutiaData = this.generateRandomData(30, 600, 1000)\n      const otherData = this.generateRandomData(30, 300, 600)\n\n      const option = {\n        title: {\n          text: '近30天平台舆情趋势',\n          left: 'center',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'normal',\n            color: '#333'\n          }\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            label: {\n              backgroundColor: '#6a7985'\n            }\n          },\n          formatter: function (params) {\n            let result = params[0].name + '<br/>'\n            params.forEach(param => {\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\n            })\n            return result\n          }\n        },\n        legend: {\n          data: ['微博', '微信', '抖音', '头条', '其他'],\n          top: 30,\n          textStyle: {\n            fontSize: 12\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          top: '15%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: dates,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#f0f0f0'\n            }\n          }\n        },\n        series: [\n          {\n            name: '微博',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#1890ff',\n              width: 2\n            },\n            itemStyle: {\n              color: '#1890ff'\n            },\n            data: weiboData\n          },\n          {\n            name: '微信',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#52c41a',\n              width: 2\n            },\n            itemStyle: {\n              color: '#52c41a'\n            },\n            data: wechatData\n          },\n          {\n            name: '抖音',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#722ed1',\n              width: 2\n            },\n            itemStyle: {\n              color: '#722ed1'\n            },\n            data: douyinData\n          },\n          {\n            name: '头条',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#fa8c16',\n              width: 2\n            },\n            itemStyle: {\n              color: '#fa8c16'\n            },\n            data: toutiaData\n          },\n          {\n            name: '其他',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#a0d911',\n              width: 2\n            },\n            itemStyle: {\n              color: '#a0d911'\n            },\n            data: otherData\n          }\n        ]\n      }\n\n      this.platformChart.setOption(option)\n    },\n    initSentimentChart() {\n      // 初始化情感属性饼图\n      const chartContainer = document.getElementById('sentiment-chart')\n      if (!chartContainer) return\n\n      this.sentimentChart = echarts.init(chartContainer)\n\n      const option = {\n        title: {\n          text: '情感分布',\n          left: 'center',\n          textStyle: {\n            fontSize: 14,\n            fontWeight: 'normal',\n            color: '#333'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle',\n          data: ['正面', '中性', '负面'],\n          textStyle: {\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            name: '情感分布',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              position: 'center'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '18',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [\n              {\n                name: '正面',\n                value: 65.2,\n                itemStyle: {\n                  color: '#52c41a'\n                }\n              },\n              {\n                name: '中性',\n                value: 28.3,\n                itemStyle: {\n                  color: '#faad14'\n                }\n              },\n              {\n                name: '负面',\n                value: 6.5,\n                itemStyle: {\n                  color: '#ff4d4f'\n                }\n              }\n            ]\n          }\n        ]\n      }\n\n      this.sentimentChart.setOption(option)\n    },\n    // 生成随机数据的辅助方法\n    generateRandomData(count, min, max) {\n      const data = []\n      for (let i = 0; i < count; i++) {\n        const value = Math.floor(Math.random() * (max - min + 1)) + min\n        data.push(value)\n      }\n      return data\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-overview {\n  display: flex;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 16px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .sidebar-search {\n    padding: 16px;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 0 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 8px;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 40px;\n        line-height: 40px;\n        margin-bottom: 4px;\n        border-radius: 4px;\n\n        &.active-item {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &:hover {\n          background-color: #f5f5f5;\n        }\n      }\n    }\n  }\n}\n\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n\n  .top-tabs {\n    background-color: #fff;\n    border-bottom: 1px solid #e8e8e8;\n\n    .el-tabs {\n      .el-tabs__header {\n        margin: 0;\n\n        .el-tabs__nav-wrap {\n          padding: 0 24px;\n        }\n\n        .el-tabs__item {\n          height: 50px;\n          line-height: 50px;\n          font-size: 14px;\n\n          &.is-active {\n            color: #1890ff;\n            border-bottom-color: #1890ff;\n          }\n        }\n      }\n    }\n  }\n\n  .main-content {\n    flex: 1;\n    padding: 24px;\n    overflow-y: auto;\n  }\n}\n\n.section-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 24px;\n\n    h3 {\n      margin: 0;\n      font-size: 18px;\n      font-weight: 600;\n    }\n\n    .stats {\n      display: flex;\n      gap: 24px;\n\n      .stat-item {\n        text-align: center;\n\n        .label {\n          display: block;\n          font-size: 14px;\n          color: #666;\n          margin-bottom: 4px;\n        }\n\n        .value {\n          display: block;\n          font-size: 24px;\n          font-weight: 600;\n        }\n\n        &.positive .value {\n          color: #52c41a;\n        }\n\n        &.neutral .value {\n          color: #faad14;\n        }\n\n        &.negative .value {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n}\n\n.bottom-section {\n  display: flex;\n  gap: 24px;\n\n  .left-charts {\n    flex: 2;\n  }\n\n  .right-stats {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n  }\n\n  .left-articles {\n    flex: 1;\n  }\n\n  .right-announcements {\n    flex: 1;\n  }\n}\n\n.chart-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n}\n\n.stats-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .total-count {\n    font-size: 32px;\n    font-weight: 600;\n    text-align: center;\n    margin: 24px 0;\n    letter-spacing: 2px;\n  }\n\n  .platform-stats {\n    .platform-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .platform-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 4px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n        font-weight: 600;\n        margin-right: 12px;\n      }\n\n      &.weibo .platform-icon {\n        background-color: #1890ff;\n      }\n\n      &.wechat .platform-icon {\n        background-color: #52c41a;\n      }\n\n      &.weibo-red .platform-icon {\n        background-color: #ff4d4f;\n      }\n\n      &.xiaohongshu .platform-icon {\n        background-color: #eb2f96;\n      }\n\n      &.app .platform-icon {\n        background-color: #13c2c2;\n      }\n\n      &.toutiao .platform-icon {\n        background-color: #fa8c16;\n      }\n\n      &.douyin .platform-icon {\n        background-color: #722ed1;\n      }\n\n      &.news .platform-icon {\n        background-color: #faad14;\n      }\n\n      &.forum .platform-icon {\n        background-color: #a0d911;\n      }\n\n      .platform-info {\n        flex: 1;\n\n        .platform-name {\n          display: block;\n          font-size: 14px;\n          color: #333;\n        }\n\n        .platform-count {\n          display: block;\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .platform-change {\n          display: block;\n          font-size: 12px;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n\n// 热门文章样式\n.article-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n\n  .article-list {\n    .article-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .article-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 4px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n        font-weight: 600;\n        margin-right: 12px;\n        font-size: 14px;\n\n        &.negative {\n          background-color: #ff4d4f;\n        }\n\n        &.neutral {\n          background-color: #faad14;\n        }\n\n        &.positive {\n          background-color: #52c41a;\n        }\n      }\n\n      .article-content {\n        flex: 1;\n\n        .article-title {\n          font-size: 14px;\n          color: #333;\n          margin-bottom: 4px;\n          line-height: 1.4;\n        }\n\n        .article-meta {\n          font-size: 12px;\n          color: #666;\n\n          .article-source {\n            margin-right: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 最新公告样式\n.announcement-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n\n  .announcement-list {\n    .announcement-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .announcement-indicator {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 12px;\n\n        &.high {\n          background-color: #ff4d4f;\n        }\n\n        &.medium {\n          background-color: #faad14;\n        }\n\n        &.low {\n          background-color: #52c41a;\n        }\n      }\n\n      .announcement-content {\n        flex: 1;\n\n        .announcement-title {\n          font-size: 14px;\n          color: #333;\n          margin-bottom: 4px;\n          line-height: 1.4;\n        }\n\n        .announcement-time {\n          font-size: 12px;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}