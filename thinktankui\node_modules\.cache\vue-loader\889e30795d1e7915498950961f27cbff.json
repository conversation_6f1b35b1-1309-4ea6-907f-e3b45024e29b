{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return\n      }\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = []\n      const router = this.$route\n      const pathNum = this.findPathNum(router.path)\n      // multi-level menu\n      if (pathNum > 2) {\n        const reg = /\\/\\w+/gi\n        const pathList = router.path.match(reg).map((item, index) => {\n          if (index !== 0) item = item.slice(1)\n          return item\n        })\n        this.getMatched(pathList, this.$store.getters.defaultRoutes, matched)\n      } else {\n        matched = router.matched.filter(item => item.meta && item.meta.title)\n      }\n      // 判断是否为首页\n      if (!this.isDashboard(matched[0])) {\n        matched = [{ path: \"/index\", meta: { title: \"首页\" } }].concat(matched)\n      }\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    findPathNum(str, char = \"/\") {\n      let index = str.indexOf(char)\n      let num = 0\n      while (index !== -1) {\n        num++\n        index = str.indexOf(char, index + 1)\n      }\n      return num\n    },\n    getMatched(pathList, routeList, matched) {\n      let data = routeList.find(item => item.path == pathList[0] || (item.name += '').toLowerCase() == pathList[0])\n      if (data) {\n        matched.push(data)\n        if (data.children && pathList.length) {\n          pathList.shift()\n          this.getMatched(pathList, data.children, matched)\n        }\n      }\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim() === 'Index'\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"]}]}