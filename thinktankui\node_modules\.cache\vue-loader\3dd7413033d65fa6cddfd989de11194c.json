{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue?vue&type=template&id=6ed73c63&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue", "mtime": 1749115113523}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749105919489}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}