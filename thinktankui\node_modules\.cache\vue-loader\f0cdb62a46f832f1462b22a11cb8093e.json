{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\gen\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\gen\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RUYWJsZSwgcHJldmlld1RhYmxlLCBkZWxUYWJsZSwgZ2VuQ29kZSwgc3luY2hEYiB9IGZyb20gIkAvYXBpL3Rvb2wvZ2VuIjsKaW1wb3J0IGltcG9ydFRhYmxlIGZyb20gIi4vaW1wb3J0VGFibGUiOwppbXBvcnQgY3JlYXRlVGFibGUgZnJvbSAiLi9jcmVhdGVUYWJsZSI7CmltcG9ydCBobGpzIGZyb20gImhpZ2hsaWdodC5qcy9saWIvaGlnaGxpZ2h0IjsKaW1wb3J0ICJoaWdobGlnaHQuanMvc3R5bGVzL2dpdGh1Yi1naXN0LmNzcyI7CmhsanMucmVnaXN0ZXJMYW5ndWFnZSgicHkiLCByZXF1aXJlKCJoaWdobGlnaHQuanMvbGliL2xhbmd1YWdlcy9weXRob24iKSk7CmhsanMucmVnaXN0ZXJMYW5ndWFnZSgiaHRtbCIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL3htbCIpKTsKaGxqcy5yZWdpc3Rlckxhbmd1YWdlKCJ2dWUiLCByZXF1aXJlKCJoaWdobGlnaHQuanMvbGliL2xhbmd1YWdlcy94bWwiKSk7CmhsanMucmVnaXN0ZXJMYW5ndWFnZSgiamF2YXNjcmlwdCIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL2phdmFzY3JpcHQiKSk7CmhsanMucmVnaXN0ZXJMYW5ndWFnZSgic3FsIiwgcmVxdWlyZSgiaGlnaGxpZ2h0LmpzL2xpYi9sYW5ndWFnZXMvc3FsIikpOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJHZW4iLAogIGNvbXBvbmVudHM6IHsgaW1wb3J0VGFibGUsIGNyZWF0ZVRhYmxlIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDllK/kuIDmoIfor4bnrKYKICAgICAgdW5pcXVlSWQ6ICIiLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6YCJ5Lit6KGo5pWw57uECiAgICAgIHRhYmxlTmFtZXM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOihqOaVsOaNrgogICAgICB0YWJsZUxpc3Q6IFtdLAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiAiIiwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdGFibGVOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgdGFibGVDb21tZW50OiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6aKE6KeI5Y+C5pWwCiAgICAgIHByZXZpZXc6IHsKICAgICAgICBvcGVuOiBmYWxzZSwKICAgICAgICB0aXRsZTogIuS7o+eggemihOiniCIsCiAgICAgICAgZGF0YToge30sCiAgICAgICAgYWN0aXZlTmFtZTogImRvLnB5IgogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgYWN0aXZhdGVkKCkgewogICAgY29uc3QgdGltZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnQ7CiAgICBpZiAodGltZSAhPSBudWxsICYmIHRpbWUgIT0gdGhpcy51bmlxdWVJZCkgewogICAgICB0aGlzLnVuaXF1ZUlkID0gdGltZTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gTnVtYmVyKHRoaXMuJHJvdXRlLnF1ZXJ5LnBhZ2VOdW0pOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Looajpm4blkIggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RUYWJsZSh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy50YWJsZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICApOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog55Sf5oiQ5Luj56CB5pON5L2cICovCiAgICBoYW5kbGVHZW5UYWJsZShyb3cpIHsKICAgICAgY29uc3QgdGFibGVOYW1lcyA9IHJvdy50YWJsZU5hbWUgfHwgdGhpcy50YWJsZU5hbWVzOwogICAgICBpZiAodGFibGVOYW1lcyA9PSAiIikgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHnlJ/miJDnmoTmlbDmja4iKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYocm93LmdlblR5cGUgPT09ICIxIikgewogICAgICAgIGdlbkNvZGUocm93LnRhYmxlTmFtZSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmiJDlip/nlJ/miJDliLDoh6rlrprkuYnot6/lvoTvvJoiICsgcm93LmdlblBhdGgpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJGRvd25sb2FkLnppcCgiL3Rvb2wvZ2VuL2JhdGNoR2VuQ29kZT90YWJsZXM9IiArIHRhYmxlTmFtZXMsICJ2ZmFkbWluLnppcCIpOwogICAgICB9CiAgICB9LAogICAgLyoqIOWQjOatpeaVsOaNruW6k+aTjeS9nCAqLwogICAgaGFuZGxlU3luY2hEYihyb3cpIHsKICAgICAgY29uc3QgdGFibGVOYW1lID0gcm93LnRhYmxlTmFtZTsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaB5by65Yi25ZCM5q2lIicgKyB0YWJsZU5hbWUgKyAnIuihqOe7k+aehOWQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIHN5bmNoRGIodGFibGVOYW1lKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5ZCM5q2l5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5omT5byA5a+85YWl6KGo5by556qXICovCiAgICBvcGVuSW1wb3J0VGFibGUoKSB7CiAgICAgIHRoaXMuJHJlZnMuaW1wb3J0LnNob3coKTsKICAgIH0sCiAgICAvKiog5omT5byA5Yib5bu66KGo5by556qXICovCiAgICBvcGVuQ3JlYXRlVGFibGUoKSB7CiAgICAgIHRoaXMuJHJlZnMuY3JlYXRlLnNob3coKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog6aKE6KeI5oyJ6ZKuICovCiAgICBoYW5kbGVQcmV2aWV3KHJvdykgewogICAgICBwcmV2aWV3VGFibGUocm93LnRhYmxlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucHJldmlldy5kYXRhID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnByZXZpZXcub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy5wcmV2aWV3LmFjdGl2ZU5hbWUgPSAiZG8ucHkiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6auY5Lqu5pi+56S6ICovCiAgICBoaWdobGlnaHRlZENvZGUoY29kZSwga2V5KSB7CiAgICAgIGNvbnN0IHZtTmFtZSA9IGtleS5zdWJzdHJpbmcoa2V5Lmxhc3RJbmRleE9mKCIvIikgKyAxLCBrZXkuaW5kZXhPZigiLmppbmphMiIpKTsKICAgICAgdmFyIGxhbmd1YWdlID0gdm1OYW1lLnN1YnN0cmluZyh2bU5hbWUuaW5kZXhPZigiLiIpICsgMSwgdm1OYW1lLmxlbmd0aCk7CiAgICAgIGNvbnN0IHJlc3VsdCA9IGhsanMuaGlnaGxpZ2h0KGxhbmd1YWdlLCBjb2RlIHx8ICIiLCB0cnVlKTsKICAgICAgcmV0dXJuIHJlc3VsdC52YWx1ZSB8fCAnJm5ic3A7JzsKICAgIH0sCiAgICAvKiog5aSN5Yi25Luj56CB5oiQ5YqfICovCiAgICBjbGlwYm9hcmRTdWNjZXNzKCkgewogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlpI3liLbmiJDlip8iKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS50YWJsZUlkKTsKICAgICAgdGhpcy50YWJsZU5hbWVzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udGFibGVOYW1lKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFZGl0VGFibGUocm93KSB7CiAgICAgIGNvbnN0IHRhYmxlSWQgPSByb3cudGFibGVJZCB8fCB0aGlzLmlkc1swXTsKICAgICAgY29uc3QgdGFibGVOYW1lID0gcm93LnRhYmxlTmFtZSB8fCB0aGlzLnRhYmxlTmFtZXNbMF07CiAgICAgIGNvbnN0IHBhcmFtcyA9IHsgcGFnZU51bTogdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtIH07CiAgICAgIHRoaXMuJHRhYi5vcGVuUGFnZSgi5L+u5pS5WyIgKyB0YWJsZU5hbWUgKyAiXeeUn+aIkOmFjee9riIsICcvdG9vbC9nZW4tZWRpdC9pbmRleC8nICsgdGFibGVJZCwgcGFyYW1zKTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IHRhYmxlSWRzID0gcm93LnRhYmxlSWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOihqOe8luWPt+S4uiInICsgdGFibGVJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbFRhYmxlKHRhYmxlSWRzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"表名称\" prop=\"tableName\">\n        <el-input\n          v-model=\"queryParams.tableName\"\n          placeholder=\"请输入表名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\n        <el-input\n          v-model=\"queryParams.tableComment\"\n          placeholder=\"请输入表描述\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleGenTable\"\n          v-hasPermi=\"['tool:gen:code']\"\n        >生成</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"openCreateTable\"\n          v-hasRole=\"['admin']\"\n        >创建</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload\"\n          size=\"mini\"\n          @click=\"openImportTable\"\n          v-hasPermi=\"['tool:gen:import']\"\n        >导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleEditTable\"\n          v-hasPermi=\"['tool:gen:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['tool:gen:remove']\"\n        >删除</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" align=\"center\" width=\"55\"></el-table-column>\n      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"表名称\"\n        align=\"center\"\n        prop=\"tableName\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column\n        label=\"表描述\"\n        align=\"center\"\n        prop=\"tableComment\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column\n        label=\"实体\"\n        align=\"center\"\n        prop=\"className\"\n        :show-overflow-tooltip=\"true\"\n        width=\"120\"\n      />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-view\"\n            @click=\"handlePreview(scope.row)\"\n            v-hasPermi=\"['tool:gen:preview']\"\n          >预览</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEditTable(scope.row)\"\n            v-hasPermi=\"['tool:gen:edit']\"\n          >编辑</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['tool:gen:remove']\"\n          >删除</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-refresh\"\n            @click=\"handleSynchDb(scope.row)\"\n            v-hasPermi=\"['tool:gen:edit']\"\n          >同步</el-button>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            icon=\"el-icon-download\"\n            @click=\"handleGenTable(scope.row)\"\n            v-hasPermi=\"['tool:gen:code']\"\n          >生成代码</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n    <!-- 预览界面 -->\n    <el-dialog :title=\"preview.title\" :visible.sync=\"preview.open\" width=\"80%\" top=\"5vh\" append-to-body class=\"scrollbar\">\n      <el-tabs v-model=\"preview.activeName\">\n        <el-tab-pane\n          v-for=\"(value, key) in preview.data\"\n          :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\n          :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\n          :key=\"key\"\n        >\n          <el-link :underline=\"false\" icon=\"el-icon-document-copy\" v-clipboard:copy=\"value\" v-clipboard:success=\"clipboardSuccess\" style=\"float:right\">复制</el-link>\n          <pre><code class=\"hljs\" v-html=\"highlightedCode(value, key)\"></code></pre>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n    <import-table ref=\"import\" @ok=\"handleQuery\" />\n    <create-table ref=\"create\" @ok=\"handleQuery\" />\n  </div>\n</template>\n\n<script>\nimport { listTable, previewTable, delTable, genCode, synchDb } from \"@/api/tool/gen\";\nimport importTable from \"./importTable\";\nimport createTable from \"./createTable\";\nimport hljs from \"highlight.js/lib/highlight\";\nimport \"highlight.js/styles/github-gist.css\";\nhljs.registerLanguage(\"py\", require(\"highlight.js/lib/languages/python\"));\nhljs.registerLanguage(\"html\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"vue\", require(\"highlight.js/lib/languages/xml\"));\nhljs.registerLanguage(\"javascript\", require(\"highlight.js/lib/languages/javascript\"));\nhljs.registerLanguage(\"sql\", require(\"highlight.js/lib/languages/sql\"));\n\nexport default {\n  name: \"Gen\",\n  components: { importTable, createTable },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 唯一标识符\n      uniqueId: \"\",\n      // 选中数组\n      ids: [],\n      // 选中表数组\n      tableNames: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表数据\n      tableList: [],\n      // 日期范围\n      dateRange: \"\",\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        tableName: undefined,\n        tableComment: undefined\n      },\n      // 预览参数\n      preview: {\n        open: false,\n        title: \"代码预览\",\n        data: {},\n        activeName: \"do.py\"\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  activated() {\n    const time = this.$route.query.t;\n    if (time != null && time != this.uniqueId) {\n      this.uniqueId = time;\n      this.queryParams.pageNum = Number(this.$route.query.pageNum);\n      this.getList();\n    }\n  },\n  methods: {\n    /** 查询表集合 */\n    getList() {\n      this.loading = true;\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.tableList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 生成代码操作 */\n    handleGenTable(row) {\n      const tableNames = row.tableName || this.tableNames;\n      if (tableNames == \"\") {\n        this.$modal.msgError(\"请选择要生成的数据\");\n        return;\n      }\n      if(row.genType === \"1\") {\n        genCode(row.tableName).then(response => {\n          this.$modal.msgSuccess(\"成功生成到自定义路径：\" + row.genPath);\n        });\n      } else {\n        this.$download.zip(\"/tool/gen/batchGenCode?tables=\" + tableNames, \"vfadmin.zip\");\n      }\n    },\n    /** 同步数据库操作 */\n    handleSynchDb(row) {\n      const tableName = row.tableName;\n      this.$modal.confirm('确认要强制同步\"' + tableName + '\"表结构吗？').then(function() {\n        return synchDb(tableName);\n      }).then(() => {\n        this.$modal.msgSuccess(\"同步成功\");\n      }).catch(() => {});\n    },\n    /** 打开导入表弹窗 */\n    openImportTable() {\n      this.$refs.import.show();\n    },\n    /** 打开创建表弹窗 */\n    openCreateTable() {\n      this.$refs.create.show();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 预览按钮 */\n    handlePreview(row) {\n      previewTable(row.tableId).then(response => {\n        this.preview.data = response.data;\n        this.preview.open = true;\n        this.preview.activeName = \"do.py\";\n      });\n    },\n    /** 高亮显示 */\n    highlightedCode(code, key) {\n      const vmName = key.substring(key.lastIndexOf(\"/\") + 1, key.indexOf(\".jinja2\"));\n      var language = vmName.substring(vmName.indexOf(\".\") + 1, vmName.length);\n      const result = hljs.highlight(language, code || \"\", true);\n      return result.value || '&nbsp;';\n    },\n    /** 复制代码成功 */\n    clipboardSuccess() {\n      this.$modal.msgSuccess(\"复制成功\");\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.tableId);\n      this.tableNames = selection.map(item => item.tableName);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    /** 修改按钮操作 */\n    handleEditTable(row) {\n      const tableId = row.tableId || this.ids[0];\n      const tableName = row.tableName || this.tableNames[0];\n      const params = { pageNum: this.queryParams.pageNum };\n      this.$tab.openPage(\"修改[\" + tableName + \"]生成配置\", '/tool/gen-edit/index/' + tableId, params);\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const tableIds = row.tableId || this.ids;\n      this.$modal.confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项？').then(function() {\n        return delTable(tableIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>\n"]}]}