{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\role.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\role.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZFJvbGUgPSBhZGRSb2xlOwpleHBvcnRzLmFsbG9jYXRlZFVzZXJMaXN0ID0gYWxsb2NhdGVkVXNlckxpc3Q7CmV4cG9ydHMuYXV0aFVzZXJDYW5jZWwgPSBhdXRoVXNlckNhbmNlbDsKZXhwb3J0cy5hdXRoVXNlckNhbmNlbEFsbCA9IGF1dGhVc2VyQ2FuY2VsQWxsOwpleHBvcnRzLmF1dGhVc2VyU2VsZWN0QWxsID0gYXV0aFVzZXJTZWxlY3RBbGw7CmV4cG9ydHMuY2hhbmdlUm9sZVN0YXR1cyA9IGNoYW5nZVJvbGVTdGF0dXM7CmV4cG9ydHMuZGF0YVNjb3BlID0gZGF0YVNjb3BlOwpleHBvcnRzLmRlbFJvbGUgPSBkZWxSb2xlOwpleHBvcnRzLmRlcHRUcmVlU2VsZWN0ID0gZGVwdFRyZWVTZWxlY3Q7CmV4cG9ydHMuZ2V0Um9sZSA9IGdldFJvbGU7CmV4cG9ydHMubGlzdFJvbGUgPSBsaXN0Um9sZTsKZXhwb3J0cy51bmFsbG9jYXRlZFVzZXJMaXN0ID0gdW5hbGxvY2F0ZWRVc2VyTGlzdDsKZXhwb3J0cy51cGRhdGVSb2xlID0gdXBkYXRlUm9sZTsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouinkuiJsuWIl+ihqApmdW5jdGlvbiBsaXN0Um9sZShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6KeS6Imy6K+m57uGCmZ1bmN0aW9uIGdldFJvbGUocm9sZUlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvJyArIHJvbGVJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe6KeS6ImyCmZ1bmN0aW9uIGFkZFJvbGUoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnop5LoibIKZnVuY3Rpb24gdXBkYXRlUm9sZShkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g6KeS6Imy5pWw5o2u5p2D6ZmQCmZ1bmN0aW9uIGRhdGFTY29wZShkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvZGF0YVNjb3BlJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOinkuiJsueKtuaAgeS/ruaUuQpmdW5jdGlvbiBjaGFuZ2VSb2xlU3RhdHVzKHJvbGVJZCwgc3RhdHVzKSB7CiAgdmFyIGRhdGEgPSB7CiAgICByb2xlSWQ6IHJvbGVJZCwKICAgIHN0YXR1czogc3RhdHVzCiAgfTsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vcm9sZS9jaGFuZ2VTdGF0dXMnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6KeS6ImyCmZ1bmN0aW9uIGRlbFJvbGUocm9sZUlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvJyArIHJvbGVJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5p+l6K+i6KeS6Imy5bey5o6I5p2D55So5oi35YiX6KGoCmZ1bmN0aW9uIGFsbG9jYXRlZFVzZXJMaXN0KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvYXV0aFVzZXIvYWxsb2NhdGVkTGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lop5LoibLmnKrmjojmnYPnlKjmiLfliJfooagKZnVuY3Rpb24gdW5hbGxvY2F0ZWRVc2VyTGlzdChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlL2F1dGhVc2VyL3VuYWxsb2NhdGVkTGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDlj5bmtojnlKjmiLfmjojmnYPop5LoibIKZnVuY3Rpb24gYXV0aFVzZXJDYW5jZWwoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlL2F1dGhVc2VyL2NhbmNlbCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDmibnph4/lj5bmtojnlKjmiLfmjojmnYPop5LoibIKZnVuY3Rpb24gYXV0aFVzZXJDYW5jZWxBbGwoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9yb2xlL2F1dGhVc2VyL2NhbmNlbEFsbCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgcGFyYW1zOiBkYXRhCiAgfSk7Cn0KCi8vIOaOiOadg+eUqOaIt+mAieaLqQpmdW5jdGlvbiBhdXRoVXNlclNlbGVjdEFsbChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvYXV0aFVzZXIvc2VsZWN0QWxsJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBwYXJhbXM6IGRhdGEKICB9KTsKfQoKLy8g5qC55o2u6KeS6ImySUTmn6Xor6Lpg6jpl6jmoJHnu5PmnoQKZnVuY3Rpb24gZGVwdFRyZWVTZWxlY3Qocm9sZUlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3JvbGUvZGVwdFRyZWUvJyArIHJvbGVJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listRole", "query", "request", "url", "method", "params", "getRole", "roleId", "addRole", "data", "updateRole", "dataScope", "changeRoleStatus", "status", "delRole", "allocatedUserList", "unallocatedUserList", "authUserCancel", "authUserCancelAll", "authUserSelectAll", "deptTreeSelect"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/system/role.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询角色列表\nexport function listRole(query) {\n  return request({\n    url: '/system/role/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询角色详细\nexport function getRole(roleId) {\n  return request({\n    url: '/system/role/' + roleId,\n    method: 'get'\n  })\n}\n\n// 新增角色\nexport function addRole(data) {\n  return request({\n    url: '/system/role',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改角色\nexport function updateRole(data) {\n  return request({\n    url: '/system/role',\n    method: 'put',\n    data: data\n  })\n}\n\n// 角色数据权限\nexport function dataScope(data) {\n  return request({\n    url: '/system/role/dataScope',\n    method: 'put',\n    data: data\n  })\n}\n\n// 角色状态修改\nexport function changeRoleStatus(roleId, status) {\n  const data = {\n    roleId,\n    status\n  }\n  return request({\n    url: '/system/role/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除角色\nexport function delRole(roleId) {\n  return request({\n    url: '/system/role/' + roleId,\n    method: 'delete'\n  })\n}\n\n// 查询角色已授权用户列表\nexport function allocatedUserList(query) {\n  return request({\n    url: '/system/role/authUser/allocatedList',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询角色未授权用户列表\nexport function unallocatedUserList(query) {\n  return request({\n    url: '/system/role/authUser/unallocatedList',\n    method: 'get',\n    params: query\n  })\n}\n\n// 取消用户授权角色\nexport function authUserCancel(data) {\n  return request({\n    url: '/system/role/authUser/cancel',\n    method: 'put',\n    data: data\n  })\n}\n\n// 批量取消用户授权角色\nexport function authUserCancelAll(data) {\n  return request({\n    url: '/system/role/authUser/cancelAll',\n    method: 'put',\n    params: data\n  })\n}\n\n// 授权用户选择\nexport function authUserSelectAll(data) {\n  return request({\n    url: '/system/role/authUser/selectAll',\n    method: 'put',\n    params: data\n  })\n}\n\n// 根据角色ID查询部门树结构\nexport function deptTreeSelect(roleId) {\n  return request({\n    url: '/system/role/deptTree/' + roleId,\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,gBAAgBA,CAACL,MAAM,EAAEM,MAAM,EAAE;EAC/C,IAAMJ,IAAI,GAAG;IACXF,MAAM,EAANA,MAAM;IACNM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,iBAAiBA,CAACd,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,mBAAmBA,CAACf,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,cAAcA,CAACR,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,iBAAiBA,CAACT,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,iBAAiBA,CAACV,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,cAAcA,CAACb,MAAM,EAAE;EACrC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}