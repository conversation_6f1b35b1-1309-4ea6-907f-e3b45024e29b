{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\index_v1.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\index_v1.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBQYW5lbEdyb3VwIGZyb20gJy4vZGFzaGJvYXJkL1BhbmVsR3JvdXAnCmltcG9ydCBMaW5lQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvTGluZUNoYXJ0JwppbXBvcnQgUmFkZGFyQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvUmFkZGFyQ2hhcnQnCmltcG9ydCBQaWVDaGFydCBmcm9tICcuL2Rhc2hib2FyZC9QaWVDaGFydCcKaW1wb3J0IEJhckNoYXJ0IGZyb20gJy4vZGFzaGJvYXJkL0JhckNoYXJ0JwoKY29uc3QgbGluZUNoYXJ0RGF0YSA9IHsKICBuZXdWaXNpdGlzOiB7CiAgICBleHBlY3RlZERhdGE6IFsxMDAsIDEyMCwgMTYxLCAxMzQsIDEwNSwgMTYwLCAxNjVdLAogICAgYWN0dWFsRGF0YTogWzEyMCwgODIsIDkxLCAxNTQsIDE2MiwgMTQwLCAxNDVdCiAgfSwKICBtZXNzYWdlczogewogICAgZXhwZWN0ZWREYXRhOiBbMjAwLCAxOTIsIDEyMCwgMTQ0LCAxNjAsIDEzMCwgMTQwXSwKICAgIGFjdHVhbERhdGE6IFsxODAsIDE2MCwgMTUxLCAxMDYsIDE0NSwgMTUwLCAxMzBdCiAgfSwKICBwdXJjaGFzZXM6IHsKICAgIGV4cGVjdGVkRGF0YTogWzgwLCAxMDAsIDEyMSwgMTA0LCAxMDUsIDkwLCAxMDBdLAogICAgYWN0dWFsRGF0YTogWzEyMCwgOTAsIDEwMCwgMTM4LCAxNDIsIDEzMCwgMTMwXQogIH0sCiAgc2hvcHBpbmdzOiB7CiAgICBleHBlY3RlZERhdGE6IFsxMzAsIDE0MCwgMTQxLCAxNDIsIDE0NSwgMTUwLCAxNjBdLAogICAgYWN0dWFsRGF0YTogWzEyMCwgODIsIDkxLCAxNTQsIDE2MiwgMTQwLCAxMzBdCiAgfQp9CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0luZGV4JywKICBjb21wb25lbnRzOiB7CiAgICBQYW5lbEdyb3VwLAogICAgTGluZUNoYXJ0LAogICAgUmFkZGFyQ2hhcnQsCiAgICBQaWVDaGFydCwKICAgIEJhckNoYXJ0CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGluZUNoYXJ0RGF0YTogbGluZUNoYXJ0RGF0YS5uZXdWaXNpdGlzCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZXRMaW5lQ2hhcnREYXRhKHR5cGUpIHsKICAgICAgdGhpcy5saW5lQ2hhcnREYXRhID0gbGluZUNoYXJ0RGF0YVt0eXBlXQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index_v1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index_v1.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n\n    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />\n\n    <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n      <line-chart :chart-data=\"lineChartData\" />\n    </el-row>\n\n    <el-row :gutter=\"32\">\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <raddar-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <pie-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <bar-chart />\n        </div>\n      </el-col>\n    </el-row>\n\n    \n  </div>\n</template>\n\n<script>\nimport PanelGroup from './dashboard/PanelGroup'\nimport LineChart from './dashboard/LineChart'\nimport RaddarChart from './dashboard/Raddar<PERSON>hart'\nimport PieChart from './dashboard/PieChart'\nimport BarChart from './dashboard/BarChart'\n\nconst lineChartData = {\n  newVisitis: {\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\n    actualData: [120, 82, 91, 154, 162, 140, 145]\n  },\n  messages: {\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\n    actualData: [180, 160, 151, 106, 145, 150, 130]\n  },\n  purchases: {\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\n    actualData: [120, 90, 100, 138, 142, 130, 130]\n  },\n  shoppings: {\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\n    actualData: [120, 82, 91, 154, 162, 140, 130]\n  }\n}\n\nexport default {\n  name: 'Index',\n  components: {\n    PanelGroup,\n    LineChart,\n    RaddarChart,\n    PieChart,\n    BarChart\n  },\n  data() {\n    return {\n      lineChartData: lineChartData.newVisitis\n    }\n  },\n  methods: {\n    handleSetLineChartData(type) {\n      this.lineChartData = lineChartData[type]\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-editor-container {\n  padding: 32px;\n  background-color: rgb(240, 242, 245);\n  position: relative;\n\n  .chart-wrapper {\n    background: #fff;\n    padding: 16px 16px 0;\n    margin-bottom: 32px;\n  }\n}\n\n@media (max-width:1024px) {\n  .chart-wrapper {\n    padding: 8px;\n  }\n}\n</style>\n"]}]}