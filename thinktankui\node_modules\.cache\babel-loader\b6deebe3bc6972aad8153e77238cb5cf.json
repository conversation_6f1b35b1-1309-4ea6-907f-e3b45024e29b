{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\info-summary\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\info-summary\\index.vue", "mtime": 1748095831240}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "computed", "paginatedList", "start", "currentPage", "pageSize", "infoList", "slice", "Math", "min", "length", "data", "originalTopNav", "undefined", "searchKeyword", "positiveSentimentDialogVisible", "selectedSentiment", "editingItem", "activeMenu", "activeTab", "platformTypes", "sentimentTypes", "infoAttributes", "totalItems", "createPlanDialogVisible", "planActiveTab", "themeInputVisible", "themeInputValue", "planForm", "scope", "monitorObject", "location", "themes", "industry", "timeRange", "channels", "advancedPlanForm", "industryDialogVisible", "selectedIndustry", "industryTreeProps", "label", "children", "sendAlertDialogVisible", "addToAlertMaterialDialogVisible", "alertForm", "title", "selectedReceivers", "receivers", "type", "persons", "industryTreeData", "id", "selected", "content", "source", "time", "sentiment", "views", "comments", "images", "mounted", "$store", "state", "settings", "topNav", "dispatch", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleMenuClick", "menuName", "console", "log", "getSentimentIcon", "icons", "positive", "neutral", "negative", "getSentimentText", "texts", "handleCommand", "command", "handleSearchKeyword", "trim", "$router", "push", "path", "query", "q", "$message", "warning", "handleSizeChange", "size", "handleCurrentChange", "page", "showCreatePlanDialog", "handleRemoveTheme", "tag", "splice", "indexOf", "showThemeInput", "_this", "$nextTick", "_", "$refs", "themeInput", "input", "focus", "handleAddTheme", "inputValue", "includes", "handleMonitorObjectCommand", "showLocationMap", "info", "showIndustrySelect", "handleIndustryNodeClick", "confirmIndustrySelect", "savePlan", "formData", "showSendAlertDialog", "cancelSendAlert", "handleSentimentClick", "item", "handlePositiveDialogConfirm", "confirmSend<PERSON><PERSON><PERSON>", "showAddToAlertMaterialDialog", "cancelAddToAlertMaterial", "selectedMaterialLibrary", "confirmAddToAlertMaterial"], "sources": ["src/views/info-summary/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <el-button type=\"primary\" icon=\"el-icon-message\" @click=\"showSendAlertDialog\">发送预警</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreatePlanDialog\">新建方案</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-folder-add\" @click=\"showAddToAlertMaterialDialog\">加入至报告素材</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-share\" @click=\"showInfoGraphDialog\">信息图谱</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-document-checked\" @click=\"showOriginalProofreadingDialog\">原稿校对</el-button>\n    </div>\n\n    <div class=\"info-summary-container\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"sidebar-header\">\n          <span class=\"sidebar-title\">方太</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        <div class=\"sidebar-menu\">\n          <el-menu\n            :default-active=\"activeMenu\"\n            class=\"el-menu-vertical\"\n          >\n            <el-menu-item index=\"1\" @click=\"handleMenuClick('总览')\">\n              <i class=\"el-icon-s-home\"></i>\n              <span slot=\"title\">总览</span>\n            </el-menu-item>\n            <el-menu-item index=\"2\" @click=\"handleMenuClick('品牌')\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span slot=\"title\">品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"3\" @click=\"handleMenuClick('人物')\">\n              <i class=\"el-icon-user\"></i>\n              <span slot=\"title\">人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"4\" @click=\"handleMenuClick('机构')\">\n              <i class=\"el-icon-office-building\"></i>\n              <span slot=\"title\">机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"5\" @click=\"handleMenuClick('产品')\">\n              <i class=\"el-icon-shopping-bag-1\"></i>\n              <span slot=\"title\">产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"6\" @click=\"handleMenuClick('事件')\">\n              <i class=\"el-icon-bell\"></i>\n              <span slot=\"title\">事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"7\" @click=\"handleMenuClick('话题')\">\n              <i class=\"el-icon-chat-dot-square\"></i>\n              <span slot=\"title\">话题(0)</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n\n      <!-- 右侧内容区域 -->\n      <div class=\"right-content\">\n        <!-- 标题和操作区域 -->\n        <div class=\"content-header\">\n          <div class=\"entity-title\">\n            <span class=\"entity-name\">方太</span>\n            <i class=\"el-icon-arrow-right\"></i>\n          </div>\n          <div class=\"view-actions\">\n            <el-button-group>\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-menu\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\"></el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 标签页 -->\n        <div class=\"tabs-container\">\n          <div class=\"filter-tabs\">\n            <el-radio-group v-model=\"activeTab\" size=\"small\">\n              <el-radio-button label=\"today\">今天</el-radio-button>\n              <el-radio-button label=\"yesterday\">昨天</el-radio-button>\n              <el-radio-button label=\"before_yesterday\">前天</el-radio-button>\n              <el-radio-button label=\"earlier\">更早</el-radio-button>\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\n            </el-radio-group>\n          </div>\n        </div>\n\n        <!-- 信息类型筛选 -->\n        <div class=\"filter-section\">\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">平台分类:</span>\n            <el-checkbox-group v-model=\"platformTypes\" size=\"small\">\n              <el-checkbox label=\"news\">新闻 (46/217)</el-checkbox>\n              <el-checkbox label=\"weibo\">微博 (5/34)</el-checkbox>\n              <el-checkbox label=\"wechat\">微信 (14/54)</el-checkbox>\n              <el-checkbox label=\"video\">视频 (2/78)</el-checkbox>\n              <el-checkbox label=\"app\">APP (1/29)</el-checkbox>\n              <el-checkbox label=\"forum\">论坛 (0/9)</el-checkbox>\n              <el-checkbox label=\"ecommerce\">电商 (44/446)</el-checkbox>\n              <el-checkbox label=\"qa\">问答 (6/21)</el-checkbox>\n              <el-checkbox label=\"other\">其他 (1/2)</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">情感倾向:</span>\n            <el-checkbox-group v-model=\"sentimentTypes\" size=\"small\">\n              <el-checkbox label=\"positive\">正面 (46/217)</el-checkbox>\n              <el-checkbox label=\"neutral\">中性 (7/8)</el-checkbox>\n              <el-checkbox label=\"negative\">负面 (3/57)</el-checkbox>\n              <el-checkbox label=\"other\">其他 (1/3)</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">信息属性:</span>\n            <el-checkbox-group v-model=\"infoAttributes\" size=\"small\">\n              <el-checkbox label=\"official\">官方发布</el-checkbox>\n              <el-checkbox label=\"media\">媒体报道</el-checkbox>\n              <el-checkbox label=\"user\">用户评价</el-checkbox>\n              <el-checkbox label=\"competitor\">竞品信息</el-checkbox>\n              <el-checkbox label=\"industry\">行业动态</el-checkbox>\n              <el-checkbox label=\"policy\">政策法规</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-actions\">\n            <el-button size=\"small\" type=\"primary\">筛选</el-button>\n            <el-button size=\"small\">重置</el-button>\n          </div>\n        </div>\n\n        <!-- 操作栏 -->\n        <div class=\"action-bar\">\n          <div class=\"left-actions\">\n            <el-button size=\"small\" type=\"primary\">全选</el-button>\n            <el-button size=\"small\">导出</el-button>\n          </div>\n          <div class=\"right-actions\">\n            <el-input\n              placeholder=\"搜索关键词\"\n              prefix-icon=\"el-icon-search\"\n              v-model=\"searchKeyword\"\n              size=\"small\"\n              clearable\n              class=\"search-input\"\n              @keyup.enter=\"handleSearchKeyword\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearchKeyword\"></el-button>\n            </el-input>\n            <el-dropdown size=\"small\" split-button type=\"primary\" @command=\"handleCommand\">\n              排序\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"time_desc\">时间降序</el-dropdown-item>\n                <el-dropdown-item command=\"time_asc\">时间升序</el-dropdown-item>\n                <el-dropdown-item command=\"relevance\">相关性</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n        </div>\n\n        <!-- 信息列表 -->\n        <div class=\"info-list\">\n          <div v-for=\"(item, index) in paginatedList\" :key=\"index\" class=\"info-item\">\n            <el-checkbox v-model=\"item.selected\" class=\"item-checkbox\"></el-checkbox>\n            <div class=\"info-content\">\n              <div class=\"info-header\">\n                <div class=\"info-title\" v-html=\"item.title\"></div>\n                <div class=\"info-actions\">\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\n                  <el-button type=\"text\" icon=\"el-icon-share\"></el-button>\n                  <el-button type=\"text\" icon=\"el-icon-more\"></el-button>\n                </div>\n              </div>\n              <div class=\"info-summary\" v-html=\"item.content\"></div>\n              <div class=\"info-footer\">\n                <span class=\"info-source\">{{ item.source }}</span>\n                <span class=\"info-time\">{{ item.time }}</span>\n                <span class=\"info-sentiment\" :class=\"'sentiment-' + item.sentiment\">\n                  <el-button\n                    :type=\"item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'\"\n                    size=\"mini\"\n                    @click=\"handleSentimentClick(item)\"\n                  >\n                    <i :class=\"getSentimentIcon(item.sentiment)\"></i>\n                    {{ getSentimentText(item.sentiment) }}\n                  </el-button>\n                </span>\n                <span class=\"info-views\">\n                  <i class=\"el-icon-view\"></i> {{ item.views }}\n                </span>\n                <span class=\"info-comments\">\n                  <i class=\"el-icon-chat-line-square\"></i> {{ item.comments }}\n                </span>\n                <span class=\"info-index\">{{ (currentPage - 1) * pageSize + index + 1 }}</span>\n              </div>\n              <div class=\"info-images\" v-if=\"item.images && item.images.length > 0\">\n                <img v-for=\"(img, imgIndex) in item.images\" :key=\"imgIndex\" :src=\"img\" class=\"info-image\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[10, 20, 30, 50]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalItems\"\n            background\n          ></el-pagination>\n        </div>\n      </div>\n    </div>\n\n    <!-- 新建方案对话框 -->\n    <el-dialog\n      title=\"新建方案\"\n      :visible.sync=\"createPlanDialogVisible\"\n      width=\"50%\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      append-to-body\n      custom-class=\"create-plan-dialog\"\n    >\n      <el-tabs v-model=\"planActiveTab\">\n        <el-tab-pane label=\"监测方式\" name=\"standard\">\n          <el-form :model=\"planForm\" label-width=\"70px\" size=\"small\">\n            <el-form-item label=\"方案名称\">\n              <el-input v-model=\"planForm.name\" placeholder=\"请输入方案名称\"></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"作用范围\">\n              <el-select v-model=\"planForm.scope\" placeholder=\"请选择\" style=\"width: 100%\">\n                <el-option label=\"全部\" value=\"all\"></el-option>\n                <el-option label=\"选项1\" value=\"option1\"></el-option>\n                <el-option label=\"选项2\" value=\"option2\"></el-option>\n              </el-select>\n            </el-form-item>\n\n            <el-form-item label=\"监测对象\">\n              <div class=\"monitor-object-select\">\n                <el-input v-model=\"planForm.monitorObject\" placeholder=\"请输入监测对象\"></el-input>\n                <el-dropdown trigger=\"click\" @command=\"handleMonitorObjectCommand\">\n                  <span class=\"el-dropdown-link\">\n                    <i class=\"el-icon-arrow-down el-icon--right\"></i>\n                  </span>\n                  <el-dropdown-menu slot=\"dropdown\">\n                    <el-dropdown-item command=\"option1\">选项1</el-dropdown-item>\n                    <el-dropdown-item command=\"option2\">选项2</el-dropdown-item>\n                    <el-dropdown-item command=\"option3\">选项3</el-dropdown-item>\n                  </el-dropdown-menu>\n                </el-dropdown>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"地域\">\n              <div class=\"location-select\">\n                <el-input v-model=\"planForm.location\" placeholder=\"请选择地域\" readonly></el-input>\n                <el-button type=\"text\" class=\"location-btn\" @click=\"showLocationMap\">\n                  <i class=\"el-icon-location\"></i>\n                </el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"主题\">\n              <div class=\"theme-row\">\n                <el-tag\n                  v-for=\"(tag, index) in planForm.themes\"\n                  :key=\"index\"\n                  closable\n                  @close=\"handleRemoveTheme(tag)\"\n                >\n                  {{ tag }}\n                </el-tag>\n                <el-input\n                  class=\"theme-input\"\n                  v-if=\"themeInputVisible\"\n                  v-model=\"themeInputValue\"\n                  ref=\"themeInput\"\n                  size=\"small\"\n                  @keyup.enter.native=\"handleAddTheme\"\n                  @blur=\"handleAddTheme\"\n                >\n                </el-input>\n                <el-button v-else class=\"theme-button\" size=\"small\" @click=\"showThemeInput\">+ 添加主题</el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"行业分类\">\n              <div class=\"industry-row\">\n                <el-tag\n                  v-if=\"planForm.industry\"\n                  closable\n                  @close=\"planForm.industry = ''\"\n                >\n                  {{ planForm.industry }}\n                </el-tag>\n                <el-button v-if=\"!planForm.industry\" class=\"industry-button\" size=\"small\" @click=\"showIndustrySelect\">+ 添加行业分类</el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"时间段\">\n              <el-date-picker\n                v-model=\"planForm.timeRange\"\n                type=\"daterange\"\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n\n            <el-form-item label=\"渠道\">\n              <el-checkbox-group v-model=\"planForm.channels\">\n                <div class=\"channels-row\">\n                  <el-checkbox label=\"news\">新闻</el-checkbox>\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\n                  <el-checkbox label=\"wechat\">微信</el-checkbox>\n                </div>\n                <div class=\"channels-row\">\n                  <el-checkbox label=\"video\">视频</el-checkbox>\n                  <el-checkbox label=\"app\">APP</el-checkbox>\n                  <el-checkbox label=\"forum\">论坛</el-checkbox>\n                </div>\n              </el-checkbox-group>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"高级方式\" name=\"advanced\">\n          <el-form :model=\"advancedPlanForm\" label-width=\"70px\" size=\"small\">\n            <!-- 高级模式的表单内容 -->\n            <el-form-item label=\"方案名称\">\n              <el-input v-model=\"advancedPlanForm.name\" placeholder=\"请输入方案名称\"></el-input>\n            </el-form-item>\n\n            <!-- 其他高级选项 -->\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"createPlanDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 行业分类弹窗 -->\n    <el-dialog\n      title=\"行业分类\"\n      :visible.sync=\"industryDialogVisible\"\n      width=\"40%\"\n      :close-on-click-modal=\"true\"\n      :close-on-press-escape=\"true\"\n      append-to-body\n      custom-class=\"industry-dialog\"\n    >\n      <div class=\"industry-dialog-content\">\n        <div class=\"industry-tree-container\">\n          <el-tree\n            :data=\"industryTreeData\"\n            :props=\"industryTreeProps\"\n            node-key=\"id\"\n            default-expand-all\n            highlight-current\n            @node-click=\"handleIndustryNodeClick\"\n          />\n        </div>\n        <div class=\"industry-selected-container\">\n          <div class=\"industry-selected-title\">\n            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"industryDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmIndustrySelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 发送预警弹窗 -->\n    <el-dialog\n      title=\"发送预警\"\n      :visible.sync=\"sendAlertDialogVisible\"\n      width=\"40%\"\n      :close-on-click-modal=\"true\"\n      :close-on-press-escape=\"true\"\n      append-to-body\n      custom-class=\"send-alert-dialog\"\n    >\n      <div class=\"send-alert-content\">\n        <el-form :model=\"alertForm\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"预警标题\">\n            <el-input v-model=\"alertForm.title\" placeholder=\"请输入预警标题\"></el-input>\n          </el-form-item>\n\n          <el-form-item label=\"接收人\">\n            <div class=\"receiver-list\">\n              <div class=\"receiver-item\" v-for=\"(receiver, index) in receivers\" :key=\"index\">\n                <div class=\"receiver-type\">{{ receiver.type }}</div>\n                <el-select\n                  v-model=\"alertForm.selectedReceivers[index]\"\n                  :placeholder=\"'请选择' + receiver.type\"\n                  class=\"receiver-select\"\n                >\n                  <el-option\n                    v-for=\"person in receiver.persons\"\n                    :key=\"person\"\n                    :label=\"person\"\n                    :value=\"person\"\n                  ></el-option>\n                </el-select>\n              </div>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelSendAlert\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmSendAlert\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 正面情感信息弹窗 -->\n    <el-dialog\n      title=\"情感属性纠错\"\n      :visible.sync=\"positiveSentimentDialogVisible\"\n      width=\"50%\"\n      append-to-body\n      custom-class=\"positive-sentiment-dialog\"\n    >\n      <el-radio-group v-model=\"selectedSentiment\" size=\"small\">\n        <el-radio-button label=\"positive\">正面</el-radio-button>\n        <el-radio-button label=\"neutral\">中性</el-radio-button>\n        <el-radio-button label=\"negative\">负面</el-radio-button>\n      </el-radio-group>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"positiveSentimentDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handlePositiveDialogConfirm\">确定</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 加入至报告素材对话框 -->\n    <el-dialog\n      title=\"加入至报告素材\"\n      :visible.sync=\"addToAlertMaterialDialogVisible\"\n      width=\"30%\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      append-to-body\n      custom-class=\"add-to-alert-material-dialog\"\n    >\n      <div class=\"add-to-alert-material-content\">\n        <el-form label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"选择素材库\">\n            <el-select v-model=\"selectedMaterialLibrary\" placeholder=\"请选择素材库\" style=\"width: 100%\">\n              <!-- 这里需要根据实际数据填充选项 -->\n              <el-option label=\"素材库1\" value=\"library1\"></el-option>\n              <el-option label=\"素材库2\" value=\"library2\"></el-option>\n            </el-select>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelAddToAlertMaterial\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddToAlertMaterial\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'InfoSummary',\n  computed: {\n    // 根据当前页码和每页显示数量计算当前页的数据\n    paginatedList() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      // 实际应用中，这里应该是从后端获取分页数据\n      // 这里为了演示，我们从本地数据中截取一部分\n      return this.infoList.slice(0, Math.min(this.infoList.length, this.pageSize));\n    }\n  },\n  data() {\n    return {\n      // 页面基础数据\n      originalTopNav: undefined, // 存储原始的topNav状态\n      searchKeyword: '',\n      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示\n      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值\n      editingItem: null, // 当前编辑的情感条目\n      activeMenu: '2', // 默认选中品牌\n      activeTab: 'today', // 默认选中今天\n      platformTypes: ['news', 'weibo', 'wechat', 'video', 'app'], // 默认选中的平台类型\n      sentimentTypes: ['positive', 'neutral'], // 默认选中的情感类型\n      infoAttributes: ['official', 'media'], // 默认选中的信息属性\n\n      // 分页相关数据\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 120, // 假设总共有120条数据\n\n      // 新建方案对话框相关数据\n      createPlanDialogVisible: false,\n      planActiveTab: 'standard',\n      themeInputVisible: false,\n      themeInputValue: '',\n      planForm: {\n        name: '',\n        scope: 'all',\n        monitorObject: '',\n        location: '',\n        themes: [],\n        industry: '',\n        timeRange: '',\n        channels: ['news', 'weibo', 'wechat']\n      },\n      advancedPlanForm: {\n        name: ''\n      },\n\n      // 行业分类弹窗相关数据\n      industryDialogVisible: false,\n      selectedIndustry: null,\n      industryTreeProps: {\n        label: 'label',\n        children: 'children'\n      },\n\n      // 发送预警弹窗相关数据\n      sendAlertDialogVisible: false,\n      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示\n      alertForm: {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      },\n      receivers: [\n        {\n          type: '总监',\n          persons: ['王总监', '李总监', '张总监']\n        },\n        {\n          type: '经理',\n          persons: ['王经理', '李经理', '张经理']\n        },\n        {\n          type: '主管',\n          persons: ['王主管', '李主管', '张主管']\n        },\n        {\n          type: '员工',\n          persons: ['王员工', '李员工', '张员工']\n        },\n        {\n          type: '外部人员',\n          persons: ['外部人员1', '外部人员2', '外部人员3']\n        },\n        {\n          type: '其他',\n          persons: ['其他人员1', '其他人员2', '其他人员3']\n        }\n      ],\n      industryTreeData: [\n        {\n          id: 1,\n          label: '制造',\n          children: []\n        },\n        {\n          id: 2,\n          label: '公共',\n          children: []\n        },\n        {\n          id: 3,\n          label: '教育',\n          children: []\n        },\n        {\n          id: 4,\n          label: '工业设备',\n          children: []\n        },\n        {\n          id: 5,\n          label: '环保设备',\n          children: []\n        },\n        {\n          id: 6,\n          label: '金融',\n          children: []\n        },\n        {\n          id: 7,\n          label: '商业',\n          children: []\n        },\n        {\n          id: 8,\n          label: '民用与商用',\n          children: []\n        },\n        {\n          id: 9,\n          label: '政府部门',\n          children: []\n        }\n      ],\n\n      // 信息列表数据\n      infoList: [\n        {\n          selected: false,\n          title: '新，<span class=\"highlight\">高价值联名</span>打造<span class=\"highlight\">方太</span>(Fotile)厨电新品牌',\n          content: '据<span class=\"highlight\">方太</span>(Fotile)官方消息，<span class=\"highlight\">方太</span>与设计师联名打造了一系列高端厨电产品，引领厨房新潮流...',\n          source: '新浪财经',\n          time: '2023-04-29 20:27:51',\n          sentiment: 'positive',\n          views: 6437,\n          comments: 89,\n          images: []\n        },\n        {\n          selected: false,\n          title: '招聘一位【<span class=\"highlight\">方太</span>集团(Fotile)-111 <span class=\"highlight\">方太</span>集团(Fotile)-400.6808.655，一键呼叫，服务响应一一】的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅',\n          content: '招聘一位【<span class=\"highlight\">方太</span>集团(Fotile)官方认证的<span class=\"highlight\">方太</span>集团(Fotile)-111<span class=\"highlight\">方太</span>集团(Fotile)400.6808.655，一键呼叫，服务响应一一】的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅...',\n          source: '头条新闻',\n          time: '2023-04-29 20:24:22',\n          sentiment: 'neutral',\n          views: 5321,\n          comments: 42,\n          images: ['/images/sample1.jpg', '/images/sample2.jpg']\n        },\n        {\n          selected: false,\n          title: '<span class=\"highlight\">方太</span>集团(Fotile)官方服务中心-<span class=\"highlight\">方太</span>集团(Fotile)-111 <span class=\"highlight\">方太</span>集团(Fotile) 400.6808.655，一键呼叫，服务响应一一的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅',\n          content: '<span class=\"highlight\">方太</span>集团(Fotile)官方服务中心-<span class=\"highlight\">方太</span>集团(Fotile)-111<span class=\"highlight\">方太</span>集团(Fotile)400.6808.655，<span class=\"highlight\">方太</span>集团(Fotile)维修部。可预约上门，一站式解决<span class=\"highlight\">方太</span>厨电故障问题...',\n          source: '今日头条',\n          time: '2023-04-29 20:14:03',\n          sentiment: 'neutral',\n          views: 3642,\n          comments: 33,\n          images: ['/images/sample3.jpg', '/images/sample4.jpg']\n        }\n      ]\n    };\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n\n    // 菜单点击事件\n    handleMenuClick(menuName) {\n      console.log('选择菜单:', menuName);\n    },\n\n    // 获取情感图标\n    getSentimentIcon(sentiment) {\n      const icons = {\n        positive: 'el-icon-sunny',\n        neutral: 'el-icon-partly-cloudy',\n        negative: 'el-icon-cloudy'\n      };\n      return icons[sentiment] || 'el-icon-question';\n    },\n\n    // 获取情感文本\n    getSentimentText(sentiment) {\n      const texts = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      };\n      return texts[sentiment] || '未知';\n    },\n\n    // 下拉菜单命令处理\n    handleCommand(command) {\n      console.log('执行命令:', command);\n    },\n\n    // 处理搜索关键词\n    handleSearchKeyword() {\n      if (this.searchKeyword.trim()) {\n        // 跳转到搜索结果页面，并传递搜索关键词\n        this.$router.push({\n          path: '/search-results',\n          query: {\n            q: this.searchKeyword.trim()\n          }\n        });\n      } else {\n        this.$message.warning('请输入搜索关键词');\n      }\n    },\n\n    // 分页相关方法\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1; // 切换每页显示数量时，重置为第一页\n      console.log('每页显示数量:', size);\n    },\n\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      console.log('当前页码:', page);\n    },\n\n    // 新建方案相关方法\n    showCreatePlanDialog() {\n      this.createPlanDialogVisible = true;\n    },\n\n    // 主题标签相关方法\n    handleRemoveTheme(tag) {\n      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);\n    },\n\n    showThemeInput() {\n      this.themeInputVisible = true;\n      this.$nextTick(_ => {\n        this.$refs.themeInput.$refs.input.focus();\n      });\n    },\n\n    handleAddTheme() {\n      let inputValue = this.themeInputValue;\n      if (inputValue) {\n        if (!this.planForm.themes.includes(inputValue)) {\n          this.planForm.themes.push(inputValue);\n        }\n      }\n      this.themeInputVisible = false;\n      this.themeInputValue = '';\n    },\n\n    // 监测对象下拉菜单命令处理\n    handleMonitorObjectCommand(command) {\n      this.planForm.monitorObject = command;\n    },\n\n    // 显示地域选择地图\n    showLocationMap() {\n      // 这里可以实现地图选择功能\n      this.$message.info('显示地域选择地图');\n    },\n\n    // 显示行业分类选择\n    showIndustrySelect() {\n      this.industryDialogVisible = true;\n    },\n\n    // 处理行业分类树节点点击\n    handleIndustryNodeClick(data) {\n      this.selectedIndustry = data;\n    },\n\n    // 确认行业分类选择\n    confirmIndustrySelect() {\n      if (this.selectedIndustry) {\n        this.planForm.industry = this.selectedIndustry.label;\n      }\n      this.industryDialogVisible = false;\n    },\n\n    // 保存方案\n    savePlan() {\n      // 根据当前激活的标签页选择不同的表单数据\n      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;\n\n      console.log('保存方案:', formData);\n      // 这里可以添加表单验证和提交到后端的逻辑\n\n      // 关闭对话框\n      this.createPlanDialogVisible = false;\n\n      // 重置表单\n      if (this.planActiveTab === 'standard') {\n        this.planForm = {\n          name: '',\n          scope: 'all',\n          monitorObject: '',\n          location: '',\n          themes: [],\n          industry: '',\n          timeRange: '',\n          channels: ['news', 'weibo', 'wechat']\n        };\n      } else {\n        this.advancedPlanForm = {\n          name: ''\n        };\n      }\n    },\n\n    // 显示发送预警弹窗\n    showSendAlertDialog() {\n      this.sendAlertDialogVisible = true;\n    },\n\n    // 取消发送预警\n    cancelSendAlert() {\n      this.sendAlertDialogVisible = false;\n      // 重置表单\n      this.alertForm = {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      };\n    },\n    handleSentimentClick(item) {\n      this.editingItem = item;\n      this.selectedSentiment = item.sentiment;\n      this.positiveSentimentDialogVisible = true;\n    },\n    handlePositiveDialogConfirm() {\n      if (this.editingItem) {\n        this.editingItem.sentiment = this.selectedSentiment;\n        // 在实际应用中，这里可能需要调用API将更改保存到后端\n        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);\n      }\n      this.positiveSentimentDialogVisible = false;\n    },\n\n    // 确认发送预警\n    confirmSendAlert() {\n      // 在这里处理发送预警的逻辑\n      console.log('发送预警:', this.alertForm);\n      this.sendAlertDialogVisible = false;\n      // 清空表单\n      this.alertForm = {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      };\n    },\n\n    // 加入至报告素材对话框相关方法\n    showAddToAlertMaterialDialog() {\n      this.addToAlertMaterialDialogVisible = true;\n    },\n    cancelAddToAlertMaterial() {\n      this.addToAlertMaterialDialogVisible = false;\n      this.selectedMaterialLibrary = ''; // 清空选中值\n    },\n    confirmAddToAlertMaterial() {\n      // 这里添加确认逻辑，例如提交选中的素材库\n      console.log('Selected Material Library:', this.selectedMaterialLibrary);\n      this.addToAlertMaterialDialogVisible = false;\n      this.selectedMaterialLibrary = ''; // 清空选中值\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\nel-dialog\ntitle=\"信息图谱\"\n:visible.sync=\"infoGraphDialogVisible\"\nwidth=\"50%\"\nappend-to-body\ncustom-class=\"info-graph-dialog\"\n>\n<div class=\"info-graph-content\">\n<!-- 根据提供的图示调整内容布局 -->\n<div class=\"graph-container\">\n<div class=\"graph-node\">东木头人</div>\n<div class=\"graph-node\">永兴队</div>\n<!-- 添加更多节点 -->\n</div>\n</div>\n<div slot=\"footer\" class=\"dialog-footer\">\n<el-button @click=\"infoGraphDialogVisible = false\">关闭</el-button>\n</div>\n</el-dialog>\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA4dA;EACAA,IAAA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,KAAA,SAAAC,WAAA,aAAAC,QAAA;MACA;MACA;MACA,YAAAC,QAAA,CAAAC,KAAA,IAAAC,IAAA,CAAAC,GAAA,MAAAH,QAAA,CAAAI,MAAA,OAAAL,QAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA,EAAAC,SAAA;MAAA;MACAC,aAAA;MACAC,8BAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,cAAA;MAAA;MACAC,cAAA;MAAA;;MAEA;MACAlB,WAAA;MACAC,QAAA;MACAkB,UAAA;MAAA;;MAEA;MACAC,uBAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,QAAA;QACA5B,IAAA;QACA6B,KAAA;QACAC,aAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACAC,gBAAA;QACApC,IAAA;MACA;MAEA;MACAqC,qBAAA;MACAC,gBAAA;MACAC,iBAAA;QACAC,KAAA;QACAC,QAAA;MACA;MAEA;MACAC,sBAAA;MACAC,+BAAA;MAAA;MACAC,SAAA;QACAC,KAAA;QACAC,iBAAA;MACA;MACAC,SAAA,GACA;QACAC,IAAA;QACAC,OAAA;MACA,GACA;QACAD,IAAA;QACAC,OAAA;MACA,GACA;QACAD,IAAA;QACAC,OAAA;MACA,GACA;QACAD,IAAA;QACAC,OAAA;MACA,GACA;QACAD,IAAA;QACAC,OAAA;MACA,GACA;QACAD,IAAA;QACAC,OAAA;MACA,EACA;MACAC,gBAAA,GACA;QACAC,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,GACA;QACAU,EAAA;QACAX,KAAA;QACAC,QAAA;MACA,EACA;MAEA;MACAnC,QAAA,GACA;QACA8C,QAAA;QACAP,KAAA;QACAQ,OAAA;QACAC,MAAA;QACAC,IAAA;QACAC,SAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;MACA,GACA;QACAP,QAAA;QACAP,KAAA;QACAQ,OAAA;QACAC,MAAA;QACAC,IAAA;QACAC,SAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;MACA,GACA;QACAP,QAAA;QACAP,KAAA;QACAQ,OAAA;QACAC,MAAA;QACAC,IAAA;QACAC,SAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAhD,cAAA,QAAAiD,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACAC,KAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAxD,cAAA,KAAAC,SAAA;MACA,KAAAgD,MAAA,CAAAI,QAAA;QACAC,GAAA;QACAC,KAAA,OAAAvD;MACA;IACA;EACA;EACAyD,OAAA;IAEA;IACAC,eAAA,WAAAA,gBAAAC,QAAA;MACAC,OAAA,CAAAC,GAAA,UAAAF,QAAA;IACA;IAEA;IACAG,gBAAA,WAAAA,iBAAAlB,SAAA;MACA,IAAAmB,KAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,OAAAH,KAAA,CAAAnB,SAAA;IACA;IAEA;IACAuB,gBAAA,WAAAA,iBAAAvB,SAAA;MACA,IAAAwB,KAAA;QACAJ,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,OAAAE,KAAA,CAAAxB,SAAA;IACA;IAEA;IACAyB,aAAA,WAAAA,cAAAC,OAAA;MACAV,OAAA,CAAAC,GAAA,UAAAS,OAAA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAA;MACA,SAAArE,aAAA,CAAAsE,IAAA;QACA;QACA,KAAAC,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YACAC,CAAA,OAAA3E,aAAA,CAAAsE,IAAA;UACA;QACA;MACA;QACA,KAAAM,QAAA,CAAAC,OAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAxF,QAAA,GAAAwF,IAAA;MACA,KAAAzF,WAAA;MACAoE,OAAA,CAAAC,GAAA,YAAAoB,IAAA;IACA;IAEAC,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAA3F,WAAA,GAAA2F,IAAA;MACAvB,OAAA,CAAAC,GAAA,UAAAsB,IAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAxE,uBAAA;IACA;IAEA;IACAyE,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAtE,QAAA,CAAAI,MAAA,CAAAmE,MAAA,MAAAvE,QAAA,CAAAI,MAAA,CAAAoE,OAAA,CAAAF,GAAA;IACA;IAEAG,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,KAAA5E,iBAAA;MACA,KAAA6E,SAAA,WAAAC,CAAA;QACAF,KAAA,CAAAG,KAAA,CAAAC,UAAA,CAAAD,KAAA,CAAAE,KAAA,CAAAC,KAAA;MACA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACA,IAAAC,UAAA,QAAAnF,eAAA;MACA,IAAAmF,UAAA;QACA,UAAAlF,QAAA,CAAAI,MAAA,CAAA+E,QAAA,CAAAD,UAAA;UACA,KAAAlF,QAAA,CAAAI,MAAA,CAAAsD,IAAA,CAAAwB,UAAA;QACA;MACA;MACA,KAAApF,iBAAA;MACA,KAAAC,eAAA;IACA;IAEA;IACAqF,0BAAA,WAAAA,2BAAA9B,OAAA;MACA,KAAAtD,QAAA,CAAAE,aAAA,GAAAoD,OAAA;IACA;IAEA;IACA+B,eAAA,WAAAA,gBAAA;MACA;MACA,KAAAvB,QAAA,CAAAwB,IAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAA9E,qBAAA;IACA;IAEA;IACA+E,uBAAA,WAAAA,wBAAAzG,IAAA;MACA,KAAA2B,gBAAA,GAAA3B,IAAA;IACA;IAEA;IACA0G,qBAAA,WAAAA,sBAAA;MACA,SAAA/E,gBAAA;QACA,KAAAV,QAAA,CAAAK,QAAA,QAAAK,gBAAA,CAAAE,KAAA;MACA;MACA,KAAAH,qBAAA;IACA;IAEA;IACAiF,QAAA,WAAAA,SAAA;MACA;MACA,IAAAC,QAAA,QAAA9F,aAAA,uBAAAG,QAAA,QAAAQ,gBAAA;MAEAoC,OAAA,CAAAC,GAAA,UAAA8C,QAAA;MACA;;MAEA;MACA,KAAA/F,uBAAA;;MAEA;MACA,SAAAC,aAAA;QACA,KAAAG,QAAA;UACA5B,IAAA;UACA6B,KAAA;UACAC,aAAA;UACAC,QAAA;UACAC,MAAA;UACAC,QAAA;UACAC,SAAA;UACAC,QAAA;QACA;MACA;QACA,KAAAC,gBAAA;UACApC,IAAA;QACA;MACA;IACA;IAEA;IACAwH,mBAAA,WAAAA,oBAAA;MACA,KAAA9E,sBAAA;IACA;IAEA;IACA+E,eAAA,WAAAA,gBAAA;MACA,KAAA/E,sBAAA;MACA;MACA,KAAAE,SAAA;QACAC,KAAA;QACAC,iBAAA;MACA;IACA;IACA4E,oBAAA,WAAAA,qBAAAC,IAAA;MACA,KAAA1G,WAAA,GAAA0G,IAAA;MACA,KAAA3G,iBAAA,GAAA2G,IAAA,CAAAnE,SAAA;MACA,KAAAzC,8BAAA;IACA;IACA6G,2BAAA,WAAAA,4BAAA;MACA,SAAA3G,WAAA;QACA,KAAAA,WAAA,CAAAuC,SAAA,QAAAxC,iBAAA;QACA;QACA;MACA;MACA,KAAAD,8BAAA;IACA;IAEA;IACA8G,gBAAA,WAAAA,iBAAA;MACA;MACArD,OAAA,CAAAC,GAAA,eAAA7B,SAAA;MACA,KAAAF,sBAAA;MACA;MACA,KAAAE,SAAA;QACAC,KAAA;QACAC,iBAAA;MACA;IACA;IAEA;IACAgF,4BAAA,WAAAA,6BAAA;MACA,KAAAnF,+BAAA;IACA;IACAoF,wBAAA,WAAAA,yBAAA;MACA,KAAApF,+BAAA;MACA,KAAAqF,uBAAA;IACA;IACAC,yBAAA,WAAAA,0BAAA;MACA;MACAzD,OAAA,CAAAC,GAAA,oCAAAuD,uBAAA;MACA,KAAArF,+BAAA;MACA,KAAAqF,uBAAA;IACA;EACA;AACA", "ignoreList": []}]}