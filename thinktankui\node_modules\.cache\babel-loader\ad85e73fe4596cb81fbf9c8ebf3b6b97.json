{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\formats\\header.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\formats\\header.js", "mtime": 1749105929553}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "Header", "_Block", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "formats", "domNode", "tagName", "indexOf", "Block", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/header.ts"], "sourcesContent": ["import Block from '../blots/block.js';\n\nclass Header extends Block {\n  static blotName = 'header';\n  static tagName = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'];\n\n  static formats(domNode: Element) {\n    return this.tagName.indexOf(domNode.tagName) + 1;\n  }\n}\n\nexport default Header;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,IAE/BC,MAAM,0BAAAC,MAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,MAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,MAAA,EAAAC,MAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,MAAA;IAAAQ,GAAA;IAAAC,KAAA,EAIV,SAAOC,OAAOA,CAACC,OAAgB,EAAE;MAC/B,OAAO,IAAI,CAACC,OAAO,CAACC,OAAO,CAACF,OAAO,CAACC,OAAO,CAAC,GAAG,CAAC;IAClD;EAAA;AAAA,EANmBE,cAAK;AAAA,IAAAC,gBAAA,CAAAZ,OAAA,EAApBH,MAAM,cACQ,QAAQ;AAAA,IAAAe,gBAAA,CAAAZ,OAAA,EADtBH,MAAM,aAEO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAAA,IAAAgB,QAAA,GAAAC,OAAA,CAAAd,OAAA,GAOxCH,MAAM", "ignoreList": []}]}