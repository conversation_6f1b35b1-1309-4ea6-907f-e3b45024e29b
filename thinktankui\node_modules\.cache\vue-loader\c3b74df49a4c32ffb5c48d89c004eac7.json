{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\TopNav\\index.vue", "mtime": 1747908152292}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\n  <el-menu\n    :default-active=\"activeMenu\"\n    mode=\"horizontal\"\n    @select=\"handleSelect\"\n  >\n    <template v-for=\"(item, index) in topMenus\">\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\">\n        <svg-icon\n        v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\n        :icon-class=\"item.meta.icon\"/>\n        {{ item.meta.title }}\n      </el-menu-item>\n    </template>\n\n    <!-- 顶部菜单超出数量折叠 -->\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" :key=\"visibleNumber\" v-if=\"topMenus.length > visibleNumber\">\n      <template slot=\"title\">更多菜单</template>\n      <template v-for=\"(item, index) in topMenus\">\n        <el-menu-item\n          :index=\"item.path\"\n          :key=\"index\"\n          v-if=\"index >= visibleNumber\">\n          <svg-icon\n            v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\n            :icon-class=\"item.meta.icon\"/>\n          {{ item.meta.title }}\n        </el-menu-item>\n      </template>\n    </el-submenu>\n  </el-menu>\n</template>\n\n<script>\nimport { constantRoutes } from \"@/router\";\nimport { isHttp } from \"@/utils/validate\";\n\n// 隐藏侧边栏路由\nconst hideList = ['/index', '/user/profile'];\n\nexport default {\n  data() {\n    return {\n      // 顶部栏初始数\n      visibleNumber: 5,\n      // 当前激活菜单的 index\n      currentIndex: undefined\n    };\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme;\n    },\n    // 顶部显示菜单\n    topMenus() {\n      let topMenus = [];\n      this.routers.map((menu) => {\n        if (menu.hidden !== true) {\n          // 兼容顶部栏一级菜单内部跳转\n          if (menu.path === \"/\") {\n            topMenus.push(menu.children[0]);\n          } else {\n            topMenus.push(menu);\n          }\n        }\n      });\n      return topMenus;\n    },\n    // 所有的路由信息\n    routers() {\n      return this.$store.state.permission.topbarRouters;\n    },\n    // 设置子路由\n    childrenMenus() {\n      var childrenMenus = [];\n      this.routers.map((router) => {\n        for (var item in router.children) {\n          if (router.children[item].parentPath === undefined) {\n            if(router.path === \"/\") {\n              router.children[item].path = \"/\" + router.children[item].path;\n            } else {\n              if(!isHttp(router.children[item].path)) {\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\n              }\n            }\n            router.children[item].parentPath = router.path;\n          }\n          childrenMenus.push(router.children[item]);\n        }\n      });\n      return constantRoutes.concat(childrenMenus);\n    },\n    // 默认激活的菜单\n    activeMenu() {\n      const path = this.$route.path;\n      let activePath = path;\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\n        const tmpPath = path.substring(1, path.length);\n        if (!this.$route.meta.link) {\n          activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\n        }\n      } else if(!this.$route.children) {\n        activePath = path;\n        this.$store.dispatch('app/toggleSideBarHide', true);\n      }\n      this.activeRoutes(activePath);\n      return activePath;\n    },\n  },\n  beforeMount() {\n    window.addEventListener('resize', this.setVisibleNumber)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.setVisibleNumber)\n  },\n  mounted() {\n    this.setVisibleNumber();\n  },\n  methods: {\n    // 根据宽度计算设置显示栏数\n    setVisibleNumber() {\n      const width = document.body.getBoundingClientRect().width / 3;\n      this.visibleNumber = parseInt(width / 85);\n    },\n    // 菜单选择事件\n    handleSelect(key, keyPath) {\n      this.currentIndex = key;\n      const route = this.routers.find(item => item.path === key);\n      if (isHttp(key)) {\n        // http(s):// 路径新窗口打开\n        window.open(key, \"_blank\");\n      } else if (!route || !route.children) {\n        // 没有子路由路径内部打开\n        const routeMenu = this.childrenMenus.find(item => item.path === key);\n        if (routeMenu && routeMenu.query) {\n          let query = JSON.parse(routeMenu.query);\n          this.$router.push({ path: key, query: query });\n        } else {\n          this.$router.push({ path: key });\n        }\n        this.$store.dispatch('app/toggleSideBarHide', true);\n      } else {\n        // 显示左侧联动菜单\n        this.activeRoutes(key);\n        this.$store.dispatch('app/toggleSideBarHide', false);\n      }\n    },\n    // 当前激活的路由\n    activeRoutes(key) {\n      var routes = [];\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\n        this.childrenMenus.map((item) => {\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\n            routes.push(item);\n          }\n        });\n      }\n      if(routes.length > 0) {\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\n      } else {\n        this.$store.dispatch('app/toggleSideBarHide', true);\n      }\n    }\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.topmenu-container.el-menu--horizontal > .el-menu-item {\n  float: left;\n  height: 50px !important;\n  line-height: 50px !important;\n  color: #999093 !important;\n  padding: 0 5px !important;\n  margin: 0 10px !important;\n}\n\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\n  border-bottom: 2px solid #{'var(--theme)'} !important;\n  color: #303133;\n}\n\n/* submenu item */\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\n  float: left;\n  height: 50px !important;\n  line-height: 50px !important;\n  color: #999093 !important;\n  padding: 0 5px !important;\n  margin: 0 10px !important;\n}\n</style>\n"]}]}