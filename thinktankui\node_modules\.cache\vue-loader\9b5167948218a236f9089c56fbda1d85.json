{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\index.vue?vue&type=template&id=764a0da0&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\index.vue", "mtime": 1749114975231}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749105919489}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}