from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger
from config.database import Base


class KeywordData(Base):
    """
    关键词数据表
    """

    __tablename__ = 'keyword_data'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    title = Column(String(255), nullable=True, comment='标题')
    content = Column(String(10000), nullable=True, comment='内容')
    url = Column(String(255), nullable=True, comment='链接')
    keyword = Column(String(255), nullable=True, comment='关键词')
    type = Column(String(255), nullable=True, comment='类型')
    createtime = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    web = Column(String(255), nullable=True, comment='网页')
