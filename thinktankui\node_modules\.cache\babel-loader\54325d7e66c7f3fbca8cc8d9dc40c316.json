{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\user.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\user.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_ruoyi", "listUser", "query", "request", "url", "method", "params", "getUser", "userId", "parseStrEmpty", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "resetUserPwd", "password", "changeUserStatus", "status", "getUserProfile", "updateUserProfile", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "headers", "getAuthRole", "updateAuthRole", "deptTreeSelect"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/system/user.js"], "sourcesContent": ["import request from '@/utils/request'\nimport { parseStrEmpty } from \"@/utils/ruoyi\";\n\n// 查询用户列表\nexport function listUser(query) {\n  return request({\n    url: '/system/user/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询用户详细\nexport function getUser(userId) {\n  return request({\n    url: '/system/user/' + parseStrEmpty(userId),\n    method: 'get'\n  })\n}\n\n// 新增用户\nexport function addUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改用户\nexport function updateUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除用户\nexport function delUser(userId) {\n  return request({\n    url: '/system/user/' + userId,\n    method: 'delete'\n  })\n}\n\n// 用户密码重置\nexport function resetUserPwd(userId, password) {\n  const data = {\n    userId,\n    password\n  }\n  return request({\n    url: '/system/user/resetPwd',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户状态修改\nexport function changeUserStatus(userId, status) {\n  const data = {\n    userId,\n    status\n  }\n  return request({\n    url: '/system/user/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n// 查询用户个人信息\nexport function getUserProfile() {\n  return request({\n    url: '/system/user/profile',\n    method: 'get'\n  })\n}\n\n// 修改用户个人信息\nexport function updateUserProfile(data) {\n  return request({\n    url: '/system/user/profile',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户密码重置\nexport function updateUserPwd(oldPassword, newPassword) {\n  const data = {\n    oldPassword,\n    newPassword\n  }\n  return request({\n    url: '/system/user/profile/updatePwd',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户头像上传\nexport function uploadAvatar(data) {\n  return request({\n    url: '/system/user/profile/avatar',\n    method: 'post',\n    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n    data: data\n  })\n}\n\n// 查询授权角色\nexport function getAuthRole(userId) {\n  return request({\n    url: '/system/user/authRole/' + userId,\n    method: 'get'\n  })\n}\n\n// 保存授权角色\nexport function updateAuthRole(data) {\n  return request({\n    url: '/system/user/authRole',\n    method: 'put',\n    params: data\n  })\n}\n\n// 查询部门下拉树结构\nexport function deptTreeSelect() {\n  return request({\n    url: '/system/user/deptTree',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA;AACO,SAASE,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAG,IAAAK,oBAAa,EAACD,MAAM,CAAC;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNO,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACR,MAAM,EAAES,MAAM,EAAE;EAC/C,IAAMN,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNS,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,iBAAiBA,CAACR,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMX,IAAI,GAAG;IACXU,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAACZ,IAAI,EAAE;EACjC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdmB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChEb,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,WAAWA,CAACjB,MAAM,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASqB,cAAcA,CAACf,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAxB,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}