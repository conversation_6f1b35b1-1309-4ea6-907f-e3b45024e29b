{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\IconSelect\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBpY29ucyBmcm9tICcuL3JlcXVpcmVJY29ucycKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdJY29uU2VsZWN0JywKICBwcm9wczogewogICAgYWN0aXZlSWNvbjogewogICAgICB0eXBlOiBTdHJpbmcKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBuYW1lOiAnJywKICAgICAgaWNvbkxpc3Q6IGljb25zCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaWx0ZXJJY29ucygpIHsKICAgICAgdGhpcy5pY29uTGlzdCA9IGljb25zCiAgICAgIGlmICh0aGlzLm5hbWUpIHsKICAgICAgICB0aGlzLmljb25MaXN0ID0gdGhpcy5pY29uTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmluY2x1ZGVzKHRoaXMubmFtZSkpCiAgICAgIH0KICAgIH0sCiAgICBzZWxlY3RlZEljb24obmFtZSkgewogICAgICB0aGlzLiRlbWl0KCdzZWxlY3RlZCcsIG5hbWUpCiAgICAgIGRvY3VtZW50LmJvZHkuY2xpY2soKQogICAgfSwKICAgIHJlc2V0KCkgewogICAgICB0aGlzLm5hbWUgPSAnJwogICAgICB0aGlzLmljb25MaXN0ID0gaWNvbnMKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div class=\"list-container\">\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\n            <span>{{ item }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  props: {\n    activeIcon: {\n      type: String\n    }\n  },\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-search {\n      position: relative;\n      margin-bottom: 5px;\n    }\n    .icon-list {\n      height: 200px;\n      overflow: auto;\n      .list-container {\n        display: flex;\n        flex-wrap: wrap;\n        .icon-item-wrapper {\n          width: calc(100% / 3);\n          height: 25px;\n          line-height: 25px;\n          cursor: pointer;\n          display: flex;\n          .icon-item {\n            display: flex;\n            max-width: 100%;\n            height: 100%;\n            padding: 0 5px;\n            &:hover {\n              background: #ececec;\n              border-radius: 5px;\n            }\n            .icon {\n              flex-shrink: 0;\n            }\n            span {\n              display: inline-block;\n              vertical-align: -0.15em;\n              fill: currentColor;\n              padding-left: 2px;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n          }\n          .icon-item.active {\n            background: #ececec;\n            border-radius: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n"]}]}