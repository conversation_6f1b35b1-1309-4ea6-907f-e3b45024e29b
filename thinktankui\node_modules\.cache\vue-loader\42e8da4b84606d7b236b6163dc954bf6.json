{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\index.vue?vue&type=style&index=0&id=39cfdb14&lang=scss", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lZGl0b3ItdGFic3sKICBiYWNrZ3JvdW5kOiAjMTIxMzE1OwogIC5lbC10YWJzX19oZWFkZXJ7CiAgICBtYXJnaW46IDA7CiAgICBib3JkZXItYm90dG9tLWNvbG9yOiAjMTIxMzE1OwogICAgLmVsLXRhYnNfX25hdnsKICAgICAgYm9yZGVyLWNvbG9yOiAjMTIxMzE1OwogICAgfQogIH0KICAuZWwtdGFic19faXRlbXsKICAgIGhlaWdodDogMzJweDsKICAgIGxpbmUtaGVpZ2h0OiAzMnB4OwogICAgY29sb3I6ICM4ODhhOGU7CiAgICBib3JkZXItbGVmdDogMXB4IHNvbGlkICMxMjEzMTUgIWltcG9ydGFudDsKICAgIGJhY2tncm91bmQ6ICMzNjM2MzY7CiAgICBtYXJnaW4tcmlnaHQ6IDVweDsKICAgIHVzZXItc2VsZWN0OiBub25lOwogIH0KICAuZWwtdGFic19faXRlbS5pcy1hY3RpdmV7CiAgICBiYWNrZ3JvdW5kOiAjMWUxZTFlOwogICAgYm9yZGVyLWJvdHRvbS1jb2xvcjogIzFlMWUxZSFpbXBvcnRhbnQ7CiAgICBjb2xvcjogI2ZmZjsKICB9CiAgLmVsLWljb24tZWRpdHsKICAgIGNvbG9yOiAjZjFmYThjOwogIH0KICAuZWwtaWNvbi1kb2N1bWVudHsKICAgIGNvbG9yOiAjYTk1ODEyOwogIH0KfQoKLy8gaG9tZQoucmlnaHQtc2Nyb2xsYmFyIHsKICAuZWwtc2Nyb2xsYmFyX192aWV3IHsKICAgIHBhZGRpbmc6IDEycHggMThweCAxNXB4IDE1cHg7CiAgfQp9Ci5sZWZ0LXNjcm9sbGJhciAuZWwtc2Nyb2xsYmFyX193cmFwIHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIG92ZXJmbG93LXg6IGhpZGRlbiAhaW1wb3J0YW50OwogIG1hcmdpbi1ib3R0b206IDAgIWltcG9ydGFudDsKfQouY2VudGVyLXRhYnN7CiAgLmVsLXRhYnNfX2hlYWRlcnsKICAgIG1hcmdpbi1ib3R0b206IDAhaW1wb3J0YW50OwogIH0KICAuZWwtdGFic19faXRlbXsKICAgIHdpZHRoOiA1MCU7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgfQogIC5lbC10YWJzX19uYXZ7CiAgICB3aWR0aDogMTAwJTsKICB9Cn0KLnJlZy1pdGVtewogIHBhZGRpbmc6IDEycHggNnB4OwogIGJhY2tncm91bmQ6ICNmOGY4Zjg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICAuY2xvc2UtYnRuewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgcmlnaHQ6IC02cHg7CiAgICB0b3A6IC02cHg7CiAgICBkaXNwbGF5OiBibG9jazsKICAgIHdpZHRoOiAxNnB4OwogICAgaGVpZ2h0OiAxNnB4OwogICAgbGluZS1oZWlnaHQ6IDE2cHg7CiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMik7CiAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICBjb2xvcjogI2ZmZjsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIHotaW5kZXg6IDE7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICBmb250LXNpemU6IDEycHg7CiAgICAmOmhvdmVyewogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDIxMCwgMjMsIDIzLCAwLjUpCiAgICB9CiAgfQogICYgKyAucmVnLWl0ZW17CiAgICBtYXJnaW4tdG9wOiAxOHB4OwogIH0KfQouYWN0aW9uLWJhcnsKICAmIC5lbC1idXR0b24rLmVsLWJ1dHRvbiB7CiAgICBtYXJnaW4tbGVmdDogMTVweDsKICB9CiAgJiBpIHsKICAgIGZvbnQtc2l6ZTogMjBweDsKICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICB0b3A6IC0xcHg7CiAgfQp9CgouY3VzdG9tLXRyZWUtbm9kZXsKICB3aWR0aDogMTAwJTsKICBmb250LXNpemU6IDE0cHg7CiAgLm5vZGUtb3BlcmF0aW9uewogICAgZmxvYXQ6IHJpZ2h0OwogIH0KICBpW2NsYXNzKj0iZWwtaWNvbiJdICsgaVtjbGFzcyo9ImVsLWljb24iXXsKICAgIG1hcmdpbi1sZWZ0OiA2cHg7CiAgfQogIC5lbC1pY29uLXBsdXN7CiAgICBjb2xvcjogIzQwOUVGRjsKICB9CiAgLmVsLWljb24tZGVsZXRlewogICAgY29sb3I6ICMxNTdhMGM7CiAgfQp9CgoubGVmdC1zY3JvbGxiYXIgLmVsLXNjcm9sbGJhcl9fdmlld3sKICBvdmVyZmxvdy14OiBoaWRkZW47Cn0KCi5lbC1yYXRlewogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB2ZXJ0aWNhbC1hbGlnbjogdGV4dC10b3A7Cn0KLmVsLXVwbG9hZF9fdGlwewogIGxpbmUtaGVpZ2h0OiAxLjI7Cn0KCiRzZWxlY3RlZENvbG9yOiAjZjZmN2ZmOwokbGlnaHRlckJsdWU6ICM0MDlFRkY7CgouY29udGFpbmVyIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9CgouY29tcG9uZW50cy1saXN0IHsKICBwYWRkaW5nOiA4cHg7CiAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICBoZWlnaHQ6IDEwMCU7CiAgLmNvbXBvbmVudHMtaXRlbSB7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICB3aWR0aDogNDglOwogICAgbWFyZ2luOiAxJTsKICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwbXMgIWltcG9ydGFudDsKICB9Cn0KLmNvbXBvbmVudHMtZHJhZ2dhYmxlewogIHBhZGRpbmctYm90dG9tOiAyMHB4Owp9Ci5jb21wb25lbnRzLXRpdGxlewogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzIyMjsKICBtYXJnaW46IDZweCAycHg7CiAgLnN2Zy1pY29uewogICAgY29sb3I6ICM2NjY7CiAgICBmb250LXNpemU6IDE4cHg7CiAgfQp9CgouY29tcG9uZW50cy1ib2R5IHsKICBwYWRkaW5nOiA4cHggMTBweDsKICBiYWNrZ3JvdW5kOiAkc2VsZWN0ZWRDb2xvcjsKICBmb250LXNpemU6IDEycHg7CiAgY3Vyc29yOiBtb3ZlOwogIGJvcmRlcjogMXB4IGRhc2hlZCAkc2VsZWN0ZWRDb2xvcjsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgLnN2Zy1pY29uewogICAgY29sb3I6ICM3Nzc7CiAgICBmb250LXNpemU6IDE1cHg7CiAgfQogICY6aG92ZXIgewogICAgYm9yZGVyOiAxcHggZGFzaGVkICM3ODdiZTg7CiAgICBjb2xvcjogIzc4N2JlODsKICAgIC5zdmctaWNvbiB7CiAgICAgIGNvbG9yOiAjNzg3YmU4OwogICAgfQogIH0KfQoKLmxlZnQtYm9hcmQgewogIHdpZHRoOiAyNjBweDsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgbGVmdDogMDsKICB0b3A6IDA7CiAgaGVpZ2h0OiAxMDB2aDsKfQoubGVmdC1zY3JvbGxiYXJ7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDJweCk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQouY2VudGVyLXNjcm9sbGJhciB7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDJweCk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBib3JkZXItbGVmdDogMXB4IHNvbGlkICNmMWU4ZTg7CiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2YxZThlODsKICBib3gtc2l6aW5nOiBib3JkZXItYm94Owp9Ci5jZW50ZXItYm9hcmQgewogIGhlaWdodDogMTAwdmg7CiAgd2lkdGg6IGF1dG87CiAgbWFyZ2luOiAwIDM1MHB4IDAgMjYwcHg7CiAgYm94LXNpemluZzogYm9yZGVyLWJveDsKfQouZW1wdHktaW5mb3sKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiA0NiU7CiAgbGVmdDogMDsKICByaWdodDogMDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgZm9udC1zaXplOiAxOHB4OwogIGNvbG9yOiAjY2NiMWVhOwogIGxldHRlci1zcGFjaW5nOiA0cHg7Cn0KLmFjdGlvbi1iYXJ7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogNDJweDsKICB0ZXh0LWFsaWduOiByaWdodDsKICBwYWRkaW5nOiAwIDE1cHg7CiAgYm94LXNpemluZzogYm9yZGVyLWJveDs7CiAgYm9yZGVyOiAxcHggc29saWQgI2YxZThlODsKICBib3JkZXItdG9wOiBub25lOwogIGJvcmRlci1sZWZ0OiBub25lOwogIC5kZWxldGUtYnRuewogICAgY29sb3I6ICNGNTZDNkM7CiAgfQp9Ci5sb2dvLXdyYXBwZXJ7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogNDJweDsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjFlOGU4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7Cn0KLmxvZ297CiAgcG9zaXRpb246IGFic29sdXRlOwogIGxlZnQ6IDEycHg7CiAgdG9wOiA2cHg7CiAgbGluZS1oZWlnaHQ6IDMwcHg7CiAgY29sb3I6ICMwMGFmZmY7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBmb250LXNpemU6IDE3cHg7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICA+IGltZ3sKICAgIHdpZHRoOiAzMHB4OwogICAgaGVpZ2h0OiAzMHB4OwogICAgdmVydGljYWwtYWxpZ246IHRvcDsKICB9CiAgLmdpdGh1YnsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHZlcnRpY2FsLWFsaWduOiBzdWI7CiAgICBtYXJnaW4tbGVmdDogMTVweDsKICAgID4gaW1newogICAgICBoZWlnaHQ6IDIycHg7CiAgICB9CiAgfQp9CgouY2VudGVyLWJvYXJkLXJvdyB7CiAgcGFkZGluZzogMTJweCAxMnB4IDE1cHggMTJweDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICYgPiAuZWwtZm9ybSB7CiAgICAvLyA2OSA9IDEyKzE1KzQyCiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA2OXB4KTsKICB9Cn0KLmRyYXdpbmctYm9hcmQgewogIGhlaWdodDogMTAwJTsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgLmNvbXBvbmVudHMtYm9keSB7CiAgICBwYWRkaW5nOiAwOwogICAgbWFyZ2luOiAwOwogICAgZm9udC1zaXplOiAwOwogIH0KICAuc29ydGFibGUtZ2hvc3QgewogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgZGlzcGxheTogYmxvY2s7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgJjo6YmVmb3JlIHsKICAgICAgY29udGVudDogIiAiOwogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIGxlZnQ6IDA7CiAgICAgIHJpZ2h0OiAwOwogICAgICB0b3A6IDA7CiAgICAgIGhlaWdodDogM3B4OwogICAgICBiYWNrZ3JvdW5kOiByZ2IoODksIDg5LCAyMjMpOwogICAgICB6LWluZGV4OiAyOwogICAgfQogIH0KICAuY29tcG9uZW50cy1pdGVtLnNvcnRhYmxlLWdob3N0IHsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiA2MHB4OwogICAgYmFja2dyb3VuZC1jb2xvcjogJHNlbGVjdGVkQ29sb3I7CiAgfQogIC5hY3RpdmUtZnJvbS1pdGVtIHsKICAgICYgPiAuZWwtZm9ybS1pdGVtewogICAgICBiYWNrZ3JvdW5kOiAkc2VsZWN0ZWRDb2xvcjsKICAgICAgYm9yZGVyLXJhZGl1czogNnB4OwogICAgfQogICAgJiA+IC5kcmF3aW5nLWl0ZW0tY29weSwgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlewogICAgICBkaXNwbGF5OiBpbml0aWFsOwogICAgfQogICAgJiA+IC5jb21wb25lbnQtbmFtZXsKICAgICAgY29sb3I6ICRsaWdodGVyQmx1ZTsKICAgIH0KICB9CiAgLmVsLWZvcm0taXRlbXsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgfQp9Ci5kcmF3aW5nLWl0ZW17CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGN1cnNvcjogbW92ZTsKICAmLnVuZm9jdXMtYm9yZGVyZWQ6bm90KC5hY3RpdmVGcm9tSXRlbSkgPiBkaXY6Zmlyc3QtY2hpbGQgIHsKICAgIGJvcmRlcjogMXB4IGRhc2hlZCAjY2NjOwogIH0KICAuZWwtZm9ybS1pdGVtewogICAgcGFkZGluZzogMTJweCAxMHB4OwogIH0KfQouZHJhd2luZy1yb3ctaXRlbXsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgY3Vyc29yOiBtb3ZlOwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgYm9yZGVyOiAxcHggZGFzaGVkICNjY2M7CiAgYm9yZGVyLXJhZGl1czogM3B4OwogIHBhZGRpbmc6IDAgMnB4OwogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgLmRyYXdpbmctcm93LWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMnB4OwogIH0KICAuZWwtY29sewogICAgbWFyZ2luLXRvcDogMjJweDsKICB9CiAgLmVsLWZvcm0taXRlbXsKICAgIG1hcmdpbi1ib3R0b206IDA7CiAgfQogIC5kcmFnLXdyYXBwZXJ7CiAgICBtaW4taGVpZ2h0OiA4MHB4OwogIH0KICAmLmFjdGl2ZS1mcm9tLWl0ZW17CiAgICBib3JkZXI6IDFweCBkYXNoZWQgJGxpZ2h0ZXJCbHVlOwogIH0KICAuY29tcG9uZW50LW5hbWV7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDA7CiAgICBsZWZ0OiAwOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgY29sb3I6ICNiYmI7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICBwYWRkaW5nOiAwIDZweDsKICB9Cn0KLmRyYXdpbmctaXRlbSwgLmRyYXdpbmctcm93LWl0ZW17CiAgJjpob3ZlciB7CiAgICAmID4gLmVsLWZvcm0taXRlbXsKICAgICAgYmFja2dyb3VuZDogJHNlbGVjdGVkQ29sb3I7CiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDsKICAgIH0KICAgICYgPiAuZHJhd2luZy1pdGVtLWNvcHksICYgPiAuZHJhd2luZy1pdGVtLWRlbGV0ZXsKICAgICAgZGlzcGxheTogaW5pdGlhbDsKICAgIH0KICB9CiAgJiA+IC5kcmF3aW5nLWl0ZW0tY29weSwgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlewogICAgZGlzcGxheTogbm9uZTsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogLTEwcHg7CiAgICB3aWR0aDogMjJweDsKICAgIGhlaWdodDogMjJweDsKICAgIGxpbmUtaGVpZ2h0OiAyMnB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgYm9yZGVyOiAxcHggc29saWQ7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICB6LWluZGV4OiAxOwogIH0KICAmID4gLmRyYXdpbmctaXRlbS1jb3B5ewogICAgcmlnaHQ6IDU2cHg7CiAgICBib3JkZXItY29sb3I6ICRsaWdodGVyQmx1ZTsKICAgIGNvbG9yOiAkbGlnaHRlckJsdWU7CiAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgJjpob3ZlcnsKICAgICAgYmFja2dyb3VuZDogJGxpZ2h0ZXJCbHVlOwogICAgICBjb2xvcjogI2ZmZjsKICAgIH0KICB9CiAgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlewogICAgcmlnaHQ6IDI0cHg7CiAgICBib3JkZXItY29sb3I6ICNGNTZDNkM7CiAgICBjb2xvcjogI0Y1NkM2QzsKICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAmOmhvdmVyewogICAgICBiYWNrZ3JvdW5kOiAjRjU2QzZDOwogICAgICBjb2xvcjogI2ZmZjsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <div class=\"left-board\">\n      <div class=\"logo-wrapper\">\n        <div class=\"logo\">\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\n        </div>\n      </div>\n      <el-scrollbar class=\"left-scrollbar\">\n        <div class=\"components-list\">\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />输入型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"inputComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />选择型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"selectComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in selectComponents\"\n              :key=\"index\"\n              class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" /> 布局型组件\n          </div>\n          <draggable\n            class=\"components-draggable\" :list=\"layoutComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n        </div>\n      </el-scrollbar>\n    </div>\n\n    <div class=\"center-board\">\n      <div class=\"action-bar\">\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\n          导出vue文件\n        </el-button>\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\n          复制代码\n        </el-button>\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\n          清空\n        </el-button>\n      </div>\n      <el-scrollbar class=\"center-scrollbar\">\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\n          <el-form\n            :size=\"formConf.size\"\n            :label-position=\"formConf.labelPosition\"\n            :disabled=\"formConf.disabled\"\n            :label-width=\"formConf.labelWidth + 'px'\"\n          >\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\n              <draggable-item\n                v-for=\"(element, index) in drawingList\"\n                :key=\"element.renderKey\"\n                :drawing-list=\"drawingList\"\n                :element=\"element\"\n                :index=\"index\"\n                :active-id=\"activeId\"\n                :form-conf=\"formConf\"\n                @activeItem=\"activeFormItem\"\n                @copyItem=\"drawingItemCopy\"\n                @deleteItem=\"drawingItemDelete\"\n              />\n            </draggable>\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\n              从左侧拖入或点选组件进行表单设计\n            </div>\n          </el-form>\n        </el-row>\n      </el-scrollbar>\n    </div>\n\n    <right-panel\n      :active-data=\"activeData\"\n      :form-conf=\"formConf\"\n      :show-field=\"!!drawingList.length\"\n      @tag-change=\"tagChange\"\n    />\n\n    <code-type-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"选择生成类型\"\n      :show-file-name=\"showFileName\"\n      @confirm=\"generate\"\n    />\n    <input id=\"copyNode\" type=\"hidden\">\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport beautifier from 'js-beautify'\nimport ClipboardJS from 'clipboard'\nimport render from '@/utils/generator/render'\nimport RightPanel from './RightPanel'\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\nimport { beautifierConf, titleCase } from '@/utils/index'\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport drawingDefault from '@/utils/generator/drawingDefault'\nimport logo from '@/assets/logo/logo.png'\nimport CodeTypeDialog from './CodeTypeDialog'\nimport DraggableItem from './DraggableItem'\n\nlet oldActiveId\nlet tempActiveData\n\nexport default {\n  components: {\n    draggable,\n    render,\n    RightPanel,\n    CodeTypeDialog,\n    DraggableItem\n  },\n  data() {\n    return {\n      logo,\n      idGlobal: 100,\n      formConf,\n      inputComponents,\n      selectComponents,\n      layoutComponents,\n      labelWidth: 100,\n      drawingList: drawingDefault,\n      drawingData: {},\n      activeId: drawingDefault[0].formId,\n      drawerVisible: false,\n      formData: {},\n      dialogVisible: false,\n      generateConf: null,\n      showFileName: false,\n      activeData: drawingDefault[0]\n    }\n  },\n  created() {\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\n    document.body.ondrop = event => {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n  },\n  watch: {\n    // eslint-disable-next-line func-names\n    'activeData.label': function (val, oldVal) {\n      if (\n        this.activeData.placeholder === undefined\n        || !this.activeData.tag\n        || oldActiveId !== this.activeId\n      ) {\n        return\n      }\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\n    },\n    activeId: {\n      handler(val) {\n        oldActiveId = val\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    const clipboard = new ClipboardJS('#copyNode', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  methods: {\n    activeFormItem(element) {\n      this.activeData = element\n      this.activeId = element.formId\n    },\n    onEnd(obj, a) {\n      if (obj.from !== obj.to) {\n        this.activeData = tempActiveData\n        this.activeId = this.idGlobal\n      }\n    },\n    addComponent(item) {\n      const clone = this.cloneComponent(item)\n      this.drawingList.push(clone)\n      this.activeFormItem(clone)\n    },\n    cloneComponent(origin) {\n      const clone = JSON.parse(JSON.stringify(origin))\n      clone.formId = ++this.idGlobal\n      clone.span = formConf.span\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\n      if (!clone.layout) clone.layout = 'colFormItem'\n      if (clone.layout === 'colFormItem') {\n        clone.vModel = `field${this.idGlobal}`\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\n        tempActiveData = clone\n      } else if (clone.layout === 'rowFormItem') {\n        delete clone.label\n        clone.componentName = `row${this.idGlobal}`\n        clone.gutter = this.formConf.gutter\n        tempActiveData = clone\n      }\n      return tempActiveData\n    },\n    AssembleFormData() {\n      this.formData = {\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\n        ...this.formConf\n      }\n    },\n    generate(data) {\n      const func = this[`exec${titleCase(this.operationType)}`]\n      this.generateConf = data\n      func && func(data)\n    },\n    execRun(data) {\n      this.AssembleFormData()\n      this.drawerVisible = true\n    },\n    execDownload(data) {\n      const codeStr = this.generateCode()\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n      this.$download.saveAs(blob, data.fileName)\n    },\n    execCopy(data) {\n      document.getElementById('copyNode').click()\n    },\n    empty() {\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\n        () => {\n          this.drawingList = []\n        }\n      )\n    },\n    drawingItemCopy(item, parent) {\n      let clone = JSON.parse(JSON.stringify(item))\n      clone = this.createIdAndKey(clone)\n      parent.push(clone)\n      this.activeFormItem(clone)\n    },\n    createIdAndKey(item) {\n      item.formId = ++this.idGlobal\n      item.renderKey = +new Date()\n      if (item.layout === 'colFormItem') {\n        item.vModel = `field${this.idGlobal}`\n      } else if (item.layout === 'rowFormItem') {\n        item.componentName = `row${this.idGlobal}`\n      }\n      if (Array.isArray(item.children)) {\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\n      }\n      return item\n    },\n    drawingItemDelete(index, parent) {\n      parent.splice(index, 1)\n      this.$nextTick(() => {\n        const len = this.drawingList.length\n        if (len) {\n          this.activeFormItem(this.drawingList[len - 1])\n        }\n      })\n    },\n    generateCode() {\n      const { type } = this.generateConf\n      this.AssembleFormData()\n      const script = vueScript(makeUpJs(this.formData, type))\n      const html = vueTemplate(makeUpHtml(this.formData, type))\n      const css = cssStyle(makeUpCss(this.formData))\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    download() {\n      this.dialogVisible = true\n      this.showFileName = true\n      this.operationType = 'download'\n    },\n    run() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'run'\n    },\n    copy() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'copy'\n    },\n    tagChange(newTag) {\n      newTag = this.cloneComponent(newTag)\n      newTag.vModel = this.activeData.vModel\n      newTag.formId = this.activeId\n      newTag.span = this.activeData.span\n      delete this.activeData.tag\n      delete this.activeData.tagIcon\n      delete this.activeData.document\n      Object.keys(newTag).forEach(key => {\n        if (this.activeData[key] !== undefined\n          && typeof this.activeData[key] === typeof newTag[key]) {\n          newTag[key] = this.activeData[key]\n        }\n      })\n      this.activeData = newTag\n      this.updateDrawingList(newTag, this.drawingList)\n    },\n    updateDrawingList(newTag, list) {\n      const index = list.findIndex(item => item.formId === this.activeId)\n      if (index > -1) {\n        list.splice(index, 1, newTag)\n      } else {\n        list.forEach(item => {\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang='scss'>\n.editor-tabs{\n  background: #121315;\n  .el-tabs__header{\n    margin: 0;\n    border-bottom-color: #121315;\n    .el-tabs__nav{\n      border-color: #121315;\n    }\n  }\n  .el-tabs__item{\n    height: 32px;\n    line-height: 32px;\n    color: #888a8e;\n    border-left: 1px solid #121315 !important;\n    background: #363636;\n    margin-right: 5px;\n    user-select: none;\n  }\n  .el-tabs__item.is-active{\n    background: #1e1e1e;\n    border-bottom-color: #1e1e1e!important;\n    color: #fff;\n  }\n  .el-icon-edit{\n    color: #f1fa8c;\n  }\n  .el-icon-document{\n    color: #a95812;\n  }\n}\n\n// home\n.right-scrollbar {\n  .el-scrollbar__view {\n    padding: 12px 18px 15px 15px;\n  }\n}\n.left-scrollbar .el-scrollbar__wrap {\n  box-sizing: border-box;\n  overflow-x: hidden !important;\n  margin-bottom: 0 !important;\n}\n.center-tabs{\n  .el-tabs__header{\n    margin-bottom: 0!important;\n  }\n  .el-tabs__item{\n    width: 50%;\n    text-align: center;\n  }\n  .el-tabs__nav{\n    width: 100%;\n  }\n}\n.reg-item{\n  padding: 12px 6px;\n  background: #f8f8f8;\n  position: relative;\n  border-radius: 4px;\n  .close-btn{\n    position: absolute;\n    right: -6px;\n    top: -6px;\n    display: block;\n    width: 16px;\n    height: 16px;\n    line-height: 16px;\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 50%;\n    color: #fff;\n    text-align: center;\n    z-index: 1;\n    cursor: pointer;\n    font-size: 12px;\n    &:hover{\n      background: rgba(210, 23, 23, 0.5)\n    }\n  }\n  & + .reg-item{\n    margin-top: 18px;\n  }\n}\n.action-bar{\n  & .el-button+.el-button {\n    margin-left: 15px;\n  }\n  & i {\n    font-size: 20px;\n    vertical-align: middle;\n    position: relative;\n    top: -1px;\n  }\n}\n\n.custom-tree-node{\n  width: 100%;\n  font-size: 14px;\n  .node-operation{\n    float: right;\n  }\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\n    margin-left: 6px;\n  }\n  .el-icon-plus{\n    color: #409EFF;\n  }\n  .el-icon-delete{\n    color: #157a0c;\n  }\n}\n\n.left-scrollbar .el-scrollbar__view{\n  overflow-x: hidden;\n}\n\n.el-rate{\n  display: inline-block;\n  vertical-align: text-top;\n}\n.el-upload__tip{\n  line-height: 1.2;\n}\n\n$selectedColor: #f6f7ff;\n$lighterBlue: #409EFF;\n\n.container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.components-list {\n  padding: 8px;\n  box-sizing: border-box;\n  height: 100%;\n  .components-item {\n    display: inline-block;\n    width: 48%;\n    margin: 1%;\n    transition: transform 0ms !important;\n  }\n}\n.components-draggable{\n  padding-bottom: 20px;\n}\n.components-title{\n  font-size: 14px;\n  color: #222;\n  margin: 6px 2px;\n  .svg-icon{\n    color: #666;\n    font-size: 18px;\n  }\n}\n\n.components-body {\n  padding: 8px 10px;\n  background: $selectedColor;\n  font-size: 12px;\n  cursor: move;\n  border: 1px dashed $selectedColor;\n  border-radius: 3px;\n  .svg-icon{\n    color: #777;\n    font-size: 15px;\n  }\n  &:hover {\n    border: 1px dashed #787be8;\n    color: #787be8;\n    .svg-icon {\n      color: #787be8;\n    }\n  }\n}\n\n.left-board {\n  width: 260px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100vh;\n}\n.left-scrollbar{\n  height: calc(100vh - 42px);\n  overflow: hidden;\n}\n.center-scrollbar {\n  height: calc(100vh - 42px);\n  overflow: hidden;\n  border-left: 1px solid #f1e8e8;\n  border-right: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.center-board {\n  height: 100vh;\n  width: auto;\n  margin: 0 350px 0 260px;\n  box-sizing: border-box;\n}\n.empty-info{\n  position: absolute;\n  top: 46%;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 18px;\n  color: #ccb1ea;\n  letter-spacing: 4px;\n}\n.action-bar{\n  position: relative;\n  height: 42px;\n  text-align: right;\n  padding: 0 15px;\n  box-sizing: border-box;;\n  border: 1px solid #f1e8e8;\n  border-top: none;\n  border-left: none;\n  .delete-btn{\n    color: #F56C6C;\n  }\n}\n.logo-wrapper{\n  position: relative;\n  height: 42px;\n  background: #fff;\n  border-bottom: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.logo{\n  position: absolute;\n  left: 12px;\n  top: 6px;\n  line-height: 30px;\n  color: #00afff;\n  font-weight: 600;\n  font-size: 17px;\n  white-space: nowrap;\n  > img{\n    width: 30px;\n    height: 30px;\n    vertical-align: top;\n  }\n  .github{\n    display: inline-block;\n    vertical-align: sub;\n    margin-left: 15px;\n    > img{\n      height: 22px;\n    }\n  }\n}\n\n.center-board-row {\n  padding: 12px 12px 15px 12px;\n  box-sizing: border-box;\n  & > .el-form {\n    // 69 = 12+15+42\n    height: calc(100vh - 69px);\n  }\n}\n.drawing-board {\n  height: 100%;\n  position: relative;\n  .components-body {\n    padding: 0;\n    margin: 0;\n    font-size: 0;\n  }\n  .sortable-ghost {\n    position: relative;\n    display: block;\n    overflow: hidden;\n    &::before {\n      content: \" \";\n      position: absolute;\n      left: 0;\n      right: 0;\n      top: 0;\n      height: 3px;\n      background: rgb(89, 89, 223);\n      z-index: 2;\n    }\n  }\n  .components-item.sortable-ghost {\n    width: 100%;\n    height: 60px;\n    background-color: $selectedColor;\n  }\n  .active-from-item {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n    & > .component-name{\n      color: $lighterBlue;\n    }\n  }\n  .el-form-item{\n    margin-bottom: 15px;\n  }\n}\n.drawing-item{\n  position: relative;\n  cursor: move;\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\n    border: 1px dashed #ccc;\n  }\n  .el-form-item{\n    padding: 12px 10px;\n  }\n}\n.drawing-row-item{\n  position: relative;\n  cursor: move;\n  box-sizing: border-box;\n  border: 1px dashed #ccc;\n  border-radius: 3px;\n  padding: 0 2px;\n  margin-bottom: 15px;\n  .drawing-row-item {\n    margin-bottom: 2px;\n  }\n  .el-col{\n    margin-top: 22px;\n  }\n  .el-form-item{\n    margin-bottom: 0;\n  }\n  .drag-wrapper{\n    min-height: 80px;\n  }\n  &.active-from-item{\n    border: 1px dashed $lighterBlue;\n  }\n  .component-name{\n    position: absolute;\n    top: 0;\n    left: 0;\n    font-size: 12px;\n    color: #bbb;\n    display: inline-block;\n    padding: 0 6px;\n  }\n}\n.drawing-item, .drawing-row-item{\n  &:hover {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n  }\n  & > .drawing-item-copy, & > .drawing-item-delete{\n    display: none;\n    position: absolute;\n    top: -10px;\n    width: 22px;\n    height: 22px;\n    line-height: 22px;\n    text-align: center;\n    border-radius: 50%;\n    font-size: 12px;\n    border: 1px solid;\n    cursor: pointer;\n    z-index: 1;\n  }\n  & > .drawing-item-copy{\n    right: 56px;\n    border-color: $lighterBlue;\n    color: $lighterBlue;\n    background: #fff;\n    &:hover{\n      background: $lighterBlue;\n      color: #fff;\n    }\n  }\n  & > .drawing-item-delete{\n    right: 24px;\n    border-color: #F56C6C;\n    color: #F56C6C;\n    background: #fff;\n    &:hover{\n      background: #F56C6C;\n      color: #fff;\n    }\n  }\n}\n</style>\n"]}]}