{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\authRole.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\authRole.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["authRole.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "authRole.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <h4 class=\"form-header h4\">基本信息</h4>\n    <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\n      <el-row>\n        <el-col :span=\"8\" :offset=\"2\">\n          <el-form-item label=\"用户昵称\" prop=\"nickName\">\n            <el-input v-model=\"form.nickName\" disabled />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"8\" :offset=\"2\">\n          <el-form-item label=\"登录账号\" prop=\"userName\">\n            <el-input  v-model=\"form.userName\" disabled />\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <h4 class=\"form-header h4\">角色信息</h4>\n    <el-table v-loading=\"loading\" :row-key=\"getRowKey\" @row-click=\"clickRow\" ref=\"table\" @selection-change=\"handleSelectionChange\" :data=\"roles.slice((pageNum-1)*pageSize,pageNum*pageSize)\">\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column type=\"selection\" :reserve-selection=\"true\" width=\"55\"></el-table-column>\n      <el-table-column label=\"角色编号\" align=\"center\" prop=\"roleId\" />\n      <el-table-column label=\"角色名称\" align=\"center\" prop=\"roleName\" />\n      <el-table-column label=\"权限字符\" align=\"center\" prop=\"roleKey\" />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\n\n    <el-form label-width=\"100px\">\n      <el-form-item style=\"text-align: center;margin-left:-120px;margin-top:30px;\">\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\n        <el-button @click=\"close()\">返回</el-button>\n      </el-form-item>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { getAuthRole, updateAuthRole } from \"@/api/system/user\";\n\nexport default {\n  name: \"AuthRole\",\n  data() {\n    return {\n       // 遮罩层\n      loading: true,\n      // 分页信息\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      // 选中角色编号\n      roleIds:[],\n      // 角色信息\n      roles: [],\n      // 用户信息\n      form: {}\n    };\n  },\n  created() {\n    const userId = this.$route.params && this.$route.params.userId;\n    if (userId) {\n      this.loading = true;\n      getAuthRole(userId).then((response) => {\n        this.form = response.user;\n        this.roles = response.roles;\n        this.total = this.roles.length;\n        this.$nextTick(() => {\n          this.roles.forEach((row) => {\n            if (row.flag) {\n              this.$refs.table.toggleRowSelection(row);\n            }\n          });\n        });\n        this.loading = false;\n      });\n    }\n  },\n  methods: {\n    /** 单击选中行数据 */\n    clickRow(row) {\n      this.$refs.table.toggleRowSelection(row);\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.roleIds = selection.map((item) => item.roleId);\n    },\n    // 保存选中的数据编号\n    getRowKey(row) {\n      return row.roleId;\n    },\n    /** 提交按钮 */\n    submitForm() {\n      const userId = this.form.userId;\n      const roleIds = this.roleIds.join(\",\");\n      updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {\n        this.$modal.msgSuccess(\"授权成功\");\n        this.close();\n      });\n    },\n    /** 关闭按钮 */\n    close() {\n      const obj = { path: \"/system/user\" };\n      this.$tab.closeOpenPage(obj);\n    },\n  },\n};\n</script>"]}]}