{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\hour.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\hour.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["hour.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "hour.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n\t<el-form size=\"small\">\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\n\t\t\t\t小时，允许的通配符[, - * /]\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\n\t\t\t\t周期从\n\t\t\t\t<el-input-number v-model='cycle01' :min=\"0\" :max=\"22\" /> -\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : 1\" :max=\"23\" /> 小时\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\n\t\t\t\t从\n\t\t\t\t<el-input-number v-model='average01' :min=\"0\" :max=\"22\" /> 小时开始，每\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"23 - average01 || 0\" /> 小时执行一次\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\n\t\t\t\t指定\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\n\t\t\t\t\t<el-option v-for=\"item in 24\" :key=\"item\" :value=\"item-1\">{{item-1}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\t</el-form>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tradioValue: 1,\n\t\t\tcycle01: 0,\n\t\t\tcycle02: 1,\n\t\t\taverage01: 0,\n\t\t\taverage02: 1,\n\t\t\tcheckboxList: [],\n\t\t\tcheckNum: this.$options.propsData.check\n\t\t}\n\t},\n\tname: 'crontab-hour',\n\tprops: ['check', 'cron'],\n\tmethods: {\n\t\t// 单选按钮值变化时\n\t\tradioChange() {\n\t\t\tif (this.cron.min === '*') {\n\t\t\t    this.$emit('update', 'min', '0', 'hour');\n\t\t\t}\n\t\t\tif (this.cron.second === '*') {\n\t\t\t    this.$emit('update', 'second', '0', 'hour');\n\t\t\t}\n\t\t\tswitch (this.radioValue) {\n\t\t\t\tcase 1:\n\t\t\t\t\tthis.$emit('update', 'hour', '*')\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tthis.$emit('update', 'hour', this.cycleTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tthis.$emit('update', 'hour', this.averageTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tthis.$emit('update', 'hour', this.checkboxString);\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 周期两个值变化时\n\t\tcycleChange() {\n\t\t\tif (this.radioValue == '2') {\n\t\t\t\tthis.$emit('update', 'hour', this.cycleTotal);\n\t\t\t}\n\t\t},\n\t\t// 平均两个值变化时\n\t\taverageChange() {\n\t\t\tif (this.radioValue == '3') {\n\t\t\t\tthis.$emit('update', 'hour', this.averageTotal);\n\t\t\t}\n\t\t},\n\t\t// checkbox值变化时\n\t\tcheckboxChange() {\n\t\t\tif (this.radioValue == '4') {\n\t\t\t\tthis.$emit('update', 'hour', this.checkboxString);\n\t\t\t}\n\t\t}\n\t},\n\twatch: {\n\t\t'radioValue': 'radioChange',\n\t\t'cycleTotal': 'cycleChange',\n\t\t'averageTotal': 'averageChange',\n\t\t'checkboxString': 'checkboxChange'\n\t},\n\tcomputed: {\n\t\t// 计算两个周期值\n\t\tcycleTotal: function () {\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, 0, 22)\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 23)\n\t\t\treturn cycle01 + '-' + cycle02;\n\t\t},\n\t\t// 计算平均用到的值\n\t\taverageTotal: function () {\n\t\t\tconst average01 = this.checkNum(this.average01, 0, 22)\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 23 - average01 || 0)\n\t\t\treturn average01 + '/' + average02;\n\t\t},\n\t\t// 计算勾选的checkbox值合集\n\t\tcheckboxString: function () {\n\t\t\tlet str = this.checkboxList.join();\n\t\t\treturn str == '' ? '*' : str;\n\t\t}\n\t}\n}\n</script>\n"]}]}