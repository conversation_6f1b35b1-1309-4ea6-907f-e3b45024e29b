{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\SizeSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\SizeSelect\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2l6ZU9wdGlvbnM6IFsKICAgICAgICB7IGxhYmVsOiAnRGVmYXVsdCcsIHZhbHVlOiAnZGVmYXVsdCcgfSwKICAgICAgICB7IGxhYmVsOiAnTWVkaXVtJywgdmFsdWU6ICdtZWRpdW0nIH0sCiAgICAgICAgeyBsYWJlbDogJ1NtYWxsJywgdmFsdWU6ICdzbWFsbCcgfSwKICAgICAgICB7IGxhYmVsOiAnTWluaScsIHZhbHVlOiAnbWluaScgfQogICAgICBdCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgc2l6ZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuc2l6ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlU2V0U2l6ZShzaXplKSB7CiAgICAgIHRoaXMuJEVMRU1FTlQuc2l6ZSA9IHNpemUKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRTaXplJywgc2l6ZSkKICAgICAgdGhpcy5yZWZyZXNoVmlldygpCiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIG1lc3NhZ2U6ICdTd2l0Y2ggU2l6ZSBTdWNjZXNzJywKICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgfSkKICAgIH0sCiAgICByZWZyZXNoVmlldygpIHsKICAgICAgLy8gSW4gb3JkZXIgdG8gbWFrZSB0aGUgY2FjaGVkIHBhZ2UgcmUtcmVuZGVyZWQKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbEFsbENhY2hlZFZpZXdzJywgdGhpcy4kcm91dGUpCgogICAgICBjb25zdCB7IGZ1bGxQYXRoIH0gPSB0aGlzLiRyb3V0ZQoKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsKICAgICAgICAgIHBhdGg6ICcvcmVkaXJlY3QnICsgZnVsbFBhdGgKICAgICAgICB9KQogICAgICB9KQogICAgfQogIH0KCn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/components/SizeSelect", "sourcesContent": ["<template>\n  <el-dropdown trigger=\"click\" @command=\"handleSetSize\">\n    <div>\n      <svg-icon class-name=\"size-icon\" icon-class=\"size\" />\n    </div>\n    <el-dropdown-menu slot=\"dropdown\">\n      <el-dropdown-item v-for=\"item of sizeOptions\" :key=\"item.value\" :disabled=\"size===item.value\" :command=\"item.value\">\n        {{ item.label }}\n      </el-dropdown-item>\n    </el-dropdown-menu>\n  </el-dropdown>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      sizeOptions: [\n        { label: 'Default', value: 'default' },\n        { label: 'Medium', value: 'medium' },\n        { label: 'Small', value: 'small' },\n        { label: 'Mini', value: 'mini' }\n      ]\n    }\n  },\n  computed: {\n    size() {\n      return this.$store.getters.size\n    }\n  },\n  methods: {\n    handleSetSize(size) {\n      this.$ELEMENT.size = size\n      this.$store.dispatch('app/setSize', size)\n      this.refreshView()\n      this.$message({\n        message: 'Switch Size Success',\n        type: 'success'\n      })\n    },\n    refreshView() {\n      // In order to make the cached page re-rendered\n      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)\n\n      const { fullPath } = this.$route\n\n      this.$nextTick(() => {\n        this.$router.replace({\n          path: '/redirect' + fullPath\n        })\n      })\n    }\n  }\n\n}\n</script>\n"]}]}