{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\CodeTypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\CodeTypeDialog.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBpbmhlcml0QXR0cnM6IGZhbHNlLAogIHByb3BzOiBbJ3Nob3dGaWxlTmFtZSddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmb3JtRGF0YTogewogICAgICAgIGZpbGVOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgdHlwZTogJ2ZpbGUnCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZmlsZU5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXmlofku7blkI0nLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgdHlwZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+eUn+aIkOexu+Wei+S4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHR5cGVPcHRpb25zOiBbewogICAgICAgIGxhYmVsOiAn6aG16Z2iJywKICAgICAgICB2YWx1ZTogJ2ZpbGUnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+W8ueeqlycsCiAgICAgICAgdmFsdWU6ICdkaWFsb2cnCiAgICAgIH1dCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogIH0sCiAgd2F0Y2g6IHt9LAogIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICBvbk9wZW4oKSB7CiAgICAgIGlmICh0aGlzLnNob3dGaWxlTmFtZSkgewogICAgICAgIHRoaXMuZm9ybURhdGEuZmlsZU5hbWUgPSBgJHsrbmV3IERhdGUoKX0udnVlYAogICAgICB9CiAgICB9LAogICAgb25DbG9zZSgpIHsKICAgIH0sCiAgICBjbG9zZShlKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgZmFsc2UpCiAgICB9LAogICAgaGFuZGxlQ29uZmlybSgpIHsKICAgICAgdGhpcy4kcmVmcy5lbEZvcm0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICghdmFsaWQpIHJldHVybgogICAgICAgIHRoaXMuJGVtaXQoJ2NvbmZpcm0nLCB7IC4uLnRoaXMuZm9ybURhdGEgfSkKICAgICAgICB0aGlzLmNsb3NlKCkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["CodeTypeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CodeTypeDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"15\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"medium\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item label=\"生成类型\" prop=\"type\">\n              <el-radio-group v-model=\"formData.type\">\n                <el-radio-button\n                  v-for=\"(item, index) in typeOptions\"\n                  :key=\"index\"\n                  :label=\"item.value\"\n                  :disabled=\"item.disabled\"\n                >\n                  {{ item.label }}\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\n              <el-input v-model=\"formData.fileName\" placeholder=\"请输入文件名\" clearable />\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n\n      <div slot=\"footer\">\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  inheritAttrs: false,\n  props: ['showFileName'],\n  data() {\n    return {\n      formData: {\n        fileName: undefined,\n        type: 'file'\n      },\n      rules: {\n        fileName: [{\n          required: true,\n          message: '请输入文件名',\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '生成类型不能为空',\n          trigger: 'change'\n        }]\n      },\n      typeOptions: [{\n        label: '页面',\n        value: 'file'\n      }, {\n        label: '弹窗',\n        value: 'dialog'\n      }]\n    }\n  },\n  computed: {\n  },\n  watch: {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      if (this.showFileName) {\n        this.formData.fileName = `${+new Date()}.vue`\n      }\n    },\n    onClose() {\n    },\n    close(e) {\n      this.$emit('update:visible', false)\n    },\n    handleConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        this.$emit('confirm', { ...this.formData })\n        this.close()\n      })\n    }\n  }\n}\n</script>\n"]}]}