{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\dept.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\dept.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZERlcHQgPSBhZGREZXB0OwpleHBvcnRzLmRlbERlcHQgPSBkZWxEZXB0OwpleHBvcnRzLmdldERlcHQgPSBnZXREZXB0OwpleHBvcnRzLmxpc3REZXB0ID0gbGlzdERlcHQ7CmV4cG9ydHMubGlzdERlcHRFeGNsdWRlQ2hpbGQgPSBsaXN0RGVwdEV4Y2x1ZGVDaGlsZDsKZXhwb3J0cy51cGRhdGVEZXB0ID0gdXBkYXRlRGVwdDsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivoumDqOmXqOWIl+ihqApmdW5jdGlvbiBsaXN0RGVwdChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0L2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6YOo6Zeo5YiX6KGo77yI5o6S6Zmk6IqC54K577yJCmZ1bmN0aW9uIGxpc3REZXB0RXhjbHVkZUNoaWxkKGRlcHRJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0L2xpc3QvZXhjbHVkZS8nICsgZGVwdElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmn6Xor6Lpg6jpl6jor6bnu4YKZnVuY3Rpb24gZ2V0RGVwdChkZXB0SWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdC8nICsgZGVwdElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7pg6jpl6gKZnVuY3Rpb24gYWRkRGVwdChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcHQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuemDqOmXqApmdW5jdGlvbiB1cGRhdGVEZXB0KGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTpg6jpl6gKZnVuY3Rpb24gZGVsRGVwdChkZXB0SWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdC8nICsgZGVwdElkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDept", "query", "request", "url", "method", "params", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deptId", "getDept", "addDept", "data", "updateDept", "delDept"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/system/dept.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询部门列表\nexport function listDept(query) {\n  return request({\n    url: '/system/dept/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询部门列表（排除节点）\nexport function listDeptExcludeChild(deptId) {\n  return request({\n    url: '/system/dept/list/exclude/' + deptId,\n    method: 'get'\n  })\n}\n\n// 查询部门详细\nexport function getDept(deptId) {\n  return request({\n    url: '/system/dept/' + deptId,\n    method: 'get'\n  })\n}\n\n// 新增部门\nexport function addDept(data) {\n  return request({\n    url: '/system/dept',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改部门\nexport function updateDept(data) {\n  return request({\n    url: '/system/dept',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除部门\nexport function delDept(deptId) {\n  return request({\n    url: '/system/dept/' + deptId,\n    method: 'delete'\n  })\n}"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGI,MAAM;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACD,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}