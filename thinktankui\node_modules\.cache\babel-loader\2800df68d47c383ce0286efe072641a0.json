{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\iFrame\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\iFrame\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBwcm9wczogewogICAgc3JjOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoZWlnaHQ6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4OyIsCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIHVybDogdGhpcy5zcmMKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICB9LCAzMDApOwogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgd2luZG93Lm9ucmVzaXplID0gZnVuY3Rpb24gdGVtcCgpIHsKICAgICAgdGhhdC5oZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiOwogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["props", "src", "type", "String", "required", "data", "height", "document", "documentElement", "clientHeight", "loading", "url", "mounted", "_this", "setTimeout", "that", "window", "onresize", "temp"], "sources": ["src/components/iFrame/index.vue"], "sourcesContent": ["<template>\n  <div v-loading=\"loading\" :style=\"'height:' + height\">\n    <iframe\n      :src=\"src\"\n      frameborder=\"no\"\n      style=\"width: 100%; height: 100%\"\n      scrolling=\"auto\"\n    />\n  </div>\n</template>\n<script>\nexport default {\n  props: {\n    src: {\n      type: String,\n      required: true\n    },\n  },\n  data() {\n    return {\n      height: document.documentElement.clientHeight - 94.5 + \"px;\",\n      loading: true,\n      url: this.src\n    };\n  },\n  mounted: function () {\n    setTimeout(() => {\n      this.loading = false;\n    }, 300);\n    const that = this;\n    window.onresize = function temp() {\n      that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\n    };\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;iCAWA;EACAA,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACAC,OAAA;MACAC,GAAA,OAAAV;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,UAAA;MACAD,KAAA,CAAAH,OAAA;IACA;IACA,IAAAK,IAAA;IACAC,MAAA,CAAAC,QAAA,YAAAC,KAAA;MACAH,IAAA,CAAAT,MAAA,GAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;IACA;EACA;AACA", "ignoreList": []}]}