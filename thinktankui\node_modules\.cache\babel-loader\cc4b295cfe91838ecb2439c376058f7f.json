{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\blots\\inline.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\blots\\inline.js", "mtime": 1749105929558}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_break", "_interopRequireDefault", "_text", "_Inline", "Inline", "_InlineBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "formatAt", "index", "length", "name", "compare", "statics", "blotName", "scroll", "query", "<PERSON><PERSON>", "BLOT", "blot", "isolate", "wrap", "_superPropGet2", "optimize", "context", "parent", "offset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "self", "other", "selfIndex", "order", "indexOf", "otherIndex", "InlineBlot", "_defineProperty2", "Break", "EmbedBlot", "Text", "_default", "exports"], "sources": ["../../src/blots/inline.ts"], "sourcesContent": ["import { EmbedBlot, InlineBlot, Scope } from 'parchment';\nimport type { BlotConstructor } from 'parchment';\nimport Break from './break.js';\nimport Text from './text.js';\n\nclass Inline extends InlineBlot {\n  static allowedChildren: BlotConstructor[] = [Inline, Break, EmbedBlot, Text];\n  // Lower index means deeper in the DOM tree, since not found (-1) is for embeds\n  static order = [\n    'cursor',\n    'inline', // Must be lower\n    'link', // Chrome wants <a> to be lower\n    'underline',\n    'strike',\n    'italic',\n    'bold',\n    'script',\n    'code', // Must be higher\n  ];\n\n  static compare(self: string, other: string) {\n    const selfIndex = Inline.order.indexOf(self);\n    const otherIndex = Inline.order.indexOf(other);\n    if (selfIndex >= 0 || otherIndex >= 0) {\n      return selfIndex - otherIndex;\n    }\n    if (self === other) {\n      return 0;\n    }\n    if (self < other) {\n      return -1;\n    }\n    return 1;\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (\n      Inline.compare(this.statics.blotName, name) < 0 &&\n      this.scroll.query(name, Scope.BLOT)\n    ) {\n      const blot = this.isolate(index, length);\n      if (value) {\n        blot.wrap(name, value);\n      }\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    if (\n      this.parent instanceof Inline &&\n      Inline.compare(this.statics.blotName, this.parent.statics.blotName) > 0\n    ) {\n      const parent = this.parent.isolate(this.offset(), this.length());\n      // @ts-expect-error TODO: make isolate generic\n      this.moveChildren(parent);\n      parent.wrap(this);\n    }\n  }\n}\n\nexport default Inline;\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AAA4B,IAAAI,OAAA;AAAA,IAEtBC,MAAM,0BAAAC,WAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,MAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,MAAA,EAAAC,WAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,MAAA;IAAAQ,GAAA;IAAAC,KAAA,EA8BV,SAAAC,QAAQA,CAACC,KAAa,EAAEC,MAAc,EAAEC,IAAY,EAAEJ,KAAc,EAAE;MACpE,IACET,MAAM,CAACc,OAAO,CAAC,IAAI,CAACC,OAAO,CAACC,QAAQ,EAAEH,IAAI,CAAC,GAAG,CAAC,IAC/C,IAAI,CAACI,MAAM,CAACC,KAAK,CAACL,IAAI,EAAEM,gBAAK,CAACC,IAAI,CAAC,EACnC;QACA,IAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACX,KAAK,EAAEC,MAAM,CAAC;QACxC,IAAIH,KAAK,EAAE;UACTY,IAAI,CAACE,IAAI,CAACV,IAAI,EAAEJ,KAAK,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAAe,cAAA,CAAArB,OAAA,EAAAH,MAAA,wBAAeW,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,KAAK;MAC3C;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAgB,QAAQA,CAACC,OAA+B,EAAE;MACxC,IAAAF,cAAA,CAAArB,OAAA,EAAAH,MAAA,wBAAe0B,OAAO;MACtB,IACE,IAAI,CAACC,MAAM,YAAY3B,MAAM,IAC7BA,MAAM,CAACc,OAAO,CAAC,IAAI,CAACC,OAAO,CAACC,QAAQ,EAAE,IAAI,CAACW,MAAM,CAACZ,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,EACvE;QACA,IAAMW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACL,OAAO,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAAChB,MAAM,CAAC,CAAC,CAAC;QAChE;QACA,IAAI,CAACiB,YAAY,CAACF,MAAM,CAAC;QACzBA,MAAM,CAACJ,IAAI,CAAC,IAAI,CAAC;MACnB;IACF;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAxCA,SAAOK,OAAOA,CAACgB,IAAY,EAAEC,KAAa,EAAE;MAC1C,IAAMC,SAAS,GAAGhC,MAAM,CAACiC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC;MAC5C,IAAMK,UAAU,GAAGnC,MAAM,CAACiC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC;MAC9C,IAAIC,SAAS,IAAI,CAAC,IAAIG,UAAU,IAAI,CAAC,EAAE;QACrC,OAAOH,SAAS,GAAGG,UAAU;MAC/B;MACA,IAAIL,IAAI,KAAKC,KAAK,EAAE;QAClB,OAAO,CAAC;MACV;MACA,IAAID,IAAI,GAAGC,KAAK,EAAE;QAChB,OAAO,CAAC,CAAC;MACX;MACA,OAAO,CAAC;IACV;EAAA;AAAA,EA5BmBK,qBAAU;AAAArC,OAAA,GAAzBC,MAAM;AAAA,IAAAqC,gBAAA,CAAAlC,OAAA,EAANH,MAAM,qBACkC,CAACA,OAAM,EAAEsC,cAAK,EAAEC,oBAAS,EAAEC,aAAI,CAAC;AAC5E;AAAA,IAAAH,gBAAA,CAAAlC,OAAA,EAFIH,MAAM,WAGK,CACb,QAAQ,EACR,QAAQ;AAAE;AACV,MAAM;AAAE;AACR,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,CAAE;AAAA,CACT;AAAA,IAAAyC,QAAA,GAAAC,OAAA,CAAAvC,OAAA,GA6CYH,MAAM", "ignoreList": []}]}