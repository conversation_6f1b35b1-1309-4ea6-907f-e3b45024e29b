{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\HeaderSearch\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\n  <div :class=\"{'show':show}\" class=\"header-search\">\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\n    <el-select\n      ref=\"headerSearchSelect\"\n      v-model=\"search\"\n      :remote-method=\"querySearch\"\n      filterable\n      default-first-option\n      remote\n      placeholder=\"Search\"\n      class=\"header-search-select\"\n      @change=\"change\"\n    >\n      <el-option v-for=\"option in options\" :key=\"option.item.path\" :value=\"option.item\" :label=\"option.item.title.join(' > ')\" />\n    </el-select>\n  </div>\n</template>\n\n<script>\n// fuse is a lightweight fuzzy-search module\n// make search results more in line with expectations\nimport Fuse from 'fuse.js/dist/fuse.min.js'\nimport path from 'path'\nimport { isHttp } from '@/utils/validate'\n\nexport default {\n  name: 'HeaderSearch',\n  data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      show: false,\n      fuse: undefined\n    }\n  },\n  computed: {\n    routes() {\n      return this.$store.getters.permission_routes\n    }\n  },\n  watch: {\n    routes() {\n      this.searchPool = this.generateRoutes(this.routes)\n    },\n    searchPool(list) {\n      this.initFuse(list)\n    },\n    show(value) {\n      if (value) {\n        document.body.addEventListener('click', this.close)\n      } else {\n        document.body.removeEventListener('click', this.close)\n      }\n    }\n  },\n  mounted() {\n    this.searchPool = this.generateRoutes(this.routes)\n  },\n  methods: {\n    click() {\n      this.show = !this.show\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\n      }\n    },\n    close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\n      this.options = []\n      this.show = false\n    },\n    change(val) {\n      const path = val.path;\n      const query = val.query;\n      if(isHttp(val.path)) {\n        // http(s):// 路径新窗口打开\n        const pindex = path.indexOf(\"http\");\n        window.open(path.substr(pindex, path.length), \"_blank\");\n      } else {\n        if (query) {\n          this.$router.push({ path: path, query: JSON.parse(query) });\n        } else {\n          this.$router.push(path)\n        }\n      }\n      this.search = ''\n      this.options = []\n      this.$nextTick(() => {\n        this.show = false\n      })\n    },\n    initFuse(list) {\n      this.fuse = new Fuse(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        minMatchCharLength: 1,\n        keys: [{\n          name: 'title',\n          weight: 0.7\n        }, {\n          name: 'path',\n          weight: 0.3\n        }]\n      })\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\n      let res = []\n\n      for (const router of routes) {\n        // skip hidden router\n        if (router.hidden) { continue }\n\n        const data = {\n          path: !isHttp(router.path) ? path.resolve(basePath, router.path) : router.path,\n          title: [...prefixTitle]\n        }\n\n        if (router.meta && router.meta.title) {\n          data.title = [...data.title, router.meta.title]\n\n          if (router.redirect !== 'noRedirect') {\n            // only push the routes with title\n            // special case: need to exclude parent router without redirect\n            res.push(data)\n          }\n        }\n\n        if (router.query) {\n          data.query = router.query\n        }\n\n        // recursive child routes\n        if (router.children) {\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\n          if (tempRoutes.length >= 1) {\n            res = [...res, ...tempRoutes]\n          }\n        }\n      }\n      return res\n    },\n    querySearch(query) {\n      if (query !== '') {\n        this.options = this.fuse.search(query)\n      } else {\n        this.options = []\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.header-search {\n  font-size: 0 !important;\n\n  .search-icon {\n    cursor: pointer;\n    font-size: 18px;\n    vertical-align: middle;\n  }\n\n  .header-search-select {\n    font-size: 18px;\n    transition: width 0.2s;\n    width: 0;\n    overflow: hidden;\n    background: transparent;\n    border-radius: 0;\n    display: inline-block;\n    vertical-align: middle;\n\n    ::v-deep .el-input__inner {\n      border-radius: 0;\n      border: 0;\n      padding-left: 0;\n      padding-right: 0;\n      box-shadow: none !important;\n      border-bottom: 1px solid #d9d9d9;\n      vertical-align: middle;\n    }\n  }\n\n  &.show {\n    .header-search-select {\n      width: 210px;\n      margin-left: 10px;\n    }\n  }\n}\n</style>\n"]}]}