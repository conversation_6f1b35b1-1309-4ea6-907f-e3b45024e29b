<template>
  <div class="app-container">
    <div class="page-header">
      <h2>事件分析</h2>
      <div class="header-actions">
        <el-button type="primary" size="small">新建分析</el-button>
        <el-button size="small">导出分析</el-button>
      </div>
    </div>

    <!-- 创建事件分析对话框 -->
    <el-dialog title="新建事件" :visible.sync="dialogVisible" width="600px">
      <div class="dialog-content">
        <el-form :model="eventForm" label-width="80px">
          <el-form-item label="事件名称:">
            <el-input v-model="eventForm.name" placeholder="请输入事件名称"></el-input>
          </el-form-item>
          <el-form-item label="时间范围:">
            <el-date-picker
              v-model="eventForm.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="关键词:">
            <el-input
              type="textarea"
              v-model="eventForm.keywords"
              placeholder="多个关键词用逗号分隔"
              :rows="3"
            ></el-input>
          </el-form-item>
          <el-form-item label="分析词:">
            <div class="analysis-word-container">
              <div class="word-input-section">
                <el-button type="primary" size="mini" icon="el-icon-plus">+</el-button>
                <el-button size="mini">|</el-button>
                <el-button size="mini">||</el-button>
                <el-button size="mini">()</el-button>
                <el-button type="primary" size="mini">获取分词</el-button>
                <i class="el-icon-question analysis-help-icon"></i>
              </div>
              <el-input
                type="textarea"
                v-model="eventForm.analysisWords"
                placeholder="请输入分析词"
                :rows="3"
                class="analysis-textarea"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="排除词:">
            <el-input
              type="textarea"
              v-model="eventForm.excludeWords"
              placeholder="多个排除词之间以"，"隔开，词汇、词组之间以"与"关系"
              :rows="3"
            ></el-input>
          </el-form-item>
          <el-form-item label="监控内容:">
            <el-checkbox-group v-model="eventForm.monitorTypes">
              <el-checkbox label="web">网页</el-checkbox>
              <el-checkbox label="weibo">微博</el-checkbox>
              <el-checkbox label="toutiao">头条号</el-checkbox>
              <el-checkbox label="app">APP</el-checkbox>
              <el-checkbox label="video">视频</el-checkbox>
              <el-checkbox label="sms">短信</el-checkbox>
              <el-checkbox label="newspaper">报刊</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEventForm">确定</el-button>
      </span>
    </el-dialog>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧导航 -->
      <div class="left-nav">
        <div class="nav-header">
          <span>事件分析</span>
          <i class="el-icon-plus" @click="dialogVisible = true"></i>
        </div>
        <el-input
          placeholder="搜索分析项"
          prefix-icon="el-icon-search"
          v-model="searchQuery"
          clearable
          size="small"
          class="search-input"
        ></el-input>
        <div class="nav-list">
          <div
            v-for="(item, index) in filteredAnalysisList"
            :key="index"
            class="nav-item"
            :class="{ 'active': currentAnalysis === item }"
            @click="selectAnalysis(item)"
          >
            <span>{{ item.name }}</span>
            <span class="item-date">{{ item.date }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <div v-if="currentAnalysis" class="analysis-detail">
          <div class="detail-header">
            <h3>{{ currentAnalysis.name }}</h3>
            <div class="detail-actions">
              <el-button size="mini" type="primary" icon="el-icon-refresh">刷新</el-button>
              <el-button size="mini" icon="el-icon-download">导出</el-button>
            </div>
          </div>

          <!-- 筛选条件 -->
          <div class="filter-section">
            <el-form :inline="true" size="small">
              <el-form-item label="时间范围:">
                <el-radio-group v-model="timeRange">
                  <el-radio-button label="today">今天</el-radio-button>
                  <el-radio-button label="yesterday">昨天</el-radio-button>
                  <el-radio-button label="7d">近七天</el-radio-button>
                  <el-radio-button label="30d">近30天</el-radio-button>
                  <el-radio-button label="custom">自定义</el-radio-button>
                </el-radio-group>
                <el-date-picker
                  v-if="timeRange === 'custom'"
                  v-model="customTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="margin-left: 10px; width: 240px;"
                ></el-date-picker>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据概览 -->
          <el-row :gutter="20" class="data-overview">
            <el-col :span="6">
              <el-card shadow="hover">
                <div class="overview-item">
                  <div class="item-label">信息总量</div>
                  <div class="item-value">{{ currentAnalysis.totalInfo }}</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover">
                <div class="overview-item">
                  <div class="item-label">正面声量</div>
                  <div class="item-value positive">{{ currentAnalysis.positiveCount }}</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover">
                <div class="overview-item">
                  <div class="item-label">中性声量</div>
                  <div class="item-value neutral">{{ currentAnalysis.neutralCount }}</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover">
                <div class="overview-item">
                  <div class="item-label">负面声量</div>
                  <div class="item-value negative">{{ currentAnalysis.negativeCount }}</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 图表区域 -->
          <div class="charts-container">
            <el-card class="chart-card">
              <div slot="header">
                <span>事件趋势分析</span>
              </div>
              <div class="chart" ref="trendChart"></div>
            </el-card>
          </div>
        </div>
        <div v-else class="empty-state">
          <i class="el-icon-data-analysis"></i>
          <p>请选择或创建一个事件分析项目</p>
          <el-button type="primary" @click="dialogVisible = true">创建分析</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'EventAnalysis',
  data() {
    return {
      dialogVisible: false,
      searchQuery: '',
      currentAnalysis: null,
      timeRange: 'today',
      customTimeRange: [],
      eventForm: {
        name: '',
        timeRange: [],
        keywords: '',
        analysisWords: '',
        excludeWords: '',
        monitorTypes: ['web', 'weibo']
      },
      analysisList: [
        {
          name: '方太品牌事件分析',
          date: '2023-04-22',
          totalInfo: '4,653',
          positiveCount: '58',
          neutralCount: '4,583',
          negativeCount: '12'
        },
        {
          name: '竞品事件分析',
          date: '2023-04-20',
          totalInfo: '3,245',
          positiveCount: '42',
          neutralCount: '3,180',
          negativeCount: '23'
        }
      ],
      charts: {
        trendChart: null
      }
    }
  },
  computed: {
    filteredAnalysisList() {
      if (!this.searchQuery) return this.analysisList

      const query = this.searchQuery.toLowerCase()
      return this.analysisList.filter(item =>
        item.name.toLowerCase().includes(query)
      )
    }
  },
  mounted() {
    this.initCharts()
  },
  methods: {
    selectAnalysis(analysis) {
      this.currentAnalysis = analysis
      this.$nextTick(() => {
        this.renderCharts()
      })
    },
    submitEventForm() {
      // 处理表单提交
      const newAnalysis = {
        name: this.eventForm.name,
        date: new Date().toISOString().split('T')[0],
        totalInfo: '0',
        positiveCount: '0',
        neutralCount: '0',
        negativeCount: '0'
      }

      this.analysisList.unshift(newAnalysis)
      this.selectAnalysis(newAnalysis)
      this.dialogVisible = false

      // 重置表单
      this.eventForm = {
        name: '',
        timeRange: [],
        keywords: '',
        analysisWords: '',
        excludeWords: '',
        monitorTypes: ['web', 'weibo']
      }
    },
    initCharts() {
      if (this.currentAnalysis) {
        this.$nextTick(() => {
          if (this.$refs.trendChart) {
            this.charts.trendChart = echarts.init(this.$refs.trendChart)
            this.renderTrendChart()
          }
        })
      }
    },
    renderCharts() {
      if (this.$refs.trendChart) {
        if (!this.charts.trendChart) {
          this.charts.trendChart = echarts.init(this.$refs.trendChart)
        }
        this.renderTrendChart()
      }
    },
    renderTrendChart() {
      if (!this.charts.trendChart) return

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['总量', '正面', '中性', '负面']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '总量',
            type: 'line',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '正面',
            type: 'line',
            data: [20, 32, 21, 34, 20, 30, 10]
          },
          {
            name: '中性',
            type: 'line',
            data: [90, 82, 71, 84, 60, 190, 190]
          },
          {
            name: '负面',
            type: 'line',
            data: [10, 18, 9, 16, 10, 10, 10]
          }
        ]
      }

      this.charts.trendChart.setOption(option)
    }
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      chart && chart.dispose()
    })
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.main-content {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 150px);
}

.left-nav {
  width: 250px;
  border-right: 1px solid #e6e6e6;

  .nav-header {
    padding: 15px;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    justify-content: space-between;
    align-items: center;

    i {
      cursor: pointer;
      color: #409EFF;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .search-input {
    padding: 10px;
  }

  .nav-list {
    .nav-item {
      padding: 12px 15px;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      flex-direction: column;

      &:hover {
        background-color: #f5f7fa;
      }

      &.active {
        background-color: #ecf5ff;
        color: #409EFF;
        border-right: 3px solid #409EFF;
      }

      .item-date {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
}

.right-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .filter-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .data-overview {
    margin-bottom: 20px;

    .overview-item {
      text-align: center;

      .item-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 5px;
      }

      .item-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;

        &.positive {
          color: #67C23A;
        }

        &.neutral {
          color: #E6A23C;
        }

        &.negative {
          color: #F56C6C;
        }
      }
    }
  }

  .charts-container {
    .chart-card {
      margin-bottom: 20px;

      .chart {
        height: 300px;
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 20px;
    }

    p {
      margin-bottom: 20px;
    }
  }
}

.dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 分析词容器样式 */
.analysis-word-container {
  .word-input-section {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 8px;

    .el-button {
      padding: 5px 10px;
      font-size: 12px;
    }

    .analysis-help-icon {
      color: #909399;
      cursor: pointer;
      font-size: 16px;

      &:hover {
        color: #409EFF;
      }
    }
  }

  .analysis-textarea {
    .el-textarea__inner {
      border-radius: 4px;
    }
  }
}
</style>
