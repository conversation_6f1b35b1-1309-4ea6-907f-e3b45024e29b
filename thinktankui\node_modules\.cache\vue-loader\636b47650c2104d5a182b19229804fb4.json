{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\Radar.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\Radar.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBWaXNlciBmcm9tICd2aXNlci12dWUnOwppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CgpWdWUudXNlKFZpc2VyKTsKCmNvbnN0IGF4aXMxT3B0cyA9IHsKICBkYXRhS2V5OiAnaXRlbScsCiAgbGluZTogbnVsbCwKICB0aWNrTGluZTogbnVsbCwKICBncmlkOiB7CiAgICBsaW5lU3R5bGU6IHsKICAgICAgbGluZURhc2g6IG51bGwKICAgIH0sCiAgICBoaWRlRmlyc3RMaW5lOiBmYWxzZQogIH0KfQpjb25zdCBheGlzMk9wdHMgPSB7CiAgZGF0YUtleTogJ3Njb3JlJywKICBsaW5lOiBudWxsLAogIHRpY2tMaW5lOiBudWxsLAogIGdyaWQ6IHsKICAgIHR5cGU6ICdwb2x5Z29uJywKICAgIGxpbmVTdHlsZTogewogICAgICBsaW5lRGFzaDogbnVsbAogICAgfQogIH0KfQoKY29uc3Qgc2NhbGUgPSBbCiAgewogICAgZGF0YUtleTogJ3Njb3JlJywKICAgIG1pbjogMCwKICAgIG1heDogODAKICB9LCB7CiAgICBkYXRhS2V5OiAndXNlcicsCiAgICBhbGlhczogJ+exu+WeiycKICB9Cl0KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUmFkYXInLAogIHByb3BzOiB7CiAgICBkYXRhOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9CiAgfSwKICBkYXRhICgpIHsKICAgIHJldHVybiB7CiAgICAgIGF4aXMxT3B0cywKICAgICAgYXhpczJPcHRzLAogICAgICBzY2FsZQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["Radar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Radar.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <v-chart :forceFit=\"true\" height=\"400\" :data=\"data\" :padding=\"[20, 20, 95, 20]\" :scale=\"scale\">\n    <v-tooltip></v-tooltip>\n    <v-axis :dataKey=\"axis1Opts.dataKey\" :line=\"axis1Opts.line\" :tickLine=\"axis1Opts.tickLine\" :grid=\"axis1Opts.grid\" />\n    <v-axis :dataKey=\"axis2Opts.dataKey\" :line=\"axis2Opts.line\" :tickLine=\"axis2Opts.tickLine\" :grid=\"axis2Opts.grid\" />\n    <v-legend dataKey=\"user\" marker=\"circle\" :offset=\"30\" />\n    <v-coord type=\"polar\" radius=\"0.8\" />\n    <v-line position=\"item*score\" color=\"user\" :size=\"2\" />\n    <v-point position=\"item*score\" color=\"user\" :size=\"4\" shape=\"circle\" />\n  </v-chart>\n</template>\n\n<script>\nimport Viser from 'viser-vue';\nimport Vue from 'vue';\n\nVue.use(Viser);\n\nconst axis1Opts = {\n  dataKey: 'item',\n  line: null,\n  tickLine: null,\n  grid: {\n    lineStyle: {\n      lineDash: null\n    },\n    hideFirstLine: false\n  }\n}\nconst axis2Opts = {\n  dataKey: 'score',\n  line: null,\n  tickLine: null,\n  grid: {\n    type: 'polygon',\n    lineStyle: {\n      lineDash: null\n    }\n  }\n}\n\nconst scale = [\n  {\n    dataKey: 'score',\n    min: 0,\n    max: 80\n  }, {\n    dataKey: 'user',\n    alias: '类型'\n  }\n]\n\nexport default {\n  name: 'Radar',\n  props: {\n    data: {\n      type: Array,\n      default: null\n    }\n  },\n  data () {\n    return {\n      axis1Opts,\n      axis2Opts,\n      scale\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}