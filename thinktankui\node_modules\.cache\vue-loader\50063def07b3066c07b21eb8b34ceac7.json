{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\druid\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\druid\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBpRnJhbWUgZnJvbSAiQC9jb21wb25lbnRzL2lGcmFtZS9pbmRleCI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRHJ1aWQiLAogIGNvbXBvbmVudHM6IHsgaUZyYW1lIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZHJ1aWQvbG9naW4uaHRtbCIKICAgIH07CiAgfSwKfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/druid", "sourcesContent": ["<template>\n  <!-- <i-frame :src=\"url\" /> -->\n  <div>\n    <div>我是数据监控</div>\n  </div>\n</template>\n<script>\nimport iFrame from \"@/components/iFrame/index\";\nexport default {\n  name: \"Druid\",\n  components: { iFrame },\n  data() {\n    return {\n      url: process.env.VUE_APP_BASE_API + \"/druid/login.html\"\n    };\n  },\n};\n</script>\n"]}]}