{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\n  <el-drawer size=\"280px\" :visible=\"visible\" :with-header=\"false\" :append-to-body=\"true\" :show-close=\"false\">\n    <div class=\"drawer-container\">\n      <div>\n        <div class=\"setting-drawer-content\">\n          <div class=\"setting-drawer-title\">\n            <h3 class=\"drawer-title\">主题风格设置</h3>\n          </div>\n          <div class=\"setting-drawer-block-checbox\">\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\n              <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\n              <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                  </svg>\n                </i>\n              </div>\n            </div>\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\n              <img src=\"@/assets/images/light.svg\" alt=\"light\">\n              <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                  </svg>\n                </i>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"drawer-item\">\n            <span>主题颜色</span>\n            <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n          </div>\n        </div>\n\n        <el-divider/>\n\n        <h3 class=\"drawer-title\">系统布局配置</h3>\n\n        <div class=\"drawer-item\">\n          <span>开启 TopNav</span>\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>开启 Tags-Views</span>\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>固定 Header</span>\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>显示 Logo</span>\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>动态标题</span>\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\" />\n        </div>\n\n        <el-divider/>\n\n        <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\n        <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\n      </div>\n    </div>\n  </el-drawer>\n</template>\n\n<script>\nimport ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: { ThemePicker },\n  data() {\n    return {\n      theme: this.$store.state.settings.theme,\n      sideTheme: this.$store.state.settings.sideTheme\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.$store.state.settings.showSettings\n      }\n    },\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    topNav: {\n      get() {\n        return this.$store.state.settings.topNav\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'topNav',\n          value: val\n        })\n        if (!val) {\n          this.$store.dispatch('app/toggleSideBarHide', false);\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes);\n        }\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    },\n    dynamicTitle: {\n      get() {\n        return this.$store.state.settings.dynamicTitle\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'dynamicTitle',\n          value: val\n        })\n      }\n    },\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n      this.theme = val;\n    },\n    handleTheme(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'sideTheme',\n        value: val\n      })\n      this.sideTheme = val;\n    },\n    saveSetting() {\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\n      this.$cache.local.set(\n        \"layout-setting\",\n        `{\n            \"topNav\":${this.topNav},\n            \"tagsView\":${this.tagsView},\n            \"fixedHeader\":${this.fixedHeader},\n            \"sidebarLogo\":${this.sidebarLogo},\n            \"dynamicTitle\":${this.dynamicTitle},\n            \"sideTheme\":\"${this.sideTheme}\",\n            \"theme\":\"${this.theme}\"\n          }`\n      );\n      setTimeout(this.$modal.closeLoading(), 1000)\n    },\n    resetSetting() {\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\n      this.$cache.local.remove(\"layout-setting\")\n      setTimeout(\"window.location.reload()\", 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .setting-drawer-content {\n    .setting-drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n      font-weight: bold;\n    }\n\n    .setting-drawer-block-checbox {\n      display: flex;\n      justify-content: flex-start;\n      align-items: center;\n      margin-top: 10px;\n      margin-bottom: 20px;\n\n      .setting-drawer-block-checbox-item {\n        position: relative;\n        margin-right: 16px;\n        border-radius: 2px;\n        cursor: pointer;\n\n        img {\n          width: 48px;\n          height: 48px;\n        }\n\n        .setting-drawer-block-checbox-selectIcon {\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 100%;\n          height: 100%;\n          padding-top: 15px;\n          padding-left: 24px;\n          color: #1890ff;\n          font-weight: 700;\n          font-size: 14px;\n        }\n      }\n    }\n  }\n\n  .drawer-container {\n    padding: 20px;\n    font-size: 14px;\n    line-height: 1.5;\n    word-wrap: break-word;\n\n    .drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n    }\n\n    .drawer-item {\n      color: rgba(0, 0, 0, .65);\n      font-size: 14px;\n      padding: 12px 0;\n    }\n\n    .drawer-switch {\n      float: right\n    }\n  }\n</style>\n"]}]}