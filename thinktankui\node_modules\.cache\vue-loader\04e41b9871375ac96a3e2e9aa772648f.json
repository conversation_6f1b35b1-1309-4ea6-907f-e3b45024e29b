{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\user-management.vue?vue&type=style&index=0&id=2deb1e0e&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\user-management.vue", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnVzZXItbWFuYWdlbWVudC1jb250YWluZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCi51c2VyLW1hbmFnZW1lbnQtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDIwcHg7CgogIC50aXRsZSB7CiAgICBmb250LXNpemU6IDE4cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKICB9Cn0KCi51c2VyLW1hbmFnZW1lbnQtY29udGVudCB7CiAgLmVsLXRhYmxlIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgfQoKICAuZGVsZXRlLWJ0biB7CiAgICBjb2xvcjogI2Y1NmM2YzsKICB9Cn0KCi5wYWdpbmF0aW9uLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBtYXJnaW4tdG9wOiAyMHB4Owp9CgouZGlhbG9nLWZvb3RlciB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwoKICAuY29uZmlybS1idG4gewogICAgYmFja2dyb3VuZC1jb2xvcjogIzQwOUVGRjsKICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRjsKICAgIG1hcmdpbi1yaWdodDogMTBweDsKICB9CgogIC5jYW5jZWwtYnRuIHsKICAgIGNvbG9yOiAjNjA2MjY2OwogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICAgIGJvcmRlci1jb2xvcjogI2RjZGZlNjsKICB9Cn0KCi51c2VyLWRpYWxvZyB7CiAgLmVsLWRpYWxvZ19faGVhZGVyIHsKICAgIHBhZGRpbmc6IDE1cHggMjBweDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwoKICAgIC5lbC1kaWFsb2dfX3RpdGxlIHsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBmb250LXdlaWdodDogNTAwOwogICAgfQoKICAgIC5lbC1kaWFsb2dfX2hlYWRlcmJ0biB7CiAgICAgIHRvcDogMTVweDsKICAgIH0KICB9CgogIC5lbC1kaWFsb2dfX2JvZHkgewogICAgcGFkZGluZzogMjBweDsKICB9CgogIC5lbC1mb3JtLWl0ZW0gewogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAucmVxdWlyZWQtbWFyayB7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgbGVmdDogLTEwcHg7CiAgICAgIHRvcDogMTBweDsKICAgICAgY29sb3I6ICNmNTZjNmM7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIH0KCiAgICAuZWwtZm9ybS1pdGVtX19sYWJlbCB7CiAgICAgIGNvbG9yOiAjNjA2MjY2OwogICAgICBmb250LXdlaWdodDogbm9ybWFsOwogICAgfQoKICAgIC5lbC1pbnB1dF9faW5uZXIgewogICAgICBib3JkZXItcmFkaXVzOiAzcHg7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["user-management.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAySA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "user-management.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\n  <div class=\"user-management-container\">\n    <div class=\"user-management-header\">\n      <div class=\"title\">用户管理</div>\n      <div class=\"actions\">\n        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"handleAdd\">添加用户</el-button>\n      </div>\n    </div>\n\n    <div class=\"user-management-content\">\n      <el-table\n        :data=\"userList\"\n        style=\"width: 100%\"\n        border\n        stripe\n        :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\n      >\n        <el-table-column\n          prop=\"userId\"\n          label=\"用户ID\"\n          width=\"100\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"userName\"\n          label=\"用户名称\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"phoneNumber\"\n          label=\"手机号\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"email\"\n          label=\"邮箱\"\n          width=\"180\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"maxVisits\"\n          label=\"访问权限\"\n          width=\"100\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"status\"\n          label=\"状态\"\n          width=\"100\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-switch\n              v-model=\"scope.row.status\"\n              active-color=\"#13ce66\"\n              inactive-color=\"#ff4949\"\n              :active-value=\"1\"\n              :inactive-value=\"0\"\n              @change=\"handleStatusChange(scope.row)\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"操作\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit\"\n              @click=\"handleEdit(scope.row)\"\n            >编辑</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-delete\"\n              class=\"delete-btn\"\n              @click=\"handleDelete(scope.row)\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"prev, pager, next, jumper\"\n          :total=\"total\"\n          :current-page.sync=\"currentPage\"\n          :page-size=\"pageSize\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加用户对话框 -->\n    <el-dialog title=\"添加用户\" :visible.sync=\"dialogVisible\" width=\"500px\" center class=\"user-dialog\">\n      <el-form ref=\"userForm\" :model=\"userForm\" :rules=\"userFormRules\" label-width=\"100px\">\n        <el-form-item label=\"用户名称\" prop=\"userName\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.userName\" placeholder=\"请输入用户名/手机号/邮箱等登录名\" />\n        </el-form-item>\n        <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.phoneNumber\" placeholder=\"输入手机号码\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.email\" placeholder=\"输入邮箱\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\" show-password />\n        </el-form-item>\n        <el-form-item label=\"最大访问量\" prop=\"maxVisits\">\n          <el-input v-model=\"userForm.maxVisits\" placeholder=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-switch\n            v-model=\"userForm.status\"\n            active-color=\"#13ce66\"\n            inactive-color=\"#ff4949\"\n            :active-value=\"1\"\n            :inactive-value=\"0\"\n          />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"confirm-btn\">确定</el-button>\n        <el-button @click=\"dialogVisible = false\" class=\"cancel-btn\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"UserManagement\",\n  data() {\n    return {\n      // 用户列表\n      userList: [\n        {\n          userId: 1,\n          userName: \"admin\",\n          phoneNumber: \"13800138000\",\n          email: \"<EMAIL>\",\n          password: \"******\",\n          maxVisits: 1000,\n          role: \"管理员\",\n          department: \"技术部\",\n          status: 1\n        },\n        {\n          userId: 2,\n          userName: \"user1\",\n          phoneNumber: \"13800138001\",\n          email: \"<EMAIL>\",\n          password: \"******\",\n          maxVisits: 500,\n          role: \"普通用户\",\n          department: \"市场部\",\n          status: 1\n        },\n        {\n          userId: 3,\n          userName: \"user2\",\n          phoneNumber: \"13800138002\",\n          email: \"<EMAIL>\",\n          password: \"******\",\n          maxVisits: 300,\n          role: \"普通用户\",\n          department: \"销售部\",\n          status: 0\n        }\n      ],\n      // 分页相关\n      total: 3,\n      currentPage: 1,\n      pageSize: 10,\n      // 对话框相关\n      dialogVisible: false,\n      dialogTitle: \"添加用户\",\n      userForm: {\n        userId: null,\n        userName: \"\",\n        phoneNumber: \"\",\n        email: \"\",\n        password: \"\",\n        maxVisits: 0,\n        role: \"\",\n        department: \"\",\n        status: 1\n      },\n      userFormRules: {\n        userName: [\n          { required: true, message: \"请输入用户名称\", trigger: \"blur\" },\n          { min: 3, max: 20, message: \"长度在 3 到 20 个字符\", trigger: \"blur\" }\n        ],\n        phoneNumber: [\n          { required: true, message: \"请输入手机号码\", trigger: \"blur\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n          { type: \"email\", message: \"请输入正确的邮箱地址\", trigger: \"blur\" }\n        ],\n        password: [\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\n          { min: 8, max: 16, message: \"长度在 8 到 16 个字符\", trigger: \"blur\" },\n          {\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$/,\n            message: \"密码必须同时包含数字、大小写字母和符号\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  methods: {\n    // 处理页码变化\n    handleCurrentChange(val) {\n      this.currentPage = val;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n    // 添加用户\n    handleAdd() {\n      this.dialogTitle = \"添加用户\";\n      this.userForm = {\n        userId: null,\n        userName: \"\",\n        phoneNumber: \"\",\n        email: \"\",\n        password: \"\",\n        maxVisits: 0,\n        role: \"\",\n        department: \"\",\n        status: 1\n      };\n      this.dialogVisible = true;\n    },\n    // 编辑用户\n    handleEdit(row) {\n      this.dialogTitle = \"编辑用户\";\n      this.userForm = JSON.parse(JSON.stringify(row));\n      this.dialogVisible = true;\n    },\n    // 删除用户\n    handleDelete(row) {\n      this.$confirm(`确定要删除用户\"${row.userName}\"吗？`, \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        // 实际应用中这里需要调用接口删除用户\n        this.userList = this.userList.filter(item => item.userId !== row.userId);\n        this.total = this.userList.length;\n        this.$message.success(\"删除成功\");\n      }).catch(() => {\n        this.$message.info(\"已取消删除\");\n      });\n    },\n    // 修改用户状态\n    handleStatusChange(row) {\n      // 实际应用中这里需要调用接口修改用户状态\n      this.$message.success(`用户\"${row.userName}\"状态已${row.status === 1 ? '启用' : '禁用'}`);\n    },\n    // 提交表单\n    submitForm() {\n      this.$refs.userForm.validate(valid => {\n        if (valid) {\n          if (this.userForm.userId) {\n            // 编辑用户\n            const index = this.userList.findIndex(item => item.userId === this.userForm.userId);\n            if (index !== -1) {\n              this.userList.splice(index, 1, this.userForm);\n              this.$message.success(\"修改成功\");\n            }\n          } else {\n            // 添加用户\n            this.userForm.userId = this.userList.length + 1;\n            this.userList.push(this.userForm);\n            this.total = this.userList.length;\n            this.$message.success(\"添加成功\");\n          }\n          this.dialogVisible = false;\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-management-container {\n  background-color: #fff;\n  padding: 20px;\n  border-radius: 4px;\n}\n\n.user-management-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.user-management-content {\n  .el-table {\n    margin-bottom: 20px;\n  }\n\n  .delete-btn {\n    color: #f56c6c;\n  }\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n.dialog-footer {\n  text-align: center;\n\n  .confirm-btn {\n    background-color: #409EFF;\n    border-color: #409EFF;\n    margin-right: 10px;\n  }\n\n  .cancel-btn {\n    color: #606266;\n    background-color: #fff;\n    border-color: #dcdfe6;\n  }\n}\n\n.user-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e4e7ed;\n\n    .el-dialog__title {\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .el-dialog__headerbtn {\n      top: 15px;\n    }\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n\n  .el-form-item {\n    position: relative;\n    margin-bottom: 20px;\n\n    .required-mark {\n      position: absolute;\n      left: -10px;\n      top: 10px;\n      color: #f56c6c;\n      font-size: 14px;\n    }\n\n    .el-form-item__label {\n      color: #606266;\n      font-weight: normal;\n    }\n\n    .el-input__inner {\n      border-radius: 3px;\n    }\n  }\n}\n</style>\n"]}]}