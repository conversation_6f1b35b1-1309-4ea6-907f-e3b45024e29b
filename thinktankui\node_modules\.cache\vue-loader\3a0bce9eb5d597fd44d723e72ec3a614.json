{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\meta-search\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\meta-search\\index.vue", "mtime": 1748099318318}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWV0YVNlYXJjaCcsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZVRhYjogJ2Z1bGx0ZXh0JywgLy8g6buY6K6k5pi+56S65YWo5paH5qOA57SiCiAgICAgIHNlYXJjaEtleXdvcmQ6ICfmlrnlpKonLAogICAgICBoYXNTZWFyY2hlZDogdHJ1ZSwKCiAgICAgIC8vIOWFqOaWh+ajgOe0ouebuOWFs+aVsOaNrgogICAgICBzZWxlY3RlZFRpbWU6ICcyNGgnLAogICAgICBzZWxlY3RlZFBsYXRmb3JtOiAnYWxsJywKICAgICAgc2VsZWN0ZWRFbW90aW9uOiAnYWxsJywKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgdG90YWxSZXN1bHRzOiAxMDAwMCwKCiAgICAgIHRpbWVPcHRpb25zOiBbCiAgICAgICAgeyBsYWJlbDogJzI05bCP5pe2JywgdmFsdWU6ICcyNGgnIH0sCiAgICAgICAgeyBsYWJlbDogJ+S4gOWRqCcsIHZhbHVlOiAnMXcnIH0sCiAgICAgICAgeyBsYWJlbDogJ+WNiuW5tCcsIHZhbHVlOiAnNm0nIH0sCiAgICAgICAgeyBsYWJlbDogJ+S4gOW5tCcsIHZhbHVlOiAnMXknIH0sCiAgICAgICAgeyBsYWJlbDogJ+iHquWumuS5iScsIHZhbHVlOiAnY3VzdG9tJyB9CiAgICAgIF0sCgogICAgICBwbGF0Zm9ybU9wdGlvbnM6IFsKICAgICAgICB7IGxhYmVsOiAn5YWo6YOoJywgdmFsdWU6ICdhbGwnLCBjb3VudDogMTA1NDAgfSwKICAgICAgICB7IGxhYmVsOiAn5b6u5L+hJywgdmFsdWU6ICd3ZWNoYXQnLCBjb3VudDogMTg0NyB9LAogICAgICAgIHsgbGFiZWw6ICflvq7ljZonLCB2YWx1ZTogJ3dlaWJvJywgY291bnQ6IDIwMDggfSwKICAgICAgICB7IGxhYmVsOiAn5a6i5oi356uvJywgdmFsdWU6ICdhcHAnLCBjb3VudDogMTc0OCB9LAogICAgICAgIHsgbGFiZWw6ICforrrlnZsnLCB2YWx1ZTogJ2ZvcnVtJywgY291bnQ6IDY3MyB9CiAgICAgIF0sCgogICAgICBlbW90aW9uT3B0aW9uczogWwogICAgICAgIHsgbGFiZWw6ICflhajpg6gnLCB2YWx1ZTogJ2FsbCcgfSwKICAgICAgICB7IGxhYmVsOiAn5q2j6Z2iJywgdmFsdWU6ICdwb3NpdGl2ZScgfSwKICAgICAgICB7IGxhYmVsOiAn6LSf6Z2iJywgdmFsdWU6ICduZWdhdGl2ZScgfSwKICAgICAgICB7IGxhYmVsOiAn5Lit5oCnJywgdmFsdWU6ICduZXV0cmFsJyB9CiAgICAgIF0sCgogICAgICBzZWFyY2hSZXN1bHRzOiBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfku47mlL/lupzpg6jpl6g0NOS4ieS4quS4quS9k+eahOeDreeCuemXrumimO+8jOWIsOWqkuS9kzHvvIzopobnm5bnmoTvvIjlkKvkuYnvvInvvIzmlpfkuonkuLvkuYnlj6Plj7fvvIzniZvlubTkuLvopoHnmoTpl67popjvvIzlhbHlkIzkurrlt6Xmmbrog73nmoTpl67popjkurrlkZjvvIzlpoLlsbHlsbHnmoTpl67popjnmoTkuLvopoHpl67popjvvIzmlrDlt6XkurrvvIzmiZPlt6XvvIznlKjlj4vkuInlrrYuLi4nLAogICAgICAgICAgc291cmNlOiAn5paw5Y2O572RJywKICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyMi0wNi0yOSAyMDowNzowNCcsCiAgICAgICAgICBhdXRob3I6ICc3N+S6uuiuqOiuuicsCiAgICAgICAgICBwbGF0Zm9ybTogJ+W5s+WPsOadpea6kCcsCiAgICAgICAgICByZWFkQ291bnQ6ICfml6AnLAogICAgICAgICAgbG9jYXRpb246ICfml6DmiYDlnKjlnLAnLAogICAgICAgICAgY2F0ZWdvcnk6ICfmlrDpl7snLAogICAgICAgICAgY29udGVudDogJ+S7juaUv+W6nOmDqOmXqDQ05LiJ5Liq5Liq5L2T55qE54Ot54K56Zeu6aKY77yM5Yiw5aqS5L2TMe+8jOimhueblueahO+8iOWQq+S5ie+8ie+8jOaWl+S6ieS4u+S5ieWPo+WPt++8jOeJm+W5tOS4u+imgeeahOmXrumimO+8jOWFseWQjOS6uuW3peaZuuiDveeahOmXrumimOS6uuWRmO+8jOWmguWxseWxseeahOmXrumimOeahOS4u+imgemXrumimO+8jOaWsOW3peS6uu+8jOaJk+W3pe+8jOeUqOWPi+S4ieWuti4uLicKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn5Lit5aSnLeiuuuaWh+WPkeihqCgyMDI15bm05Lit5aSn6K665paH5Y+R6KGoKeiHqueEtuaMh+aVsOS4reWkpy3orrrmloflj5HooajkuJbnlYzlnLDkvY3orrrlnZstMjAyNeW5tOeahOS7t+WAvOS4reWbveiuuuaWhycsCiAgICAgICAgICBzb3VyY2U6ICfkuK3lpKforrrmloflj5HooagnLAogICAgICAgICAgcHVibGlzaFRpbWU6ICcyMDIyLTA2LTI5IDIwOjA3OjA0JywKICAgICAgICAgIGF1dGhvcjogJzc35Lq66K6o6K66JywKICAgICAgICAgIHBsYXRmb3JtOiAn5bmz5Y+w5p2l5rqQJywKICAgICAgICAgIHJlYWRDb3VudDogJ+aXoCcsCiAgICAgICAgICBsb2NhdGlvbjogJ+aXoOaJgOWcqOWcsCcsCiAgICAgICAgICBjYXRlZ29yeTogJ+iuuuaWhycsCiAgICAgICAgICBjb250ZW50OiAn5Lit5aSnLeiuuuaWh+WPkeihqCgyMDI15bm05Lit5aSn6K665paH5Y+R6KGoKeiHqueEtuaMh+aVsOS4reWkpy3orrrmloflj5HooajkuJbnlYzlnLDkvY3orrrlnZstMjAyNeW5tOeahOS7t+WAvOS4reWbveiuuuaWhy4uLicKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn6L2s5Y+R5b6u5Y2aI+S4rSPlpKflrabnlJ/vvIzkurrmg4XkuJbmlYXjgIInLAogICAgICAgICAgc291cmNlOiAn5b6u5Y2aJywKICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyMi0wNi0yOSAyMDowNzowNCcsCiAgICAgICAgICBhdXRob3I6ICc3N+S6uuiuqOiuuicsCiAgICAgICAgICBwbGF0Zm9ybTogJ+W+ruWNmicsCiAgICAgICAgICByZWFkQ291bnQ6ICcxMDAwJywKICAgICAgICAgIGxvY2F0aW9uOiAn5YyX5LqsJywKICAgICAgICAgIGNhdGVnb3J5OiAn56S+5Lqk5aqS5L2TJywKICAgICAgICAgIGNvbnRlbnQ6ICfovazlj5Hlvq7ljZoj5LitI+Wkp+WtpueUn++8jOS6uuaDheS4luaVheOAgui/meaYr+S4gOadoeWFs+S6juWkp+WtpueUn+S6uumZheWFs+ezu+eahOW+ruWNmuWGheWuuS4uLicKICAgICAgICB9CiAgICAgIF0sCgogICAgICAvLyDlhYPmkJzntKLnm7jlhbPmlbDmja4KICAgICAgYWN0aXZlRW5naW5lOiAnYmluZycsCiAgICAgIHNlbGVjdGVkRW5naW5lczogWydiaW5nJywgJ2JhaWR1JywgJzM2MCddLAogICAgICBzZWFyY2hFbmdpbmVzOiBbCiAgICAgICAgeyBpZDogJ2JpbmcnLCBuYW1lOiAnTWljcm9zb2Z0IEJpbmcnLCBpY29uOiAnaHR0cHM6Ly93d3cuYmluZy5jb20vZmF2aWNvbi5pY28nIH0sCiAgICAgICAgeyBpZDogJ2JhaWR1JywgbmFtZTogJ+eZvuW6puaQnOe0oicsIGljb246ICdodHRwczovL3d3dy5iYWlkdS5jb20vZmF2aWNvbi5pY28nIH0sCiAgICAgICAgeyBpZDogJzM2MCcsIG5hbWU6ICczNjDmkJzntKInLCBpY29uOiAnaHR0cHM6Ly93d3cuc28uY29tL2Zhdmljb24uaWNvJyB9CiAgICAgIF0sCiAgICAgIGJpbmdSZXN1bHRzOiBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfmlrnlpKrlrpjnvZFf6auY56uv5YWo5Zy65pmv5Y6o55S1JywKICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20nLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20nLAogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrigJTigJTkuJPkuJrnlJ/kuqfpq5jnq6/ljqjmiL/nlLXlmajnmoTpooblr7zlk4HniYzvvIzkuLvokKXkuqflk4HvvJrlkLjmsrnng5/mnLrjgIHnh4PmsJTngbbjgIHmtojmr5Lmn5zjgIHokrjnrrHjgIHng6TnrrHjgIHmtJfnopfmnLrnrYnljqjmiL/nlLXlmajvvIzmlrnlpKrljqjnlLXvvIzmlrnlpKrpm4bmiJDng7npparkuK3lv4MuLi4nCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+OAkOaWueWkqumbhuWbouWumOe9keOAkScsCiAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy5mb3RpbGUuY29tL2Fib3V0JywKICAgICAgICAgIGxpbms6ICdodHRwczovL3d3dy5mb3RpbGUuY29tL2Fib3V0JywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5pa55aSq4oCU4oCU5LiT5Lia55Sf5Lqn6auY56uv5Y6o5oi/55S15Zmo55qE6aKG5a+85ZOB54mM77yM5Li76JCl5Lqn5ZOB77ya5ZC45rK554Of5py644CB54eD5rCU54G244CB5raI5q+S5p+c44CB6JK4566x44CB54Ok566x44CB5rSX56KX5py6562J5Y6o5oi/55S15Zmo77yM5pa55aSq5Y6o55S177yM5pa55aSq6ZuG5oiQ54O56aWq5Lit5b+DLi4uJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfkuI3lubPlh6EnLAogICAgICAgICAgdXJsOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9wcm9kdWN0JywKICAgICAgICAgIGxpbms6ICdodHRwczovL3d3dy5mb3RpbGUuY29tL3Byb2R1Y3QnLAogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrpq5jnq6/ljqjnlLXvvIzkuJPms6jpq5jnq6/ljqjmiL/nlLXlmagyMOW5tO+8jOS4u+iQpeS6p+WTge+8muWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqO+8jOaWueWkquWOqOeUte+8jOaWueWkqumbhuaIkOeDuemlquS4reW/gy4uLicKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGJhaWR1UmVzdWx0czogWwogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn5pa55aSq5a6Y572RX+mrmOerr+WFqOWcuuaZr+WOqOeUtScsCiAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy5mb3RpbGUuY29tJywKICAgICAgICAgIGxpbms6ICdodHRwczovL3d3dy5mb3RpbGUuY29tJywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5pa55aSq4oCU4oCU5LiT5Lia55Sf5Lqn6auY56uv5Y6o5oi/55S15Zmo55qE6aKG5a+85ZOB54mM77yM5Li76JCl5Lqn5ZOB77ya5ZC45rK554Of5py644CB54eD5rCU54G244CB5raI5q+S5p+c44CB6JK4566x44CB54Ok566x44CB5rSX56KX5py6562J5Y6o5oi/55S15Zmo77yM5pa55aSq5Y6o55S177yM5pa55aSq6ZuG5oiQ54O56aWq5Lit5b+DLi4uJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfmlrnlpKrljqjnlLUg6auY56uv6ZuG5oiQ5Y6o5oi/55S15Zmo5ZOB54mMJywKICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20vcHJvZHVjdCcsCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9wcm9kdWN0JywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5YWo5Zu95pyN5Yqh54Ot57q/77yaNDAwLTMxNS0wMDAwIOaWueWkquKAlOKAlOS4k+S4mueUn+S6p+mrmOerr+WOqOaIv+eUteWZqOeahOmihuWvvOWTgeeJjO+8jOS4u+iQpeS6p+WTge+8muWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqC4uLicKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn5pa55aSqIC0g55m+5bqm55m+56eRJywKICAgICAgICAgIHVybDogJ2h0dHBzOi8vYmFpa2UuYmFpZHUuY29tL2l0ZW0v5pa55aSqLzE4MzAnLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vYmFpa2UuYmFpZHUuY29tL2l0ZW0v5pa55aSqLzE4MzAnLAogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrvvIzmmK/kuK3lm73pq5jnq6/ljqjnlLXpooblr7zlk4HniYzvvIzliJvnq4vkuo4xOTk25bm077yM5oC76YOo5L2N5LqO5rWZ5rGf5a6B5rOi77yM5piv5LiA5a626ZuG56CU5Y+R44CB55Sf5Lqn44CB6ZSA5ZSu5LqO5LiA5L2T55qE546w5Luj5YyW5LyB5Lia77yM5Li76KaB5Lqn5ZOB5YyF5ous5ZC45rK554Of5py644CB54eD5rCU54G244CB5raI5q+S5p+c44CB6JK4566x44CB54Ok566x44CB5rSX56KX5py6562J5Y6o5oi/55S15ZmoLi4uJwogICAgICAgIH0KICAgICAgXSwKICAgICAgc28zNjBSZXN1bHRzOiBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfmlrnlpKrlrpjnvZFf6auY56uv5YWo5Zy65pmv5Y6o55S1JywKICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20nLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20nLAogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrigJTigJTkuJPkuJrnlJ/kuqfpq5jnq6/ljqjmiL/nlLXlmajnmoTpooblr7zlk4HniYzvvIzkuLvokKXkuqflk4HvvJrlkLjmsrnng5/mnLrjgIHnh4PmsJTngbbjgIHmtojmr5Lmn5zjgIHokrjnrrHjgIHng6TnrrHjgIHmtJfnopfmnLrnrYnljqjmiL/nlLXlmajvvIzmlrnlpKrljqjnlLXvvIzmlrnlpKrpm4bmiJDng7npparkuK3lv4MuLi4nCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+aWueWkquWOqOeUteaXl+iIsOW6ly3lpKnnjKsnLAogICAgICAgICAgdXJsOiAnaHR0cHM6Ly9mb3RpbGUudG1hbGwuY29tJywKICAgICAgICAgIGxpbms6ICdodHRwczovL2ZvdGlsZS50bWFsbC5jb20nLAogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrljqjnlLXml5foiLDlupcs5o+Q5L6b5pa55aSq5rK554Of5py6LOaWueWkqueHg+awlOeBtizmlrnlpKrmtojmr5Lmn5ws5pa55aSq5rSX56KX5py6LOaWueWkquiSuOeusSzmlrnlpKrng6TnrrEs5pa55aSq5b6u5rOi54KJLOaWueWkquawtOanvea0l+eil+acuuetieS6p+WTgeOAguWkqeeMq+ato+WTgeS/nemanCzmj5DkvpsuLi4nCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+aWueWkqumbhuWbouaciemZkOWFrOWPuCcsCiAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy5mb3RpbGUuY29tL2Fib3V0JywKICAgICAgICAgIGxpbms6ICdodHRwczovL3d3dy5mb3RpbGUuY29tL2Fib3V0JywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5pa55aSq6ZuG5Zui5pyJ6ZmQ5YWs5Y+45Yib56uL5LqOMTk5NuW5tO+8jOaAu+mDqOS9jeS6jua1meaxn+Wugeazou+8jOaYr+S4gOWutumbhueglOWPkeOAgeeUn+S6p+OAgemUgOWUruS6juS4gOS9k+eahOeOsOS7o+WMluS8geS4mu+8jOS4u+imgeS6p+WTgeWMheaLrOWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqC4uLicKICAgICAgICB9CiAgICAgIF0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOagh+etvumhteWIh+aNogogICAgc3dpdGNoVGFiKHRhYikgewogICAgICB0aGlzLmFjdGl2ZVRhYiA9IHRhYgogICAgfSwKCiAgICAvLyDmkJzntKLlip/og70KICAgIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5oYXNTZWFyY2hlZCA9IHRydWUKICAgICAgLy8g5a6e6ZmF6aG555uu5Lit6L+Z6YeM5bqU6K+l6LCD55SoQVBJ6I635Y+W5pCc57Si57uT5p6cCiAgICAgIGNvbnNvbGUubG9nKCfmkJzntKLlhbPplK7or406JywgdGhpcy5zZWFyY2hLZXl3b3JkKQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaQnOe0ojogJHt0aGlzLnNlYXJjaEtleXdvcmR9YCkKICAgIH0sCgogICAgLy8g5YWo5paH5qOA57Si562b6YCJ5pa55rOVCiAgICBzZWxlY3RUaW1lKHZhbHVlKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRUaW1lID0gdmFsdWUKICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKQogICAgfSwKCiAgICBzZWxlY3RQbGF0Zm9ybSh2YWx1ZSkgewogICAgICB0aGlzLnNlbGVjdGVkUGxhdGZvcm0gPSB2YWx1ZQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpCiAgICB9LAoKICAgIHNlbGVjdEVtb3Rpb24odmFsdWUpIHsKICAgICAgdGhpcy5zZWxlY3RlZEVtb3Rpb24gPSB2YWx1ZQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpCiAgICB9LAoKICAgIGhhbmRsZVBhZ2VDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZQogICAgICAvLyDliqDovb3lr7nlupTpobXpnaLmlbDmja4KICAgIH0sCgogICAgLy8g5YWD5pCc57Si5byV5pOO5YiH5o2iCiAgICB0b2dnbGVFbmdpbmUoZW5naW5lSWQpIHsKICAgICAgLy8g5YiH5o2i6YCJ5Lit54q25oCBCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkRW5naW5lcy5pbmNsdWRlcyhlbmdpbmVJZCkpIHsKICAgICAgICAvLyDlpoLmnpzlt7Lnu4/pgInkuK3vvIzkuJTkuI3mmK/mnIDlkI7kuIDkuKrpgInkuK3nmoTlvJXmk47vvIzliJnlj5bmtojpgInkuK0KICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEVuZ2luZXMubGVuZ3RoID4gMSkgewogICAgICAgICAgdGhpcy5zZWxlY3RlZEVuZ2luZXMgPSB0aGlzLnNlbGVjdGVkRW5naW5lcy5maWx0ZXIoaWQgPT4gaWQgIT09IGVuZ2luZUlkKQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpzmnKrpgInkuK3vvIzliJnmt7vliqDliLDpgInkuK3liJfooagKICAgICAgICB0aGlzLnNlbGVjdGVkRW5naW5lcy5wdXNoKGVuZ2luZUlkKQogICAgICB9CgogICAgICAvLyDorr7nva7lvZPliY3mtLvliqjlvJXmk44KICAgICAgdGhpcy5hY3RpdmVFbmdpbmUgPSBlbmdpbmVJZAogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqNA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/meta-search", "sourcesContent": ["<template>\n  <div class=\"meta-search-container\">\n    <!-- 顶部搜索区域 -->\n    <div class=\"search-header\">\n      <div class=\"search-tabs\">\n        <div\n          class=\"tab\"\n          :class=\"{ active: activeTab === 'fulltext' }\"\n          @click=\"switchTab('fulltext')\"\n        >\n          全文检索\n        </div>\n        <div\n          class=\"tab\"\n          :class=\"{ active: activeTab === 'meta' }\"\n          @click=\"switchTab('meta')\"\n        >\n          元搜索\n        </div>\n      </div>\n\n      <div class=\"search-box\">\n        <input\n          type=\"text\"\n          class=\"search-input\"\n          v-model=\"searchKeyword\"\n          placeholder=\"请输入搜索关键词\"\n          @keyup.enter=\"handleSearch\"\n        />\n        <el-button type=\"primary\" class=\"search-btn\" @click=\"handleSearch\">搜索</el-button>\n      </div>\n    </div>\n\n    <!-- 全文检索结果 -->\n    <div v-if=\"activeTab === 'fulltext' && hasSearched\" class=\"fulltext-results\">\n      <!-- 筛选条件区域 -->\n      <div class=\"filter-section\">\n        <!-- 时间筛选 -->\n        <div class=\"filter-row\">\n          <span class=\"filter-label\">时间范围:</span>\n          <div class=\"filter-options\">\n            <el-button\n              v-for=\"time in timeOptions\"\n              :key=\"time.value\"\n              :type=\"selectedTime === time.value ? 'primary' : ''\"\n              size=\"small\"\n              @click=\"selectTime(time.value)\"\n            >\n              {{ time.label }}\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 平台筛选 -->\n        <div class=\"filter-row\">\n          <span class=\"filter-label\">平台类型:</span>\n          <div class=\"filter-options\">\n            <el-button\n              v-for=\"platform in platformOptions\"\n              :key=\"platform.value\"\n              :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\n              size=\"small\"\n              @click=\"selectPlatform(platform.value)\"\n            >\n              {{ platform.label }}\n              <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 情感筛选 -->\n        <div class=\"filter-row\">\n          <span class=\"filter-label\">情感类型:</span>\n          <div class=\"filter-options\">\n            <el-button\n              v-for=\"emotion in emotionOptions\"\n              :key=\"emotion.value\"\n              :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\n              size=\"small\"\n              @click=\"selectEmotion(emotion.value)\"\n            >\n              {{ emotion.label }}\n              <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 结果统计 -->\n      <div class=\"result-stats\">\n        <span>共{{ totalResults }}条结果</span>\n        <div class=\"action-buttons\">\n          <el-button size=\"small\">导出</el-button>\n          <el-button type=\"primary\" size=\"small\">分析</el-button>\n        </div>\n      </div>\n\n      <!-- 搜索结果列表 -->\n      <div class=\"results-list\">\n        <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\n          <div class=\"result-header\">\n            <h3 class=\"result-title\">{{ item.title }}</h3>\n            <div class=\"result-actions\">\n              <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\n            </div>\n          </div>\n\n          <div class=\"result-meta\">\n            <span class=\"meta-item\">{{ item.source }}</span>\n            <span class=\"meta-item\">{{ item.publishTime }}</span>\n            <span class=\"meta-item\">{{ item.author }}</span>\n            <span class=\"meta-item\">{{ item.platform }}</span>\n            <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\n            <span class=\"meta-item\">{{ item.location }}</span>\n            <span class=\"meta-item\">{{ item.category }}</span>\n          </div>\n\n          <div class=\"result-content\">\n            {{ item.content }}\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"prev, pager, next\"\n          :total=\"totalResults\"\n          :current-page.sync=\"currentPage\"\n          :page-size=\"pageSize\"\n          @current-change=\"handlePageChange\"\n        ></el-pagination>\n      </div>\n    </div>\n\n    <!-- 元搜索结果 -->\n    <div v-if=\"activeTab === 'meta' && hasSearched\" class=\"meta-results\">\n      <!-- 搜索引擎选项卡 -->\n      <div class=\"search-engines\">\n        <div\n          v-for=\"engine in searchEngines\"\n          :key=\"engine.id\"\n          class=\"engine-item\"\n          :class=\"{ active: engine.id === activeEngine }\"\n          @click=\"toggleEngine(engine.id)\"\n        >\n          <div class=\"checkbox\">\n            <i class=\"el-icon-check\" v-if=\"selectedEngines.includes(engine.id)\"></i>\n          </div>\n          <img :src=\"engine.icon\" :alt=\"engine.name\" class=\"engine-icon\" />\n          <span class=\"engine-name\">{{ engine.name }}</span>\n        </div>\n      </div>\n\n      <!-- 搜索结果展示区 -->\n      <div class=\"results-container\">\n        <!-- 左侧搜索结果 -->\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('bing')\">\n          <div class=\"result-header\">\n            <img src=\"https://www.bing.com/favicon.ico\" alt=\"Microsoft Bing\" class=\"result-icon\" />\n            <span class=\"result-title\">Microsoft Bing</span>\n          </div>\n          <div class=\"result-list\">\n            <div class=\"result-item\" v-for=\"(item, index) in bingResults\" :key=\"'bing-'+index\">\n              <h3 class=\"item-title\">\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\n              </h3>\n              <div class=\"item-url\">{{ item.url }}</div>\n              <div class=\"item-desc\">{{ item.description }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 中间搜索结果 -->\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('baidu')\">\n          <div class=\"result-header\">\n            <img src=\"https://www.baidu.com/favicon.ico\" alt=\"百度搜索\" class=\"result-icon\" />\n            <span class=\"result-title\">百度搜索</span>\n          </div>\n          <div class=\"result-list\">\n            <div class=\"result-item\" v-for=\"(item, index) in baiduResults\" :key=\"'baidu-'+index\">\n              <h3 class=\"item-title\">\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\n              </h3>\n              <div class=\"item-url\">{{ item.url }}</div>\n              <div class=\"item-desc\">{{ item.description }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 右侧搜索结果 -->\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('360')\">\n          <div class=\"result-header\">\n            <img src=\"https://www.so.com/favicon.ico\" alt=\"360搜索\" class=\"result-icon\" />\n            <span class=\"result-title\">360搜索</span>\n          </div>\n          <div class=\"result-list\">\n            <div class=\"result-item\" v-for=\"(item, index) in so360Results\" :key=\"'360-'+index\">\n              <h3 class=\"item-title\">\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\n              </h3>\n              <div class=\"item-url\">{{ item.url }}</div>\n              <div class=\"item-desc\">{{ item.description }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'MetaSearch',\n  data() {\n    return {\n      activeTab: 'fulltext', // 默认显示全文检索\n      searchKeyword: '方太',\n      hasSearched: true,\n\n      // 全文检索相关数据\n      selectedTime: '24h',\n      selectedPlatform: 'all',\n      selectedEmotion: 'all',\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 10000,\n\n      timeOptions: [\n        { label: '24小时', value: '24h' },\n        { label: '一周', value: '1w' },\n        { label: '半年', value: '6m' },\n        { label: '一年', value: '1y' },\n        { label: '自定义', value: 'custom' }\n      ],\n\n      platformOptions: [\n        { label: '全部', value: 'all', count: 10540 },\n        { label: '微信', value: 'wechat', count: 1847 },\n        { label: '微博', value: 'weibo', count: 2008 },\n        { label: '客户端', value: 'app', count: 1748 },\n        { label: '论坛', value: 'forum', count: 673 }\n      ],\n\n      emotionOptions: [\n        { label: '全部', value: 'all' },\n        { label: '正面', value: 'positive' },\n        { label: '负面', value: 'negative' },\n        { label: '中性', value: 'neutral' }\n      ],\n\n      searchResults: [\n        {\n          title: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...',\n          source: '新华网',\n          publishTime: '2022-06-29 20:07:04',\n          author: '77人讨论',\n          platform: '平台来源',\n          readCount: '无',\n          location: '无所在地',\n          category: '新闻',\n          content: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...'\n        },\n        {\n          title: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文',\n          source: '中大论文发表',\n          publishTime: '2022-06-29 20:07:04',\n          author: '77人讨论',\n          platform: '平台来源',\n          readCount: '无',\n          location: '无所在地',\n          category: '论文',\n          content: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...'\n        },\n        {\n          title: '转发微博#中#大学生，人情世故。',\n          source: '微博',\n          publishTime: '2022-06-29 20:07:04',\n          author: '77人讨论',\n          platform: '微博',\n          readCount: '1000',\n          location: '北京',\n          category: '社交媒体',\n          content: '转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...'\n        }\n      ],\n\n      // 元搜索相关数据\n      activeEngine: 'bing',\n      selectedEngines: ['bing', 'baidu', '360'],\n      searchEngines: [\n        { id: 'bing', name: 'Microsoft Bing', icon: 'https://www.bing.com/favicon.ico' },\n        { id: 'baidu', name: '百度搜索', icon: 'https://www.baidu.com/favicon.ico' },\n        { id: '360', name: '360搜索', icon: 'https://www.so.com/favicon.ico' }\n      ],\n      bingResults: [\n        {\n          title: '方太官网_高端全场景厨电',\n          url: 'https://www.fotile.com',\n          link: 'https://www.fotile.com',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '【方太集团官网】',\n          url: 'https://www.fotile.com/about',\n          link: 'https://www.fotile.com/about',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '不平凡',\n          url: 'https://www.fotile.com/product',\n          link: 'https://www.fotile.com/product',\n          description: '方太高端厨电，专注高端厨房电器20年，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        }\n      ],\n      baiduResults: [\n        {\n          title: '方太官网_高端全场景厨电',\n          url: 'https://www.fotile.com',\n          link: 'https://www.fotile.com',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '方太厨电 高端集成厨房电器品牌',\n          url: 'https://www.fotile.com/product',\n          link: 'https://www.fotile.com/product',\n          description: '全国服务热线：400-315-0000 方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\n        },\n        {\n          title: '方太 - 百度百科',\n          url: 'https://baike.baidu.com/item/方太/1830',\n          link: 'https://baike.baidu.com/item/方太/1830',\n          description: '方太，是中国高端厨电领导品牌，创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\n        }\n      ],\n      so360Results: [\n        {\n          title: '方太官网_高端全场景厨电',\n          url: 'https://www.fotile.com',\n          link: 'https://www.fotile.com',\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\n        },\n        {\n          title: '方太厨电旗舰店-天猫',\n          url: 'https://fotile.tmall.com',\n          link: 'https://fotile.tmall.com',\n          description: '方太厨电旗舰店,提供方太油烟机,方太燃气灶,方太消毒柜,方太洗碗机,方太蒸箱,方太烤箱,方太微波炉,方太水槽洗碗机等产品。天猫正品保障,提供...'\n        },\n        {\n          title: '方太集团有限公司',\n          url: 'https://www.fotile.com/about',\n          link: 'https://www.fotile.com/about',\n          description: '方太集团有限公司创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\n        }\n      ]\n    }\n  },\n  methods: {\n    // 标签页切换\n    switchTab(tab) {\n      this.activeTab = tab\n    },\n\n    // 搜索功能\n    handleSearch() {\n      this.hasSearched = true\n      // 实际项目中这里应该调用API获取搜索结果\n      console.log('搜索关键词:', this.searchKeyword)\n      this.$message.success(`搜索: ${this.searchKeyword}`)\n    },\n\n    // 全文检索筛选方法\n    selectTime(value) {\n      this.selectedTime = value\n      this.handleSearch()\n    },\n\n    selectPlatform(value) {\n      this.selectedPlatform = value\n      this.handleSearch()\n    },\n\n    selectEmotion(value) {\n      this.selectedEmotion = value\n      this.handleSearch()\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page\n      // 加载对应页面数据\n    },\n\n    // 元搜索引擎切换\n    toggleEngine(engineId) {\n      // 切换选中状态\n      if (this.selectedEngines.includes(engineId)) {\n        // 如果已经选中，且不是最后一个选中的引擎，则取消选中\n        if (this.selectedEngines.length > 1) {\n          this.selectedEngines = this.selectedEngines.filter(id => id !== engineId)\n        }\n      } else {\n        // 如果未选中，则添加到选中列表\n        this.selectedEngines.push(engineId)\n      }\n\n      // 设置当前活动引擎\n      this.activeEngine = engineId\n    }\n  }\n}\n</script>\n\n<style scoped>\n.meta-search-container {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n.search-header {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.search-tabs {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.tab {\n  padding: 8px 16px;\n  margin-right: 10px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n}\n\n.tab.active {\n  color: #409EFF;\n  border-bottom: 2px solid #409EFF;\n  font-weight: bold;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  flex: 1;\n  height: 40px;\n  padding: 0 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  margin-right: 10px;\n  font-size: 14px;\n}\n\n.search-btn {\n  height: 40px;\n}\n\n.search-results {\n  display: flex;\n  flex-direction: column;\n}\n\n.search-engines {\n  display: flex;\n  background: white;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.engine-item {\n  display: flex;\n  align-items: center;\n  padding: 8px 16px;\n  margin-right: 15px;\n  cursor: pointer;\n  border-radius: 4px;\n  transition: all 0.3s;\n  border: 1px solid #dcdfe6;\n  background: #fff;\n}\n\n.engine-item:hover {\n  border-color: #c6e2ff;\n}\n\n.engine-item.active {\n  background: #ecf5ff;\n  color: #409EFF;\n  border-color: #409EFF;\n}\n\n.checkbox {\n  width: 16px;\n  height: 16px;\n  border: 1px solid #dcdfe6;\n  border-radius: 2px;\n  margin-right: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  background-color: #409EFF;\n}\n\n.engine-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n}\n\n.results-container {\n  display: flex;\n  gap: 20px;\n}\n\n.result-column {\n  flex: 1;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.result-header {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  background: #f5f7fa;\n}\n\n.result-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n}\n\n.result-title {\n  font-weight: bold;\n  color: #303133;\n}\n\n.result-list {\n  padding: 15px;\n}\n\n.result-item {\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.result-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.item-title {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n}\n\n.item-title a {\n  color: #0366d6;\n  text-decoration: none;\n}\n\n.item-title a:hover {\n  text-decoration: underline;\n}\n\n.item-url {\n  color: #67c23a;\n  font-size: 12px;\n  margin-bottom: 8px;\n}\n\n.item-desc {\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* 全文检索样式 */\n.fulltext-results {\n  margin-top: 20px;\n}\n\n.filter-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.count {\n  color: #999;\n  font-size: 12px;\n}\n\n.result-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 15px 20px;\n  margin-bottom: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.results-list {\n  background: white;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.results-list .result-item {\n  padding: 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.results-list .result-item:last-child {\n  border-bottom: none;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.result-title {\n  font-size: 16px;\n  color: #333;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n  margin-right: 20px;\n}\n\n.result-meta {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: #999;\n}\n\n.meta-item {\n  white-space: nowrap;\n}\n\n.result-content {\n  color: #666;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n</style>\n"]}]}