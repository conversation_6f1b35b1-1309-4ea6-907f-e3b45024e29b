{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\logininfor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\logininfor\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3QsIGRlbExvZ2luaW5mb3IsIGNsZWFuTG9naW5pbmZvciwgdW5sb2NrTG9naW5pbmZvciB9IGZyb20gIkAvYXBpL21vbml0b3IvbG9naW5pbmZvciI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkxvZ2luaW5mb3IiLAogIGRpY3RzOiBbJ3N5c19jb21tb25fc3RhdHVzJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDpgInmi6nnlKjmiLflkI0KICAgICAgc2VsZWN0TmFtZTogIiIsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICBsaXN0OiBbXSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOm7mOiupOaOkuW6jwogICAgICBkZWZhdWx0U29ydDoge3Byb3A6ICdsb2dpblRpbWUnLCBvcmRlcjogJ2Rlc2NlbmRpbmcnfSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgaXBhZGRyOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoueZu+W9leaXpeW/l+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdCh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0KICAgICAgKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLiRyZWZzLnRhYmxlcy5zb3J0KHRoaXMuZGVmYXVsdFNvcnQucHJvcCwgdGhpcy5kZWZhdWx0U29ydC5vcmRlcikKICAgIH0sCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaW5mb0lkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICAgIHRoaXMuc2VsZWN0TmFtZSA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnVzZXJOYW1lKTsKICAgIH0sCiAgICAvKiog5o6S5bqP6Kem5Y+R5LqL5Lu2ICovCiAgICBoYW5kbGVTb3J0Q2hhbmdlKGNvbHVtbiwgcHJvcCwgb3JkZXIpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcmRlckJ5Q29sdW1uID0gY29sdW1uLnByb3A7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXNBc2MgPSBjb2x1bW4ub3JkZXI7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaW5mb0lkcyA9IHJvdy5pbmZvSWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOiuv+mXrue8luWPt+S4uiInICsgaW5mb0lkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsTG9naW5pbmZvcihpbmZvSWRzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5riF56m65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVDbGVhbigpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5riF56m65omA5pyJ55m75b2V5pel5b+X5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gY2xlYW5Mb2dpbmluZm9yKCk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOino+mUgeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVW5sb2NrKCkgewogICAgICBjb25zdCB1c2VybmFtZSA9IHRoaXMuc2VsZWN0TmFtZTsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k6Kej6ZSB55So5oi3IicgKyB1c2VybmFtZSArICci5pWw5o2u6aG5PycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIHVubG9ja0xvZ2luaW5mb3IodXNlcm5hbWUpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLnlKjmiLciICsgdXNlcm5hbWUgKyAi6Kej6ZSB5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ21vbml0b3IvbG9naW5pbmZvci9leHBvcnQnLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcwogICAgICB9LCBgbG9naW5pbmZvcl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/logininfor", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"登录地址\" prop=\"ipaddr\">\n        <el-input\n          v-model=\"queryParams.ipaddr\"\n          placeholder=\"请输入登录地址\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"登录状态\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_common_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"登录时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          :default-time=\"['00:00:00', '23:59:59']\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['monitor:logininfor:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          @click=\"handleClean\"\n          v-hasPermi=\"['monitor:logininfor:remove']\"\n        >清空</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-unlock\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUnlock\"\n          v-hasPermi=\"['monitor:logininfor:unlock']\"\n        >解锁</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['monitor:logininfor:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"访问编号\" align=\"center\" prop=\"infoId\" />\n      <el-table-column label=\"用户名称\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\" />\n      <el-table-column label=\"登录地址\" align=\"center\" prop=\"ipaddr\" width=\"130\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"登录地点\" align=\"center\" prop=\"loginLocation\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"浏览器\" align=\"center\" prop=\"browser\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作系统\" align=\"center\" prop=\"os\" />\n      <el-table-column label=\"登录状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作信息\" align=\"center\" prop=\"msg\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"登录日期\" align=\"center\" prop=\"loginTime\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.loginTime) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { list, delLogininfor, cleanLogininfor, unlockLogininfor } from \"@/api/monitor/logininfor\";\n\nexport default {\n  name: \"Logininfor\",\n  dicts: ['sys_common_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 选择用户名\n      selectName: \"\",\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 日期范围\n      dateRange: [],\n      // 默认排序\n      defaultSort: {prop: 'loginTime', order: 'descending'},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        ipaddr: undefined,\n        userName: undefined,\n        status: undefined\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询登录日志列表 */\n    getList() {\n      this.loading = true;\n      list(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.list = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.queryParams.pageNum = 1;\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n    },\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.infoId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n      this.selectName = selection.map(item => item.userName);\n    },\n    /** 排序触发事件 */\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop;\n      this.queryParams.isAsc = column.order;\n      this.getList();\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const infoIds = row.infoId || this.ids;\n      this.$modal.confirm('是否确认删除访问编号为\"' + infoIds + '\"的数据项？').then(function() {\n        return delLogininfor(infoIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 清空按钮操作 */\n    handleClean() {\n      this.$modal.confirm('是否确认清空所有登录日志数据项？').then(function() {\n        return cleanLogininfor();\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"清空成功\");\n      }).catch(() => {});\n    },\n    /** 解锁按钮操作 */\n    handleUnlock() {\n      const username = this.selectName;\n      this.$modal.confirm('是否确认解锁用户\"' + username + '\"数据项?').then(function() {\n        return unlockLogininfor(username);\n      }).then(() => {\n        this.$modal.msgSuccess(\"用户\" + username + \"解锁成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('monitor/logininfor/export', {\n        ...this.queryParams\n      }, `logininfor_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n\n"]}]}