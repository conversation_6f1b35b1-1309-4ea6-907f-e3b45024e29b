{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\min.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\min.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["min.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "min.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n\t<el-form size=\"small\">\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\n\t\t\t\t分钟，允许的通配符[, - * /]\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\n\t\t\t\t周期从\n\t\t\t\t<el-input-number v-model='cycle01' :min=\"0\" :max=\"58\" /> -\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : 1\" :max=\"59\" /> 分钟\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\n\t\t\t\t从\n\t\t\t\t<el-input-number v-model='average01' :min=\"0\" :max=\"58\" /> 分钟开始，每\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"59 - average01 || 0\" /> 分钟执行一次\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\n\t\t\t\t指定\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\n\t\t\t\t\t<el-option v-for=\"item in 60\" :key=\"item\" :value=\"item-1\">{{item-1}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\t</el-form>\n\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tradioValue: 1,\n\t\t\tcycle01: 1,\n\t\t\tcycle02: 2,\n\t\t\taverage01: 0,\n\t\t\taverage02: 1,\n\t\t\tcheckboxList: [],\n\t\t\tcheckNum: this.$options.propsData.check\n\t\t}\n\t},\n\tname: 'crontab-min',\n\tprops: ['check', 'cron'],\n\tmethods: {\n\t\t// 单选按钮值变化时\n\t\tradioChange() {\n\t\t\tswitch (this.radioValue) {\n\t\t\t\tcase 1:\n\t\t\t\t\tthis.$emit('update', 'min', '*', 'min');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tthis.$emit('update', 'min', this.cycleTotal, 'min');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tthis.$emit('update', 'min', this.averageTotal, 'min');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tthis.$emit('update', 'min', this.checkboxString, 'min');\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 周期两个值变化时\n\t\tcycleChange() {\n\t\t\tif (this.radioValue == '2') {\n\t\t\t\tthis.$emit('update', 'min', this.cycleTotal, 'min');\n\t\t\t}\n\t\t},\n\t\t// 平均两个值变化时\n\t\taverageChange() {\n\t\t\tif (this.radioValue == '3') {\n\t\t\t\tthis.$emit('update', 'min', this.averageTotal, 'min');\n\t\t\t}\n\t\t},\n\t\t// checkbox值变化时\n\t\tcheckboxChange() {\n\t\t\tif (this.radioValue == '4') {\n\t\t\t\tthis.$emit('update', 'min', this.checkboxString, 'min');\n\t\t\t}\n\t\t},\n\n\t},\n\twatch: {\n\t\t'radioValue': 'radioChange',\n\t\t'cycleTotal': 'cycleChange',\n\t\t'averageTotal': 'averageChange',\n\t\t'checkboxString': 'checkboxChange',\n\t},\n\tcomputed: {\n\t\t// 计算两个周期值\n\t\tcycleTotal: function () {\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, 0, 58)\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 59)\n\t\t\treturn cycle01 + '-' + cycle02;\n\t\t},\n\t\t// 计算平均用到的值\n\t\taverageTotal: function () {\n\t\t\tconst average01 = this.checkNum(this.average01, 0, 58)\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 59 - average01 || 0)\n\t\t\treturn average01 + '/' + average02;\n\t\t},\n\t\t// 计算勾选的checkbox值合集\n\t\tcheckboxString: function () {\n\t\t\tlet str = this.checkboxList.join();\n\t\t\treturn str == '' ? '*' : str;\n\t\t}\n\t}\n}\n</script>"]}]}