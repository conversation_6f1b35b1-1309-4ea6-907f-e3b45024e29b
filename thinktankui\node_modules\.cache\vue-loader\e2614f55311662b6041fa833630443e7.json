{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\PieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\PieChart.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\n        },\n        legend: {\n          left: 'center',\n          bottom: '10',\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\n        },\n        series: [\n          {\n            name: 'WEEKLY WRITE ARTICLES',\n            type: 'pie',\n            roseType: 'radius',\n            radius: [15, 95],\n            center: ['50%', '38%'],\n            data: [\n              { value: 320, name: 'Industries' },\n              { value: 240, name: 'Technology' },\n              { value: 149, name: 'Forex' },\n              { value: 100, name: 'Gold' },\n              { value: 59, name: 'Forecasts' }\n            ],\n            animationEasing: 'cubicInOut',\n            animationDuration: 2600\n          }\n        ]\n      })\n    }\n  }\n}\n</script>\n"]}]}