{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\IconsDialog.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBpY29uTGlzdCBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9pY29uLmpzb24nCgpjb25zdCBvcmlnaW5MaXN0ID0gaWNvbkxpc3QubWFwKG5hbWUgPT4gYGVsLWljb24tJHtuYW1lfWApCgpleHBvcnQgZGVmYXVsdCB7CiAgaW5oZXJpdEF0dHJzOiBmYWxzZSwKICBwcm9wczogWydjdXJyZW50J10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGljb25MaXN0OiBvcmlnaW5MaXN0LAogICAgICBhY3RpdmU6IG51bGwsCiAgICAgIGtleTogJycKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBrZXkodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLmljb25MaXN0ID0gb3JpZ2luTGlzdC5maWx0ZXIobmFtZSA9PiBuYW1lLmluZGV4T2YodmFsKSA+IC0xKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaWNvbkxpc3QgPSBvcmlnaW5MaXN0CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIG9uT3BlbigpIHsKICAgICAgdGhpcy5hY3RpdmUgPSB0aGlzLmN1cnJlbnQKICAgICAgdGhpcy5rZXkgPSAnJwogICAgfSwKICAgIG9uQ2xvc2UoKSB7fSwKICAgIG9uU2VsZWN0KGljb24pIHsKICAgICAgdGhpcy5hY3RpdmUgPSBpY29uCiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdCcsIGljb24pCiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgZmFsc2UpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["IconsDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IconsDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"icon-dialog\">\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"980px\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <div slot=\"title\">\n        选择图标\n        <el-input\n          v-model=\"key\"\n          size=\"mini\"\n          :style=\"{width: '260px'}\"\n          placeholder=\"请输入图标名称\"\n          prefix-icon=\"el-icon-search\"\n          clearable\n        />\n      </div>\n      <ul class=\"icon-ul\">\n        <li\n          v-for=\"icon in iconList\"\n          :key=\"icon\"\n          :class=\"active===icon?'active-item':''\"\n          @click=\"onSelect(icon)\"\n        >\n          <i :class=\"icon\" />\n          <div>{{ icon }}</div>\n        </li>\n      </ul>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport iconList from '@/utils/generator/icon.json'\n\nconst originList = iconList.map(name => `el-icon-${name}`)\n\nexport default {\n  inheritAttrs: false,\n  props: ['current'],\n  data() {\n    return {\n      iconList: originList,\n      active: null,\n      key: ''\n    }\n  },\n  watch: {\n    key(val) {\n      if (val) {\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\n      } else {\n        this.iconList = originList\n      }\n    }\n  },\n  methods: {\n    onOpen() {\n      this.active = this.current\n      this.key = ''\n    },\n    onClose() {},\n    onSelect(icon) {\n      this.active = icon\n      this.$emit('select', icon)\n      this.$emit('update:visible', false)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.icon-ul {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  li {\n    list-style-type: none;\n    text-align: center;\n    font-size: 14px;\n    display: inline-block;\n    width: 16.66%;\n    box-sizing: border-box;\n    height: 108px;\n    padding: 15px 6px 6px 6px;\n    cursor: pointer;\n    overflow: hidden;\n    &:hover {\n      background: #f2f2f2;\n    }\n    &.active-item{\n      background: #e1f3fb;\n      color: #7a6df0\n    }\n    > i {\n      font-size: 30px;\n      line-height: 50px;\n    }\n  }\n}\n.icon-dialog {\n  ::v-deep .el-dialog {\n    border-radius: 8px;\n    margin-bottom: 0;\n    margin-top: 4vh !important;\n    display: flex;\n    flex-direction: column;\n    max-height: 92vh;\n    overflow: hidden;\n    box-sizing: border-box;\n    .el-dialog__header {\n      padding-top: 14px;\n    }\n    .el-dialog__body {\n      margin: 0 20px 20px 20px;\n      padding: 0;\n      overflow: auto;\n    }\n  }\n}\n</style>\n"]}]}