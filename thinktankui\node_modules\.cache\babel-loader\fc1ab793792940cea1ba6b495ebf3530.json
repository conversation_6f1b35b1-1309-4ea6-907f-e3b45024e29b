{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ScrollPane", "_interopRequireDefault", "require", "_path", "components", "ScrollPane", "data", "visible", "top", "left", "selectedTag", "affixTags", "computed", "visitedViews", "$store", "state", "tagsView", "routes", "permission", "theme", "settings", "watch", "$route", "addTags", "moveToCurrentTag", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "methods", "isActive", "route", "path", "activeStyle", "tag", "isAffix", "meta", "affix", "isFirstView", "fullPath", "err", "isLastView", "length", "filterAffixTags", "_this", "basePath", "arguments", "undefined", "tags", "for<PERSON>ach", "tagPath", "resolve", "push", "name", "_objectSpread2", "default", "children", "tempTags", "concat", "_toConsumableArray2", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "dispatch", "e", "f", "_this2", "$refs", "$nextTick", "_iterator2", "_step2", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "refreshSelectedTag", "view", "$tab", "refreshPage", "link", "closeSelectedTag", "_this3", "closePage", "then", "_ref", "toLastView", "closeRightTags", "_this4", "closeRightPage", "find", "i", "closeLeftTags", "_this5", "closeLeftPage", "closeOthersTags", "_this6", "$router", "catch", "closeOtherPage", "closeAllTags", "_this7", "closeAllPage", "_ref2", "some", "latestView", "slice", "replace", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY", "handleScroll"], "sources": ["src/layout/components/TagsView/index.vue"], "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        :style=\"activeStyle(tag)\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\"><i class=\"el-icon-refresh-right\"></i> 刷新页面</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\"><i class=\"el-icon-close\"></i> 关闭当前</li>\n      <li @click=\"closeOthersTags\"><i class=\"el-icon-circle-close\"></i> 关闭其他</li>\n      <li v-if=\"!isFirstView()\" @click=\"closeLeftTags\"><i class=\"el-icon-back\"></i> 关闭左侧</li>\n      <li v-if=\"!isLastView()\" @click=\"closeRightTags\"><i class=\"el-icon-right\"></i> 关闭右侧</li>\n      <li @click=\"closeAllTags(selectedTag)\"><i class=\"el-icon-circle-close\"></i> 全部关闭</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    },\n    routes() {\n      return this.$store.state.permission.routes\n    },\n    theme() {\n      return this.$store.state.settings.theme;\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    activeStyle(tag) {\n      if (!this.isActive(tag)) return {};\n      return {\n        \"background-color\": this.theme,\n        \"border-color\": this.theme\n      };\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    isFirstView() {\n      try {\n        return this.selectedTag.fullPath === '/index' || this.selectedTag.fullPath === this.visitedViews[1].fullPath\n      } catch (err) {\n        return false\n      }\n    },\n    isLastView() {\n      try {\n        return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath\n      } catch (err) {\n        return false\n      }\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      routes.forEach(route => {\n        if (route.meta && route.meta.affix) {\n          const tagPath = path.resolve(basePath, route.path)\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: { ...route.meta }\n          })\n        }\n        if (route.children) {\n          const tempTags = this.filterAffixTags(route.children, route.path)\n          if (tempTags.length >= 1) {\n            tags = [...tags, ...tempTags]\n          }\n        }\n      })\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      if (name) {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n    },\n    moveToCurrentTag() {\n      const tags = this.$refs.tag\n      this.$nextTick(() => {\n        for (const tag of tags) {\n          if (tag.to.path === this.$route.path) {\n            this.$refs.scrollPane.moveToTarget(tag)\n            // when query is different then update\n            if (tag.to.fullPath !== this.$route.fullPath) {\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n            }\n            break\n          }\n        }\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$tab.refreshPage(view);\n      if (this.$route.meta.link) {\n        this.$store.dispatch('tagsView/delIframeView', this.$route)\n      }\n    },\n    closeSelectedTag(view) {\n      this.$tab.closePage(view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeRightTags() {\n      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\n          this.toLastView(visitedViews)\n        }\n      })\n    },\n    closeLeftTags() {\n      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\n          this.toLastView(visitedViews)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag.fullPath).catch(()=>{});\n      this.$tab.closeOtherPage(this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$tab.closeAllPage().then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === 'Dashboard') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-view-container {\n  height: 34px;\n  width: 100%;\n  background: #fff;\n  border-bottom: 1px solid #d8dce5;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 26px;\n      line-height: 26px;\n      border: 1px solid #d8dce5;\n      color: #495060;\n      background: #fff;\n      padding: 0 8px;\n      font-size: 12px;\n      margin-left: 5px;\n      margin-top: 4px;\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n      &.active {\n        background-color: #42b983;\n        color: #fff;\n        border-color: #42b983;\n        &::before {\n          content: '';\n          background: #fff;\n          display: inline-block;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 2px;\n        }\n      }\n    }\n  }\n  .contextmenu {\n    margin: 0;\n    background: #fff;\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 5px 0;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 400;\n    color: #333;\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\n    li {\n      margin: 0;\n      padding: 7px 16px;\n      cursor: pointer;\n      &:hover {\n        background: #eee;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\n      transform-origin: 100% 50%;\n      &:before {\n        transform: scale(.6);\n        display: inline-block;\n        vertical-align: -3px;\n      }\n      &:hover {\n        background-color: #b4bccc;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA+BA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,YAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAG,UAAA,CAAAD,MAAA;IACA;IACAE,KAAA,WAAAA,MAAA;MACA,YAAAL,MAAA,CAAAC,KAAA,CAAAK,QAAA,CAAAD,KAAA;IACA;EACA;EACAE,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;IACA;IACAjB,OAAA,WAAAA,QAAAkB,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,SAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,SAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAT,OAAA;EACA;EACAU,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MACA,OAAAA,KAAA,CAAAC,IAAA,UAAAd,MAAA,CAAAc,IAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,UAAAJ,QAAA,CAAAI,GAAA;MACA;QACA,yBAAAnB,KAAA;QACA,qBAAAA;MACA;IACA;IACAoB,OAAA,WAAAA,QAAAD,GAAA;MACA,OAAAA,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAC,KAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA;QACA,YAAAhC,WAAA,CAAAiC,QAAA,sBAAAjC,WAAA,CAAAiC,QAAA,UAAA9B,YAAA,IAAA8B,QAAA;MACA,SAAAC,GAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA;QACA,YAAAnC,WAAA,CAAAiC,QAAA,UAAA9B,YAAA,MAAAA,YAAA,CAAAiC,MAAA,MAAAH,QAAA;MACA,SAAAC,GAAA;QACA;MACA;IACA;IACAG,eAAA,WAAAA,gBAAA9B,MAAA;MAAA,IAAA+B,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,IAAA;MACAnC,MAAA,CAAAoC,OAAA,WAAAlB,KAAA;QACA,IAAAA,KAAA,CAAAK,IAAA,IAAAL,KAAA,CAAAK,IAAA,CAAAC,KAAA;UACA,IAAAa,OAAA,GAAAlB,aAAA,CAAAmB,OAAA,CAAAN,QAAA,EAAAd,KAAA,CAAAC,IAAA;UACAgB,IAAA,CAAAI,IAAA;YACAb,QAAA,EAAAW,OAAA;YACAlB,IAAA,EAAAkB,OAAA;YACAG,IAAA,EAAAtB,KAAA,CAAAsB,IAAA;YACAjB,IAAA,MAAAkB,cAAA,CAAAC,OAAA,MAAAxB,KAAA,CAAAK,IAAA;UACA;QACA;QACA,IAAAL,KAAA,CAAAyB,QAAA;UACA,IAAAC,QAAA,GAAAb,KAAA,CAAAD,eAAA,CAAAZ,KAAA,CAAAyB,QAAA,EAAAzB,KAAA,CAAAC,IAAA;UACA,IAAAyB,QAAA,CAAAf,MAAA;YACAM,IAAA,MAAAU,MAAA,KAAAC,mBAAA,CAAAJ,OAAA,EAAAP,IAAA,OAAAW,mBAAA,CAAAJ,OAAA,EAAAE,QAAA;UACA;QACA;MACA;MACA,OAAAT,IAAA;IACA;IACApB,QAAA,WAAAA,SAAA;MACA,IAAArB,SAAA,QAAAA,SAAA,QAAAoC,eAAA,MAAA9B,MAAA;MAAA,IAAA+C,SAAA,OAAAC,2BAAA,CAAAN,OAAA,EACAhD,SAAA;QAAAuD,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA/B,GAAA,GAAA4B,KAAA,CAAAzC,KAAA;UACA;UACA,IAAAa,GAAA,CAAAmB,IAAA;YACA,KAAA3C,MAAA,CAAAwD,QAAA,4BAAAhC,GAAA;UACA;QACA;MAAA,SAAAM,GAAA;QAAAoB,SAAA,CAAAO,CAAA,CAAA3B,GAAA;MAAA;QAAAoB,SAAA,CAAAQ,CAAA;MAAA;IACA;IACAjD,OAAA,WAAAA,QAAA;MACA,IAAAkC,IAAA,QAAAnC,MAAA,CAAAmC,IAAA;MACA,IAAAA,IAAA;QACA,KAAA3C,MAAA,CAAAwD,QAAA,0BAAAhD,MAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAAiD,MAAA;MACA,IAAArB,IAAA,QAAAsB,KAAA,CAAApC,GAAA;MACA,KAAAqC,SAAA;QAAA,IAAAC,UAAA,OAAAX,2BAAA,CAAAN,OAAA,EACAP,IAAA;UAAAyB,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAAR,CAAA,IAAAC,IAAA;YAAA,IAAA/B,GAAA,GAAAuC,MAAA,CAAApD,KAAA;YACA,IAAAa,GAAA,CAAAwC,EAAA,CAAA1C,IAAA,KAAAqC,MAAA,CAAAnD,MAAA,CAAAc,IAAA;cACAqC,MAAA,CAAAC,KAAA,CAAAK,UAAA,CAAAC,YAAA,CAAA1C,GAAA;cACA;cACA,IAAAA,GAAA,CAAAwC,EAAA,CAAAnC,QAAA,KAAA8B,MAAA,CAAAnD,MAAA,CAAAqB,QAAA;gBACA8B,MAAA,CAAA3D,MAAA,CAAAwD,QAAA,+BAAAG,MAAA,CAAAnD,MAAA;cACA;cACA;YACA;UACA;QAAA,SAAAsB,GAAA;UAAAgC,UAAA,CAAAL,CAAA,CAAA3B,GAAA;QAAA;UAAAgC,UAAA,CAAAJ,CAAA;QAAA;MACA;IACA;IACAS,kBAAA,WAAAA,mBAAAC,IAAA;MACA,KAAAC,IAAA,CAAAC,WAAA,CAAAF,IAAA;MACA,SAAA5D,MAAA,CAAAkB,IAAA,CAAA6C,IAAA;QACA,KAAAvE,MAAA,CAAAwD,QAAA,gCAAAhD,MAAA;MACA;IACA;IACAgE,gBAAA,WAAAA,iBAAAJ,IAAA;MAAA,IAAAK,MAAA;MACA,KAAAJ,IAAA,CAAAK,SAAA,CAAAN,IAAA,EAAAO,IAAA,WAAAC,IAAA;QAAA,IAAA7E,YAAA,GAAA6E,IAAA,CAAA7E,YAAA;QACA,IAAA0E,MAAA,CAAArD,QAAA,CAAAgD,IAAA;UACAK,MAAA,CAAAI,UAAA,CAAA9E,YAAA,EAAAqE,IAAA;QACA;MACA;IACA;IACAU,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,IAAA,CAAAW,cAAA,MAAApF,WAAA,EAAA+E,IAAA,WAAA5E,YAAA;QACA,KAAAA,YAAA,CAAAkF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,QAAA,KAAAkD,MAAA,CAAAvE,MAAA,CAAAqB,QAAA;QAAA;UACAkD,MAAA,CAAAF,UAAA,CAAA9E,YAAA;QACA;MACA;IACA;IACAoF,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAf,IAAA,CAAAgB,aAAA,MAAAzF,WAAA,EAAA+E,IAAA,WAAA5E,YAAA;QACA,KAAAA,YAAA,CAAAkF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,QAAA,KAAAuD,MAAA,CAAA5E,MAAA,CAAAqB,QAAA;QAAA;UACAuD,MAAA,CAAAP,UAAA,CAAA9E,YAAA;QACA;MACA;IACA;IACAuF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,OAAA,CAAA9C,IAAA,MAAA9C,WAAA,CAAAiC,QAAA,EAAA4D,KAAA;MACA,KAAApB,IAAA,CAAAqB,cAAA,MAAA9F,WAAA,EAAA+E,IAAA;QACAY,MAAA,CAAA7E,gBAAA;MACA;IACA;IACAiF,YAAA,WAAAA,aAAAvB,IAAA;MAAA,IAAAwB,MAAA;MACA,KAAAvB,IAAA,CAAAwB,YAAA,GAAAlB,IAAA,WAAAmB,KAAA;QAAA,IAAA/F,YAAA,GAAA+F,KAAA,CAAA/F,YAAA;QACA,IAAA6F,MAAA,CAAA/F,SAAA,CAAAkG,IAAA,WAAAvE,GAAA;UAAA,OAAAA,GAAA,CAAAF,IAAA,KAAAsE,MAAA,CAAApF,MAAA,CAAAc,IAAA;QAAA;UACA;QACA;QACAsE,MAAA,CAAAf,UAAA,CAAA9E,YAAA,EAAAqE,IAAA;MACA;IACA;IACAS,UAAA,WAAAA,WAAA9E,YAAA,EAAAqE,IAAA;MACA,IAAA4B,UAAA,GAAAjG,YAAA,CAAAkG,KAAA;MACA,IAAAD,UAAA;QACA,KAAAR,OAAA,CAAA9C,IAAA,CAAAsD,UAAA,CAAAnE,QAAA;MACA;QACA;QACA;QACA,IAAAuC,IAAA,CAAAzB,IAAA;UACA;UACA,KAAA6C,OAAA,CAAAU,OAAA;YAAA5E,IAAA,gBAAA8C,IAAA,CAAAvC;UAAA;QACA;UACA,KAAA2D,OAAA,CAAA9C,IAAA;QACA;MACA;IACA;IACAyD,QAAA,WAAAA,SAAA3E,GAAA,EAAAiC,CAAA;MACA,IAAA2C,YAAA;MACA,IAAAC,UAAA,QAAAC,GAAA,CAAAC,qBAAA,GAAA5G,IAAA;MACA,IAAA6G,WAAA,QAAAF,GAAA,CAAAE,WAAA;MACA,IAAAC,OAAA,GAAAD,WAAA,GAAAJ,YAAA;MACA,IAAAzG,IAAA,GAAA8D,CAAA,CAAAiD,OAAA,GAAAL,UAAA;;MAEA,IAAA1G,IAAA,GAAA8G,OAAA;QACA,KAAA9G,IAAA,GAAA8G,OAAA;MACA;QACA,KAAA9G,IAAA,GAAAA,IAAA;MACA;MAEA,KAAAD,GAAA,GAAA+D,CAAA,CAAAkD,OAAA;MACA,KAAAlH,OAAA;MACA,KAAAG,WAAA,GAAA4B,GAAA;IACA;IACAT,SAAA,WAAAA,UAAA;MACA,KAAAtB,OAAA;IACA;IACAmH,YAAA,WAAAA,aAAA;MACA,KAAA7F,SAAA;IACA;EACA;AACA", "ignoreList": []}]}