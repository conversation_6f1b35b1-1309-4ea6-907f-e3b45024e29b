{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\modules\\normalizeExternalHTML\\normalizers\\googleDocs.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\modules\\normalizeExternalHTML\\normalizers\\googleDocs.js", "mtime": 1749105929552}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSBub3JtYWxpemU7CnZhciBfdG9Db25zdW1hYmxlQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvdG9Db25zdW1hYmxlQXJyYXkuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZyb20uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubWF0Y2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnZhciBub3JtYWxXZWlnaHRSZWdleHAgPSAvZm9udC13ZWlnaHQ6XHMqbm9ybWFsLzsKdmFyIGJsb2NrVGFnTmFtZXMgPSBbJ1AnLCAnT0wnLCAnVUwnXTsKdmFyIGlzQmxvY2tFbGVtZW50ID0gZnVuY3Rpb24gaXNCbG9ja0VsZW1lbnQoZWxlbWVudCkgewogIHJldHVybiBlbGVtZW50ICYmIGJsb2NrVGFnTmFtZXMuaW5jbHVkZXMoZWxlbWVudC50YWdOYW1lKTsKfTsKdmFyIG5vcm1hbGl6ZUVtcHR5TGluZXMgPSBmdW5jdGlvbiBub3JtYWxpemVFbXB0eUxpbmVzKGRvYykgewogIEFycmF5LmZyb20oZG9jLnF1ZXJ5U2VsZWN0b3JBbGwoJ2JyJykpLmZpbHRlcihmdW5jdGlvbiAoYnIpIHsKICAgIHJldHVybiBpc0Jsb2NrRWxlbWVudChici5wcmV2aW91c0VsZW1lbnRTaWJsaW5nKSAmJiBpc0Jsb2NrRWxlbWVudChici5uZXh0RWxlbWVudFNpYmxpbmcpOwogIH0pLmZvckVhY2goZnVuY3Rpb24gKGJyKSB7CiAgICB2YXIgX2JyJHBhcmVudE5vZGU7CiAgICAoX2JyJHBhcmVudE5vZGUgPSBici5wYXJlbnROb2RlKSA9PT0gbnVsbCB8fCBfYnIkcGFyZW50Tm9kZSA9PT0gdm9pZCAwIHx8IF9iciRwYXJlbnROb2RlLnJlbW92ZUNoaWxkKGJyKTsKICB9KTsKfTsKdmFyIG5vcm1hbGl6ZUZvbnRXZWlnaHQgPSBmdW5jdGlvbiBub3JtYWxpemVGb250V2VpZ2h0KGRvYykgewogIEFycmF5LmZyb20oZG9jLnF1ZXJ5U2VsZWN0b3JBbGwoJ2Jbc3R5bGUqPSJmb250LXdlaWdodCJdJykpLmZpbHRlcihmdW5jdGlvbiAobm9kZSkgewogICAgdmFyIF9ub2RlJGdldEF0dHJpYnV0ZTsKICAgIHJldHVybiAoX25vZGUkZ2V0QXR0cmlidXRlID0gbm9kZS5nZXRBdHRyaWJ1dGUoJ3N0eWxlJykpID09PSBudWxsIHx8IF9ub2RlJGdldEF0dHJpYnV0ZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX25vZGUkZ2V0QXR0cmlidXRlLm1hdGNoKG5vcm1hbFdlaWdodFJlZ2V4cCk7CiAgfSkuZm9yRWFjaChmdW5jdGlvbiAobm9kZSkgewogICAgdmFyIF9ub2RlJHBhcmVudE5vZGU7CiAgICB2YXIgZnJhZ21lbnQgPSBkb2MuY3JlYXRlRG9jdW1lbnRGcmFnbWVudCgpOwogICAgZnJhZ21lbnQuYXBwZW5kLmFwcGx5KGZyYWdtZW50LCAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShub2RlLmNoaWxkTm9kZXMpKTsKICAgIChfbm9kZSRwYXJlbnROb2RlID0gbm9kZS5wYXJlbnROb2RlKSA9PT0gbnVsbCB8fCBfbm9kZSRwYXJlbnROb2RlID09PSB2b2lkIDAgfHwgX25vZGUkcGFyZW50Tm9kZS5yZXBsYWNlQ2hpbGQoZnJhZ21lbnQsIG5vZGUpOwogIH0pOwp9OwpmdW5jdGlvbiBub3JtYWxpemUoZG9jKSB7CiAgaWYgKGRvYy5xdWVyeVNlbGVjdG9yKCdbaWRePSJkb2NzLWludGVybmFsLWd1aWQtIl0nKSkgewogICAgbm9ybWFsaXplRm9udFdlaWdodChkb2MpOwogICAgbm9ybWFsaXplRW1wdHlMaW5lcyhkb2MpOwogIH0KfQ=="}, {"version": 3, "names": ["normalWeightRegexp", "blockTagNames", "isBlockElement", "element", "includes", "tagName", "normalizeEmptyLines", "doc", "Array", "from", "querySelectorAll", "filter", "br", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "for<PERSON>ach", "_br$parentNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "normalizeFontWeight", "node", "_node$getAttribute", "getAttribute", "match", "_node$parentNode", "fragment", "createDocumentFragment", "append", "apply", "_toConsumableArray2", "default", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "normalize", "querySelector"], "sources": ["../../../../src/modules/normalizeExternalHTML/normalizers/googleDocs.ts"], "sourcesContent": ["const normalWeightRegexp = /font-weight:\\s*normal/;\nconst blockTagNames = ['P', 'OL', 'UL'];\n\nconst isBlockElement = (element: Element | null) => {\n  return element && blockTagNames.includes(element.tagName);\n};\n\nconst normalizeEmptyLines = (doc: Document) => {\n  Array.from(doc.querySelectorAll('br'))\n    .filter(\n      (br) =>\n        isBlockElement(br.previousElementSibling) &&\n        isBlockElement(br.nextElementSibling),\n    )\n    .forEach((br) => {\n      br.parentNode?.removeChild(br);\n    });\n};\n\nconst normalizeFontWeight = (doc: Document) => {\n  Array.from(doc.querySelectorAll('b[style*=\"font-weight\"]'))\n    .filter((node) => node.getAttribute('style')?.match(normalWeightRegexp))\n    .forEach((node) => {\n      const fragment = doc.createDocumentFragment();\n      fragment.append(...node.childNodes);\n      node.parentNode?.replaceChild(fragment, node);\n    });\n};\n\nexport default function normalize(doc: Document) {\n  if (doc.querySelector('[id^=\"docs-internal-guid-\"]')) {\n    normalizeFontWeight(doc);\n    normalizeEmptyLines(doc);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAMA,kBAAkB,GAAG,uBAAuB;AAClD,IAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;AAEvC,IAAMC,cAAc,GAAI,SAAlBA,cAAcA,CAAIC,OAAuB,EAAK;EAClD,OAAOA,OAAO,IAAIF,aAAa,CAACG,QAAQ,CAACD,OAAO,CAACE,OAAO,CAAC;AAC3D,CAAC;AAED,IAAMC,mBAAmB,GAAI,SAAvBA,mBAAmBA,CAAIC,GAAa,EAAK;EAC7CC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CACnCC,MAAM,CACJ,UAAAC,EAAE;IAAA,OACDV,cAAc,CAACU,EAAE,CAACC,sBAAsB,CAAC,IACzCX,cAAc,CAACU,EAAE,CAACE,kBAAkB,CACxC;EAAA,EAAC,CACAC,OAAO,CAAE,UAAAH,EAAE,EAAK;IAAA,IAAAI,cAAA;IACf,CAAAA,cAAA,GAAAJ,EAAE,CAACK,UAAU,cAAAD,cAAA,eAAbA,cAAA,CAAeE,WAAW,CAACN,EAAE,CAAC;EAChC,CAAC,CAAC;AACN,CAAC;AAED,IAAMO,mBAAmB,GAAI,SAAvBA,mBAAmBA,CAAIZ,GAAa,EAAK;EAC7CC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,CACxDC,MAAM,CAAE,UAAAS,IAAI;IAAA,IAAAC,kBAAA;IAAA,QAAAA,kBAAA,GAAKD,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC,cAAAD,kBAAA,uBAA1BA,kBAAA,CAA4BE,KAAK,CAACvB,kBAAkB,CAAC;EAAA,EAAC,CACvEe,OAAO,CAAE,UAAAK,IAAI,EAAK;IAAA,IAAAI,gBAAA;IACjB,IAAMC,QAAQ,GAAGlB,GAAG,CAACmB,sBAAsB,CAAC,CAAC;IAC7CD,QAAQ,CAACE,MAAM,CAAAC,KAAA,CAAfH,QAAQ,MAAAI,mBAAA,CAAAC,OAAA,EAAWV,IAAI,CAACW,UAAU,EAAC;IACnC,CAAAP,gBAAA,GAAAJ,IAAI,CAACH,UAAU,cAAAO,gBAAA,eAAfA,gBAAA,CAAiBQ,YAAY,CAACP,QAAQ,EAAEL,IAAI,CAAC;EAC/C,CAAC,CAAC;AACN,CAAC;AAEc,SAASa,SAASA,CAAC1B,GAAa,EAAE;EAC/C,IAAIA,GAAG,CAAC2B,aAAa,CAAC,6BAA6B,CAAC,EAAE;IACpDf,mBAAmB,CAACZ,GAAG,CAAC;IACxBD,mBAAmB,CAACC,GAAG,CAAC;EAC1B;AACF", "ignoreList": []}]}