{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\index.vue?vue&type=style&index=0&id=2216c3ec&scoped=true&lang=css", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoucG9wX2J0biB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi10b3A6IDIwcHg7Cn0KLnBvcHVwLW1haW4gewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBtYXJnaW46IDEwcHggYXV0bzsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJvcmRlci1yYWRpdXM6IDVweDsKICBmb250LXNpemU6IDEycHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoucG9wdXAtdGl0bGUgewogIG92ZXJmbG93OiBoaWRkZW47CiAgbGluZS1oZWlnaHQ6IDM0cHg7CiAgcGFkZGluZy10b3A6IDZweDsKICBiYWNrZ3JvdW5kOiAjZjJmMmYyOwp9Ci5wb3B1cC1yZXN1bHQgewogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgbWFyZ2luOiAyNXB4IGF1dG87CiAgcGFkZGluZzogMTVweCAxMHB4IDEwcHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2NjYzsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KLnBvcHVwLXJlc3VsdCAudGl0bGUgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IC0yOHB4OwogIGxlZnQ6IDUwJTsKICB3aWR0aDogMTQwcHg7CiAgZm9udC1zaXplOiAxNHB4OwogIG1hcmdpbi1sZWZ0OiAtNzBweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbGluZS1oZWlnaHQ6IDMwcHg7CiAgYmFja2dyb3VuZDogI2ZmZjsKfQoucG9wdXAtcmVzdWx0IHRhYmxlIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgd2lkdGg6IDEwMCU7CiAgbWFyZ2luOiAwIGF1dG87Cn0KLnBvcHVwLXJlc3VsdCB0YWJsZSBzcGFuIHsKICBkaXNwbGF5OiBibG9jazsKICB3aWR0aDogMTAwJTsKICBmb250LWZhbWlseTogYXJpYWw7CiAgbGluZS1oZWlnaHQ6IDMwcHg7CiAgaGVpZ2h0OiAzMHB4OwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4Owp9Ci5wb3B1cC1yZXN1bHQtc2Nyb2xsIHsKICBmb250LXNpemU6IDEycHg7CiAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgaGVpZ2h0OiAxMGVtOwogIG92ZXJmbG93LXk6IGF1dG87Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n  <div>\n    <el-tabs type=\"border-card\">\n      <el-tab-pane label=\"秒\" v-if=\"shouldHide('second')\">\n        <CrontabSecond\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronsecond\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"分钟\" v-if=\"shouldHide('min')\">\n        <CrontabMin\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronmin\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"小时\" v-if=\"shouldHide('hour')\">\n        <CrontabHour\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronhour\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"日\" v-if=\"shouldHide('day')\">\n        <CrontabDay\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronday\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"月\" v-if=\"shouldHide('month')\">\n        <CrontabMonth\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronmonth\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"周\" v-if=\"shouldHide('week')\">\n        <CrontabWeek\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronweek\"\n        />\n      </el-tab-pane>\n\n      <el-tab-pane label=\"年\" v-if=\"shouldHide('year')\">\n        <CrontabYear\n          @update=\"updateCrontabValue\"\n          :check=\"checkNumber\"\n          :cron=\"crontabValueObj\"\n          ref=\"cronyear\"\n        />\n      </el-tab-pane>\n    </el-tabs>\n\n    <div class=\"popup-main\">\n      <div class=\"popup-result\">\n        <p class=\"title\">时间表达式</p>\n        <table>\n          <thead>\n            <th v-for=\"item of tabTitles\" width=\"40\" :key=\"item\">{{item}}</th>\n            <th>Cron 表达式</th>\n          </thead>\n          <tbody>\n            <td>\n              <span>{{crontabValueObj.second}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.min}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.hour}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.day}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.month}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.week}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueObj.year}}</span>\n            </td>\n            <td>\n              <span>{{crontabValueString}}</span>\n            </td>\n          </tbody>\n        </table>\n      </div>\n      <CrontabResult :ex=\"crontabValueString\"></CrontabResult>\n\n      <div class=\"pop_btn\">\n        <el-button size=\"small\" type=\"primary\" @click=\"submitFill\">确定</el-button>\n        <el-button size=\"small\" type=\"warning\" @click=\"clearCron\">重置</el-button>\n        <el-button size=\"small\" @click=\"hidePopup\">取消</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CrontabSecond from \"./second.vue\";\nimport CrontabMin from \"./min.vue\";\nimport CrontabHour from \"./hour.vue\";\nimport CrontabDay from \"./day.vue\";\nimport CrontabMonth from \"./month.vue\";\nimport CrontabWeek from \"./week.vue\";\nimport CrontabYear from \"./year.vue\";\nimport CrontabResult from \"./result.vue\";\n\nexport default {\n  data() {\n    return {\n      tabTitles: [\"秒\", \"分钟\", \"小时\", \"日\", \"月\", \"周\", \"年\"],\n      tabActive: 0,\n      myindex: 0,\n      crontabValueObj: {\n        second: \"*\",\n        min: \"*\",\n        hour: \"*\",\n        day: \"*\",\n        month: \"*\",\n        week: \"?\",\n        year: \"\",\n      },\n    };\n  },\n  name: \"vcrontab\",\n  props: [\"expression\", \"hideComponent\"],\n  methods: {\n    shouldHide(key) {\n      if (this.hideComponent && this.hideComponent.includes(key)) return false;\n      return true;\n    },\n    resolveExp() {\n      // 反解析 表达式\n      if (this.expression) {\n        let arr = this.expression.split(\" \");\n        if (arr.length >= 6) {\n          //6 位以上是合法表达式\n          let obj = {\n            second: arr[0],\n            min: arr[1],\n            hour: arr[2],\n            day: arr[3],\n            month: arr[4],\n            week: arr[5],\n            year: arr[6] ? arr[6] : \"\",\n          };\n          this.crontabValueObj = {\n            ...obj,\n          };\n          for (let i in obj) {\n            if (obj[i]) this.changeRadio(i, obj[i]);\n          }\n        }\n      } else {\n        // 没有传入的表达式 则还原\n        this.clearCron();\n      }\n    },\n    // tab切换值\n    tabCheck(index) {\n      this.tabActive = index;\n    },\n    // 由子组件触发，更改表达式组成的字段值\n    updateCrontabValue(name, value, from) {\n      \"updateCrontabValue\", name, value, from;\n      this.crontabValueObj[name] = value;\n      if (from && from !== name) {\n        console.log(`来自组件 ${from} 改变了 ${name} ${value}`);\n        this.changeRadio(name, value);\n      }\n    },\n    // 赋值到组件\n    changeRadio(name, value) {\n      let arr = [\"second\", \"min\", \"hour\", \"month\"],\n        refName = \"cron\" + name,\n        insValue;\n\n      if (!this.$refs[refName]) return;\n\n      if (arr.includes(name)) {\n        if (value === \"*\") {\n          insValue = 1;\n        } else if (value.indexOf(\"-\") > -1) {\n          let indexArr = value.split(\"-\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].cycle01 = 0)\n            : (this.$refs[refName].cycle01 = indexArr[0]);\n          this.$refs[refName].cycle02 = indexArr[1];\n          insValue = 2;\n        } else if (value.indexOf(\"/\") > -1) {\n          let indexArr = value.split(\"/\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].average01 = 0)\n            : (this.$refs[refName].average01 = indexArr[0]);\n          this.$refs[refName].average02 = indexArr[1];\n          insValue = 3;\n        } else {\n          insValue = 4;\n          this.$refs[refName].checkboxList = value.split(\",\");\n        }\n      } else if (name == \"day\") {\n        if (value === \"*\") {\n          insValue = 1;\n        } else if (value == \"?\") {\n          insValue = 2;\n        } else if (value.indexOf(\"-\") > -1) {\n          let indexArr = value.split(\"-\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].cycle01 = 0)\n            : (this.$refs[refName].cycle01 = indexArr[0]);\n          this.$refs[refName].cycle02 = indexArr[1];\n          insValue = 3;\n        } else if (value.indexOf(\"/\") > -1) {\n          let indexArr = value.split(\"/\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].average01 = 0)\n            : (this.$refs[refName].average01 = indexArr[0]);\n          this.$refs[refName].average02 = indexArr[1];\n          insValue = 4;\n        } else if (value.indexOf(\"W\") > -1) {\n          let indexArr = value.split(\"W\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].workday = 0)\n            : (this.$refs[refName].workday = indexArr[0]);\n          insValue = 5;\n        } else if (value === \"L\") {\n          insValue = 6;\n        } else {\n          this.$refs[refName].checkboxList = value.split(\",\");\n          insValue = 7;\n        }\n      } else if (name == \"week\") {\n        if (value === \"*\") {\n          insValue = 1;\n        } else if (value == \"?\") {\n          insValue = 2;\n        } else if (value.indexOf(\"-\") > -1) {\n          let indexArr = value.split(\"-\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].cycle01 = 0)\n            : (this.$refs[refName].cycle01 = indexArr[0]);\n          this.$refs[refName].cycle02 = indexArr[1];\n          insValue = 3;\n        } else if (value.indexOf(\"#\") > -1) {\n          let indexArr = value.split(\"#\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].average01 = 1)\n            : (this.$refs[refName].average01 = indexArr[0]);\n          this.$refs[refName].average02 = indexArr[1];\n          insValue = 4;\n        } else if (value.indexOf(\"L\") > -1) {\n          let indexArr = value.split(\"L\");\n          isNaN(indexArr[0])\n            ? (this.$refs[refName].weekday = 1)\n            : (this.$refs[refName].weekday = indexArr[0]);\n          insValue = 5;\n        } else {\n          this.$refs[refName].checkboxList = value.split(\",\");\n          insValue = 6;\n        }\n      } else if (name == \"year\") {\n        if (value == \"\") {\n          insValue = 1;\n        } else if (value == \"*\") {\n          insValue = 2;\n        } else if (value.indexOf(\"-\") > -1) {\n          insValue = 3;\n        } else if (value.indexOf(\"/\") > -1) {\n          insValue = 4;\n        } else {\n          this.$refs[refName].checkboxList = value.split(\",\");\n          insValue = 5;\n        }\n      }\n      this.$refs[refName].radioValue = insValue;\n    },\n    // 表单选项的子组件校验数字格式（通过-props传递）\n    checkNumber(value, minLimit, maxLimit) {\n      // 检查必须为整数\n      value = Math.floor(value);\n      if (value < minLimit) {\n        value = minLimit;\n      } else if (value > maxLimit) {\n        value = maxLimit;\n      }\n      return value;\n    },\n    // 隐藏弹窗\n    hidePopup() {\n      this.$emit(\"hide\");\n    },\n    // 填充表达式\n    submitFill() {\n      this.$emit(\"fill\", this.crontabValueString);\n      this.hidePopup();\n    },\n    clearCron() {\n      // 还原选择项\n      (\"准备还原\");\n      this.crontabValueObj = {\n        second: \"*\",\n        min: \"*\",\n        hour: \"*\",\n        day: \"*\",\n        month: \"*\",\n        week: \"?\",\n        year: \"\",\n      };\n      for (let j in this.crontabValueObj) {\n        this.changeRadio(j, this.crontabValueObj[j]);\n      }\n    },\n  },\n  computed: {\n    crontabValueString: function() {\n      let obj = this.crontabValueObj;\n      let str =\n        obj.second +\n        \" \" +\n        obj.min +\n        \" \" +\n        obj.hour +\n        \" \" +\n        obj.day +\n        \" \" +\n        obj.month +\n        \" \" +\n        obj.week +\n        (obj.year == \"\" ? \"\" : \" \" + obj.year);\n      return str;\n    },\n  },\n  components: {\n    CrontabSecond,\n    CrontabMin,\n    CrontabHour,\n    CrontabDay,\n    CrontabMonth,\n    CrontabWeek,\n    CrontabYear,\n    CrontabResult,\n  },\n  watch: {\n    expression: \"resolveExp\",\n    hideComponent(value) {\n      // 隐藏部分组件\n    },\n  },\n  mounted: function() {\n    this.resolveExp();\n  },\n};\n</script>\n<style scoped>\n.pop_btn {\n  text-align: center;\n  margin-top: 20px;\n}\n.popup-main {\n  position: relative;\n  margin: 10px auto;\n  background: #fff;\n  border-radius: 5px;\n  font-size: 12px;\n  overflow: hidden;\n}\n.popup-title {\n  overflow: hidden;\n  line-height: 34px;\n  padding-top: 6px;\n  background: #f2f2f2;\n}\n.popup-result {\n  box-sizing: border-box;\n  line-height: 24px;\n  margin: 25px auto;\n  padding: 15px 10px 10px;\n  border: 1px solid #ccc;\n  position: relative;\n}\n.popup-result .title {\n  position: absolute;\n  top: -28px;\n  left: 50%;\n  width: 140px;\n  font-size: 14px;\n  margin-left: -70px;\n  text-align: center;\n  line-height: 30px;\n  background: #fff;\n}\n.popup-result table {\n  text-align: center;\n  width: 100%;\n  margin: 0 auto;\n}\n.popup-result table span {\n  display: block;\n  width: 100%;\n  font-family: arial;\n  line-height: 30px;\n  height: 30px;\n  white-space: nowrap;\n  overflow: hidden;\n  border: 1px solid #e8e8e8;\n}\n.popup-result-scroll {\n  font-size: 12px;\n  line-height: 24px;\n  height: 10em;\n  overflow-y: auto;\n}\n</style>\n"]}]}