{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnTWVudUl0ZW0nLAogIGZ1bmN0aW9uYWw6IHRydWUsCiAgcHJvcHM6IHsKICAgIGljb246IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHRpdGxlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgsIGNvbnRleHQpIHsKICAgIHZhciBfY29udGV4dCRwcm9wcyA9IGNvbnRleHQucHJvcHMsCiAgICAgIGljb24gPSBfY29udGV4dCRwcm9wcy5pY29uLAogICAgICB0aXRsZSA9IF9jb250ZXh0JHByb3BzLnRpdGxlOwogICAgdmFyIHZub2RlcyA9IFtdOwogICAgaWYgKGljb24pIHsKICAgICAgdm5vZGVzLnB1c2goaCgic3ZnLWljb24iLCB7CiAgICAgICAgImF0dHJzIjogewogICAgICAgICAgImljb24tY2xhc3MiOiBpY29uCiAgICAgICAgfQogICAgICB9KSk7CiAgICB9CiAgICBpZiAodGl0bGUpIHsKICAgICAgaWYgKHRpdGxlLmxlbmd0aCA+IDUpIHsKICAgICAgICB2bm9kZXMucHVzaChoKCJzcGFuIiwgewogICAgICAgICAgInNsb3QiOiAndGl0bGUnLAogICAgICAgICAgImF0dHJzIjogewogICAgICAgICAgICAidGl0bGUiOiB0aXRsZQogICAgICAgICAgfQogICAgICAgIH0sIFt0aXRsZV0pKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB2bm9kZXMucHVzaChoKCJzcGFuIiwgewogICAgICAgICAgInNsb3QiOiAndGl0bGUnCiAgICAgICAgfSwgW3RpdGxlXSkpOwogICAgICB9CiAgICB9CiAgICByZXR1cm4gdm5vZGVzOwogIH0KfTs="}, {"version": 3, "names": ["name", "functional", "props", "icon", "type", "String", "default", "title", "render", "h", "context", "_context$props", "vnodes", "push", "length"], "sources": ["src/layout/components/Sidebar/Item.vue"], "sourcesContent": ["<script>\nexport default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render(h, context) {\n    const { icon, title } = context.props\n    const vnodes = []\n\n    if (icon) {\n      vnodes.push(<svg-icon icon-class={icon}/>)\n    }\n\n    if (title) {\n      if (title.length > 5) {\n        vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)\n      } else {\n        vnodes.push(<span slot='title'>{(title)}</span>)\n      }\n    }\n    return vnodes\n  }\n}\n</script>\n"], "mappings": ";;;;;;;iCACA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,MAAA,WAAAA,OAAAC,CAAA,EAAAC,OAAA;IACA,IAAAC,cAAA,GAAAD,OAAA,CAAAR,KAAA;MAAAC,IAAA,GAAAQ,cAAA,CAAAR,IAAA;MAAAI,KAAA,GAAAI,cAAA,CAAAJ,KAAA;IACA,IAAAK,MAAA;IAEA,IAAAT,IAAA;MACAS,MAAA,CAAAC,IAAA,CAAAJ,CAAA;QAAA;UAAA,cAAAN;QAAA;MAAA;IACA;IAEA,IAAAI,KAAA;MACA,IAAAA,KAAA,CAAAO,MAAA;QACAF,MAAA,CAAAC,IAAA,CAAAJ,CAAA;UAAA;UAAA;YAAA,SAAAF;UAAA;QAAA,IAAAA,KAAA;MACA;QACAK,MAAA,CAAAC,IAAA,CAAAJ,CAAA;UAAA;QAAA,IAAAF,KAAA;MACA;IACA;IACA,OAAAK,MAAA;EACA;AACA", "ignoreList": []}]}