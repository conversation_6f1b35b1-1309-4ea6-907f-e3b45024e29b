{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\cache\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\cache\\list.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RDYWNoZU5hbWUsIGxpc3RDYWNoZUtleSwgZ2V0Q2FjaGVWYWx1ZSwgY2xlYXJDYWNoZU5hbWUsIGNsZWFyQ2FjaGVLZXksIGNsZWFyQ2FjaGVBbGwgfSBmcm9tICJAL2FwaS9tb25pdG9yL2NhY2hlIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQ2FjaGVMaXN0IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY2FjaGVOYW1lczogW10sCiAgICAgIGNhY2hlS2V5czogW10sCiAgICAgIGNhY2hlRm9ybToge30sCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIHN1YkxvYWRpbmc6IGZhbHNlLAogICAgICBub3dDYWNoZU5hbWU6ICIiLAogICAgICB0YWJsZUhlaWdodDogd2luZG93LmlubmVySGVpZ2h0IC0gMjAwCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0Q2FjaGVOYW1lcygpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoue8k+WtmOWQjeensOWIl+ihqCAqLwogICAgZ2V0Q2FjaGVOYW1lcygpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdENhY2hlTmFtZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuY2FjaGVOYW1lcyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliLfmlrDnvJPlrZjlkI3np7DliJfooaggKi8KICAgIHJlZnJlc2hDYWNoZU5hbWVzKCkgewogICAgICB0aGlzLmdldENhY2hlTmFtZXMoKTsKICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yi35paw57yT5a2Y5YiX6KGo5oiQ5YqfIik7CiAgICB9LAogICAgLyoqIOa4heeQhuaMh+WumuWQjeensOe8k+WtmCAqLwogICAgaGFuZGxlQ2xlYXJDYWNoZU5hbWUocm93KSB7CiAgICAgIGNsZWFyQ2FjaGVOYW1lKHJvdy5jYWNoZU5hbWUpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heeQhue8k+WtmOWQjeensFsiICsgcm93LmNhY2hlTmFtZSArICJd5oiQ5YqfIik7CiAgICAgICAgdGhpcy5nZXRDYWNoZUtleXMoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoue8k+WtmOmUruWQjeWIl+ihqCAqLwogICAgZ2V0Q2FjaGVLZXlzKHJvdykgewogICAgICBjb25zdCBjYWNoZU5hbWUgPSByb3cgIT09IHVuZGVmaW5lZCA/IHJvdy5jYWNoZU5hbWUgOiB0aGlzLm5vd0NhY2hlTmFtZTsKICAgICAgaWYgKGNhY2hlTmFtZSA9PT0gIiIpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zdWJMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdENhY2hlS2V5KGNhY2hlTmFtZSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5jYWNoZUtleXMgPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMuc3ViTG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMubm93Q2FjaGVOYW1lID0gY2FjaGVOYW1lOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yi35paw57yT5a2Y6ZSu5ZCN5YiX6KGoICovCiAgICByZWZyZXNoQ2FjaGVLZXlzKCkgewogICAgICB0aGlzLmdldENhY2hlS2V5cygpOwogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliLfmlrDplK7lkI3liJfooajmiJDlip8iKTsKICAgIH0sCiAgICAvKiog5riF55CG5oyH5a6a6ZSu5ZCN57yT5a2YICovCiAgICBoYW5kbGVDbGVhckNhY2hlS2V5KGNhY2hlS2V5KSB7CiAgICAgIGNsZWFyQ2FjaGVLZXkoY2FjaGVLZXkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heeQhue8k+WtmOmUruWQjVsiICsgY2FjaGVLZXkgKyAiXeaIkOWKnyIpOwogICAgICAgIHRoaXMuZ2V0Q2FjaGVLZXlzKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliJfooajliY3nvIDljrvpmaQgKi8KICAgIG5hbWVGb3JtYXR0ZXIocm93KSB7CiAgICAgIHJldHVybiByb3cuY2FjaGVOYW1lLnJlcGxhY2UoIjoiLCAiIik7CiAgICB9LAogICAgLyoqIOmUruWQjeWJjee8gOWOu+mZpCAqLwogICAga2V5Rm9ybWF0dGVyKGNhY2hlS2V5KSB7CiAgICAgIHJldHVybiBjYWNoZUtleS5yZXBsYWNlKHRoaXMubm93Q2FjaGVOYW1lLCAiIik7CiAgICB9LAogICAgLyoqIOafpeivoue8k+WtmOWGheWuueivpue7hiAqLwogICAgaGFuZGxlQ2FjaGVWYWx1ZShjYWNoZUtleSkgewogICAgICBnZXRDYWNoZVZhbHVlKHRoaXMubm93Q2FjaGVOYW1lLCBjYWNoZUtleSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5jYWNoZUZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5riF55CG5YWo6YOo57yT5a2YICovCiAgICBoYW5kbGVDbGVhckNhY2hlQWxsKCkgewogICAgICBjbGVhckNhY2hlQWxsKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5riF55CG5YWo6YOo57yT5a2Y5oiQ5YqfIik7CiAgICAgIH0pOwogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\">\n      <el-col :span=\"8\">\n        <el-card style=\"height: calc(100vh - 125px)\">\n          <div slot=\"header\">\n            <span><i class=\"el-icon-collection\"></i> 缓存列表</span>\n            <el-button\n              style=\"float: right; padding: 3px 0\"\n              type=\"text\"\n              icon=\"el-icon-refresh-right\"\n              @click=\"refreshCacheNames()\"\n            ></el-button>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"cacheNames\"\n            :height=\"tableHeight\"\n            highlight-current-row\n            @row-click=\"getCacheKeys\"\n            style=\"width: 100%\"\n          >\n            <el-table-column\n              label=\"序号\"\n              width=\"60\"\n              type=\"index\"\n            ></el-table-column>\n\n            <el-table-column\n              label=\"缓存名称\"\n              align=\"center\"\n              prop=\"cacheName\"\n              :show-overflow-tooltip=\"true\"\n              :formatter=\"nameFormatter\"\n            ></el-table-column>\n\n            <el-table-column\n              label=\"备注\"\n              align=\"center\"\n              prop=\"remark\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"操作\"\n              width=\"60\"\n              align=\"center\"\n              class-name=\"small-padding fixed-width\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"handleClearCacheName(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"8\">\n        <el-card style=\"height: calc(100vh - 125px)\">\n          <div slot=\"header\">\n            <span><i class=\"el-icon-key\"></i> 键名列表</span>\n            <el-button\n              style=\"float: right; padding: 3px 0\"\n              type=\"text\"\n              icon=\"el-icon-refresh-right\"\n              @click=\"refreshCacheKeys()\"\n            ></el-button>\n          </div>\n          <el-table\n            v-loading=\"subLoading\"\n            :data=\"cacheKeys\"\n            :height=\"tableHeight\"\n            highlight-current-row\n            @row-click=\"handleCacheValue\"\n            style=\"width: 100%\"\n          >\n            <el-table-column\n              label=\"序号\"\n              width=\"60\"\n              type=\"index\"\n            ></el-table-column>\n            <el-table-column\n              label=\"缓存键名\"\n              align=\"center\"\n              :show-overflow-tooltip=\"true\"\n              :formatter=\"keyFormatter\"\n            >\n            </el-table-column>\n            <el-table-column\n              label=\"操作\"\n              width=\"60\"\n              align=\"center\"\n              class-name=\"small-padding fixed-width\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"handleClearCacheKey(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"8\">\n        <el-card :bordered=\"false\" style=\"height: calc(100vh - 125px)\">\n          <div slot=\"header\">\n            <span><i class=\"el-icon-document\"></i> 缓存内容</span>\n            <el-button\n              style=\"float: right; padding: 3px 0\"\n              type=\"text\"\n              icon=\"el-icon-refresh-right\"\n              @click=\"handleClearCacheAll()\"\n              >清理全部</el-button\n            >\n          </div>\n          <el-form :model=\"cacheForm\">\n            <el-row :gutter=\"32\">\n              <el-col :offset=\"1\" :span=\"22\">\n                <el-form-item label=\"缓存名称:\" prop=\"cacheName\">\n                  <el-input v-model=\"cacheForm.cacheName\" :readOnly=\"true\" />\n                </el-form-item>\n              </el-col>\n              <el-col :offset=\"1\" :span=\"22\">\n                <el-form-item label=\"缓存键名:\" prop=\"cacheKey\">\n                  <el-input v-model=\"cacheForm.cacheKey\" :readOnly=\"true\" />\n                </el-form-item>\n              </el-col>\n              <el-col :offset=\"1\" :span=\"22\">\n                <el-form-item label=\"缓存内容:\" prop=\"cacheValue\">\n                  <el-input\n                    v-model=\"cacheForm.cacheValue\"\n                    type=\"textarea\"\n                    :rows=\"8\"\n                    :readOnly=\"true\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { listCacheName, listCacheKey, getCacheValue, clearCacheName, clearCacheKey, clearCacheAll } from \"@/api/monitor/cache\";\n\nexport default {\n  name: \"CacheList\",\n  data() {\n    return {\n      cacheNames: [],\n      cacheKeys: [],\n      cacheForm: {},\n      loading: true,\n      subLoading: false,\n      nowCacheName: \"\",\n      tableHeight: window.innerHeight - 200\n    };\n  },\n  created() {\n    this.getCacheNames();\n  },\n  methods: {\n    /** 查询缓存名称列表 */\n    getCacheNames() {\n      this.loading = true;\n      listCacheName().then(response => {\n        this.cacheNames = response.data;\n        this.loading = false;\n      });\n    },\n    /** 刷新缓存名称列表 */\n    refreshCacheNames() {\n      this.getCacheNames();\n      this.$modal.msgSuccess(\"刷新缓存列表成功\");\n    },\n    /** 清理指定名称缓存 */\n    handleClearCacheName(row) {\n      clearCacheName(row.cacheName).then(response => {\n        this.$modal.msgSuccess(\"清理缓存名称[\" + row.cacheName + \"]成功\");\n        this.getCacheKeys();\n      });\n    },\n    /** 查询缓存键名列表 */\n    getCacheKeys(row) {\n      const cacheName = row !== undefined ? row.cacheName : this.nowCacheName;\n      if (cacheName === \"\") {\n        return;\n      }\n      this.subLoading = true;\n      listCacheKey(cacheName).then(response => {\n        this.cacheKeys = response.data;\n        this.subLoading = false;\n        this.nowCacheName = cacheName;\n      });\n    },\n    /** 刷新缓存键名列表 */\n    refreshCacheKeys() {\n      this.getCacheKeys();\n      this.$modal.msgSuccess(\"刷新键名列表成功\");\n    },\n    /** 清理指定键名缓存 */\n    handleClearCacheKey(cacheKey) {\n      clearCacheKey(cacheKey).then(response => {\n        this.$modal.msgSuccess(\"清理缓存键名[\" + cacheKey + \"]成功\");\n        this.getCacheKeys();\n      });\n    },\n    /** 列表前缀去除 */\n    nameFormatter(row) {\n      return row.cacheName.replace(\":\", \"\");\n    },\n    /** 键名前缀去除 */\n    keyFormatter(cacheKey) {\n      return cacheKey.replace(this.nowCacheName, \"\");\n    },\n    /** 查询缓存内容详细 */\n    handleCacheValue(cacheKey) {\n      getCacheValue(this.nowCacheName, cacheKey).then(response => {\n        this.cacheForm = response.data;\n      });\n    },\n    /** 清理全部缓存 */\n    handleClearCacheAll() {\n      clearCacheAll().then(response => {\n        this.$modal.msgSuccess(\"清理全部缓存成功\");\n      });\n    }\n  },\n};\n</script>\n"]}]}