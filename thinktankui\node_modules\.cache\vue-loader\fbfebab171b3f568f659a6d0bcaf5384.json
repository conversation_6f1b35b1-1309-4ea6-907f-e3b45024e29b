{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\server\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\server\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFNlcnZlciB9IGZyb20gIkAvYXBpL21vbml0b3Ivc2VydmVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2VydmVyIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5pyN5Yqh5Zmo5L+h5oGvCiAgICAgIHNlcnZlcjogW10KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLm9wZW5Mb2FkaW5nKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5pyN5Yqh5Zmo5L+h5oGvICovCiAgICBnZXRMaXN0KCkgewogICAgICBnZXRTZXJ2ZXIoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnNlcnZlciA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaJk+W8gOWKoOi9veWxggogICAgb3BlbkxvYWRpbmcoKSB7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWKoOi9veacjeWKoeebkeaOp+aVsOaNru+8jOivt+eojeWAme+8gSIpOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/server", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-cpu\"></i> CPU</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">值</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">核心数</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.cpuNum }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">用户使用率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.used }}%</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">系统使用率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.sys }}%</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">当前空闲率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.free }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-tickets\"></i> 内存</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">内存</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">Python</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">总内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.total }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.total }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">已用内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.used}}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.used}}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">剩余内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.free }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.free }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\" :class=\"{'text-danger': server.mem.usage > 80}\">{{ server.mem.usage }}%</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\" :class=\"{'text-danger': server.py.usage > 80}\">{{ server.py.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span><i class=\"el-icon-monitor\"></i> 服务器信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">服务器名称</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">操作系统</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osName }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">服务器IP</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerIp }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">系统架构</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osArch }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span><i class=\"el-icon-coffee-cup\"></i> Python解释器信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;table-layout:fixed;\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Python名称</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.name }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Python版本</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.version }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">启动时间</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.startTime }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时长</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.runTime }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">安装路径</div></td>\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.home }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">项目路径</div></td>\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.userDir }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span><i class=\"el-icon-receiving\"></i> 磁盘状态</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"el-table__cell el-table__cell is-leaf\"><div class=\"cell\">盘符路径</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">文件系统</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">盘符名称</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">总大小</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">可用大小</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">已用大小</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">已用百分比</div></th>\n                </tr>\n              </thead>\n              <tbody v-if=\"server.sysFiles\">\n                <tr v-for=\"(sysFile, index) in server.sysFiles\" :key=\"index\">\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.dirName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.sysTypeName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.typeName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.total }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.free }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.used }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" :class=\"{'text-danger': sysFile.usage > 80}\">{{ sysFile.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getServer } from \"@/api/monitor/server\";\n\nexport default {\n  name: \"Server\",\n  data() {\n    return {\n      // 服务器信息\n      server: []\n    };\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查询服务器信息 */\n    getList() {\n      getServer().then(response => {\n        this.server = response.data;\n        this.$modal.closeLoading();\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.$modal.loading(\"正在加载服务监控数据，请稍候！\");\n    }\n  }\n};\n</script>\n"]}]}