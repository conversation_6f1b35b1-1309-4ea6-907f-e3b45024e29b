from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text
from config.database import Base


class InfoSummary(Base):
    """
    信息汇总表
    """

    __tablename__ = 'info_summary'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='信息ID')
    title = Column(String(500), nullable=False, comment='信息标题')
    content = Column(Text, comment='信息内容')
    summary = Column(Text, comment='信息摘要')
    platform_type = Column(String(50), nullable=False, comment='平台类型(news/weibo/wechat/video/app/forum/ecommerce/qa/other)')
    source_name = Column(String(200), comment='来源名称')
    source_url = Column(String(1000), comment='原始链接')
    publish_time = Column(DateTime, comment='发布时间')
    sentiment = Column(String(20), default='neutral', comment='情感倾向(positive/neutral/negative)')
    info_attribute = Column(String(50), comment='信息属性(official/media/user/competitor/industry/policy)')
    views_count = Column(Integer, default=0, comment='浏览量')
    comments_count = Column(Integer, default=0, comment='评论数')
    shares_count = Column(Integer, default=0, comment='转发数')
    entity_type = Column(String(50), comment='关联实体类型(brand/person/organization/product/event/topic)')
    entity_name = Column(String(200), comment='关联实体名称')
    keywords = Column(Text, comment='关键词(JSON格式)')
    images = Column(Text, comment='图片链接(JSON格式)')
    status = Column(String(1), default='1', comment='状态(0停用 1正常)')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, comment='创建时间', default=datetime.now())
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, comment='更新时间', default=datetime.now())
    remark = Column(String(500), comment='备注')
