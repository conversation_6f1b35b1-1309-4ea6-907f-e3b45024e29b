<template>
  <div class="app-container">
    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 左侧搜索方案区域 -->
      <div class="sidebar">
        <div class="search-plan">
          <div class="plan-actions">
            <el-button type="primary" class="new-plan-btn">
              <i class="el-icon-plus"></i> 新建方案
            </el-button>
            <el-button class="folder-btn">
              <i class="el-icon-folder-opened"></i>
            </el-button>
          </div>
          <div class="plan-search">
            <el-input
              placeholder="方案搜索"
              prefix-icon="el-icon-search"
              v-model="searchQuery"
              clearable
              size="small"
            ></el-input>
          </div>
          <div class="plan-divider"></div>
          <div class="plan-list">
            <div class="plan-item" :class="{ 'active': searchType === 'product' }">
              <el-radio v-model="searchType" label="product">竞品(1)</el-radio>
              <i class="el-icon-arrow-down"></i>
            </div>
            <div class="plan-item" :class="{ 'active': searchType === 'brand' }">
              <el-radio v-model="searchType" label="brand">品牌(1)</el-radio>
              <i class="el-icon-arrow-up"></i>
            </div>
            <div class="brand-detail" v-if="searchType === 'brand'">
              <div class="brand-name">方太</div>
            </div>
            <div class="plan-item">
              <el-radio v-model="searchType" label="person">人物(0)</el-radio>
              <i class="el-icon-arrow-down"></i>
            </div>
            <div class="plan-item">
              <el-radio v-model="searchType" label="organization">机构(0)</el-radio>
              <i class="el-icon-arrow-down"></i>
            </div>
            <div class="plan-item">
              <el-radio v-model="searchType" label="product-detail">产品(0)</el-radio>
              <i class="el-icon-arrow-down"></i>
            </div>
            <div class="plan-item">
              <el-radio v-model="searchType" label="event">事件(0)</el-radio>
              <i class="el-icon-arrow-down"></i>
            </div>
            <div class="plan-item">
              <el-radio v-model="searchType" label="other">其他(0)</el-radio>
              <i class="el-icon-arrow-down"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧图表区域 -->
      <div class="main-content">
        <!-- 筛选条件区域 - 方太 -->
        <div class="fangtai-filter-container" v-if="searchType === 'brand' && brandName === '方太'">
          <div class="filter-header" style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; border-bottom: 1px solid #ebeef5;">
            <span class="filter-title" style="font-size: 18px; font-weight: bold;">
              {{ brandName }}
              <i class="el-icon-edit" style="margin-left: 8px; cursor: pointer; font-size: 16px;"></i>
              <i class="el-icon-s-tools" style="margin-left: 8px; cursor: pointer; font-size: 16px;"></i>
            </span>
            <!-- Add any top-right actions for this header if needed -->
          </div>

          <div class="filter-content" style="padding-top: 15px;">
            <!-- 时间范围筛选 -->
            <el-form size="small" label-width="80px">
              <el-form-item label="时间范围:">
                <el-radio-group v-model="fangtaiTimeRange" @change="handleFangtaiFilterChange">
                  <el-radio-button label="today">今天</el-radio-button>
                  <el-radio-button label="yesterday">昨天</el-radio-button>
                  <el-radio-button label="7d">近七天</el-radio-button>
                  <el-radio-button label="30d">近30天</el-radio-button>
                  <el-radio-button label="custom">自定义</el-radio-button>
                </el-radio-group>
                <el-date-picker
                  v-if="fangtaiTimeRange === 'custom'"
                  v-model="fangtaiCustomTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="margin-left: 10px; width: 240px;"
                  @change="handleFangtaiFilterChange"
                ></el-date-picker>
              </el-form-item>

              <!-- 平台类型筛选 -->
              <el-form-item label="平台类型:">
                <el-checkbox-group v-model="fangtaiSelectedPlatforms" @change="handleFangtaiFilterChange">
                  <el-checkbox label="all_platform">全部</el-checkbox>
                  <el-checkbox label="web">网页</el-checkbox>
                  <el-checkbox label="weibo">微博</el-checkbox>
                  <el-checkbox label="toutiao">头条号</el-checkbox>
                  <el-checkbox label="app">APP</el-checkbox>
                  <el-checkbox label="video">视频</el-checkbox>
                  <el-checkbox label="sms">短信</el-checkbox>
                  <el-checkbox label="newspaper">报刊</el-checkbox>
                  <el-checkbox label="sohu">搜狐</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <!-- 情感倾向筛选 -->
              <el-form-item label="情感倾向:">
                <el-checkbox-group v-model="fangtaiSelectedSentiments" @change="handleFangtaiFilterChange">
                  <el-checkbox label="all_sentiment">全部</el-checkbox>
                  <el-checkbox label="positive">正面</el-checkbox>
                  <el-checkbox label="neutral">中性</el-checkbox>
                  <el-checkbox label="negative">负面</el-checkbox>
                  <el-checkbox label="warning_target">预警对象</el-checkbox>
                  <el-checkbox label="sensitive_info">敏感信息</el-checkbox>
                  <el-checkbox label="official_letter">正式发函</el-checkbox>
                  <el-checkbox label="delete_complaint">删帖投诉</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="applyFangtaiFilters">应用</el-button>
                <el-button @click="resetFangtaiFilters">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 信息总览卡片区域 -->
        <el-row :gutter="20" class="summary-cards" style="margin-top: 20px; margin-bottom: 20px;">
          <el-col :xl="4" :lg="4" :md="8" :sm="12" :xs="24" v-for="card in summaryCards" :key="card.title">
            <el-card shadow="hover" class="summary-card" :body-style="{ padding: '15px' }">
              <div style="font-size: 14px; color: #606266;">{{ card.title }}</div>
              <div style="font-size: 24px; font-weight: bold; margin-top: 5px;" :style="{ color: card.color }">{{ card.value }}</div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 第一行图表 - 数据汇总图 -->
        <el-card class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <i class="el-icon-data-line blue-line"></i>
              <span>数据汇总</span>
            </div>
            <div class="chart-actions">
              <el-button-group size="mini">
                <el-button type="primary" plain>数据趋势</el-button>
                <el-button plain>7天监测图</el-button>
              </el-button-group>
              <i class="el-icon-refresh" style="margin-left:10px; cursor:pointer;"></i>
              <i class="el-icon-download" style="margin-left:10px; cursor:pointer;"></i>
            </div>
          </div>
          <div class="chart-content" style="height: 300px;">
            <!-- 这里放置传播热度趋势图表 -->
            <div ref="trendChart" class="chart"></div>
            <!-- 热点标记 -->
            <div class="hotspot-markers">
              <!-- 热点标记内容将由JS动态生成 -->
            </div>
          </div>
        </el-card>

        <!-- 关键热搜词云图 -->
        <el-card class="chart-card" style="margin-top: 20px;" v-if="searchType === 'brand' && brandName === '方太'">
          <div class="chart-header">
            <div class="chart-title">
              <i class="el-icon-magic-stick blue-line"></i> <!-- Consider a more relevant icon like el-icon-collection-tag or el-icon-price-tag -->
              <span>关键热搜词云</span>
            </div>
            <div class="chart-actions">
              <i class="el-icon-refresh" style="cursor:pointer;"></i>
              <i class="el-icon-download" style="margin-left:10px; cursor:pointer;"></i>
            </div>
          </div>
          <div class="chart-content" style="height: 300px;">
            <div ref="keywordCloudChart" class="chart"></div>
          </div>
        </el-card>

        <!-- 第二行图表 -->
        <el-row :gutter="20" class="chart-row">
          <!-- 平台来源占比 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-pie-chart blue-line"></i>
                  <span>平台声量占比</span>
                </div>
                <div class="chart-actions">
                  <i class="el-icon-refresh" style="cursor:pointer;"></i>
                  <i class="el-icon-download" style="margin-left:10px; cursor:pointer;"></i>
                </div>
              </div>
              <div class="chart-content" style="height: 250px;">
                <!-- 这里放置平台来源占比图表 -->
                <div ref="sourceChart" class="chart"></div>
                <!-- 图例 -->
                <div class="chart-legend">
                  <div class="legend-item">
                    <span class="legend-color" style="background-color: #4CD384;"></span>
                    <span class="legend-text">微博</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color" style="background-color: #36A3F7;"></span>
                    <span class="legend-text">微信</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color" style="background-color: #F56C6C;"></span>
                    <span class="legend-text">APP</span>
                  </div>
                  <!-- 更多图例项 -->
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 传播热度 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-data-analysis blue-line"></i>
                  <span>情感画像</span>
                </div>
                <div class="chart-actions">
                  <i class="el-icon-refresh"></i>
                  <i class="el-icon-more"></i>
                </div>
              </div>
              <div class="chart-content" style="height: 250px;">
                <!-- 这里放置传播热度图表 -->
                <div ref="heatChart" class="chart"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 第三行图表 -->
        <el-row :gutter="20" class="chart-row">
          <!-- 舆情事件分布 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-s-data blue-line"></i>
                  <span>媒体类型分布</span>
                </div>
                <div class="chart-actions">
                  <i class="el-icon-refresh"></i>
                  <i class="el-icon-more"></i>
                </div>
              </div>
              <div class="chart-content" style="height: 250px;">
                <!-- 这里放置舆情事件分布图表 -->
                <div ref="eventChart" class="chart"></div>
              </div>
            </el-card>
          </el-col>

          <!-- 舆情来源占比 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-pie-chart blue-line"></i>
                  <span>媒体声量占比</span>
                </div>
                <div class="chart-actions">
                  <i class="el-icon-refresh"></i>
                  <i class="el-icon-more"></i>
                </div>
              </div>
              <div class="chart-content" style="height: 250px;">
                <!-- 这里放置舆情来源占比图表 -->
                <div ref="sourceDistChart" class="chart"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-wordcloud'; // 引入词云图

export default {
  name: 'SpreadAnalysis',
  data() {
    return {
      originalTopNav: undefined, // 存储原始的topNav状态
      // 方太筛选相关数据
      fangtaiTimeRange: 'today',
      fangtaiCustomTimeRange: [],
      fangtaiSelectedPlatforms: ['all_platform'],
      fangtaiSelectedSentiments: ['all_sentiment'],
      summaryCards: [
        { title: '信息总量', value: '4653', color: '#409EFF' },
        { title: '正面声量', value: '58', color: '#67C23A' },
        { title: '中性声量', value: '4583', color: '#E6A23C' },
        { title: '负面声量', value: '12', color: '#F56C6C' },
        { title: '媒体总数', value: '1853', color: '#909399' }
      ],
      activeTab: 'spread',
      searchType: 'brand',
      brandName: '方太',
      searchQuery: '',
      charts: {
        trendChart: null,
        sourceChart: null,
        heatChart: null,
        eventChart: null,
        sourceDistChart: null,
        keywordCloudChart: null
      }
    }
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })

    this.initCharts()
  },
  watch: {
    searchType(newVal) {
      this.$nextTick(() => {
        this.resizeCharts();
        if (newVal === 'brand' && this.brandName === '方太') {
          // Potentially re-render or update charts specific to '方太' view if needed
          if (!this.charts.keywordCloudChart) {
             this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);
             this.renderKeywordCloudChart();
          } else {
            this.renderKeywordCloudChart(); // Re-render if already initialized
          }
        }
      });
    }
  },
  methods: {
    handleTabSelect(key) {
      this.activeTab = key
    },
    handleFangtaiFilterChange() {
      // 处理方太筛选条件变化，可以触发数据重新加载或图表更新
      console.log('方太筛选条件变化:', {
        timeRange: this.fangtaiTimeRange,
        customTime: this.fangtaiCustomTimeRange,
        platforms: this.fangtaiSelectedPlatforms,
        sentiments: this.fangtaiSelectedSentiments
      });
      // 触发图表更新逻辑
      this.applyFangtaiFilters();
    },
    applyFangtaiFilters() {
      // 应用方太筛选条件，实际应用中会调用API获取数据并更新图表
      console.log('应用方太筛选');
      // 示例：重新渲染所有图表
      this.renderTrendChart(); // 注意：图一中此图表为“数据汇总”
      this.renderSourceChart(); // 注意：图一中此图表为“平台声量占比”
      this.renderHeatChart(); // 注意：图一中此图表为“情感画像”
      this.renderEventChart(); // 注意：图一中此图表为“媒体类型分布”
      this.renderSourceDistChart(); // 注意：图一中此图表为“媒体声量占比”
      if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {
        if (!this.charts.keywordCloudChart) {
            this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);
        }
        this.renderKeywordCloudChart();
      }
    },
    resetFangtaiFilters() {
      this.fangtaiTimeRange = 'today';
      this.fangtaiCustomTimeRange = [];
      this.fangtaiSelectedPlatforms = ['all_platform'];
      this.fangtaiSelectedSentiments = ['all_sentiment'];
      console.log('重置方太筛选');
      this.applyFangtaiFilters();
    },
    initCharts() {
      this.$nextTick(() => {
        // 初始化传播热度趋势图
        this.charts.trendChart = echarts.init(this.$refs.trendChart)
        this.renderTrendChart()

        // 初始化平台来源占比图
        this.charts.sourceChart = echarts.init(this.$refs.sourceChart)
        this.renderSourceChart()

        // 初始化传播热度图
        this.charts.heatChart = echarts.init(this.$refs.heatChart)
        this.renderHeatChart()

        // 初始化舆情事件分布图
        this.charts.eventChart = echarts.init(this.$refs.eventChart)
        this.renderEventChart()

        // 初始化舆情来源占比图
        this.charts.sourceDistChart = echarts.init(this.$refs.sourceDistChart)
        this.renderSourceDistChart()

        // 如果初始加载时就是方太品牌，则初始化词云图
        if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {
          this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart)
          this.renderKeywordCloudChart()
        }

        // 监听窗口大小变化，调整图表大小
        window.addEventListener('resize', this.resizeCharts)
      })
    },
    renderKeywordCloudChart() {
      if (!this.charts.keywordCloudChart) return;
      const option = {
        tooltip: {
          show: true
        },
        series: [{
          type: 'wordCloud',
          gridSize: 2,
          sizeRange: [12, 50],
          rotationRange: [-90, 90],
          shape: 'pentagon',
          width: '90%',
          height: '90%',
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function () {
              return 'rgb(' + [
                Math.round(Math.random() * 160),
                Math.round(Math.random() * 160),
                Math.round(Math.random() * 160)
              ].join(',') + ')';
            }
          },
          emphasis: {
            focus: 'self',
            textStyle: {
              textShadowBlur: 10,
              textShadowColor: '#333'
            }
          },
          data: [
            { name: '方太产品', value: 10000 },
            { name: '厨房电器', value: 6181 },
            { name: '油烟机', value: 4386 },
            { name: '洗碗机', value: 4055 },
            { name: '集成灶', value: 2467 },
            { name: '售后服务', value: 2244 },
            { name: '智能厨电', value: 1898 },
            { name: '高端厨电', value: 1484 },
            { name: '用户体验', value: 1112 },
            { name: '新品发布', value: 965 },
            { name: '质量问题', value: 847 },
            { name: '价格优惠', value: 582 },
            { name: '安装服务', value: 555 },
            { name: '品牌活动', value: 550 },
            { name: '线上购买', value: 462 },
            { name: '线下门店', value: 366 },
            { name: '厨电科技', value: 360 },
            { name: '环保理念', value: 282 },
            { name: '设计美学', value: 273 },
            { name: '客户评价', value: 265 }
          ]
        }]
      };
      this.charts.keywordCloudChart.setOption(option);
    },
    resizeCharts() {
      Object.values(this.charts).forEach(chart => {
        chart && chart.resize()
      })
    },
    renderKeywordCloudChart() {
      if (!this.charts.keywordCloudChart) return;
      const option = {
        tooltip: {
          show: true
        },
        series: [{
          type: 'wordCloud',
          gridSize: 2,
          sizeRange: [12, 50],
          rotationRange: [-90, 90],
          shape: 'pentagon',
          width: '90%',
          height: '90%',
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function () {
              return 'rgb(' + [
                Math.round(Math.random() * 160),
                Math.round(Math.random() * 160),
                Math.round(Math.random() * 160)
              ].join(',') + ')';
            }
          },
          emphasis: {
            focus: 'self',
            textStyle: {
              textShadowBlur: 10,
              textShadowColor: '#333'
            }
          },
          data: [
            { name: '方太产品', value: 10000 },
            { name: '厨房电器', value: 6181 },
            { name: '油烟机', value: 4386 },
            { name: '洗碗机', value: 4055 },
            { name: '集成灶', value: 2467 },
            { name: '售后服务', value: 2244 },
            { name: '智能厨电', value: 1898 },
            { name: '高端厨电', value: 1484 },
            { name: '用户体验', value: 1112 },
            { name: '新品发布', value: 965 },
            { name: '质量问题', value: 847 },
            { name: '价格优惠', value: 582 },
            { name: '安装服务', value: 555 },
            { name: '品牌活动', value: 550 },
            { name: '线上购买', value: 462 },
            { name: '线下门店', value: 366 },
            { name: '厨电科技', value: 360 },
            { name: '环保理念', value: 282 },
            { name: '设计美学', value: 273 },
            { name: '客户评价', value: 265 }
          ]
        }]
      };
      this.charts.keywordCloudChart.setOption(option);
    },
    renderTrendChart() {
      // 传播热度趋势图配置
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '传播热度',
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#67C23A',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(103, 194, 58, 0.3)'
              }, {
                offset: 1,
                color: 'rgba(103, 194, 58, 0.1)'
              }]
            }
          },
          data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]
        }]
      }
      this.charts.trendChart.setOption(option)
    },
    renderSourceChart() {
      // 平台来源占比图配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: '平台来源',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {d}%'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true
          },
          data: [
            { value: 31.26, name: '微博', itemStyle: { color: '#4CD384' } },
            { value: 31.11, name: '微信', itemStyle: { color: '#36A3F7' } },
            { value: 16.32, name: 'APP', itemStyle: { color: '#F56C6C' } },
            { value: 10.47, name: '网站', itemStyle: { color: '#E6A23C' } },
            { value: 5.47, name: '论坛', itemStyle: { color: '#909399' } },
            { value: 3.29, name: '报刊', itemStyle: { color: '#9B55FF' } },
            { value: 2.08, name: '其他', itemStyle: { color: '#FF9A2F' } }
          ]
        }]
      }
      this.charts.sourceChart.setOption(option)
    },
    renderHeatChart() {
      // 传播热度图配置
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
        },
        yAxis: {
          type: 'value'
        },
        legend: {
          data: ['正面声量', '中性声量', '负面声量'],
          bottom: 10
        },
        series: [
          {
            name: '正面声量',
            type: 'line',
            smooth: true,
            lineStyle: { color: '#67C23A' },
            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(103, 194, 58, 0.3)' }, { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }]) },
            data: [10, 15, 12, 18, 22, 25, 30, 28, 35, 40, 38, 42, 45, 50, 48, 52, 55, 58, 60, 56, 50]
          },
          {
            name: '中性声量',
            type: 'line',
            smooth: true,
            lineStyle: { color: '#E6A23C' },
            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(230, 162, 60, 0.3)' }, { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }]) },
            data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]
          },
          {
            name: '负面声量',
            type: 'line',
            smooth: true,
            lineStyle: { color: '#F56C6C' },
            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(245, 108, 108, 0.3)' }, { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }]) },
            data: [5, 8, 6, 10, 12, 9, 11, 14, 10, 13, 15, 12, 16, 18, 15, 17, 20, 16, 19, 22, 18]
          }
        ]
      }
      this.charts.heatChart.setOption(option)
    },
    renderEventChart() {
      // 舆情事件分布图配置
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['新闻', 'APP', '论坛', '博客', '微博', '视频', '微信']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '事件数量',
          type: 'bar',
          barWidth: '60%',
          data: [320, 280, 220, 180, 150, 120, 100]
        }]
      }
      this.charts.eventChart.setOption(option)
    },
    renderSourceDistChart() {
      // 舆情来源占比图配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '媒体声量占比',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {d}%'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true
          },
          data: [
            { value: 35, name: '新闻客户端', itemStyle: { color: '#5470C6'} },
            { value: 25, name: '社交媒体', itemStyle: { color: '#91CC75'} },
            { value: 15, name: '视频平台', itemStyle: { color: '#FAC858'} },
            { value: 10, name: '传统媒体网站', itemStyle: { color: '#EE6666'} },
            { value: 8, name: '论坛博客', itemStyle: { color: '#73C0DE'} },
            { value: 7, name: '其他', itemStyle: { color: '#3BA272'} }
          ]
        }]
      }
      this.charts.sourceDistChart.setOption(option)
    }
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.resizeCharts)
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      chart && chart.dispose()
    })
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  background-color: #f0f2f5;
  min-height: 100vh;
  padding: 0;
}

.top-nav-container {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;

  .top-menu {
    border-bottom: none;

    .el-menu-item {
      height: 50px;
      line-height: 50px;
      font-size: 14px;

      &.is-active, &.active-menu {
        color: #1890ff;
        border-bottom: 2px solid #1890ff;
      }
    }
  }
}

.content-container {
  padding: 20px;
  display: flex;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 4px;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;

    .chart-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;

      .blue-line {
        display: inline-block;
        width: 3px;
        height: 16px;
        background-color: #1890ff;
        margin-right: 8px;
      }
    }

    .chart-tabs {
      display: flex;

      .tab-button {
        margin-right: 10px;

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
        }
      }
    }

    .chart-actions {
      display: flex;
      align-items: center;

      i {
        font-size: 16px;
        color: #909399;
        margin-left: 15px;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .chart-content {
    padding: 20px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}

.chart-row {
  margin-bottom: 20px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  margin-top: 15px;

  .legend-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 5px;

    .legend-color {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 2px;
      margin-right: 5px;
    }

    .legend-text {
      font-size: 12px;
      color: #606266;
    }
  }
}

.hotspot-markers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 左侧搜索方案区域样式 */
.sidebar {
  width: 240px;
  margin-right: 20px;
}

.search-plan {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.plan-actions {
  padding: 16px;
  display: flex;
  justify-content: space-between;

  .new-plan-btn {
    flex: 1;
    background-color: #FF9A2F;
    border-color: #FF9A2F;

    &:hover, &:focus {
      background-color: #F08C1E;
      border-color: #F08C1E;
    }
  }

  .folder-btn {
    margin-left: 10px;
    padding: 10px;
    color: #FF9A2F;
    border-color: #DCDFE6;
  }
}

.plan-search {
  padding: 0 16px 16px;

  .el-input ::v-deep .el-input__inner {
    border-radius: 4px;
    height: 32px;
  }
}

.plan-divider {
  height: 1px;
  background-color: #EBEEF5;
  margin: 0 16px;
}

.plan-list {
  padding: 16px;
}

.plan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  &.active {
    ::v-deep .el-radio__input.is-checked .el-radio__inner {
      background-color: #409EFF;
      border-color: #409EFF;
    }

    ::v-deep .el-radio__input.is-checked + .el-radio__label {
      color: #303133;
    }
  }

  i {
    color: #909399;
    cursor: pointer;
  }
}

.brand-detail {
  margin: -10px 0 16px 24px;

  .brand-name {
    background-color: #E6F1FC;
    color: #303133;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
  }
}

/* 右侧内容区域样式 */
.main-content {
  flex: 1;
}
/* 样式可以根据需要进一步细化 */

/* 方太筛选器专属样式 */
.fangtai-filter-container .filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.fangtai-filter-container .filter-title {
  font-size: 18px;
  font-weight: bold;
}

.fangtai-filter-container .filter-content {
  padding-top: 15px;
}

.fangtai-filter-container .el-form-item {
  margin-bottom: 10px;
}

.fangtai-filter-container .el-checkbox-group .el-checkbox {
  margin-right: 20px;
}

/* 信息总览卡片样式 */
.summary-cards {
  margin-top: 20px;
  margin-bottom: 20px;
}

.summary-card .el-card__body {
  padding: 15px !important; /* 强制修改element-ui默认padding */
}

.summary-card div:first-child {
  font-size: 14px;
  color: #606266;
}

.summary-card div:last-child {
  font-size: 24px;
  font-weight: bold;
  margin-top: 5px;
}

/* 通用图表卡片样式调整 */
.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0; /* 统一边框颜色 */
  margin-bottom: 15px;
}

.chart-card .chart-title {
  font-size: 16px; /* 统一标题字号 */
  font-weight: 500;
  display: flex;
  align-items: center;
}

.chart-card .chart-title .blue-line {
  color: #409EFF; /* 统一图标颜色 */
  margin-right: 8px;
  font-size: 18px;
}

.chart-card .chart-actions .el-button-group .el-button {
  padding: 5px 10px; /* 调整按钮组内按钮padding */
}

.chart-card .chart-actions .el-icon-refresh,
.chart-card .chart-actions .el-icon-download,
.chart-card .chart-actions .el-icon-more {
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px; /* 统一操作图标大小 */
  color: #606266; /* 统一操作图标颜色 */
}

.chart-card .chart-actions .el-icon-refresh:hover,
.chart-card .chart-actions .el-icon-download:hover,
.chart-card .chart-actions .el-icon-more:hover {
  color: #409EFF;
}

/* 平台来源占比图的图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  flex-wrap: wrap; /* 允许图例换行 */
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 5px; /* 增加底部间距以便换行 */
  font-size: 12px; /* 调整图例文字大小 */
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
  display: inline-block;
}

/* 左侧边栏激活状态 */
.sidebar .plan-item.active {
  background-color: #ecf5ff; /* Element UI 主题蓝的浅色 */
  color: #409EFF;
  border-right: 3px solid #409EFF;
}

.sidebar .plan-item.active .el-radio__label {
 color: #409EFF !important;
}

/* 顶部导航激活状态 */
.top-menu .el-menu-item.is-active {
  border-bottom-color: #409EFF !important;
  color: #409EFF !important;
}

.top-menu .active-menu {
   border-bottom-color: #409EFF !important;
   color: #409EFF !important;
   font-weight: bold;
}

/* 调整图表容器确保其撑满父容器 */
.chart {
  width: 100%;
  height: 100%;
}

/* 确保内容区域在小屏幕上也能良好显示 */
.content-container {
  display: flex;
  flex-direction: row; /* 保持左右布局 */
}

.sidebar {
  width: 220px; /* 固定左侧栏宽度 */
  min-width: 220px;
  border-right: 1px solid #e6e6e6;
  padding-right: 15px;
  margin-right: 15px;
}

.main-content {
  flex-grow: 1; /* 右侧内容区域占据剩余空间 */
  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
}

/* 响应式调整：当屏幕宽度小于768px时，可以考虑将侧边栏隐藏或变为抽屉式 */
@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }
  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e6e6e6;
    margin-right: 0;
    margin-bottom: 15px;
    padding-right: 0;
  }
  .fangtai-filter-container .el-form-item .el-checkbox-group,
  .fangtai-filter-container .el-form-item .el-radio-group {
    display: flex;
    flex-wrap: wrap;
  }
  .fangtai-filter-container .el-form-item .el-checkbox,
  .fangtai-filter-container .el-form-item .el-radio-button {
    margin-bottom: 5px;
  }
}
</style>
