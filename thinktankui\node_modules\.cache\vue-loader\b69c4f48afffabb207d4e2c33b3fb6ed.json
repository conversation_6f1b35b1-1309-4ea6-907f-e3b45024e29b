{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\year.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\year.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["year.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "year.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n\t<el-form size=\"small\">\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"1\" v-model='radioValue'>\n\t\t\t\t不填，允许的通配符[, - * /]\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"2\" v-model='radioValue'>\n\t\t\t\t每年\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"3\" v-model='radioValue'>\n\t\t\t\t周期从\n\t\t\t\t<el-input-number v-model='cycle01' :min='fullYear' :max=\"2098\" /> -\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : fullYear + 1\" :max=\"2099\" />\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"4\" v-model='radioValue'>\n\t\t\t\t从\n\t\t\t\t<el-input-number v-model='average01' :min='fullYear' :max=\"2098\"/> 年开始，每\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"2099 - average01 || fullYear\" /> 年执行一次\n\t\t\t</el-radio>\n\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"5\" v-model='radioValue'>\n\t\t\t\t指定\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple>\n\t\t\t\t\t<el-option v-for=\"item in 9\" :key=\"item\" :value=\"item - 1 + fullYear\" :label=\"item -1 + fullYear\" />\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\t</el-form>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tfullYear: 0,\n\t\t\tradioValue: 1,\n\t\t\tcycle01: 0,\n\t\t\tcycle02: 0,\n\t\t\taverage01: 0,\n\t\t\taverage02: 1,\n\t\t\tcheckboxList: [],\n\t\t\tcheckNum: this.$options.propsData.check\n\t\t}\n\t},\n\tname: 'crontab-year',\n\tprops: ['check', 'month', 'cron'],\n\tmethods: {\n\t\t// 单选按钮值变化时\n\t\tradioChange() {\n\t\t\tswitch (this.radioValue) {\n\t\t\t\tcase 1:\n\t\t\t\t\tthis.$emit('update', 'year', '');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tthis.$emit('update', 'year', '*');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 5:\n\t\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 周期两个值变化时\n\t\tcycleChange() {\n\t\t\tif (this.radioValue == '3') {\n\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\n\t\t\t}\n\t\t},\n\t\t// 平均两个值变化时\n\t\taverageChange() {\n\t\t\tif (this.radioValue == '4') {\n\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\n\t\t\t}\n\t\t},\n\t\t// checkbox值变化时\n\t\tcheckboxChange() {\n\t\t\tif (this.radioValue == '5') {\n\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\n\t\t\t}\n\t\t}\n\t},\n\twatch: {\n\t\t'radioValue': 'radioChange',\n\t\t'cycleTotal': 'cycleChange',\n\t\t'averageTotal': 'averageChange',\n\t\t'checkboxString': 'checkboxChange'\n\t},\n\tcomputed: {\n\t\t// 计算两个周期值\n\t\tcycleTotal: function () {\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, this.fullYear, 2098)\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : this.fullYear + 1, 2099)\n\t\t\treturn cycle01 + '-' + cycle02;\n\t\t},\n\t\t// 计算平均用到的值\n\t\taverageTotal: function () {\n\t\t\tconst average01 = this.checkNum(this.average01, this.fullYear, 2098)\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 2099 - average01 || this.fullYear)\n\t\t\treturn average01 + '/' + average02;\n\t\t},\n\t\t// 计算勾选的checkbox值合集\n\t\tcheckboxString: function () {\n\t\t\tlet str = this.checkboxList.join();\n\t\t\treturn str;\n\t\t}\n\t},\n\tmounted: function () {\n\t\t// 仅获取当前年份\n\t\tthis.fullYear = Number(new Date().getFullYear());\n\t\tthis.cycle01 = this.fullYear\n\t\tthis.average01 = this.fullYear\n\t}\n}\n</script>\n"]}]}