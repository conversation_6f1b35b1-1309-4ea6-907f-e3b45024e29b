{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\search-results\\index.vue?vue&type=style&index=0&id=7e98e9a4&scoped=true&lang=css", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\search-results\\index.vue", "mtime": 1748095893923}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmRA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/search-results", "sourcesContent": ["<template>\n  <div class=\"search-results-container\">\n    <!-- 搜索区域 -->\n    <div class=\"search-section\">\n      <div class=\"search-header\">\n        <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\" class=\"back-button\">\n          返回\n        </el-button>\n        <div class=\"search-tabs\">\n          <div class=\"tab-item active\">全文检索</div>\n          <div class=\"tab-item\">可视化</div>\n        </div>\n      </div>\n\n      <div class=\"search-box\">\n        <el-input\n          v-model=\"searchQuery\"\n          placeholder=\"请输入搜索关键词\"\n          class=\"search-input\"\n          @keyup.enter=\"handleSearch\"\n        >\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\n        </el-input>\n      </div>\n    </div>\n\n    <!-- 筛选条件区域 -->\n    <div class=\"filter-section\">\n      <!-- 时间筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">时间范围:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"time in timeOptions\"\n            :key=\"time.value\"\n            :type=\"selectedTime === time.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectTime(time.value)\"\n          >\n            {{ time.label }}\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 平台筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">平台类型:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"platform in platformOptions\"\n            :key=\"platform.value\"\n            :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectPlatform(platform.value)\"\n          >\n            {{ platform.label }}\n            <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 情感筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">情感类型:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"emotion in emotionOptions\"\n            :key=\"emotion.value\"\n            :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectEmotion(emotion.value)\"\n          >\n            {{ emotion.label }}\n            <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 其他筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">其他筛选:</span>\n        <div class=\"filter-options\">\n          <el-button size=\"small\">作者类型</el-button>\n          <el-button size=\"small\">地域</el-button>\n          <el-button size=\"small\">影响力</el-button>\n          <el-button size=\"small\">传播量</el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 结果统计 -->\n    <div class=\"result-stats\">\n      <span>共{{ totalResults }}条结果</span>\n      <div class=\"action-buttons\">\n        <el-button size=\"small\">导出</el-button>\n        <el-button type=\"primary\" size=\"small\">分析</el-button>\n      </div>\n    </div>\n\n    <!-- 搜索结果列表 -->\n    <div class=\"results-list\">\n      <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\n        <div class=\"result-header\">\n          <h3 class=\"result-title\">{{ item.title }}</h3>\n          <div class=\"result-actions\">\n            <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\n          </div>\n        </div>\n\n        <div class=\"result-meta\">\n          <span class=\"meta-item\">{{ item.source }}</span>\n          <span class=\"meta-item\">{{ item.publishTime }}</span>\n          <span class=\"meta-item\">{{ item.author }}</span>\n          <span class=\"meta-item\">{{ item.platform }}</span>\n          <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\n          <span class=\"meta-item\">{{ item.location }}</span>\n          <span class=\"meta-item\">{{ item.category }}</span>\n        </div>\n\n        <div class=\"result-content\">\n          {{ item.content }}\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        background\n        layout=\"prev, pager, next\"\n        :total=\"totalResults\"\n        :current-page.sync=\"currentPage\"\n        :page-size=\"pageSize\"\n        @current-change=\"handlePageChange\"\n      ></el-pagination>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"SearchResults\",\n  data() {\n    return {\n      originalTopNav: undefined,\n      searchQuery: \"今日 万元\",\n      selectedTime: \"24h\",\n      selectedPlatform: \"all\",\n      selectedEmotion: \"all\",\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 10000,\n\n      timeOptions: [\n        { label: \"24小时\", value: \"24h\" },\n        { label: \"一周\", value: \"1w\" },\n        { label: \"半年\", value: \"6m\" },\n        { label: \"一年\", value: \"1y\" },\n        { label: \"自定义\", value: \"custom\" }\n      ],\n\n      platformOptions: [\n        { label: \"全部\", value: \"all\", count: 10540 },\n        { label: \"微信\", value: \"wechat\", count: 1847 },\n        { label: \"微博\", value: \"weibo\", count: 2008 },\n        { label: \"客户端\", value: \"app\", count: 1748 },\n        { label: \"论坛\", value: \"forum\", count: 673 }\n      ],\n\n      emotionOptions: [\n        { label: \"全部\", value: \"all\" },\n        { label: \"正面\", value: \"positive\" },\n        { label: \"负面\", value: \"negative\" },\n        { label: \"中性\", value: \"neutral\" }\n      ],\n\n      searchResults: [\n        {\n          title: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\",\n          source: \"新华网\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"平台来源\",\n          readCount: \"无\",\n          location: \"无所在地\",\n          category: \"新闻\",\n          content: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\"\n        },\n        {\n          title: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文\",\n          source: \"中大论文发表\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"平台来源\",\n          readCount: \"无\",\n          location: \"无所在地\",\n          category: \"论文\",\n          content: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...\"\n        },\n        {\n          title: \"转发微博#中#大学生，人情世故。\",\n          source: \"微博\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"微博\",\n          readCount: \"1000\",\n          location: \"北京\",\n          category: \"社交媒体\",\n          content: \"转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...\"\n        }\n      ]\n    };\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n\n    // 获取URL参数中的搜索关键词\n    if (this.$route.query.q) {\n      this.searchQuery = this.$route.query.q;\n      this.handleSearch();\n    }\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.$message.success(`搜索: ${this.searchQuery}`);\n      // 实际搜索逻辑\n    },\n\n    selectTime(value) {\n      this.selectedTime = value;\n      this.handleSearch();\n    },\n\n    selectPlatform(value) {\n      this.selectedPlatform = value;\n      this.handleSearch();\n    },\n\n    selectEmotion(value) {\n      this.selectedEmotion = value;\n      this.handleSearch();\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page;\n      // 加载对应页面数据\n    },\n\n    goBack() {\n      // 返回上一页，如果没有历史记录则返回信息汇总页面\n      if (window.history.length > 1) {\n        this.$router.go(-1);\n      } else {\n        this.$router.push('/info-summary');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.search-results-container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.search-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.search-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.back-button {\n  margin-right: 20px;\n  color: #409EFF;\n  font-size: 14px;\n}\n\n.back-button:hover {\n  color: #66b1ff;\n}\n\n.search-tabs {\n  display: flex;\n}\n\n.tab-item {\n  padding: 8px 20px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  color: #666;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  border-bottom-color: #409EFF;\n}\n\n.search-box {\n  max-width: 600px;\n}\n\n.search-input {\n  width: 100%;\n}\n\n.filter-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.count {\n  color: #999;\n  font-size: 12px;\n}\n\n.result-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 15px 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.results-list {\n  background: white;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n\n.result-item {\n  padding: 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.result-item:last-child {\n  border-bottom: none;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.result-title {\n  font-size: 16px;\n  color: #333;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n  margin-right: 20px;\n}\n\n.result-meta {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: #999;\n}\n\n.meta-item {\n  white-space: nowrap;\n}\n\n.result-content {\n  color: #666;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  background: white;\n  padding: 20px;\n  border-radius: 4px;\n}\n</style>\n"]}]}