{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\job\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\job\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RKb2IsIGdldEpvYiwgZGVsSm9iLCBhZGRKb2IsIHVwZGF0ZUpvYiwgcnVuSm9iLCBjaGFuZ2VKb2JTdGF0dXMgfSBmcm9tICJAL2FwaS9tb25pdG9yL2pvYiI7CmltcG9ydCBDcm9udGFiIGZyb20gJ0AvY29tcG9uZW50cy9Dcm9udGFiJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsgQ3JvbnRhYiB9LAogIG5hbWU6ICJKb2IiLAogIGRpY3RzOiBbJ3N5c19qb2JfZ3JvdXAnLCAnc3lzX2pvYl9zdGF0dXMnLCAnc3lzX2pvYl9leGVjdXRvciddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5a6a5pe25Lu75Yqh6KGo5qC85pWw5o2uCiAgICAgIGpvYkxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaYvuekuuivpue7huW8ueWHuuWxggogICAgICBvcGVuVmlldzogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaYvuekukNyb27ooajovr7lvI/lvLnlh7rlsYIKICAgICAgb3BlbkNyb246IGZhbHNlLAogICAgICAvLyDkvKDlhaXnmoTooajovr7lvI8KICAgICAgZXhwcmVzc2lvbjogIiIsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGpvYk5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBqb2JHcm91cDogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgam9iTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS7u+WKoeWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBpbnZva2VUYXJnZXQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLosIPnlKjnm67moIflrZfnrKbkuLLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgY3JvbkV4cHJlc3Npb246IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICJjcm9u5omn6KGM6KGo6L6+5byP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5a6a5pe25Lu75Yqh5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Sm9iKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuam9iTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDku7vliqHnu4TlkI3lrZflhbjnv7vor5EKICAgIGpvYkdyb3VwRm9ybWF0KHJvdywgY29sdW1uKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmRpY3QudHlwZS5zeXNfam9iX2dyb3VwLCByb3cuam9iR3JvdXApOwogICAgfSwKICAgIC8vIOS7u+WKoeaJp+ihjOWZqOWQjeWtl+WFuOe/u+ivkQogICAgam9iRXhlY3V0b3JGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuZGljdC50eXBlLnN5c19qb2JfZXhlY3V0b3IsIHJvdy5qb2JFeGVjdXRvcik7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGpvYklkOiB1bmRlZmluZWQsCiAgICAgICAgam9iTmFtZTogdW5kZWZpbmVkLAogICAgICAgIGpvYkdyb3VwOiB1bmRlZmluZWQsCiAgICAgICAgaW52b2tlVGFyZ2V0OiB1bmRlZmluZWQsCiAgICAgICAgY3JvbkV4cHJlc3Npb246IHVuZGVmaW5lZCwKICAgICAgICBtaXNmaXJlUG9saWN5OiAiMSIsCiAgICAgICAgY29uY3VycmVudDogIjEiLAogICAgICAgIHN0YXR1czogIjEiCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmpvYklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvLyDmm7TlpJrmk43kvZzop6blj5EKICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCwgcm93KSB7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgImhhbmRsZVJ1biI6CiAgICAgICAgICB0aGlzLmhhbmRsZVJ1bihyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiaGFuZGxlVmlldyI6CiAgICAgICAgICB0aGlzLmhhbmRsZVZpZXcocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImhhbmRsZUpvYkxvZyI6CiAgICAgICAgICB0aGlzLmhhbmRsZUpvYkxvZyhyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy8g5Lu75Yqh54q25oCB5L+u5pS5CiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy5qb2JOYW1lICsgJyLku7vliqHlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBjaGFuZ2VKb2JTdGF0dXMocm93LmpvYklkLCByb3cuc3RhdHVzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbigpIHsKICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiDnq4vljbPmiafooYzkuIDmrKEgKi8KICAgIGhhbmRsZVJ1bihyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaB56uL5Y2z5omn6KGM5LiA5qyhIicgKyByb3cuam9iTmFtZSArICci5Lu75Yqh5ZCX77yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gcnVuSm9iKHJvdy5qb2JJZCwgcm93LmpvYkdyb3VwKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5omn6KGM5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5Lu75Yqh6K+m57uG5L+h5oGvICovCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgICBnZXRKb2Iocm93LmpvYklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlblZpZXcgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiogY3JvbuihqOi+vuW8j+aMiemSruaTjeS9nCAqLwogICAgaGFuZGxlU2hvd0Nyb24oKSB7CiAgICAgIHRoaXMuZXhwcmVzc2lvbiA9IHRoaXMuZm9ybS5jcm9uRXhwcmVzc2lvbjsKICAgICAgdGhpcy5vcGVuQ3JvbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOehruWumuWQjuWbnuS8oOWAvCAqLwogICAgY3JvbnRhYkZpbGwodmFsdWUpIHsKICAgICAgdGhpcy5mb3JtLmNyb25FeHByZXNzaW9uID0gdmFsdWU7CiAgICB9LAogICAgLyoqIOS7u+WKoeaXpeW/l+WIl+ihqOafpeivoiAqLwogICAgaGFuZGxlSm9iTG9nKHJvdykgewogICAgICBjb25zdCBqb2JJZCA9IHJvdy5qb2JJZCB8fCAwOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL21vbml0b3Ivam9iLWxvZy9pbmRleC8nICsgam9iSWQpCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Lu75YqhIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3Qgam9iSWQgPSByb3cuam9iSWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldEpvYihqb2JJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Lu75YqhIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmpvYklkICE9IHVuZGVmaW5lZCkgewogICAgICAgICAgICB1cGRhdGVKb2IodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRKb2IodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGpvYklkcyA9IHJvdy5qb2JJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5a6a5pe25Lu75Yqh57yW5Y+35Li6IicgKyBqb2JJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbEpvYihqb2JJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnbW9uaXRvci9qb2IvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGpvYl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2UA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/job", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"任务名称\" prop=\"jobName\">\n        <el-input\n          v-model=\"queryParams.jobName\"\n          placeholder=\"请输入任务名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务组名\" prop=\"jobGroup\">\n        <el-select v-model=\"queryParams.jobGroup\" placeholder=\"请选择任务组名\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_job_group\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"任务状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_job_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['monitor:job:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['monitor:job:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['monitor:job:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['monitor:job:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-s-operation\"\n          size=\"mini\"\n          @click=\"handleJobLog\"\n          v-hasPermi=\"['monitor:job:query']\"\n        >日志</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"jobList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务编号\" width=\"100\" align=\"center\" prop=\"jobId\" />\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"jobName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"任务组名\" align=\"center\" prop=\"jobGroup\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_job_group\" :value=\"scope.row.jobGroup\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"调用目标字符串\" align=\"center\" prop=\"invokeTarget\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"cron执行表达式\" align=\"center\" prop=\"cronExpression\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"状态\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"0\"\n            inactive-value=\"1\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['monitor:job:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['monitor:job:remove']\"\n          >删除</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['monitor:job:changeStatus', 'monitor:job:query']\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"handleRun\" icon=\"el-icon-caret-right\"\n                v-hasPermi=\"['monitor:job:changeStatus']\">执行一次</el-dropdown-item>\n              <el-dropdown-item command=\"handleView\" icon=\"el-icon-view\"\n                v-hasPermi=\"['monitor:job:query']\">任务详细</el-dropdown-item>\n              <el-dropdown-item command=\"handleJobLog\" icon=\"el-icon-s-operation\"\n                v-hasPermi=\"['monitor:job:query']\">调度日志</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改定时任务对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务名称\" prop=\"jobName\">\n              <el-input v-model=\"form.jobName\" placeholder=\"请输入任务名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务分组\" prop=\"jobGroup\">\n              <el-select v-model=\"form.jobGroup\" placeholder=\"请选择任务分组\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_job_group\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"jobGroup\">\n              <span slot=\"label\">\n                任务执行器\n                <el-tooltip placement=\"top\">\n                  <div slot=\"content\">\n                    调用方法为异步函数时此选项无效\n                  </div>\n                  <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n              </span>\n              <el-select v-model=\"form.jobExecutor\" placeholder=\"请选择任务执行器\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_job_executor\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"invokeTarget\">\n              <span slot=\"label\">\n                调用方法\n                <el-tooltip placement=\"top\">\n                  <div slot=\"content\">\n                    调用示例：module_task.scheduler_test.job\n                  </div>\n                  <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n              </span>\n              <el-input v-model=\"form.invokeTarget\" placeholder=\"请输入调用目标字符串\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"位置参数\" prop=\"jobArgs\">\n              <el-input v-model=\"form.jobArgs\" placeholder=\"请输入位置参数\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"关键字参数\" prop=\"jobKwargs\">\n              <el-input v-model=\"form.jobKwargs\" placeholder=\"请输入关键字参数\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"cron表达式\" prop=\"cronExpression\">\n              <el-input v-model=\"form.cronExpression\" placeholder=\"请输入cron执行表达式\">\n                <template slot=\"append\">\n                  <el-button type=\"primary\" @click=\"handleShowCron\">\n                    生成表达式\n                    <i class=\"el-icon-time el-icon--right\"></i>\n                  </el-button>\n                </template>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" v-if=\"form.jobId !== undefined\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_job_status\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"执行策略\" prop=\"misfirePolicy\">\n              <el-radio-group v-model=\"form.misfirePolicy\" size=\"small\">\n                <el-radio-button label=\"1\">立即执行</el-radio-button>\n                <el-radio-button label=\"2\">执行一次</el-radio-button>\n                <el-radio-button label=\"3\">放弃执行</el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否并发\" prop=\"concurrent\">\n              <el-radio-group v-model=\"form.concurrent\" size=\"small\">\n                <el-radio-button label=\"0\">允许</el-radio-button>\n                <el-radio-button label=\"1\">禁止</el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"Cron表达式生成器\" :visible.sync=\"openCron\" append-to-body destroy-on-close class=\"scrollbar\">\n      <crontab @hide=\"openCron=false\" @fill=\"crontabFill\" :expression=\"expression\"></crontab>\n    </el-dialog>\n\n    <!-- 任务日志详细 -->\n    <el-dialog title=\"任务详细\" :visible.sync=\"openView\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\" size=\"mini\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务编号：\">{{ form.jobId }}</el-form-item>\n            <el-form-item label=\"任务名称：\">{{ form.jobName }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务分组：\">{{ jobGroupFormat(form) }}</el-form-item>\n            <el-form-item label=\"创建时间：\">{{ parseTime(form.createTime) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"cron表达式：\">{{ form.cronExpression }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"下次执行时间：\">{{ parseTime(form.nextValidTime) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务执行器：\">{{ jobExecutorFormat(form) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"调用目标方法：\">{{ form.invokeTarget }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"位置参数：\">{{ form.jobArgs }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"关键字参数：\">{{ form.jobKwargs }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务状态：\">\n              <div v-if=\"form.status == 0\">正常</div>\n              <div v-else-if=\"form.status == 1\">暂停</div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否并发：\">\n              <div v-if=\"form.concurrent == '0'\">允许</div>\n              <div v-else-if=\"form.concurrent == '1'\">禁止</div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"执行策略：\">\n              <div v-if=\"form.misfirePolicy == '0'\">默认策略</div>\n              <div v-else-if=\"form.misfirePolicy == '1'\">立即执行</div>\n              <div v-else-if=\"form.misfirePolicy == '2'\">执行一次</div>\n              <div v-else-if=\"form.misfirePolicy == '3'\">放弃执行</div>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"openView = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus } from \"@/api/monitor/job\";\nimport Crontab from '@/components/Crontab'\n\nexport default {\n  components: { Crontab },\n  name: \"Job\",\n  dicts: ['sys_job_group', 'sys_job_status', 'sys_job_executor'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 传入的表达式\n      expression: \"\",\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        jobName: undefined,\n        jobGroup: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        jobName: [\n          { required: true, message: \"任务名称不能为空\", trigger: \"blur\" }\n        ],\n        invokeTarget: [\n          { required: true, message: \"调用目标字符串不能为空\", trigger: \"blur\" }\n        ],\n        cronExpression: [\n          { required: true, message: \"cron执行表达式不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询定时任务列表 */\n    getList() {\n      this.loading = true;\n      listJob(this.queryParams).then(response => {\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 任务组名字典翻译\n    jobGroupFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);\n    },\n    // 任务执行器名字典翻译\n    jobExecutorFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_executor, row.jobExecutor);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        jobId: undefined,\n        jobName: undefined,\n        jobGroup: undefined,\n        invokeTarget: undefined,\n        cronExpression: undefined,\n        misfirePolicy: \"1\",\n        concurrent: \"1\",\n        status: \"1\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleRun\":\n          this.handleRun(row);\n          break;\n        case \"handleView\":\n          this.handleView(row);\n          break;\n        case \"handleJobLog\":\n          this.handleJobLog(row);\n          break;\n        default:\n          break;\n      }\n    },\n    // 任务状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.jobName + '\"任务吗？').then(function() {\n        return changeJobStatus(row.jobId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      });\n    },\n    /* 立即执行一次 */\n    handleRun(row) {\n      this.$modal.confirm('确认要立即执行一次\"' + row.jobName + '\"任务吗？').then(function() {\n        return runJob(row.jobId, row.jobGroup);\n      }).then(() => {\n        this.$modal.msgSuccess(\"执行成功\");\n      }).catch(() => {});\n    },\n    /** 任务详细信息 */\n    handleView(row) {\n      getJob(row.jobId).then(response => {\n        this.form = response.data;\n        this.openView = true;\n      });\n    },\n    /** cron表达式按钮操作 */\n    handleShowCron() {\n      this.expression = this.form.cronExpression;\n      this.openCron = true;\n    },\n    /** 确定后回传值 */\n    crontabFill(value) {\n      this.form.cronExpression = value;\n    },\n    /** 任务日志列表查询 */\n    handleJobLog(row) {\n      const jobId = row.jobId || 0;\n      this.$router.push('/monitor/job-log/index/' + jobId)\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加任务\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const jobId = row.jobId || this.ids;\n      getJob(jobId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改任务\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.jobId != undefined) {\n            updateJob(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addJob(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const jobIds = row.jobId || this.ids;\n      this.$modal.confirm('是否确认删除定时任务编号为\"' + jobIds + '\"的数据项？').then(function() {\n        return delJob(jobIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('monitor/job/export', {\n        ...this.queryParams\n      }, `job_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"]}]}