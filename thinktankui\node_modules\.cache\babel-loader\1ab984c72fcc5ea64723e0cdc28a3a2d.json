{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\settings.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\settings.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgLyoqCiAgICog5L6n6L655qCP5Li76aKYIOa3seiJsuS4u+mimHRoZW1lLWRhcmvvvIzmtYXoibLkuLvpoph0aGVtZS1saWdodAogICAqLwogIHNpZGVUaGVtZTogJ3RoZW1lLWRhcmsnLAogIC8qKgogICAqIOaYr+WQpuezu+e7n+W4g+WxgOmFjee9rgogICAqLwogIHNob3dTZXR0aW5nczogZmFsc2UsCiAgLyoqCiAgICog5piv5ZCm5pi+56S66aG26YOo5a+86IiqCiAgICovCiAgdG9wTmF2OiBmYWxzZSwKICAvKioKICAgKiDmmK/lkKbmmL7npLogdGFnc1ZpZXcKICAgKi8KICB0YWdzVmlldzogdHJ1ZSwKICAvKioKICAgKiDmmK/lkKblm7rlrprlpLTpg6gKICAgKi8KICBmaXhlZEhlYWRlcjogZmFsc2UsCiAgLyoqCiAgICog5piv5ZCm5pi+56S6bG9nbwogICAqLwogIHNpZGViYXJMb2dvOiB0cnVlLAogIC8qKgogICAqIOaYr+WQpuaYvuekuuWKqOaAgeagh+mimAogICAqLwogIGR5bmFtaWNUaXRsZTogZmFsc2UsCiAgLyoqCiAgICogQHR5cGUge3N0cmluZyB8IGFycmF5fSAncHJvZHVjdGlvbicgfCBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQogICAqIEBkZXNjcmlwdGlvbiBOZWVkIHNob3cgZXJyIGxvZ3MgY29tcG9uZW50LgogICAqIFRoZSBkZWZhdWx0IGlzIG9ubHkgdXNlZCBpbiB0aGUgcHJvZHVjdGlvbiBlbnYKICAgKiBJZiB5b3Ugd2FudCB0byBhbHNvIHVzZSBpdCBpbiBkZXYsIHlvdSBjYW4gcGFzcyBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQogICAqLwogIGVycm9yTG9nOiAncHJvZHVjdGlvbicKfTs="}, {"version": 3, "names": ["module", "exports", "sideTheme", "showSettings", "topNav", "tagsView", "fixedHeader", "sidebarLogo", "dynamicTitle", "errorLog"], "sources": ["H:/项目/金刚/3/thinktankui/src/settings.js"], "sourcesContent": ["module.exports = {\n  /**\n   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light\n   */\n  sideTheme: 'theme-dark',\n\n  /**\n   * 是否系统布局配置\n   */\n  showSettings: false,\n\n  /**\n   * 是否显示顶部导航\n   */\n  topNav: false,\n\n  /**\n   * 是否显示 tagsView\n   */\n  tagsView: true,\n\n  /**\n   * 是否固定头部\n   */\n  fixedHeader: false,\n\n  /**\n   * 是否显示logo\n   */\n  sidebarLogo: true,\n\n  /**\n   * 是否显示动态标题\n   */\n  dynamicTitle: false,\n\n  /**\n   * @type {string | array} 'production' | ['production', 'development']\n   * @description Need show err logs component.\n   * The default is only used in the production env\n   * If you want to also use it in dev, you can pass ['production', 'development']\n   */\n  errorLog: 'production'\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACf;AACF;AACA;EACEC,SAAS,EAAE,YAAY;EAEvB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;EACEC,MAAM,EAAE,KAAK;EAEb;AACF;AACA;EACEC,QAAQ,EAAE,IAAI;EAEd;AACF;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}]}