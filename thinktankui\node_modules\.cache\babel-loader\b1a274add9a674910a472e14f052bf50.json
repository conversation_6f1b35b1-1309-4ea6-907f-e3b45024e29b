{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core\\theme.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core\\theme.js", "mtime": 1749105929584}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Theme", "quill", "options", "_classCallCheck2", "default", "_defineProperty2", "_createClass2", "key", "value", "init", "_this", "Object", "keys", "modules", "for<PERSON>ach", "name", "addModule", "ModuleClass", "constructor", "import", "concat", "_Theme", "_default", "exports"], "sources": ["../../src/core/theme.ts"], "sourcesContent": ["import type Quill from '../core.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type { ToolbarProps } from '../modules/toolbar.js';\nimport type Uploader from '../modules/uploader.js';\n\nexport interface ThemeOptions {\n  modules: Record<string, unknown> & {\n    toolbar?: null | ToolbarProps;\n  };\n}\n\nclass Theme {\n  static DEFAULTS: ThemeOptions = {\n    modules: {},\n  };\n\n  static themes = {\n    default: Theme,\n  };\n\n  modules: ThemeOptions['modules'] = {};\n\n  constructor(\n    protected quill: Quill,\n    protected options: ThemeOptions,\n  ) {}\n\n  init() {\n    Object.keys(this.options.modules).forEach((name) => {\n      if (this.modules[name] == null) {\n        this.addModule(name);\n      }\n    });\n  }\n\n  addModule(name: 'clipboard'): Clipboard;\n  addModule(name: 'keyboard'): Keyboard;\n  addModule(name: 'uploader'): Uploader;\n  addModule(name: 'history'): History;\n  addModule(name: string): unknown;\n  addModule(name: string) {\n    // @ts-expect-error\n    const ModuleClass = this.quill.constructor.import(`modules/${name}`);\n    this.modules[name] = new ModuleClass(\n      this.quill,\n      this.options.modules[name] || {},\n    );\n    return this.modules[name];\n  }\n}\n\nexport interface ThemeConstructor {\n  new (quill: Quill, options: unknown): Theme;\n  DEFAULTS: ThemeOptions;\n}\n\nexport default Theme;\n"], "mappings": ";;;;;;;;;;;;;;;;IAaMA,KAAK;EAWT,SAAAA,MACYC,KAAY,EACZC,OAAqB,EAC/B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,KAAA;IAAA,IAAAK,gBAAA,CAAAD,OAAA,mBALiC,CAAC,CAAC;IAKnC,KAFUH,KAAY,GAAZA,KAAY;IAAA,KACZC,OAAqB,GAArBA,OAAqB;EAC9B;EAAA,WAAAI,aAAA,CAAAF,OAAA,EAAAJ,KAAA;IAAAO,GAAA;IAAAC,KAAA,EAEH,SAAAC,IAAIA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACLC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACV,OAAO,CAACW,OAAO,CAAC,CAACC,OAAO,CAAE,UAAAC,IAAI,EAAK;QAClD,IAAIL,KAAI,CAACG,OAAO,CAACE,IAAI,CAAC,IAAI,IAAI,EAAE;UAC9BL,KAAI,CAACM,SAAS,CAACD,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAOA,SAAAQ,SAASA,CAACD,IAAY,EAAE;MACtB;MACA,IAAME,WAAW,GAAG,IAAI,CAAChB,KAAK,CAACiB,WAAW,CAACC,MAAM,YAAAC,MAAA,CAAYL,IAAK,CAAC,CAAC;MACpE,IAAI,CAACF,OAAO,CAACE,IAAI,CAAC,GAAG,IAAIE,WAAW,CAClC,IAAI,CAAChB,KAAK,EACV,IAAI,CAACC,OAAO,CAACW,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;MACD,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI,CAAC;IAC3B;EAAA;AAAA;AAAAM,MAAA,GArCIrB,KAAK;AAAA,IAAAK,gBAAA,CAAAD,OAAA,EAALJ,KAAK,cACuB;EAC9Ba,OAAO,EAAE,CAAC;AACZ,CAAC;AAAA,IAAAR,gBAAA,CAAAD,OAAA,EAHGJ,KAAK,YAKO;EACdI,OAAO,EAAEJ;AACX,CAAC;AAAA,IAAAsB,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GAsCYJ,KAAK", "ignoreList": []}]}