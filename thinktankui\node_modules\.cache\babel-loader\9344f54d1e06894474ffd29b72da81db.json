{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\blots\\break.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\blots\\break.js", "mtime": 1749105929537}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "Break", "_EmbedBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "optimize", "prev", "next", "remove", "length", "undefined", "EmbedBlot", "blotName", "tagName", "_default", "exports"], "sources": ["../../src/blots/break.ts"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\n\nclass Break extends EmbedBlot {\n  static value() {\n    return undefined;\n  }\n\n  optimize() {\n    if (this.prev || this.next) {\n      this.remove();\n    }\n  }\n\n  length() {\n    return 0;\n  }\n\n  value() {\n    return '';\n  }\n}\nBreak.blotName = 'break';\nBreak.tagName = 'BR';\n\nexport default Break;\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAqC,IAE/BC,KAAK,0BAAAC,UAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,KAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,KAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,KAAA,EAAAC,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,KAAA;IAAAQ,GAAA;IAAAC,KAAA,EAKT,SAAAC,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACC,IAAI,IAAI,IAAI,CAACC,IAAI,EAAE;QAC1B,IAAI,CAACC,MAAM,CAAC,CAAC;MACf;IACF;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEA,SAAAK,MAAMA,CAAA,EAAG;MACP,OAAO,CAAC;IACV;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,EAAE;IACX;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAhBA,SAAOA,KAAKA,CAAA,EAAG;MACb,OAAOM,SAAS;IAClB;EAAA;AAAA,EAHkBC,oBAAS;AAmB7BhB,KAAK,CAACiB,QAAQ,GAAG,OAAO;AACxBjB,KAAK,CAACkB,OAAO,GAAG,IAAI;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAjB,OAAA,GAELH,KAAK", "ignoreList": []}]}