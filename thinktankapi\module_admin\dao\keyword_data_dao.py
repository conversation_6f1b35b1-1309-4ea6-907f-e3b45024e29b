from datetime import datetime, time
from sqlalchemy import delete, select, update, func, and_, or_, case
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.keyword_data_do import KeywordData
from module_admin.entity.vo.keyword_data_vo import KeywordDataModel, KeywordDataPageQueryModel
from utils.page_util import PageUtil


class KeywordDataDao:
    """
    关键词数据管理模块数据库操作层
    """

    @classmethod
    async def get_keyword_data_detail_by_id(cls, db: AsyncSession, keyword_data_id: int):
        """
        根据关键词数据ID获取详细信息

        :param db: orm对象
        :param keyword_data_id: 关键词数据ID
        :return: 关键词数据对象
        """
        keyword_data = (await db.execute(select(KeywordData).where(KeywordData.id == keyword_data_id))).scalars().first()
        return keyword_data

    @classmethod
    async def get_keyword_data_list(cls, db: AsyncSession, query_object: KeywordDataPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取关键词数据列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 关键词数据列表信息对象
        """
        query = select(KeywordData)
        
        # 构建查询条件
        conditions = []
        
        if query_object.title:
            conditions.append(KeywordData.title.like(f'%{query_object.title}%'))
        if query_object.keyword:
            conditions.append(KeywordData.keyword.like(f'%{query_object.keyword}%'))
        if query_object.type:
            conditions.append(KeywordData.type.like(f'%{query_object.type}%'))
        if query_object.web:
            conditions.append(KeywordData.web.like(f'%{query_object.web}%'))
        if query_object.begin_time:
            begin_time = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            conditions.append(KeywordData.createtime >= begin_time)
        if query_object.end_time:
            end_time = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = end_time.replace(hour=23, minute=59, second=59)
            conditions.append(KeywordData.createtime <= end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 按创建时间倒序排列
        query = query.order_by(KeywordData.createtime.desc())

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)
        else:
            # 不分页查询
            keyword_data_list = (await db.execute(query)).scalars().all()
            return keyword_data_list

    @classmethod
    async def add_keyword_data_dao(cls, db: AsyncSession, keyword_data: KeywordDataModel):
        """
        新增关键词数据数据库操作

        :param db: orm对象
        :param keyword_data: 关键词数据对象
        :return:
        """
        db_keyword_data = KeywordData(**keyword_data.model_dump(exclude={'id'}))
        db.add(db_keyword_data)
        await db.flush()

        return db_keyword_data

    @classmethod
    async def edit_keyword_data_dao(cls, db: AsyncSession, keyword_data: dict):
        """
        编辑关键词数据数据库操作

        :param db: orm对象
        :param keyword_data: 需要更新的关键词数据字典
        :return:
        """
        await db.execute(update(KeywordData), [keyword_data])

    @classmethod
    async def delete_keyword_data_dao(cls, db: AsyncSession, keyword_data: KeywordDataModel):
        """
        删除关键词数据数据库操作

        :param db: orm对象
        :param keyword_data: 关键词数据对象
        :return:
        """
        await db.execute(delete(KeywordData).where(KeywordData.id.in_([keyword_data.id])))

    @classmethod
    async def get_type_statistics(cls, db: AsyncSession):
        """
        获取类型统计数据

        :param db: orm对象
        :return: 类型统计数据
        """
        # 获取各类型的统计数据
        query = select(
            KeywordData.type,
            func.count(KeywordData.id).label('total_count'),
            func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
        ).group_by(KeywordData.type)
        
        result = (await db.execute(query)).all()
        return result
