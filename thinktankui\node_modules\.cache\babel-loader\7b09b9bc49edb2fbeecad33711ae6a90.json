{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\AppMain.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubGluay5qcyIpOwp2YXIgX2luZGV4ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL0lmcmFtZVRvZ2dsZS9pbmRleCIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdBcHBNYWluJywKICBjb21wb25lbnRzOiB7CiAgICBpZnJhbWVUb2dnbGU6IF9pbmRleC5kZWZhdWx0CiAgfSwKICBjb21wdXRlZDogewogICAgY2FjaGVkVmlld3M6IGZ1bmN0aW9uIGNhY2hlZFZpZXdzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGFnc1ZpZXcuY2FjaGVkVmlld3M7CiAgICB9LAogICAga2V5OiBmdW5jdGlvbiBrZXkoKSB7CiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZS5wYXRoOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogZnVuY3Rpb24gJHJvdXRlKCkgewogICAgICB0aGlzLmFkZElmcmFtZSgpOwogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuYWRkSWZyYW1lKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBhZGRJZnJhbWU6IGZ1bmN0aW9uIGFkZElmcmFtZSgpIHsKICAgICAgdmFyIG5hbWUgPSB0aGlzLiRyb3V0ZS5uYW1lOwogICAgICBpZiAobmFtZSAmJiB0aGlzLiRyb3V0ZS5tZXRhLmxpbmspIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvYWRkSWZyYW1lVmlldycsIHRoaXMuJHJvdXRlKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "name", "components", "iframe<PERSON>oggle", "computed", "cachedViews", "$store", "state", "tagsView", "key", "$route", "path", "watch", "addIframe", "mounted", "methods", "meta", "link", "dispatch"], "sources": ["src/layout/components/AppMain.vue"], "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <keep-alive :include=\"cachedViews\">\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\n      </keep-alive>\n    </transition>\n    <iframe-toggle />\n  </section>\n</template>\n\n<script>\nimport iframeToggle from \"./IframeToggle/index\"\n\nexport default {\n  name: 'AppMain',\n  components: { iframeToggle },\n  computed: {\n    cachedViews() {\n      return this.$store.state.tagsView.cachedViews\n    },\n    key() {\n      return this.$route.path\n    }\n  },\n  watch: {\n    $route() {\n      this.addIframe()\n    }\n  },\n  mounted() {\n    this.addIframe()\n  },\n  methods: {\n    addIframe() {\n      const {name} = this.$route\n      if (name && this.$route.meta.link) {\n        this.$store.dispatch('tagsView/addIframeView', this.$route)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-main {\n  /* 50= navbar  50  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.fixed-header + .app-main {\n  padding-top: 50px;\n}\n\n.hasTagsView {\n  .app-main {\n    /* 84 = navbar + tags-view = 50 + 34 */\n    min-height: calc(100vh - 84px);\n  }\n\n  .fixed-header + .app-main {\n    padding-top: 84px;\n  }\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 6px;\n  }\n}\n\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background-color: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: #c0c0c0;\n  border-radius: 3px;\n}\n</style>\n"], "mappings": ";;;;;;;;;AAYA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,WAAA;IACA;IACAI,GAAA,WAAAA,IAAA;MACA,YAAAC,MAAA,CAAAC,IAAA;IACA;EACA;EACAC,KAAA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,SAAA;EACA;EACAE,OAAA;IACAF,SAAA,WAAAA,UAAA;MACA,IAAAZ,IAAA,QAAAS,MAAA,CAAAT,IAAA;MACA,IAAAA,IAAA,SAAAS,MAAA,CAAAM,IAAA,CAAAC,IAAA;QACA,KAAAX,MAAA,CAAAY,QAAA,gCAAAR,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}