{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\login.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\login.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmdldENvZGVJbWcgPSBnZXRDb2RlSW1nOwpleHBvcnRzLmdldEluZm8gPSBnZXRJbmZvOwpleHBvcnRzLmxvZ2luID0gbG9naW47CmV4cG9ydHMubG9nb3V0ID0gbG9nb3V0OwpleHBvcnRzLnJlZ2lzdGVyID0gcmVnaXN0ZXI7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDnmbvlvZXmlrnms5UKZnVuY3Rpb24gbG9naW4odXNlcm5hbWUsIHBhc3N3b3JkLCBjb2RlLCB1dWlkKSB7CiAgdmFyIGRhdGEgPSB7CiAgICB1c2VybmFtZTogdXNlcm5hbWUsCiAgICBwYXNzd29yZDogcGFzc3dvcmQsCiAgICBjb2RlOiBjb2RlLAogICAgdXVpZDogdXVpZAogIH07CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbG9naW4nLAogICAgaGVhZGVyczogewogICAgICBpc1Rva2VuOiBmYWxzZSwKICAgICAgcmVwZWF0U3VibWl0OiBmYWxzZSwKICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQnCiAgICB9LAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOazqOWGjOaWueazlQpmdW5jdGlvbiByZWdpc3RlcihkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcmVnaXN0ZXInLAogICAgaGVhZGVyczogewogICAgICBpc1Rva2VuOiBmYWxzZQogICAgfSwKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDojrflj5bnlKjmiLfor6bnu4bkv6Hmga8KZnVuY3Rpb24gZ2V0SW5mbygpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9nZXRJbmZvJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g6YCA5Ye65pa55rOVCmZ1bmN0aW9uIGxvZ291dCgpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9sb2dvdXQnLAogICAgbWV0aG9kOiAncG9zdCcKICB9KTsKfQoKLy8g6I635Y+W6aqM6K+B56CBCmZ1bmN0aW9uIGdldENvZGVJbWcoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY2FwdGNoYUltYWdlJywKICAgIGhlYWRlcnM6IHsKICAgICAgaXNUb2tlbjogZmFsc2UKICAgIH0sCiAgICBtZXRob2Q6ICdnZXQnLAogICAgdGltZW91dDogMjAwMDAKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "login", "username", "password", "code", "uuid", "data", "request", "url", "headers", "isToken", "repeatSubmit", "method", "register", "getInfo", "logout", "getCodeImg", "timeout"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/login.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 登录方法\nexport function login(username, password, code, uuid) {\n  const data = {\n    username,\n    password,\n    code,\n    uuid\n  }\n  return request({\n    url: '/login',\n    headers: {\n      isToken: false,\n      repeatSubmit: false,\n      'Content-Type': 'application/x-www-form-urlencoded'\n    },\n    method: 'post',\n    data: data\n  })\n}\n\n// 注册方法\nexport function register(data) {\n  return request({\n    url: '/register',\n    headers: {\n      isToken: false\n    },\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取用户详细信息\nexport function getInfo() {\n  return request({\n    url: '/getInfo',\n    method: 'get'\n  })\n}\n\n// 退出方法\nexport function logout() {\n  return request({\n    url: '/logout',\n    method: 'post'\n  })\n}\n\n// 获取验证码\nexport function getCodeImg() {\n  return request({\n    url: '/captchaImage',\n    headers: {\n      isToken: false\n    },\n    method: 'get',\n    timeout: 20000\n  })\n}"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACpD,IAAMC,IAAI,GAAG;IACXJ,QAAQ,EAARA,QAAQ;IACRC,QAAQ,EAARA,QAAQ;IACRC,IAAI,EAAJA,IAAI;IACJC,IAAI,EAAJA;EACF,CAAC;EACD,OAAO,IAAAE,gBAAO,EAAC;IACbC,GAAG,EAAE,QAAQ;IACbC,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE,KAAK;MACnB,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE,MAAM;IACdN,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,QAAQA,CAACP,IAAI,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE,MAAM;IACdN,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,OAAOA,CAAA,EAAG;EACxB,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,UAAU;IACfI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,MAAMA,CAAA,EAAG;EACvB,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,SAAS;IACdI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE,KAAK;IACbK,OAAO,EAAE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}]}