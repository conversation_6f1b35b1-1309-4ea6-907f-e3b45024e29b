import store from '../store/index.js';
import { url } from '../utils/index.js';

// 基础配置
const BASE_URL = 'http://192.168.0.101:9000';
const TIMEOUT = 100000;

// 封装请求方法
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 基本请求配置token
    const token = url.getStorageSync('token');
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    };

    if (token) {
      header.Authorization = token;
    }

    // 开发环境，中对请求参数进行打印输出
    const isDevelopTools = url.getSystemInfoSync().platform === 'devtools';
    if (isDevelopTools) {
      console.log('Request Options:', options);
    }

    // 显示加载提示
    if (!options.hideLoading) {
      uni.showLoading({
        title: '加载中...'
      });
    }

    // 发起请求
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data,
      header: header,
      timeout: TIMEOUT,
      success: (res) => {
        if (!options.hideLoading) {
          uni.hideLoading();
        }
        resolve(res.data);
      },
      fail: (err) => {
        if (!options.hideLoading) {
          uni.hideLoading();
        }
        reject(err);
      }
    });
  });
};

export default request;