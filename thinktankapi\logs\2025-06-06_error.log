2025-06-06 10:30:08.052 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:30:08.053 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:30:08.815 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:30:08.815 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:30:08.820 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:30:09.342 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:30:09.859 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:30:09.859 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:30:46.096 | e5f05c4c31924960bb714f1d3cef87c3 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为e929e531-66dc-4849-942d-8e1ed91cb205的会话获取图片验证码成功
2025-06-06 10:31:06.661 | eccbc9f882d441448f4b9a74f019a79e | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-06 10:31:06.867 | 33787ecfb7644745b94e0f2eb7532c3e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 10:31:07.044 | d321b3a305ae4fe9b1ec5084d74d30e9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 10:32:00.309 | c43274f58b1d481aa695f12e29287d93 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 10:32:00.480 | b494858e69fd4f0c89adced8b8afd538 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 10:34:04.596 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:34:04.597 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
