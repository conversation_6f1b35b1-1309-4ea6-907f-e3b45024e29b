{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\index.vue?vue&type=template&id=1930a3c4", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749105919489}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, null]}