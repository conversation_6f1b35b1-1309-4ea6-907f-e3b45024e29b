{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\ThemePicker\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ORIGINAL_THEME", "_default", "exports", "default", "data", "chalk", "theme", "computed", "defaultTheme", "$store", "state", "settings", "watch", "handler", "val", "oldVal", "immediate", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "setTheme", "a", "created", "methods", "_this2", "_callee2", "themeCluster", "originalCluster", "<PERSON><PERSON><PERSON><PERSON>", "url", "<PERSON><PERSON><PERSON><PERSON>", "styles", "_context2", "getThemeCluster", "replace", "variable", "id", "newStyle", "updateStyle", "styleTag", "document", "getElementById", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "innerText", "getCSSString", "slice", "call", "querySelectorAll", "filter", "style", "text", "RegExp", "test", "for<PERSON>ach", "$emit", "oldCluster", "newCluster", "color", "index", "_this3", "Promise", "resolve", "xhr", "XMLHttpRequest", "onreadystatechange", "readyState", "status", "responseText", "open", "send", "tintColor", "tint", "red", "parseInt", "green", "blue", "join", "Math", "round", "toString", "concat", "shadeColor", "shade", "clusters", "i", "push", "Number", "toFixed"], "sources": ["src/components/ThemePicker/index.vue"], "sourcesContent": ["<template>\n  <el-color-picker\n    v-model=\"theme\"\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\n    class=\"theme-picker\"\n    popper-class=\"theme-picker-dropdown\"\n  />\n</template>\n\n<script>\nconst ORIGINAL_THEME = '#409EFF' // default color\n\nexport default {\n  data() {\n    return {\n      chalk: '', // content of theme-chalk css\n      theme: ''\n    }\n  },\n  computed: {\n    defaultTheme() {\n      return this.$store.state.settings.theme\n    }\n  },\n  watch: {\n    defaultTheme: {\n      handler: function(val, oldVal) {\n        this.theme = val\n      },\n      immediate: true\n    },\n    async theme(val) {\n      await this.setTheme(val)\n    }\n  },\n  created() {\n    if(this.defaultTheme !== ORIGINAL_THEME) {\n      this.setTheme(this.defaultTheme)\n    }\n  },\n  methods: {\n    async setTheme(val) {\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\n      if (typeof val !== 'string') return\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\n\n      const getHandler = (variable, id) => {\n        return () => {\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\n\n          let styleTag = document.getElementById(id)\n          if (!styleTag) {\n            styleTag = document.createElement('style')\n            styleTag.setAttribute('id', id)\n            document.head.appendChild(styleTag)\n          }\n          styleTag.innerText = newStyle\n        }\n      }\n\n      if (!this.chalk) {\n        const url = `/styles/theme-chalk/index.css`\n        await this.getCSSString(url, 'chalk')\n      }\n\n      const chalkHandler = getHandler('chalk', 'chalk-style')\n      chalkHandler()\n\n      const styles = [].slice.call(document.querySelectorAll('style'))\n        .filter(style => {\n          const text = style.innerText\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\n        })\n      styles.forEach(style => {\n        const { innerText } = style\n        if (typeof innerText !== 'string') return\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\n      })\n\n      this.$emit('change', val)\n    },\n\n    updateStyle(style, oldCluster, newCluster) {\n      let newStyle = style\n      oldCluster.forEach((color, index) => {\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\n      })\n      return newStyle\n    },\n\n    getCSSString(url, variable) {\n      return new Promise(resolve => {\n        const xhr = new XMLHttpRequest()\n        xhr.onreadystatechange = () => {\n          if (xhr.readyState === 4 && xhr.status === 200) {\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\n            resolve()\n          }\n        }\n        xhr.open('GET', url)\n        xhr.send()\n      })\n    },\n\n    getThemeCluster(theme) {\n      const tintColor = (color, tint) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        if (tint === 0) { // when primary color is in its rgb space\n          return [red, green, blue].join(',')\n        } else {\n          red += Math.round(tint * (255 - red))\n          green += Math.round(tint * (255 - green))\n          blue += Math.round(tint * (255 - blue))\n\n          red = red.toString(16)\n          green = green.toString(16)\n          blue = blue.toString(16)\n\n          return `#${red}${green}${blue}`\n        }\n      }\n\n      const shadeColor = (color, shade) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        red = Math.round((1 - shade) * red)\n        green = Math.round((1 - shade) * green)\n        blue = Math.round((1 - shade) * blue)\n\n        red = red.toString(16)\n        green = green.toString(16)\n        blue = blue.toString(16)\n\n        return `#${red}${green}${blue}`\n      }\n\n      const clusters = [theme]\n      for (let i = 0; i <= 9; i++) {\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\n      }\n      clusters.push(shadeColor(theme, 0.1))\n      return clusters\n    }\n  }\n}\n</script>\n\n<style>\n.theme-message,\n.theme-picker-dropdown {\n  z-index: 99999 !important;\n}\n\n.theme-picker .el-color-picker__trigger {\n  height: 26px !important;\n  width: 26px !important;\n  padding: 2px;\n}\n\n.theme-picker-dropdown .el-color-dropdown__link-btn {\n  display: none;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAAA,cAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAL,KAAA;IACA;EACA;EACAM,KAAA;IACAJ,YAAA;MACAK,OAAA,WAAAA,QAAAC,GAAA,EAAAC,MAAA;QACA,KAAAT,KAAA,GAAAQ,GAAA;MACA;MACAE,SAAA;IACA;IACAV,KAAA,WAAAA,MAAAQ,GAAA;MAAA,IAAAG,KAAA;MAAA,WAAAC,kBAAA,CAAAf,OAAA,mBAAAgB,aAAA,CAAAhB,OAAA,IAAAiB,CAAA,UAAAC,QAAA;QAAA,WAAAF,aAAA,CAAAhB,OAAA,IAAAmB,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACAP,KAAA,CAAAQ,QAAA,CAAAX,GAAA;YAAA;cAAA,OAAAS,QAAA,CAAAG,CAAA;UAAA;QAAA,GAAAL,OAAA;MAAA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,SAAAnB,YAAA,KAAAR,cAAA;MACA,KAAAyB,QAAA,MAAAjB,YAAA;IACA;EACA;EACAoB,OAAA;IACAH,QAAA,WAAAA,SAAAX,GAAA;MAAA,IAAAe,MAAA;MAAA,WAAAX,kBAAA,CAAAf,OAAA,mBAAAgB,aAAA,CAAAhB,OAAA,IAAAiB,CAAA,UAAAU,SAAA;QAAA,IAAAf,MAAA,EAAAgB,YAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,YAAA,EAAAC,MAAA;QAAA,WAAAjB,aAAA,CAAAhB,OAAA,IAAAmB,CAAA,WAAAe,SAAA;UAAA,kBAAAA,SAAA,CAAAb,CAAA;YAAA;cACAT,MAAA,GAAAc,MAAA,CAAAxB,KAAA,GAAAwB,MAAA,CAAAvB,KAAA,GAAAN,cAAA;cAAA,MACA,OAAAc,GAAA;gBAAAuB,SAAA,CAAAb,CAAA;gBAAA;cAAA;cAAA,OAAAa,SAAA,CAAAX,CAAA;YAAA;cACAK,YAAA,GAAAF,MAAA,CAAAS,eAAA,CAAAxB,GAAA,CAAAyB,OAAA;cACAP,eAAA,GAAAH,MAAA,CAAAS,eAAA,CAAAvB,MAAA,CAAAwB,OAAA;cAEAN,UAAA,YAAAA,WAAAO,QAAA,EAAAC,EAAA;gBACA;kBACA,IAAAT,eAAA,GAAAH,MAAA,CAAAS,eAAA,CAAAtC,cAAA,CAAAuC,OAAA;kBACA,IAAAG,QAAA,GAAAb,MAAA,CAAAc,WAAA,CAAAd,MAAA,CAAAW,QAAA,GAAAR,eAAA,EAAAD,YAAA;kBAEA,IAAAa,QAAA,GAAAC,QAAA,CAAAC,cAAA,CAAAL,EAAA;kBACA,KAAAG,QAAA;oBACAA,QAAA,GAAAC,QAAA,CAAAE,aAAA;oBACAH,QAAA,CAAAI,YAAA,OAAAP,EAAA;oBACAI,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,QAAA;kBACA;kBACAA,QAAA,CAAAO,SAAA,GAAAT,QAAA;gBACA;cACA;cAAA,IAEAb,MAAA,CAAAxB,KAAA;gBAAAgC,SAAA,CAAAb,CAAA;gBAAA;cAAA;cACAU,GAAA;cAAAG,SAAA,CAAAb,CAAA;cAAA,OACAK,MAAA,CAAAuB,YAAA,CAAAlB,GAAA;YAAA;cAGAC,YAAA,GAAAF,UAAA;cACAE,YAAA;cAEAC,MAAA,MAAAiB,KAAA,CAAAC,IAAA,CAAAT,QAAA,CAAAU,gBAAA,WACAC,MAAA,WAAAC,KAAA;gBACA,IAAAC,IAAA,GAAAD,KAAA,CAAAN,SAAA;gBACA,WAAAQ,MAAA,CAAA5C,MAAA,OAAA6C,IAAA,CAAAF,IAAA,wBAAAE,IAAA,CAAAF,IAAA;cACA;cACAtB,MAAA,CAAAyB,OAAA,WAAAJ,KAAA;gBACA,IAAAN,SAAA,GAAAM,KAAA,CAAAN,SAAA;gBACA,WAAAA,SAAA;gBACAM,KAAA,CAAAN,SAAA,GAAAtB,MAAA,CAAAc,WAAA,CAAAQ,SAAA,EAAAnB,eAAA,EAAAD,YAAA;cACA;cAEAF,MAAA,CAAAiC,KAAA,WAAAhD,GAAA;YAAA;cAAA,OAAAuB,SAAA,CAAAX,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IACA;IAEAa,WAAA,WAAAA,YAAAc,KAAA,EAAAM,UAAA,EAAAC,UAAA;MACA,IAAAtB,QAAA,GAAAe,KAAA;MACAM,UAAA,CAAAF,OAAA,WAAAI,KAAA,EAAAC,KAAA;QACAxB,QAAA,GAAAA,QAAA,CAAAH,OAAA,KAAAoB,MAAA,CAAAM,KAAA,SAAAD,UAAA,CAAAE,KAAA;MACA;MACA,OAAAxB,QAAA;IACA;IAEAU,YAAA,WAAAA,aAAAlB,GAAA,EAAAM,QAAA;MAAA,IAAA2B,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACA,IAAAC,GAAA,OAAAC,cAAA;QACAD,GAAA,CAAAE,kBAAA;UACA,IAAAF,GAAA,CAAAG,UAAA,UAAAH,GAAA,CAAAI,MAAA;YACAP,MAAA,CAAA3B,QAAA,IAAA8B,GAAA,CAAAK,YAAA,CAAApC,OAAA;YACA8B,OAAA;UACA;QACA;QACAC,GAAA,CAAAM,IAAA,QAAA1C,GAAA;QACAoC,GAAA,CAAAO,IAAA;MACA;IACA;IAEAvC,eAAA,WAAAA,gBAAAhC,KAAA;MACA,IAAAwE,SAAA,YAAAA,UAAAb,KAAA,EAAAc,IAAA;QACA,IAAAC,GAAA,GAAAC,QAAA,CAAAhB,KAAA,CAAAZ,KAAA;QACA,IAAA6B,KAAA,GAAAD,QAAA,CAAAhB,KAAA,CAAAZ,KAAA;QACA,IAAA8B,IAAA,GAAAF,QAAA,CAAAhB,KAAA,CAAAZ,KAAA;QAEA,IAAA0B,IAAA;UAAA;UACA,QAAAC,GAAA,EAAAE,KAAA,EAAAC,IAAA,EAAAC,IAAA;QACA;UACAJ,GAAA,IAAAK,IAAA,CAAAC,KAAA,CAAAP,IAAA,UAAAC,GAAA;UACAE,KAAA,IAAAG,IAAA,CAAAC,KAAA,CAAAP,IAAA,UAAAG,KAAA;UACAC,IAAA,IAAAE,IAAA,CAAAC,KAAA,CAAAP,IAAA,UAAAI,IAAA;UAEAH,GAAA,GAAAA,GAAA,CAAAO,QAAA;UACAL,KAAA,GAAAA,KAAA,CAAAK,QAAA;UACAJ,IAAA,GAAAA,IAAA,CAAAI,QAAA;UAEA,WAAAC,MAAA,CAAAR,GAAA,EAAAQ,MAAA,CAAAN,KAAA,EAAAM,MAAA,CAAAL,IAAA;QACA;MACA;MAEA,IAAAM,UAAA,YAAAA,WAAAxB,KAAA,EAAAyB,KAAA;QACA,IAAAV,GAAA,GAAAC,QAAA,CAAAhB,KAAA,CAAAZ,KAAA;QACA,IAAA6B,KAAA,GAAAD,QAAA,CAAAhB,KAAA,CAAAZ,KAAA;QACA,IAAA8B,IAAA,GAAAF,QAAA,CAAAhB,KAAA,CAAAZ,KAAA;QAEA2B,GAAA,GAAAK,IAAA,CAAAC,KAAA,MAAAI,KAAA,IAAAV,GAAA;QACAE,KAAA,GAAAG,IAAA,CAAAC,KAAA,MAAAI,KAAA,IAAAR,KAAA;QACAC,IAAA,GAAAE,IAAA,CAAAC,KAAA,MAAAI,KAAA,IAAAP,IAAA;QAEAH,GAAA,GAAAA,GAAA,CAAAO,QAAA;QACAL,KAAA,GAAAA,KAAA,CAAAK,QAAA;QACAJ,IAAA,GAAAA,IAAA,CAAAI,QAAA;QAEA,WAAAC,MAAA,CAAAR,GAAA,EAAAQ,MAAA,CAAAN,KAAA,EAAAM,MAAA,CAAAL,IAAA;MACA;MAEA,IAAAQ,QAAA,IAAArF,KAAA;MACA,SAAAsF,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACAD,QAAA,CAAAE,IAAA,CAAAf,SAAA,CAAAxE,KAAA,EAAAwF,MAAA,EAAAF,CAAA,OAAAG,OAAA;MACA;MACAJ,QAAA,CAAAE,IAAA,CAAAJ,UAAA,CAAAnF,KAAA;MACA,OAAAqF,QAAA;IACA;EACA;AACA", "ignoreList": []}]}