{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\cache\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_cache", "require", "echarts", "_interopRequireWildcard", "name", "data", "commandstats", "usedmemory", "cache", "created", "getList", "openLoading", "methods", "_this", "getCache", "then", "response", "$modal", "closeLoading", "init", "$refs", "setOption", "tooltip", "trigger", "formatter", "series", "type", "roseType", "radius", "center", "commandStats", "animationEasing", "animationDuration", "info", "used_memory_human", "min", "max", "detail", "value", "parseFloat", "window", "addEventListener", "resize", "loading"], "sources": ["src/views/monitor/cache/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-monitor\"></i> 基本信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Redis版本</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行模式</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">端口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">客户端数</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时间(天)</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用CPU</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">内存配置</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">AOF是否开启</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">RDB是否成功</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Key数量</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">网络入口/出口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-pie-chart\"></i> 命令统计</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"commandstats\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-odometer\"></i> 内存信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getCache } from \"@/api/monitor/cache\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: \"Cache\",\n  data() {\n    return {\n      // 统计命令信息\n      commandstats: null,\n      // 使用内存\n      usedmemory: null,\n      // cache信息\n      cache: []\n    }\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查缓存询信息 */\n    getList() {\n      getCache().then((response) => {\n        this.cache = response.data;\n        this.$modal.closeLoading();\n\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\n        this.commandstats.setOption({\n          tooltip: {\n            trigger: \"item\",\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          },\n          series: [\n            {\n              name: \"命令\",\n              type: \"pie\",\n              roseType: \"radius\",\n              radius: [15, 95],\n              center: [\"50%\", \"38%\"],\n              data: response.data.commandStats,\n              animationEasing: \"cubicInOut\",\n              animationDuration: 1000,\n            }\n          ]\n        });\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\n        this.usedmemory.setOption({\n          tooltip: {\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\n          },\n          series: [\n            {\n              name: \"峰值\",\n              type: \"gauge\",\n              min: 0,\n              max: 1000,\n              detail: {\n                formatter: this.cache.info.used_memory_human,\n              },\n              data: [\n                {\n                  value: parseFloat(this.cache.info.used_memory_human),\n                  name: \"内存消耗\",\n                }\n              ]\n            }\n          ]\n        });\n        window.addEventListener(\"resize\", () => {\n          this.commandstats.resize();\n          this.usedmemory.resize();\n        });\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\");\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;AAmEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,eAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,KAAA,GAAAQ,QAAA,CAAAX,IAAA;QACAQ,KAAA,CAAAI,MAAA,CAAAC,YAAA;QAEAL,KAAA,CAAAP,YAAA,GAAAJ,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAd,YAAA;QACAO,KAAA,CAAAP,YAAA,CAAAe,SAAA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA;UACA;UACAC,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAC,QAAA;YACAC,MAAA;YACAC,MAAA;YACAxB,IAAA,EAAAW,QAAA,CAAAX,IAAA,CAAAyB,YAAA;YACAC,eAAA;YACAC,iBAAA;UACA;QAEA;QACAnB,KAAA,CAAAN,UAAA,GAAAL,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAb,UAAA;QACAM,KAAA,CAAAN,UAAA,CAAAc,SAAA;UACAC,OAAA;YACAE,SAAA,sBAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;UACA;UACAT,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAS,GAAA;YACAC,GAAA;YACAC,MAAA;cACAb,SAAA,EAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;YACA;YACA7B,IAAA,GACA;cACAiC,KAAA,EAAAC,UAAA,CAAA1B,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC,iBAAA;cACA9B,IAAA;YACA;UAEA;QAEA;QACAoC,MAAA,CAAAC,gBAAA;UACA5B,KAAA,CAAAP,YAAA,CAAAoC,MAAA;UACA7B,KAAA,CAAAN,UAAA,CAAAmC,MAAA;QACA;MACA;IACA;IACA;IACA/B,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAA0B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}