<template>
  <div class="app-container">
    <div class="page-container">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <el-button type="warning" class="new-scheme-btn" @click="createNewScheme">
            <i class="el-icon-plus"></i> 新建方案
          </el-button>
          <div class="sidebar-btn" @click="toggleSidebar">
            <i class="el-icon-s-fold"></i>
          </div>
        </div>

        <div class="sidebar-search">
          <el-input
            v-model="sidebarSearchText"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            size="small"
            @input="searchSidebar"
          ></el-input>
        </div>

        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect"
          >
            <template v-for="(item, index) in menuCategories">
              <!-- 使用唯一的key -->
              <el-menu-item
                v-if="item.isItem"
                :key="'item-' + item.name"
                :index="item.name"
                :class="{ 'active-menu-item': activeMenuItem === item.name }"
              >
                <i :class="item.icon" v-if="item.icon"></i>
                <span>{{ item.name }}</span>
              </el-menu-item>

              <!-- 如果是子菜单 -->
              <el-submenu
                v-else
                :key="'submenu-' + item.name"
                :index="item.name"
              >
                <template slot="title">
                  <i :class="item.icon" v-if="item.icon"></i>
                  <span>{{ item.name }}({{ item.count }})</span>
                </template>
                <!-- 子菜单项 -->
                <el-menu-item
                  v-for="child in item.children"
                  :key="child.name"
                  :index="child.name"
                >
                  {{ child.name }}
                </el-menu-item>
              </el-submenu>
            </template>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content">
        <!-- 主体内容 -->
        <div class="main-content">
          <!-- 标题和操作区 -->
          <div class="title-area">
            <div class="title">
              <div style="width: 100%; text-align: left;">
                <h2><i class="el-icon-warning-outline" style="color: #E6A23C; margin-right: 8px;"></i>方太<i class="el-icon-edit-outline"></i></h2>
                <div class="tabs" style="text-align: left; margin-top: 10px; margin-left: 0;">
                  <el-button type="text" icon="el-icon-user">接收人设置</el-button>
                  <el-button type="text" icon="el-icon-bell" @click="openWarningDialog">预警设置</el-button>
                  <el-button type="text" icon="el-icon-data-analysis" @click="openKeywordDialog">关键词设置</el-button>
                </div>
              </div>
            </div>
            <div class="actions">
              <el-switch v-model="autoRefresh" active-text="预警开关"></el-switch>
              <div>
                <el-button type="primary" size="small">人工预警</el-button>
                <el-button type="primary" size="small" @click="openAutoWarningDialog">自动预警</el-button>
              </div>
            </div>
          </div>

          <!-- 表格区域 -->
          <div class="table-area">
            <div class="table-toolbar">
              <div class="left-tools">
                <el-checkbox></el-checkbox>
                <el-button type="text" icon="el-icon-star-off"></el-button>
                <el-button type="text" icon="el-icon-message"></el-button>
                <el-button type="text" icon="el-icon-download"></el-button>
              </div>
              <div class="right-tools">
                <span>共计{{total}}条</span>
                <el-button type="text" icon="el-icon-download">导出下载</el-button>
                <el-dropdown>
                  <span class="el-dropdown-link">
                    字段<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                </el-dropdown>
                <div class="date-range">
                  <span>2023/04/23 08:00:00 - 2023/04/25 01:00:00</span>
                </div>
                <el-dropdown>
                  <span class="el-dropdown-link">
                    全部<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                </el-dropdown>
                <el-input
                  placeholder="搜索"
                  prefix-icon="el-icon-search"
                  v-model="searchText"
                  style="width: 200px;"
                  clearable
                ></el-input>
              </div>
            </div>

            <el-table
              :data="tableData"
              style="width: 100%"
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>
              <el-table-column
                label=""
                width="120">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-star-off"></el-button>
                  <el-button type="text" icon="el-icon-message"></el-button>
                  <el-button type="text" icon="el-icon-download"></el-button>
                </template>
              </el-table-column>
              <el-table-column
                prop="title"
                label="标题/摘要"
                show-overflow-tooltip>
              </el-table-column>
              <el-table-column
                prop="source"
                label="来源类型"
                width="100">
                <template slot-scope="scope">
                  <el-tag size="mini" type="danger">{{ scope.row.source }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="platform"
                label="平台类型"
                width="100">
              </el-table-column>
              <el-table-column
                prop="time"
                label="发布时间"
                width="150">
              </el-table-column>
              <el-table-column
                label="操作"
                width="150">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" icon="el-icon-view"></el-button>
                  <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                  <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
                  <el-button type="text" size="mini" icon="el-icon-share"></el-button>
                  <el-button type="text" size="mini" icon="el-icon-more"></el-button>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="currentPage"
              :limit.sync="pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 预警设置抽屉 -->
    <el-drawer
      title="预警设置"
      :visible.sync="warningDialogVisible"
      direction="rtl"
      size="30%"
      :before-close="closeWarningDialog"
      custom-class="warning-drawer">
      <div class="warning-drawer-content">
        <!-- 平台类型 -->
        <div class="warning-section">
          <h3>{{ warningSettings.platformType.title }}</h3>
          <div class="warning-options">
            <el-checkbox
              v-for="(option, index) in warningSettings.platformType.options"
              :key="'platform-' + index"
              v-model="option.checked"
              @change="option.value === 'all' && handleAllCheckbox(warningSettings.platformType)">
              {{ option.label }}
            </el-checkbox>
          </div>
        </div>

        <!-- 内容属性 -->
        <div class="warning-section">
          <h3>{{ warningSettings.contentProperty.title }}</h3>
          <div class="warning-options">
            <el-radio-group v-model="warningSettings.contentProperty.value">
              <el-radio
                v-for="(option, index) in warningSettings.contentProperty.options"
                :key="'content-property-' + index"
                :label="option.value">
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 信息类型 -->
        <div class="warning-section">
          <h3>{{ warningSettings.infoType.title }}</h3>
          <div class="warning-options">
            <el-radio-group v-model="warningSettings.infoType.value">
              <el-radio
                v-for="(option, index) in warningSettings.infoType.options"
                :key="'info-type-' + index"
                :label="option.value">
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 匹配对象 -->
        <div class="warning-section">
          <h3>{{ warningSettings.matchObject.title }}</h3>
          <div class="warning-options">
            <el-checkbox
              v-model="warningSettings.matchObject.allChecked"
              @change="handleMatchObjectAll">
              全部
            </el-checkbox>
            <el-checkbox
              v-for="(option, index) in warningSettings.matchObject.options"
              :key="'match-object-' + index"
              v-model="option.checked"
              :disabled="warningSettings.matchObject.allChecked">
              {{ option.label }}
            </el-checkbox>
          </div>
        </div>

        <!-- 匹配方式 -->
        <div class="warning-section">
          <h3>{{ warningSettings.matchMethod.title }}</h3>
          <div class="warning-options">
            <el-radio-group v-model="warningSettings.matchMethod.value">
              <el-radio
                v-for="(option, index) in warningSettings.matchMethod.options"
                :key="'match-method-' + index"
                :label="option.value">
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 发布地区 -->
        <div class="warning-section">
          <h3>{{ warningSettings.publishRegion.title }}</h3>
          <div class="region-section">
            <div class="region-input">
              <el-input
                placeholder="添加发布地区"
                size="small"
                style="width: 200px;"
                v-model="publishRegionInput">
                <i slot="suffix" class="el-icon-location"></i>
              </el-input>
            </div>
            <div class="region-tags" v-if="warningSettings.publishRegion.regions.length > 0">
              <el-tag
                v-for="(region, index) in warningSettings.publishRegion.regions"
                :key="'region-' + index"
                size="small"
                closable
                @close="removePublishRegion(region.name)">
                {{ region.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- IP属地 -->
        <div class="warning-section">
          <h3>{{ warningSettings.ipArea.title }}</h3>
          <div class="region-section">
            <div class="region-input">
              <el-input
                placeholder="添加IP属地"
                size="small"
                style="width: 200px;"
                v-model="ipAreaInput">
                <i slot="suffix" class="el-icon-location"></i>
              </el-input>
            </div>
            <div class="region-tags" v-if="warningSettings.ipArea.areas.length > 0">
              <el-tag
                v-for="(area, index) in warningSettings.ipArea.areas"
                :key="'ip-area-' + index"
                size="small"
                closable
                @close="removeIpArea(area.name)">
                {{ area.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 媒体类别 -->
        <div class="warning-section category-section" @click="openMediaCategoryDialog">
          <div class="category-header">
            <h3>{{ warningSettings.mediaCategory.title }}</h3>
            <div class="category-count">
              <span>(已选{{ warningSettings.mediaCategory.count }}个)</span>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <!-- 文章类别 -->
        <div class="warning-section category-section" @click="openArticleCategoryDialog">
          <div class="category-header">
            <h3>{{ warningSettings.articleCategory.title }}</h3>
            <div class="category-count">
              <span>(已选{{ warningSettings.articleCategory.count }}个)</span>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="drawer-footer">
          <el-button @click="closeWarningDialog">取消</el-button>
          <el-button type="primary" @click="saveWarningSettings">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 关键词设置抽屉 -->
    <el-drawer
      title="文本词设置"
      :visible.sync="keywordDialogVisible"
      direction="rtl"
      size="30%"
      :before-close="closeKeywordDialog"
      custom-class="keyword-drawer">
      <div class="keyword-drawer-content">
        <!-- 允许词 -->
        <div class="keyword-section">
          <h3>允许词</h3>
          <el-input
            type="textarea"
            :rows="8"
            placeholder="允许词：对文本进行筛选，命中文本的内容会被允许通过"
            v-model="keywordSettings.allowWords">
          </el-input>
        </div>

        <!-- 拒绝词 -->
        <div class="keyword-section">
          <h3>拒绝词</h3>
          <el-input
            type="textarea"
            :rows="8"
            placeholder="拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过"
            v-model="keywordSettings.rejectWords">
          </el-input>
        </div>

        <!-- 底部按钮 -->
        <div class="drawer-footer">
          <el-button @click="closeKeywordDialog">取消</el-button>
          <el-button type="primary" @click="saveKeywordSettings">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 自动预警设置抽屉 -->
    <el-drawer
      title="预警设置"
      :visible.sync="autoWarningDialogVisible"
      direction="rtl"
      size="30%"
      :before-close="closeAutoWarningDialog"
      custom-class="auto-warning-drawer">
      <div class="auto-warning-drawer-content">
        <h3 class="auto-warning-title">自动预警设置</h3>

        <!-- 预警时间 -->
        <div class="auto-warning-section">
          <div class="section-label">预警时间</div>
          <div class="time-range-selector">
            <el-select v-model="autoWarningSettings.timeRange.startHour" placeholder="小时" size="small">
              <el-option
                v-for="h in 24"
                :key="'start-hour-' + h"
                :label="(h - 1).toString().padStart(2, '0')"
                :value="(h - 1).toString().padStart(2, '0')">
              </el-option>
            </el-select>
            <span class="time-separator">:</span>
            <el-select v-model="autoWarningSettings.timeRange.startMinute" placeholder="分钟" size="small">
              <el-option
                v-for="m in 60"
                :key="'start-minute-' + m"
                :label="(m - 1).toString().padStart(2, '0')"
                :value="(m - 1).toString().padStart(2, '0')">
              </el-option>
            </el-select>
          </div>
          <div class="time-range-note">
            <span class="note-text">预警时间段开始时间到结束时间</span>
          </div>
        </div>

        <!-- 预警平台 -->
        <div class="auto-warning-section">
          <div class="section-label">预警平台</div>
          <div class="platform-checkboxes">
            <el-checkbox v-model="autoWarningSettings.platforms.weibo">微博</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.wechat">微信</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.website">网站</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.douyin">抖音</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.redbook">小红书</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.bilibili">B站</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.zhihu">知乎</el-checkbox>
          </div>
        </div>

        <!-- 预警类型 -->
        <div class="auto-warning-section">
          <div class="section-label">预警类型</div>
          <div class="warning-type-selector">
            <el-radio v-model="autoWarningSettings.warningType" label="negative">负面</el-radio>
          </div>
        </div>

        <!-- 处理方式 -->
        <div class="auto-warning-section">
          <div class="section-label">处理方式</div>
          <div class="process-method-selector">
            <el-radio v-model="autoWarningSettings.processMethod" label="all">全部预警</el-radio>
            <el-radio v-model="autoWarningSettings.processMethod" label="onlyAlert">仅告警 <span class="note-text">(只对符合条件的)</span></el-radio>
          </div>
          <div class="process-switch">
            <span class="switch-label">只对重要账号 <span class="note-text">(对方粉丝大于10万或认证账号)</span></span>
            <el-switch v-model="importantAccountOnly"></el-switch>
          </div>
        </div>

        <!-- 优先级别 -->
        <div class="auto-warning-section">
          <div class="section-label">优先级别</div>
          <div class="priority-selector">
            <el-radio v-model="autoWarningSettings.priority" label="normal">正常</el-radio>
            <el-radio v-model="autoWarningSettings.priority" label="urgent">紧急</el-radio>
          </div>
        </div>

        <!-- 处理方式 -->
        <div class="auto-warning-section">
          <div class="section-label">处理方式</div>
          <div class="handle-method-selector">
            <el-radio v-model="autoWarningSettings.handleMethod" label="auto">自动</el-radio>
            <el-radio v-model="autoWarningSettings.handleMethod" label="manual">人工</el-radio>
          </div>
        </div>

        <!-- 告知方式 -->
        <div class="auto-warning-section">
          <div class="section-label">告知方式</div>
          <div class="notify-method-checkboxes">
            <el-checkbox v-model="autoWarningSettings.notifyMethods.sms">短信</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.notifyMethods.email">邮件</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.notifyMethods.wechatNotify">微信通知</el-checkbox>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="drawer-footer">
          <el-button @click="closeAutoWarningDialog">取消</el-button>
          <el-button type="primary" @click="saveAutoWarningSettings">确定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
// 导入分页组件（如果需要）
// import Pagination from "@/components/Pagination";

export default {
  name: 'InfoSummary',
  // 注册组件（如果需要）
  // components: {
  //   Pagination
  // },
  data() {
    return {
      originalTopNav: undefined, // 存储原始的topNav状态
      autoRefresh: true,
      searchText: '',
      currentPage: 1,
      pageSize: 10,
      total: 156,
      // 预警设置弹窗相关数据
      publishRegionInput: '',
      ipAreaInput: '',
      warningDialogVisible: false,
      // 关键词设置抽屉相关数据
      keywordDialogVisible: false,
      keywordSettings: {
        allowWords: '允许词：对文本进行筛选，命中文本的内容会被允许通过',
        rejectWords: '拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过'
      },
      // 自动预警设置抽屉相关数据
      autoWarningDialogVisible: false,
      importantAccountOnly: true,
      autoWarningSettings: {
        timeRange: {
          startHour: '06',
          startMinute: '00',
          endHour: '18',
          endMinute: '00'
        },
        platforms: {
          weibo: true,
          wechat: true,
          website: true,
          douyin: true,
          redbook: true,
          bilibili: false,
          zhihu: false
        },
        warningType: 'negative', // negative, positive, all
        processMethod: 'all', // all, onlyAlert
        priority: 'normal', // normal, urgent
        handleMethod: 'auto', // auto, manual
        notifyMethods: {
          sms: true,
          email: false,
          wechatNotify: true
        }
      },
      warningSettings: {
        platformType: {
          title: '平台类型',
          options: [
            { label: '全部', value: 'all', checked: true },
            { label: '网页', value: 'webpage', checked: true },
            { label: '微信', value: 'wechat', checked: true },
            { label: '微博', value: 'weibo', checked: true },
            { label: '头条号', value: 'toutiao', checked: true },
            { label: 'APP', value: 'app', checked: true },
            { label: '视频', value: 'video', checked: true },
            { label: '论坛', value: 'forum', checked: true },
            { label: '报刊', value: 'newspaper', checked: true },
            { label: '问答', value: 'qa', checked: true }
          ]
        },
        contentProperty: {
          title: '内容属性',
          value: 'all', // all, yes, no
          options: [
            { label: '全部', value: 'all' },
            { label: '是', value: 'yes' },
            { label: '不是', value: 'no' }
          ]
        },
        infoType: {
          title: '信息类型',
          value: 'noncomment', // all, noncomment, comment
          options: [
            { label: '全部', value: 'all' },
            { label: '非评论', value: 'noncomment' },
            { label: '评论', value: 'comment' }
          ]
        },
        matchObject: {
          title: '匹配对象',
          allChecked: true,
          options: [
            { label: '标题匹配', value: 'title', checked: false },
            { label: '正文匹配', value: 'content', checked: false },
            { label: '音频/图片匹配', value: 'media', checked: false },
            { label: '原文匹配', value: 'original', checked: false }
          ]
        },
        matchMethod: {
          title: '匹配方式',
          value: 'exact', // exact, fuzzy
          options: [
            { label: '精准', value: 'exact' },
            { label: '模糊', value: 'fuzzy' }
          ]
        },
        publishRegion: {
          title: '发布地区',
          regions: [
            { name: '全部', value: 'all' }
          ]
        },
        ipArea: {
          title: 'IP属地',
          areas: [
            { name: '全部', value: 'all' }
          ]
        },
        mediaCategory: {
          title: '媒体类别',
          count: 0
        },
        articleCategory: {
          title: '文章类别',
          count: 0
        }
      },
      // 侧边栏数据
      sidebarCollapsed: false,
      sidebarSearchText: '',
      activeMenuItem: '方太',
      menuCategories: [
        { name: '总监', count: 1, children: [], icon: 'el-icon-view' },
        { name: '品牌', count: 1, children: [], icon: 'el-icon-star-on' },
        { name: '方太', count: 0, isItem: true, icon: 'el-icon-office-building' },
        { name: '人物', count: 0, children: [], icon: 'el-icon-user' },
        { name: '机构', count: 0, children: [], icon: 'el-icon-office-building' },
        { name: '产品', count: 0, children: [], icon: 'el-icon-goods' },
        { name: '事件', count: 0, children: [], icon: 'el-icon-bell' },
        { name: '话题', count: 0, children: [], icon: 'el-icon-chat-dot-square' }
      ],
      tableData: [
        {
          title: '方太集成灶新品上市发布会',
          source: '负面',
          platform: 'APP',
          time: '2023-04-24 19:01:00'
        },
        {
          title: '方太集成灶新品上市发布会',
          source: '负面',
          platform: 'APP',
          time: '2023-04-24 19:07:46'
        },
        {
          title: '在集成灶领域中的5%，持续超过了7个品牌相加，方太集成灶',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 18:22:45'
        },
        {
          title: '以空气质量提出史上最严苛标准，方太发布"致净厨房"理念',
          source: '负面',
          platform: '头条号',
          time: '2023-04-24 17:49:45'
        },
        {
          title: '厨电行业"十年一遇"的创新产品，方太发布全球首款"蒸烤一体机"',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 17:12:10'
        },
        {
          title: '方太成立20周年之际，推出第二代"智净洗碗机"，全球首次三代同台亮相',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 15:15:16'
        },
        {
          title: '厨房电器十年来变革与创新，方新一代集成灶发布，升级蒸、烤一体功能',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 14:29:09'
        },
        {
          title: '【方太】全球首款"蒸烤一体机"发布，方太再次引领厨电行业创新',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 14:19:21'
        },
        {
          title: '方太厨房电器发布全球首款"蒸烤一体机"，方太厨电再次引领厨电行业创新',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 12:48:04'
        },
        {
          title: '【方太】家用厨电市场增长放缓，方太发力高端市场，AI/IOT技术成新增长点',
          source: '负面',
          platform: '媒体',
          time: '2023-04-24 12:34:54'
        }
      ],
      multipleSelection: []
    };
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getList() {
      // 这里可以添加获取数据的逻辑
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize);
      // 模拟API调用
      // listData(this.queryParams).then(response => {
      //   this.tableData = response.rows;
      //   this.total = response.total;
      // });
    },
    // 侧边栏相关方法
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },
    handleMenuSelect(index) {
      this.activeMenuItem = index;
      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等
      this.getList();
    },
    createNewScheme() {
      // 新建方案的逻辑
      this.$message({
        message: '新建方案功能待实现',
        type: 'info'
      });
    },
    searchSidebar() {
      // 侧边栏搜索逻辑
      console.log('搜索关键词：', this.sidebarSearchText);
      // 实现搜索逻辑
    },
    // 打开预警设置弹窗
    openWarningDialog() {
      this.warningDialogVisible = true;
    },
    // 关闭预警设置弹窗
    closeWarningDialog() {
      this.warningDialogVisible = false;
    },
    // 打开关键词设置抽屉
    openKeywordDialog() {
      this.keywordDialogVisible = true;
    },
    // 关闭关键词设置抽屉
    closeKeywordDialog() {
      this.keywordDialogVisible = false;
    },
    // 保存预警设置
    saveWarningSettings() {
      // 这里可以添加保存预警设置的逻辑
      console.log('保存预警设置:', this.warningSettings);
      this.$message({
        message: '预警设置保存成功',
        type: 'success'
      });
      this.closeWarningDialog();
    },

    // 处理全部复选框
    handleAllCheckbox(section) {
      const allOption = section.options.find(opt => opt.value === 'all');
      if (allOption && allOption.checked) {
        // 如果全部被选中，则选中所有选项
        section.options.forEach(opt => {
          opt.checked = true;
        });
      }
    },

    // 处理匹配对象全部复选框
    handleMatchObjectAll(checked) {
      this.warningSettings.matchObject.allChecked = checked;
      if (checked) {
        // 如果全部被选中，则取消选中其他选项
        this.warningSettings.matchObject.options.forEach(opt => {
          opt.checked = false;
        });
      }
    },

    // 添加发布地区
    addPublishRegion(region) {
      if (region && !this.warningSettings.publishRegion.regions.some(r => r.name === region)) {
        this.warningSettings.publishRegion.regions.push({ name: region, value: region });
      }
    },

    // 删除发布地区
    removePublishRegion(region) {
      const index = this.warningSettings.publishRegion.regions.findIndex(r => r.name === region);
      if (index !== -1) {
        this.warningSettings.publishRegion.regions.splice(index, 1);
      }
    },

    // 添加IP属地
    addIpArea(area) {
      if (area && !this.warningSettings.ipArea.areas.some(a => a.name === area)) {
        this.warningSettings.ipArea.areas.push({ name: area, value: area });
      }
    },

    // 删除IP属地
    removeIpArea(area) {
      const index = this.warningSettings.ipArea.areas.findIndex(a => a.name === area);
      if (index !== -1) {
        this.warningSettings.ipArea.areas.splice(index, 1);
      }
    },

    // 打开媒体类别对话框
    openMediaCategoryDialog() {
      this.$message({
        message: '媒体类别功能待实现',
        type: 'info'
      });
    },

    // 打开文章类别对话框
    openArticleCategoryDialog() {
      this.$message({
        message: '文章类别功能待实现',
        type: 'info'
      });
    },

    // 保存关键词设置
    saveKeywordSettings() {
      // 这里可以添加保存关键词设置的逻辑
      console.log('保存关键词设置:', this.keywordSettings);
      this.$message({
        message: '关键词设置保存成功',
        type: 'success'
      });
      this.closeKeywordDialog();
    },

    // 打开自动预警设置抽屉
    openAutoWarningDialog() {
      this.autoWarningDialogVisible = true;
    },

    // 关闭自动预警设置抽屉
    closeAutoWarningDialog() {
      this.autoWarningDialogVisible = false;
    },

    // 保存自动预警设置
    saveAutoWarningSettings() {
      // 这里可以添加保存自动预警设置的逻辑
      console.log('保存自动预警设置:', this.autoWarningSettings);
      this.$message({
        message: '自动预警设置保存成功',
        type: 'success'
      });
      this.closeAutoWarningDialog();
    }
  }
};
</script>

<style scoped>
.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-container {
  display: flex;
  height: 100%;
}

/* 左侧导航栏样式 */
.left-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: width 0.3s;
}

/* 折叠状态的侧边栏 */
.left-sidebar.collapsed {
  width: 64px;
}

.left-sidebar.collapsed .sidebar-search,
.left-sidebar.collapsed .el-menu-item span,
.left-sidebar.collapsed .el-submenu__title span {
  display: none;
}

.left-sidebar.collapsed .new-scheme-btn {
  padding: 8px 0;
  font-size: 0;
}

.left-sidebar.collapsed .new-scheme-btn i {
  font-size: 16px;
  margin: 0;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.new-scheme-btn {
  flex: 1;
  font-size: 12px;
  padding: 8px 10px;
}

.sidebar-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  cursor: pointer;
  color: #909399;
}

.sidebar-search {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu-list {
  border-right: none;
}

.active-menu-item {
  background-color: #ecf5ff !important;
  color: #409EFF !important;
}

/* 菜单图标样式 */
::v-deep .el-menu-item i,
::v-deep .el-submenu__title i {
  margin-right: 8px;
  font-size: 16px;
  width: 16px;
  text-align: center;
}

::v-deep .el-menu-item i {
  color: #606266;
}

::v-deep .el-submenu__title i {
  color: #909399;
}

::v-deep .el-menu-item.is-active i,
::v-deep .active-menu-item i {
  color: #409EFF;
}

/* 右侧内容区样式 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 50px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}

.nav-items {
  display: flex;
}

.nav-item {
  padding: 0 15px;
  line-height: 50px;
  cursor: pointer;
  position: relative;
}

.nav-item.active {
  color: #409EFF;
  font-weight: bold;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info span {
  margin-left: 8px;
  font-size: 14px;
  color: #606266;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
  overflow-y: auto;
}

.title-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}

.title {
  display: flex;
  align-items: center;
  width: 100%;
}

.title h2 {
  margin: 0;
  font-size: 18px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  text-align: left;
}

.title h2 i {
  margin-left: 5px;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
}

.tabs {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.tabs .el-button {
  margin-right: 15px;
  padding-left: 0;
}

.actions {
  display: flex;
  align-items: center;
}

.actions .el-button {
  margin-left: 15px;
}

.table-area {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.left-tools, .right-tools {
  display: flex;
  align-items: center;
}

.left-tools > * {
  margin-right: 10px;
}

.right-tools > * {
  margin-left: 15px;
}

.date-range {
  font-size: 12px;
  color: #606266;
}

.el-dropdown-link {
  cursor: pointer;
  color: #606266;
}

.el-table {
  margin-bottom: 20px;
}

/* 覆盖Element UI的一些默认样式 */
::v-deep .el-menu-item, ::v-deep .el-submenu__title {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

::v-deep .el-submenu .el-menu-item {
  height: 36px;
  line-height: 36px;
  padding: 0 20px 0 40px;
}

/* 预警设置抽屉样式 */
.warning-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.warning-drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.warning-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.warning-section h3 {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

.warning-options {
  display: flex;
  flex-wrap: wrap;
}

.warning-options .el-checkbox {
  margin-right: 10px;
  margin-bottom: 10px;
  font-size: 13px;
}

.warning-options .el-radio {
  margin-right: 15px;
  margin-bottom: 10px;
  font-size: 13px;
}

.region-section {
  padding: 5px 0;
}

.region-input {
  margin-bottom: 10px;
}

.region-tags {
  display: flex;
  flex-wrap: wrap;
}

.region-tags .el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}

.category-section {
  cursor: pointer;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-count {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 13px;
}

.category-count i {
  margin-left: 5px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #eee;
  text-align: right;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* 关键词设置抽屉样式 */
.keyword-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.keyword-drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.keyword-section {
  margin-bottom: 20px;
}

.keyword-section h3 {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

/* 自动预警设置抽屉样式 */
.auto-warning-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.auto-warning-drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.auto-warning-title {
  font-size: 16px;
  margin-bottom: 20px;
  font-weight: 500;
  color: #333;
}

.auto-warning-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.section-label {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
}

.time-range-selector {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.time-separator {
  margin: 0 5px;
}

.time-range-note {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.platform-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

.platform-checkboxes .el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}

.warning-type-selector,
.process-method-selector,
.priority-selector,
.handle-method-selector {
  display: flex;
  flex-wrap: wrap;
}

.warning-type-selector .el-radio,
.process-method-selector .el-radio,
.priority-selector .el-radio,
.handle-method-selector .el-radio {
  margin-right: 20px;
  margin-bottom: 10px;
}

.process-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 5px 0;
}

.switch-label {
  font-size: 13px;
  color: #666;
}

.note-text {
  font-size: 12px;
  color: #999;
}

.notify-method-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

.notify-method-checkboxes .el-checkbox {
  margin-right: 20px;
  margin-bottom: 10px;
}
</style>
