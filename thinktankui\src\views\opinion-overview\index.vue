<template>
  <div class="opinion-overview">
    <!-- 左侧导航栏 -->
    <div class="left-sidebar">
      <div class="sidebar-header">
        <el-button type="primary" icon="el-icon-plus" size="small">新建方案</el-button>
      </div>

      <div class="sidebar-search">
        <el-input
          v-model="searchText"
          placeholder="搜索方案"
          size="small"
          prefix-icon="el-icon-search">
        </el-input>
      </div>

      <div class="sidebar-menu">
        <div class="menu-section">
          <div class="section-title">已有方案</div>
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect">
            <el-menu-item index="基础(1)">
              <i class="el-icon-s-custom"></i>
              <span>基础(1)</span>
            </el-menu-item>
            <el-menu-item index="品牌(1)">
              <i class="el-icon-s-goods"></i>
              <span>品牌(1)</span>
            </el-menu-item>
            <el-menu-item index="方太" class="active-item">
              <i class="el-icon-star-off"></i>
              <span>方太</span>
            </el-menu-item>
            <el-menu-item index="人物(0)">
              <i class="el-icon-user"></i>
              <span>人物(0)</span>
            </el-menu-item>
            <el-menu-item index="机构(0)">
              <i class="el-icon-office-building"></i>
              <span>机构(0)</span>
            </el-menu-item>
            <el-menu-item index="产品(0)">
              <i class="el-icon-goods"></i>
              <span>产品(0)</span>
            </el-menu-item>
            <el-menu-item index="事件(0)">
              <i class="el-icon-warning"></i>
              <span>事件(0)</span>
            </el-menu-item>
            <el-menu-item index="话题(0)">
              <i class="el-icon-chat-dot-round"></i>
              <span>话题(0)</span>
            </el-menu-item>
          </el-menu>
        </div>
      </div>
    </div>

    <!-- 右侧内容区 -->
    <div class="right-content">
      <!-- 顶部导航标签栏 -->
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 根据activeTab显示不同内容 -->
        <div v-if="activeTab === 'opinion-monitor'">
          <!-- 舆情监测内容 -->

          <!-- 舆情趋势图表 -->
          <div class="section-card">
            <div class="chart-container">
              <div id="trend-chart" style="width: 100%; height: 400px;"></div>
            </div>
          </div>

          <!-- 热门文章和最新公告 -->
          <div class="bottom-section">
            <div class="left-articles">
              <div class="article-card">
                <div class="card-header">
                  <h3><i class="el-icon-document" style="color: #1890ff; margin-right: 8px;"></i>热门文章</h3>
                </div>
                <div class="article-list">
                  <div class="article-item" v-for="(article, index) in hotArticles" :key="index">
                    <div class="article-icon" :class="article.type">{{ article.icon }}</div>
                    <div class="article-content">
                      <div class="article-title">{{ article.title }}</div>
                      <div class="article-meta">
                        <span class="article-source">{{ article.source }}</span>
                        <span class="article-author">{{ article.author }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="right-announcements">
              <div class="announcement-card">
                <div class="card-header">
                  <h3><i class="el-icon-bell" style="color: #52c41a; margin-right: 8px;"></i>最新公告</h3>
                </div>
                <div class="announcement-list">
                  <div class="announcement-item" v-for="(announcement, index) in announcements" :key="index">
                    <div class="announcement-indicator" :class="announcement.level"></div>
                    <div class="announcement-content">
                      <div class="announcement-title">{{ announcement.title }}</div>
                      <div class="announcement-time">{{ announcement.time }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="activeTab === 'info-summary'">
          <!-- 信息汇总内容 -->
          <!-- 近30天舆情发布地区 -->
          <div class="section-card">
            <div class="card-header">
              <h3><i class="el-icon-location" style="color: #409EFF; margin-right: 8px;"></i>近30天舆情发布地区</h3>
              <div class="stats">
                <div class="stat-item positive">
                  <span class="label">正面舆情</span>
                  <span class="value">111930</span>
                </div>
                <div class="stat-item neutral">
                  <span class="label">中性舆情</span>
                  <span class="value">1118</span>
                </div>
                <div class="stat-item negative">
                  <span class="label">负面舆情</span>
                  <span class="value">444</span>
                </div>
              </div>
            </div>
            <div class="map-container">
              <div id="china-map" style="width: 100%; height: 400px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center; color: #999;">
                <div>地图组件加载中...</div>
              </div>
            </div>
          </div>

          <!-- 今日舆情总量和平台分析 -->
          <div class="bottom-section">
          <div class="left-charts">
            <!-- 近30天平台分析 -->
            <div class="chart-card">
              <div class="card-header">
                <h3><i class="el-icon-pie-chart" style="color: #52C41A; margin-right: 8px;"></i>近30天平台分析</h3>
                <el-button type="text" icon="el-icon-download">导出</el-button>
              </div>
              <div id="platform-chart" style="width: 100%; height: 300px;"></div>
            </div>
          </div>

          <div class="right-stats">
            <!-- 今日舆情总量 -->
            <div class="stats-card">
              <div class="card-header">
                <h3><i class="el-icon-data-line" style="color: #FA8C16; margin-right: 8px;"></i>今日舆情总量</h3>
              </div>
              <div class="total-count">0 0 0,0 0 4,6 8 1</div>
              <div class="platform-stats">
                <div class="platform-item weibo">
                  <div class="platform-icon">微</div>
                  <div class="platform-info">
                    <span class="platform-name">微博</span>
                    <span class="platform-count">534</span>
                    <span class="platform-change">今日新增 -0.8%</span>
                  </div>
                </div>
                <div class="platform-item wechat">
                  <div class="platform-icon">微</div>
                  <div class="platform-info">
                    <span class="platform-name">微信</span>
                    <span class="platform-count">1483</span>
                    <span class="platform-change">今日新增 15.2%</span>
                  </div>
                </div>
                <div class="platform-item weibo-red">
                  <div class="platform-icon">微</div>
                  <div class="platform-info">
                    <span class="platform-name">微博</span>
                    <span class="platform-count">279</span>
                    <span class="platform-change">今日新增 -0.8%</span>
                  </div>
                </div>
                <div class="platform-item xiaohongshu">
                  <div class="platform-icon">小</div>
                  <div class="platform-info">
                    <span class="platform-name">小红书</span>
                    <span class="platform-count">129</span>
                    <span class="platform-change">今日新增 3.2%</span>
                  </div>
                </div>
                <div class="platform-item app">
                  <div class="platform-icon">A</div>
                  <div class="platform-info">
                    <span class="platform-name">APP</span>
                    <span class="platform-count">764</span>
                    <span class="platform-change">今日新增 -1.8%</span>
                  </div>
                </div>
                <div class="platform-item toutiao">
                  <div class="platform-icon">头</div>
                  <div class="platform-info">
                    <span class="platform-name">头条</span>
                    <span class="platform-count">1455</span>
                    <span class="platform-change">今日新增 4.5%</span>
                  </div>
                </div>
                <div class="platform-item douyin">
                  <div class="platform-icon">抖</div>
                  <div class="platform-info">
                    <span class="platform-name">抖音</span>
                    <span class="platform-count">23</span>
                    <span class="platform-change">今日新增 100%</span>
                  </div>
                </div>
                <div class="platform-item news">
                  <div class="platform-icon">新</div>
                  <div class="platform-info">
                    <span class="platform-name">新闻</span>
                    <span class="platform-count">2</span>
                    <span class="platform-change">今日新增 100%</span>
                  </div>
                </div>
                <div class="platform-item forum">
                  <div class="platform-icon">论</div>
                  <div class="platform-info">
                    <span class="platform-name">论坛</span>
                    <span class="platform-count">12</span>
                    <span class="platform-change">今日新增 -2.8%</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 近30天情感属性 -->
            <div class="chart-card">
              <div class="card-header">
                <h3><i class="el-icon-sunny" style="color: #722ED1; margin-right: 8px;"></i>近30天情感属性</h3>
                <el-button type="text" icon="el-icon-download">导出</el-button>
              </div>
              <div id="sentiment-chart" style="width: 100%; height: 200px;"></div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'OpinionOverview',
  data() {
    return {
      activeMenuItem: '方太',
      searchText: '',
      activeTab: 'opinion-monitor', // 默认激活舆情监测标签
      originalTopNav: undefined, // 存储原始的topNav状态
      // 图表实例
      mapChart: null,
      platformChart: null,
      sentimentChart: null,
      trendChart: null, // 趋势图表实例
      // 热门文章数据
      hotArticles: [
        {
          icon: '红',
          type: 'negative',
          title: '某品牌产品质量问题引发消费者不满',
          source: '新浪财经',
          author: '财经记者'
        },
        {
          icon: '黄',
          type: 'neutral',
          title: '市场分析：家电行业发展趋势',
          source: '中国经济网',
          author: '市场分析师'
        },
        {
          icon: '绿',
          type: 'positive',
          title: '创新技术推动行业发展',
          source: '科技日报',
          author: '科技记者'
        },
        {
          icon: '红',
          type: 'negative',
          title: '消费者投诉处理不当引发关注',
          source: '消费者报',
          author: '消费维权'
        },
        {
          icon: '绿',
          type: 'positive',
          title: '企业社会责任获得认可',
          source: '人民日报',
          author: '社会记者'
        }
      ],
      // 最新公告数据
      announcements: [
        {
          level: 'high',
          title: '舆情监测系统升级通知',
          time: '2023-04-20 10:30:00'
        },
        {
          level: 'medium',
          title: '五一假期监测安排',
          time: '2023-04-19 16:45:00'
        },
        {
          level: 'low',
          title: '数据统计报告已生成',
          time: '2023-04-19 14:20:00'
        },
        {
          level: 'high',
          title: '重要舆情预警提醒',
          time: '2023-04-19 09:15:00'
        },
        {
          level: 'medium',
          title: '系统维护完成通知',
          time: '2023-04-18 18:30:00'
        }
      ],
      // 地图数据
      mapData: [
        {name: '北京', value: 15000},
        {name: '上海', value: 12000},
        {name: '广东', value: 18000},
        {name: '浙江', value: 8000},
        {name: '江苏', value: 9000},
        {name: '山东', value: 7000},
        {name: '四川', value: 6000},
        {name: '湖北', value: 5000}
      ],
      // 平台数据
      platformData: [
        {name: '微博', value: 35.2, color: '#ff6b6b'},
        {name: '微信', value: 28.6, color: '#51cf66'},
        {name: '抖音', value: 18.4, color: '#339af0'},
        {name: '今日头条', value: 12.8, color: '#ffd43b'},
        {name: '其他', value: 5.0, color: '#868e96'}
      ],
      // 情感数据
      sentimentData: [
        {name: '正面', value: 65.2, color: '#52c41a'},
        {name: '中性', value: 28.3, color: '#faad14'},
        {name: '负面', value: 6.5, color: '#ff4d4f'}
      ]
    }
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })

    this.$nextTick(() => {
      // 根据当前激活的标签初始化对应的图表
      if (this.activeTab === 'opinion-monitor') {
        this.initTrendChart()
      } else if (this.activeTab === 'info-summary') {
        this.initCharts()
      }
    })
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }

    // 销毁图表实例
    if (this.mapChart) {
      this.mapChart.dispose()
    }
    if (this.platformChart) {
      this.platformChart.dispose()
    }
    if (this.sentimentChart) {
      this.sentimentChart.dispose()
    }
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },
  methods: {
    handleMenuSelect(index) {
      this.activeMenuItem = index
      console.log('Menu selected:', index)
      // 这里可以根据选择的方案加载不同的数据
    },
    handleTabClick(tab) {
      console.log('Tab clicked:', tab.name)
      // 根据不同标签加载不同内容
      this.$nextTick(() => {
        if (tab.name === 'opinion-monitor') {
          this.initTrendChart()
        } else if (tab.name === 'info-summary') {
          this.initCharts()
        }
      })
    },
    initCharts() {
      // 初始化图表
      this.initMap()
      this.initPlatformChart()
      this.initSentimentChart()
    },
    initTrendChart() {
      // 初始化舆情趋势图表
      const chartContainer = document.getElementById('trend-chart')
      if (!chartContainer) return

      this.trendChart = echarts.init(chartContainer)

      // 生成30天的日期数据
      const dates = []
      const today = new Date()
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        dates.push(date.getMonth() + 1 + '.' + date.getDate())
      }

      // 模拟各平台的数据
      const weiboData = this.generateRandomData(30, 1500, 2500)
      const wechatData = this.generateRandomData(30, 1200, 2000)
      const douyinData = this.generateRandomData(30, 800, 1500)
      const toutiaData = this.generateRandomData(30, 600, 1200)
      const otherData = this.generateRandomData(30, 300, 800)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['微博', '微信', '抖音', '头条', '其他'],
          top: 20,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [
          {
            name: '微博',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#1890ff',
              width: 2
            },
            itemStyle: {
              color: '#1890ff'
            },
            data: weiboData
          },
          {
            name: '微信',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },
                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#52c41a',
              width: 2
            },
            itemStyle: {
              color: '#52c41a'
            },
            data: wechatData
          },
          {
            name: '抖音',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },
                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#722ed1',
              width: 2
            },
            itemStyle: {
              color: '#722ed1'
            },
            data: douyinData
          },
          {
            name: '头条',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },
                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#fa8c16',
              width: 2
            },
            itemStyle: {
              color: '#fa8c16'
            },
            data: toutiaData
          },
          {
            name: '其他',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },
                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#a0d911',
              width: 2
            },
            itemStyle: {
              color: '#a0d911'
            },
            data: otherData
          }
        ]
      }

      this.trendChart.setOption(option)
    },
    initMap() {
      // 暂时跳过地图初始化，避免缺少地图数据导致的错误
      console.log('地图初始化已跳过，需要引入中国地图数据')
    },
    initPlatformChart() {
      // 初始化平台分析面积图
      const chartContainer = document.getElementById('platform-chart')
      if (!chartContainer) return

      this.platformChart = echarts.init(chartContainer)

      // 生成30天的日期数据
      const dates = []
      const today = new Date()
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        dates.push(date.getMonth() + 1 + '.' + date.getDate())
      }

      // 模拟各平台的数据
      const weiboData = this.generateRandomData(30, 1500, 2000)
      const wechatData = this.generateRandomData(30, 1200, 1800)
      const douyinData = this.generateRandomData(30, 800, 1200)
      const toutiaData = this.generateRandomData(30, 600, 1000)
      const otherData = this.generateRandomData(30, 300, 600)

      const option = {
        title: {
          text: '近30天平台舆情趋势',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['微博', '微信', '抖音', '头条', '其他'],
          top: 30,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [
          {
            name: '微博',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#1890ff',
              width: 2
            },
            itemStyle: {
              color: '#1890ff'
            },
            data: weiboData
          },
          {
            name: '微信',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },
                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#52c41a',
              width: 2
            },
            itemStyle: {
              color: '#52c41a'
            },
            data: wechatData
          },
          {
            name: '抖音',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },
                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#722ed1',
              width: 2
            },
            itemStyle: {
              color: '#722ed1'
            },
            data: douyinData
          },
          {
            name: '头条',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },
                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#fa8c16',
              width: 2
            },
            itemStyle: {
              color: '#fa8c16'
            },
            data: toutiaData
          },
          {
            name: '其他',
            type: 'line',
            stack: 'Total',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },
                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }
              ])
            },
            lineStyle: {
              color: '#a0d911',
              width: 2
            },
            itemStyle: {
              color: '#a0d911'
            },
            data: otherData
          }
        ]
      }

      this.platformChart.setOption(option)
    },
    initSentimentChart() {
      // 初始化情感属性饼图
      const chartContainer = document.getElementById('sentiment-chart')
      if (!chartContainer) return

      this.sentimentChart = echarts.init(chartContainer)

      const option = {
        title: {
          text: '情感分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          data: ['正面', '中性', '负面'],
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '情感分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {
                name: '正面',
                value: 65.2,
                itemStyle: {
                  color: '#52c41a'
                }
              },
              {
                name: '中性',
                value: 28.3,
                itemStyle: {
                  color: '#faad14'
                }
              },
              {
                name: '负面',
                value: 6.5,
                itemStyle: {
                  color: '#ff4d4f'
                }
              }
            ]
          }
        ]
      }

      this.sentimentChart.setOption(option)
    },
    // 生成随机数据的辅助方法
    generateRandomData(count, min, max) {
      const data = []
      for (let i = 0; i < count; i++) {
        const value = Math.floor(Math.random() * (max - min + 1)) + min
        data.push(value)
      }
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-overview {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.left-sidebar {
  width: 280px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .sidebar-search {
    padding: 16px;
  }

  .sidebar-menu {
    flex: 1;
    padding: 0 16px;

    .section-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .sidebar-menu-list {
      border: none;

      .el-menu-item {
        height: 40px;
        line-height: 40px;
        margin-bottom: 4px;
        border-radius: 4px;

        &.active-item {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;

  .top-tabs {
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;

    .el-tabs {
      .el-tabs__header {
        margin: 0;

        .el-tabs__nav-wrap {
          padding: 0 24px;
        }

        .el-tabs__item {
          height: 50px;
          line-height: 50px;
          font-size: 14px;

          &.is-active {
            color: #1890ff;
            border-bottom-color: #1890ff;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
  }
}

.section-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .stats {
      display: flex;
      gap: 24px;

      .stat-item {
        text-align: center;

        .label {
          display: block;
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }

        .value {
          display: block;
          font-size: 24px;
          font-weight: 600;
        }

        &.positive .value {
          color: #52c41a;
        }

        &.neutral .value {
          color: #faad14;
        }

        &.negative .value {
          color: #ff4d4f;
        }
      }
    }
  }
}

.bottom-section {
  display: flex;
  gap: 24px;

  .left-charts {
    flex: 2;
  }

  .right-stats {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .left-articles {
    flex: 1;
  }

  .right-announcements {
    flex: 1;
  }
}

.chart-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .total-count {
    font-size: 32px;
    font-weight: 600;
    text-align: center;
    margin: 24px 0;
    letter-spacing: 2px;
  }

  .platform-stats {
    .platform-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .platform-icon {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-weight: 600;
        margin-right: 12px;
      }

      &.weibo .platform-icon {
        background-color: #1890ff;
      }

      &.wechat .platform-icon {
        background-color: #52c41a;
      }

      &.weibo-red .platform-icon {
        background-color: #ff4d4f;
      }

      &.xiaohongshu .platform-icon {
        background-color: #eb2f96;
      }

      &.app .platform-icon {
        background-color: #13c2c2;
      }

      &.toutiao .platform-icon {
        background-color: #fa8c16;
      }

      &.douyin .platform-icon {
        background-color: #722ed1;
      }

      &.news .platform-icon {
        background-color: #faad14;
      }

      &.forum .platform-icon {
        background-color: #a0d911;
      }

      .platform-info {
        flex: 1;

        .platform-name {
          display: block;
          font-size: 14px;
          color: #333;
        }

        .platform-count {
          display: block;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        .platform-change {
          display: block;
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

// 热门文章样式
.article-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .article-list {
    .article-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .article-icon {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-weight: 600;
        margin-right: 12px;
        font-size: 14px;

        &.negative {
          background-color: #ff4d4f;
        }

        &.neutral {
          background-color: #faad14;
        }

        &.positive {
          background-color: #52c41a;
        }
      }

      .article-content {
        flex: 1;

        .article-title {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
          line-height: 1.4;
        }

        .article-meta {
          font-size: 12px;
          color: #666;

          .article-source {
            margin-right: 12px;
          }
        }
      }
    }
  }
}

// 最新公告样式
.announcement-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .announcement-list {
    .announcement-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .announcement-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 12px;

        &.high {
          background-color: #ff4d4f;
        }

        &.medium {
          background-color: #faad14;
        }

        &.low {
          background-color: #52c41a;
        }
      }

      .announcement-content {
        flex: 1;

        .announcement-title {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
          line-height: 1.4;
        }

        .announcement-time {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}
</style>
