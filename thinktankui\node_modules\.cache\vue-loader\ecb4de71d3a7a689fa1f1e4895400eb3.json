{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\RightPanel\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUmlnaHRQYW5lbCcsCiAgcHJvcHM6IHsKICAgIGNsaWNrTm90Q2xvc2U6IHsKICAgICAgZGVmYXVsdDogZmFsc2UsCiAgICAgIHR5cGU6IEJvb2xlYW4KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBzaG93OiB7CiAgICAgIGdldCgpIHsKICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2hvd1NldHRpbmdzCiAgICAgIH0sCiAgICAgIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICAgIGtleTogJ3Nob3dTZXR0aW5ncycsCiAgICAgICAgICB2YWx1ZTogdmFsCiAgICAgICAgfSkKICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHNob3codmFsdWUpIHsKICAgICAgaWYgKHZhbHVlICYmICF0aGlzLmNsaWNrTm90Q2xvc2UpIHsKICAgICAgICB0aGlzLmFkZEV2ZW50Q2xpY2soKQogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5hZGRFdmVudENsaWNrKCkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICBjb25zdCBlbHggPSB0aGlzLiRyZWZzLnJpZ2h0UGFuZWwKICAgIGVseC5yZW1vdmUoKQogIH0sCiAgbWV0aG9kczogewogICAgYWRkRXZlbnRDbGljaygpIHsKICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5jbG9zZVNpZGViYXIpCiAgICB9LAogICAgY2xvc2VTaWRlYmFyKGV2dCkgewogICAgICBjb25zdCBwYXJlbnQgPSBldnQudGFyZ2V0LmNsb3Nlc3QoJy5lbC1kcmF3ZXJfX2JvZHknKQogICAgICBpZiAoIXBhcmVudCkgewogICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlCiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5jbG9zZVNpZGViYXIpCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\n  <div ref=\"rightPanel\" class=\"rightPanel-container\">\n    <div class=\"rightPanel-background\" />\n    <div class=\"rightPanel\">\n      <div class=\"rightPanel-items\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RightPanel',\n  props: {\n    clickNotClose: {\n      default: false,\n      type: Boolean\n    }\n  },\n  computed: {\n    show: {\n      get() {\n        return this.$store.state.settings.showSettings\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'showSettings',\n          value: val\n        })\n      }\n    }\n  },\n  watch: {\n    show(value) {\n      if (value && !this.clickNotClose) {\n        this.addEventClick()\n      }\n    }\n  },\n  mounted() {\n    this.addEventClick()\n  },\n  beforeDestroy() {\n    const elx = this.$refs.rightPanel\n    elx.remove()\n  },\n  methods: {\n    addEventClick() {\n      window.addEventListener('click', this.closeSidebar)\n    },\n    closeSidebar(evt) {\n      const parent = evt.target.closest('.el-drawer__body')\n      if (!parent) {\n        this.show = false\n        window.removeEventListener('click', this.closeSidebar)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.rightPanel-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  opacity: 0;\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\n  background: rgba(0, 0, 0, .2);\n  z-index: -1;\n}\n\n.rightPanel {\n  width: 100%;\n  max-width: 260px;\n  height: 100vh;\n  position: fixed;\n  top: 0;\n  right: 0;\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\n  transform: translate(100%);\n  background: #fff;\n  z-index: 40000;\n}\n\n.handle-button {\n  width: 48px;\n  height: 48px;\n  position: absolute;\n  left: -48px;\n  text-align: center;\n  font-size: 24px;\n  border-radius: 6px 0 0 6px !important;\n  z-index: 0;\n  pointer-events: auto;\n  cursor: pointer;\n  color: #fff;\n  line-height: 48px;\n  i {\n    font-size: 24px;\n    line-height: 48px;\n  }\n}\n</style>\n"]}]}