<template>
  <div class="app-container">
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <el-button type="primary" icon="el-icon-message" @click="showSendAlertDialog">发送预警</el-button>
      <el-button type="primary" icon="el-icon-plus" @click="showCreatePlanDialog">新建方案</el-button>
      <el-button type="primary" icon="el-icon-folder-add" @click="showAddToAlertMaterialDialog">加入至报告素材</el-button>
      <el-button type="primary" icon="el-icon-share" @click="showInfoGraphDialog">信息图谱</el-button>
      <el-button type="primary" icon="el-icon-document-checked" @click="showOriginalProofreadingDialog">原稿校对</el-button>
    </div>

    <div class="info-summary-container">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar">
        <div class="sidebar-header">
          <span class="sidebar-title">方太</span>
          <i class="el-icon-arrow-right"></i>
        </div>
        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
          >
            <el-menu-item index="1" @click="handleMenuClick('总览')">
              <i class="el-icon-s-home"></i>
              <span slot="title">总览</span>
            </el-menu-item>
            <el-menu-item index="2" @click="handleMenuClick('品牌')">
              <i class="el-icon-s-goods"></i>
              <span slot="title">品牌(1)</span>
            </el-menu-item>
            <el-menu-item index="3" @click="handleMenuClick('人物')">
              <i class="el-icon-user"></i>
              <span slot="title">人物(0)</span>
            </el-menu-item>
            <el-menu-item index="4" @click="handleMenuClick('机构')">
              <i class="el-icon-office-building"></i>
              <span slot="title">机构(0)</span>
            </el-menu-item>
            <el-menu-item index="5" @click="handleMenuClick('产品')">
              <i class="el-icon-shopping-bag-1"></i>
              <span slot="title">产品(0)</span>
            </el-menu-item>
            <el-menu-item index="6" @click="handleMenuClick('事件')">
              <i class="el-icon-bell"></i>
              <span slot="title">事件(0)</span>
            </el-menu-item>
            <el-menu-item index="7" @click="handleMenuClick('话题')">
              <i class="el-icon-chat-dot-square"></i>
              <span slot="title">话题(0)</span>
            </el-menu-item>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <!-- 标题和操作区域 -->
        <div class="content-header">
          <div class="entity-title">
            <span class="entity-name">方太</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="view-actions">
            <el-button-group>
              <el-button size="small" type="primary" icon="el-icon-s-grid"></el-button>
              <el-button size="small" icon="el-icon-menu"></el-button>
              <el-button size="small" icon="el-icon-s-unfold"></el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs-container">
          <div class="filter-tabs">
            <el-radio-group v-model="activeTab" size="small">
              <el-radio-button label="today">今天</el-radio-button>
              <el-radio-button label="yesterday">昨天</el-radio-button>
              <el-radio-button label="before_yesterday">前天</el-radio-button>
              <el-radio-button label="earlier">更早</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 信息类型筛选 -->
        <div class="filter-section">
          <div class="filter-row">
            <span class="filter-label">平台分类:</span>
            <el-checkbox-group v-model="platformTypes" size="small">
              <el-checkbox label="news">新闻 (46/217)</el-checkbox>
              <el-checkbox label="weibo">微博 (5/34)</el-checkbox>
              <el-checkbox label="wechat">微信 (14/54)</el-checkbox>
              <el-checkbox label="video">视频 (2/78)</el-checkbox>
              <el-checkbox label="app">APP (1/29)</el-checkbox>
              <el-checkbox label="forum">论坛 (0/9)</el-checkbox>
              <el-checkbox label="ecommerce">电商 (44/446)</el-checkbox>
              <el-checkbox label="qa">问答 (6/21)</el-checkbox>
              <el-checkbox label="other">其他 (1/2)</el-checkbox>
            </el-checkbox-group>
          </div>

          <div class="filter-row">
            <span class="filter-label">情感倾向:</span>
            <el-checkbox-group v-model="sentimentTypes" size="small">
              <el-checkbox label="positive">正面 (46/217)</el-checkbox>
              <el-checkbox label="neutral">中性 (7/8)</el-checkbox>
              <el-checkbox label="negative">负面 (3/57)</el-checkbox>
              <el-checkbox label="other">其他 (1/3)</el-checkbox>
            </el-checkbox-group>
          </div>

          <div class="filter-row">
            <span class="filter-label">信息属性:</span>
            <el-checkbox-group v-model="infoAttributes" size="small">
              <el-checkbox label="official">官方发布</el-checkbox>
              <el-checkbox label="media">媒体报道</el-checkbox>
              <el-checkbox label="user">用户评价</el-checkbox>
              <el-checkbox label="competitor">竞品信息</el-checkbox>
              <el-checkbox label="industry">行业动态</el-checkbox>
              <el-checkbox label="policy">政策法规</el-checkbox>
            </el-checkbox-group>
          </div>

          <div class="filter-actions">
            <el-button size="small" type="primary">筛选</el-button>
            <el-button size="small">重置</el-button>
          </div>
        </div>

        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="left-actions">
            <el-button size="small" type="primary">全选</el-button>
            <el-button size="small">导出</el-button>
          </div>
          <div class="right-actions">
            <el-input
              placeholder="搜索关键词"
              prefix-icon="el-icon-search"
              v-model="searchKeyword"
              size="small"
              clearable
              class="search-input"
              @keyup.enter="handleSearchKeyword"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearchKeyword"></el-button>
            </el-input>
            <el-dropdown size="small" split-button type="primary" @command="handleCommand">
              排序
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="time_desc">时间降序</el-dropdown-item>
                <el-dropdown-item command="time_asc">时间升序</el-dropdown-item>
                <el-dropdown-item command="relevance">相关性</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <!-- 信息列表 -->
        <div class="info-list">
          <div v-for="(item, index) in paginatedList" :key="index" class="info-item">
            <el-checkbox v-model="item.selected" class="item-checkbox"></el-checkbox>
            <div class="info-content">
              <div class="info-header">
                <div class="info-title" v-html="item.title"></div>
                <div class="info-actions">
                  <el-button type="text" icon="el-icon-star-off"></el-button>
                  <el-button type="text" icon="el-icon-share"></el-button>
                  <el-button type="text" icon="el-icon-more"></el-button>
                </div>
              </div>
              <div class="info-summary" v-html="item.content"></div>
              <div class="info-footer">
                <span class="info-source">{{ item.source }}</span>
                <span class="info-time">{{ item.time }}</span>
                <span class="info-sentiment" :class="'sentiment-' + item.sentiment">
                  <el-button
                    :type="item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'"
                    size="mini"
                    @click="handleSentimentClick(item)"
                  >
                    <i :class="getSentimentIcon(item.sentiment)"></i>
                    {{ getSentimentText(item.sentiment) }}
                  </el-button>
                </span>
                <span class="info-views">
                  <i class="el-icon-view"></i> {{ item.views }}
                </span>
                <span class="info-comments">
                  <i class="el-icon-chat-line-square"></i> {{ item.comments }}
                </span>
                <span class="info-index">{{ (currentPage - 1) * pageSize + index + 1 }}</span>
              </div>
              <div class="info-images" v-if="item.images && item.images.length > 0">
                <img v-for="(img, imgIndex) in item.images" :key="imgIndex" :src="img" class="info-image" />
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalItems"
            background
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 新建方案对话框 -->
    <el-dialog
      title="新建方案"
      :visible.sync="createPlanDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      custom-class="create-plan-dialog"
    >
      <el-tabs v-model="planActiveTab">
        <el-tab-pane label="监测方式" name="standard">
          <el-form :model="planForm" label-width="70px" size="small">
            <el-form-item label="方案名称">
              <el-input v-model="planForm.name" placeholder="请输入方案名称"></el-input>
            </el-form-item>

            <el-form-item label="作用范围">
              <el-select v-model="planForm.scope" placeholder="请选择" style="width: 100%">
                <el-option label="全部" value="all"></el-option>
                <el-option label="选项1" value="option1"></el-option>
                <el-option label="选项2" value="option2"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="监测对象">
              <div class="monitor-object-select">
                <el-input v-model="planForm.monitorObject" placeholder="请输入监测对象"></el-input>
                <el-dropdown trigger="click" @command="handleMonitorObjectCommand">
                  <span class="el-dropdown-link">
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="option1">选项1</el-dropdown-item>
                    <el-dropdown-item command="option2">选项2</el-dropdown-item>
                    <el-dropdown-item command="option3">选项3</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </el-form-item>

            <el-form-item label="地域">
              <div class="location-select">
                <el-input v-model="planForm.location" placeholder="请选择地域" readonly></el-input>
                <el-button type="text" class="location-btn" @click="showLocationMap">
                  <i class="el-icon-location"></i>
                </el-button>
              </div>
            </el-form-item>

            <el-form-item label="主题">
              <div class="theme-row">
                <el-tag
                  v-for="(tag, index) in planForm.themes"
                  :key="index"
                  closable
                  @close="handleRemoveTheme(tag)"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  class="theme-input"
                  v-if="themeInputVisible"
                  v-model="themeInputValue"
                  ref="themeInput"
                  size="small"
                  @keyup.enter.native="handleAddTheme"
                  @blur="handleAddTheme"
                >
                </el-input>
                <el-button v-else class="theme-button" size="small" @click="showThemeInput">+ 添加主题</el-button>
              </div>
            </el-form-item>

            <el-form-item label="行业分类">
              <div class="industry-row">
                <el-tag
                  v-if="planForm.industry"
                  closable
                  @close="planForm.industry = ''"
                >
                  {{ planForm.industry }}
                </el-tag>
                <el-button v-if="!planForm.industry" class="industry-button" size="small" @click="showIndustrySelect">+ 添加行业分类</el-button>
              </div>
            </el-form-item>

            <el-form-item label="时间段">
              <el-date-picker
                v-model="planForm.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="渠道">
              <el-checkbox-group v-model="planForm.channels">
                <div class="channels-row">
                  <el-checkbox label="news">新闻</el-checkbox>
                  <el-checkbox label="weibo">微博</el-checkbox>
                  <el-checkbox label="wechat">微信</el-checkbox>
                </div>
                <div class="channels-row">
                  <el-checkbox label="video">视频</el-checkbox>
                  <el-checkbox label="app">APP</el-checkbox>
                  <el-checkbox label="forum">论坛</el-checkbox>
                </div>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="高级方式" name="advanced">
          <el-form :model="advancedPlanForm" label-width="70px" size="small">
            <!-- 高级模式的表单内容 -->
            <el-form-item label="方案名称">
              <el-input v-model="advancedPlanForm.name" placeholder="请输入方案名称"></el-input>
            </el-form-item>

            <!-- 其他高级选项 -->
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="createPlanDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlan">保存</el-button>
      </div>
    </el-dialog>

    <!-- 行业分类弹窗 -->
    <el-dialog
      title="行业分类"
      :visible.sync="industryDialogVisible"
      width="40%"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      append-to-body
      custom-class="industry-dialog"
    >
      <div class="industry-dialog-content">
        <div class="industry-tree-container">
          <el-tree
            :data="industryTreeData"
            :props="industryTreeProps"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleIndustryNodeClick"
          />
        </div>
        <div class="industry-selected-container">
          <div class="industry-selected-title">
            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="industryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmIndustrySelect">确定</el-button>
      </div>
    </el-dialog>

    <!-- 发送预警弹窗 -->
    <el-dialog
      title="发送预警"
      :visible.sync="sendAlertDialogVisible"
      width="40%"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      append-to-body
      custom-class="send-alert-dialog"
    >
      <div class="send-alert-content">
        <el-form :model="alertForm" label-width="80px" size="small">
          <el-form-item label="预警标题">
            <el-input v-model="alertForm.title" placeholder="请输入预警标题"></el-input>
          </el-form-item>

          <el-form-item label="接收人">
            <div class="receiver-list">
              <div class="receiver-item" v-for="(receiver, index) in receivers" :key="index">
                <div class="receiver-type">{{ receiver.type }}</div>
                <el-select
                  v-model="alertForm.selectedReceivers[index]"
                  :placeholder="'请选择' + receiver.type"
                  class="receiver-select"
                >
                  <el-option
                    v-for="person in receiver.persons"
                    :key="person"
                    :label="person"
                    :value="person"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSendAlert">取消</el-button>
        <el-button type="primary" @click="confirmSendAlert">确定</el-button>
      </div>
    </el-dialog>

    <!-- 正面情感信息弹窗 -->
    <el-dialog
      title="情感属性纠错"
      :visible.sync="positiveSentimentDialogVisible"
      width="50%"
      append-to-body
      custom-class="positive-sentiment-dialog"
    >
      <el-radio-group v-model="selectedSentiment" size="small">
        <el-radio-button label="positive">正面</el-radio-button>
        <el-radio-button label="neutral">中性</el-radio-button>
        <el-radio-button label="negative">负面</el-radio-button>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="positiveSentimentDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePositiveDialogConfirm">确定</el-button>
      </span>
    </el-dialog>

    <!-- 加入至报告素材对话框 -->
    <el-dialog
      title="加入至报告素材"
      :visible.sync="addToAlertMaterialDialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      custom-class="add-to-alert-material-dialog"
    >
      <div class="add-to-alert-material-content">
        <el-form label-width="80px" size="small">
          <el-form-item label="选择素材库">
            <el-select v-model="selectedMaterialLibrary" placeholder="请选择素材库" style="width: 100%">
              <!-- 这里需要根据实际数据填充选项 -->
              <el-option label="素材库1" value="library1"></el-option>
              <el-option label="素材库2" value="library2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddToAlertMaterial">取消</el-button>
        <el-button type="primary" @click="confirmAddToAlertMaterial">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'InfoSummary',
  computed: {
    // 根据当前页码和每页显示数量计算当前页的数据
    paginatedList() {
      const start = (this.currentPage - 1) * this.pageSize;
      // 实际应用中，这里应该是从后端获取分页数据
      // 这里为了演示，我们从本地数据中截取一部分
      return this.infoList.slice(0, Math.min(this.infoList.length, this.pageSize));
    }
  },
  data() {
    return {
      // 页面基础数据
      originalTopNav: undefined, // 存储原始的topNav状态
      searchKeyword: '',
      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示
      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值
      editingItem: null, // 当前编辑的情感条目
      activeMenu: '2', // 默认选中品牌
      activeTab: 'today', // 默认选中今天
      platformTypes: ['news', 'weibo', 'wechat', 'video', 'app'], // 默认选中的平台类型
      sentimentTypes: ['positive', 'neutral'], // 默认选中的情感类型
      infoAttributes: ['official', 'media'], // 默认选中的信息属性

      // 分页相关数据
      currentPage: 1,
      pageSize: 10,
      totalItems: 120, // 假设总共有120条数据

      // 新建方案对话框相关数据
      createPlanDialogVisible: false,
      planActiveTab: 'standard',
      themeInputVisible: false,
      themeInputValue: '',
      planForm: {
        name: '',
        scope: 'all',
        monitorObject: '',
        location: '',
        themes: [],
        industry: '',
        timeRange: '',
        channels: ['news', 'weibo', 'wechat']
      },
      advancedPlanForm: {
        name: ''
      },

      // 行业分类弹窗相关数据
      industryDialogVisible: false,
      selectedIndustry: null,
      industryTreeProps: {
        label: 'label',
        children: 'children'
      },

      // 发送预警弹窗相关数据
      sendAlertDialogVisible: false,
      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示
      alertForm: {
        title: '',
        selectedReceivers: ['', '', '', '', '', '']
      },
      receivers: [
        {
          type: '总监',
          persons: ['王总监', '李总监', '张总监']
        },
        {
          type: '经理',
          persons: ['王经理', '李经理', '张经理']
        },
        {
          type: '主管',
          persons: ['王主管', '李主管', '张主管']
        },
        {
          type: '员工',
          persons: ['王员工', '李员工', '张员工']
        },
        {
          type: '外部人员',
          persons: ['外部人员1', '外部人员2', '外部人员3']
        },
        {
          type: '其他',
          persons: ['其他人员1', '其他人员2', '其他人员3']
        }
      ],
      industryTreeData: [
        {
          id: 1,
          label: '制造',
          children: []
        },
        {
          id: 2,
          label: '公共',
          children: []
        },
        {
          id: 3,
          label: '教育',
          children: []
        },
        {
          id: 4,
          label: '工业设备',
          children: []
        },
        {
          id: 5,
          label: '环保设备',
          children: []
        },
        {
          id: 6,
          label: '金融',
          children: []
        },
        {
          id: 7,
          label: '商业',
          children: []
        },
        {
          id: 8,
          label: '民用与商用',
          children: []
        },
        {
          id: 9,
          label: '政府部门',
          children: []
        }
      ],

      // 信息列表数据
      infoList: []
    };
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })
    // 加载关键词数据
    this.loadKeywordData()
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
  methods: {
    // 加载关键词数据
    async loadKeywordData() {
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }

        const response = await this.$http.get('/public/keywordData/list', { params })

        if (response.data.code === 200) {
          // 将API返回的数据转换为前端需要的格式
          this.infoList = response.data.data.rows || []
          this.totalItems = response.data.data.total || 0
        } else {
          this.$message.error('获取数据失败：' + response.data.msg)
        }
      } catch (error) {
        console.error('加载关键词数据失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      }
    },

    // 菜单点击事件
    handleMenuClick(menuName) {
      console.log('选择菜单:', menuName);
    },

    // 获取情感图标
    getSentimentIcon(sentiment) {
      const icons = {
        positive: 'el-icon-sunny',
        neutral: 'el-icon-partly-cloudy',
        negative: 'el-icon-cloudy'
      };
      return icons[sentiment] || 'el-icon-question';
    },

    // 获取情感文本
    getSentimentText(sentiment) {
      const texts = {
        positive: '正面',
        neutral: '中性',
        negative: '负面'
      };
      return texts[sentiment] || '未知';
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      console.log('执行命令:', command);
    },

    // 处理搜索关键词
    handleSearchKeyword() {
      if (this.searchKeyword.trim()) {
        // 跳转到搜索结果页面，并传递搜索关键词
        this.$router.push({
          path: '/search-results',
          query: {
            q: this.searchKeyword.trim()
          }
        });
      } else {
        this.$message.warning('请输入搜索关键词');
      }
    },

    // 分页相关方法
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 切换每页显示数量时，重置为第一页
      console.log('每页显示数量:', size);
      this.loadKeywordData(); // 重新加载数据
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      console.log('当前页码:', page);
      this.loadKeywordData(); // 重新加载数据
    },

    // 新建方案相关方法
    showCreatePlanDialog() {
      this.createPlanDialogVisible = true;
    },

    // 主题标签相关方法
    handleRemoveTheme(tag) {
      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);
    },

    showThemeInput() {
      this.themeInputVisible = true;
      this.$nextTick(_ => {
        this.$refs.themeInput.$refs.input.focus();
      });
    },

    handleAddTheme() {
      let inputValue = this.themeInputValue;
      if (inputValue) {
        if (!this.planForm.themes.includes(inputValue)) {
          this.planForm.themes.push(inputValue);
        }
      }
      this.themeInputVisible = false;
      this.themeInputValue = '';
    },

    // 监测对象下拉菜单命令处理
    handleMonitorObjectCommand(command) {
      this.planForm.monitorObject = command;
    },

    // 显示地域选择地图
    showLocationMap() {
      // 这里可以实现地图选择功能
      this.$message.info('显示地域选择地图');
    },

    // 显示行业分类选择
    showIndustrySelect() {
      this.industryDialogVisible = true;
    },

    // 处理行业分类树节点点击
    handleIndustryNodeClick(data) {
      this.selectedIndustry = data;
    },

    // 确认行业分类选择
    confirmIndustrySelect() {
      if (this.selectedIndustry) {
        this.planForm.industry = this.selectedIndustry.label;
      }
      this.industryDialogVisible = false;
    },

    // 保存方案
    savePlan() {
      // 根据当前激活的标签页选择不同的表单数据
      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;

      console.log('保存方案:', formData);
      // 这里可以添加表单验证和提交到后端的逻辑

      // 关闭对话框
      this.createPlanDialogVisible = false;

      // 重置表单
      if (this.planActiveTab === 'standard') {
        this.planForm = {
          name: '',
          scope: 'all',
          monitorObject: '',
          location: '',
          themes: [],
          industry: '',
          timeRange: '',
          channels: ['news', 'weibo', 'wechat']
        };
      } else {
        this.advancedPlanForm = {
          name: ''
        };
      }
    },

    // 显示发送预警弹窗
    showSendAlertDialog() {
      this.sendAlertDialogVisible = true;
    },

    // 取消发送预警
    cancelSendAlert() {
      this.sendAlertDialogVisible = false;
      // 重置表单
      this.alertForm = {
        title: '',
        selectedReceivers: ['', '', '', '', '', '']
      };
    },
    handleSentimentClick(item) {
      this.editingItem = item;
      this.selectedSentiment = item.sentiment;
      this.positiveSentimentDialogVisible = true;
    },
    handlePositiveDialogConfirm() {
      if (this.editingItem) {
        this.editingItem.sentiment = this.selectedSentiment;
        // 在实际应用中，这里可能需要调用API将更改保存到后端
        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);
      }
      this.positiveSentimentDialogVisible = false;
    },

    // 确认发送预警
    confirmSendAlert() {
      // 在这里处理发送预警的逻辑
      console.log('发送预警:', this.alertForm);
      this.sendAlertDialogVisible = false;
      // 清空表单
      this.alertForm = {
        title: '',
        selectedReceivers: ['', '', '', '', '', '']
      };
    },

    // 加入至报告素材对话框相关方法
    showAddToAlertMaterialDialog() {
      this.addToAlertMaterialDialogVisible = true;
    },
    cancelAddToAlertMaterial() {
      this.addToAlertMaterialDialogVisible = false;
      this.selectedMaterialLibrary = ''; // 清空选中值
    },
    confirmAddToAlertMaterial() {
      // 这里添加确认逻辑，例如提交选中的素材库
      console.log('Selected Material Library:', this.selectedMaterialLibrary);
      this.addToAlertMaterialDialogVisible = false;
      this.selectedMaterialLibrary = ''; // 清空选中值
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 操作按钮区域样式
.action-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.info-summary-container {
  display: flex;
  height: 100%;
  padding-top: 60px; // 为新建方案按钮留出空间
}

// 左侧导航栏样式
.left-sidebar {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-menu {
  flex: 1;
}

.el-menu-vertical {
  border-right: none;
}

// 右侧内容区域样式
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.entity-name {
  margin-right: 5px;
}

// 标签页样式
.tabs-container {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-tabs {
  display: flex;
  align-items: center;
}

// 筛选区域样式
.filter-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.filter-row {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #606266;
  line-height: 28px;
}

.filter-actions {
  margin-top: 10px;
  padding-left: 80px;
}

// 操作栏样式
.action-bar {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

// 信息列表样式
.info-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.info-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.item-checkbox {
  margin-right: 10px;
  margin-top: 3px;
}

.info-content {
  flex: 1;
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-summary {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-footer {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.info-source, .info-time, .info-sentiment, .info-views, .info-comments {
  margin-right: 15px;
}

.info-index {
  margin-left: auto;
}

.sentiment-positive {
  color: #67c23a;
}

.sentiment-neutral {
  color: #909399;
}

.sentiment-negative {
  color: #f56c6c;
}

.info-images {
  display: flex;
  margin-top: 10px;
}

.info-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 4px;
}

// 高亮样式
:deep(.highlight) {
  color: #409eff;
  font-weight: bold;
}

// 新建方案对话框样式
.create-plan-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }
}

.monitor-object-select {
  display: flex;
  align-items: center;
  position: relative;

  .el-dropdown {
    position: absolute;
    right: 10px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.location-select {
  display: flex;
  align-items: center;
}

.location-btn {
  margin-left: 10px;
}

.industry-select-btn {
  width: 100%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }
}

.theme-row, .industry-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.theme-input, .industry-input {
  width: 100px;
  margin-left: 10px;
  vertical-align: bottom;
}

.theme-button, .industry-button {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.channels-row {
  display: flex;
  margin-bottom: 10px;

  .el-checkbox {
    margin-right: 20px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px;
  text-align: right;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
}

// 行业分类弹窗样式
.industry-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .industry-dialog-content {
    display: flex;
    height: 400px;
  }

  .industry-tree-container {
    flex: 1;
    padding: 15px;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
  }

  .industry-selected-container {
    width: 200px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }

  .industry-selected-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
}

// 发送预警弹窗样式
.send-alert-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .send-alert-content {
    padding: 20px;
  }

  .receiver-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .receiver-item {
    display: flex;
    align-items: center;
  }

  .receiver-type {
    width: 80px;
    margin-right: 10px;
  }

  .receiver-select {
    width: 100%;
  }
}

// 正面情感弹窗样式
.positive-sentiment-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .el-dialog__body {
    padding: 20px;
  }
}
</style>

<el-button type="success" size="mini" @click="handlePositiveClick">正面</el-button>,
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 操作按钮区域样式
.action-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.info-summary-container {
  display: flex;
  height: 100%;
  padding-top: 60px; // 为新建方案按钮留出空间
}

// 左侧导航栏样式
.left-sidebar {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-menu {
  flex: 1;
}

.el-menu-vertical {
  border-right: none;
}

// 右侧内容区域样式
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.entity-name {
  margin-right: 5px;
}

// 标签页样式
.tabs-container {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-tabs {
  display: flex;
  align-items: center;
}

// 筛选区域样式
.filter-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.filter-row {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #606266;
  line-height: 28px;
}

.filter-actions {
  margin-top: 10px;
  padding-left: 80px;
}

// 操作栏样式
.action-bar {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

// 信息列表样式
.info-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.info-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.item-checkbox {
  margin-right: 10px;
  margin-top: 3px;
}

.info-content {
  flex: 1;
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-summary {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-footer {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.info-source, .info-time, .info-sentiment, .info-views, .info-comments {
  margin-right: 15px;
}

.info-index {
  margin-left: auto;
}

.sentiment-positive {
  color: #67c23a;
}

.sentiment-neutral {
  color: #909399;
}

.sentiment-negative {
  color: #f56c6c;
}

.info-images {
  display: flex;
  margin-top: 10px;
}

.info-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 4px;
}

// 高亮样式
:deep(.highlight) {
  color: #409eff;
  font-weight: bold;
}

// 新建方案对话框样式
.create-plan-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }
}

.monitor-object-select {
  display: flex;
  align-items: center;
  position: relative;

  .el-dropdown {
    position: absolute;
    right: 10px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.location-select {
  display: flex;
  align-items: center;
}

.location-btn {
  margin-left: 10px;
}

.industry-select-btn {
  width: 100%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }
}

.theme-row, .industry-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.theme-input, .industry-input {
  width: 100px;
  margin-left: 10px;
  vertical-align: bottom;
}

.theme-button, .industry-button {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.channels-row {
  display: flex;
  margin-bottom: 10px;

  .el-checkbox {
    margin-right: 20px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px;
  text-align: right;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
}

// 行业分类弹窗样式
.industry-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .industry-dialog-content {
    display: flex;
    height: 400px;
  }

  .industry-tree-container {
    flex: 1;
    padding: 15px;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
  }

  .industry-selected-container {
    width: 200px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }

  .industry-selected-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
}

// 发送预警弹窗样式
.send-alert-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .send-alert-content {
    padding: 20px;
  }

  .receiver-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .receiver-item {
    display: flex;
    align-items: center;
  }

  .receiver-type {
    width: 80px;
    margin-right: 10px;
  }

  .receiver-select {
    width: 100%;
  }
}

// 正面情感弹窗样式
.positive-sentiment-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .el-dialog__body {
    padding: 20px;
  }
}
</style>

el-dialog
title="信息图谱"
:visible.sync="infoGraphDialogVisible"
width="50%"
append-to-body
custom-class="info-graph-dialog"
>
<div class="info-graph-content">
<!-- 根据提供的图示调整内容布局 -->
<div class="graph-container">
<div class="graph-node">东木头人</div>
<div class="graph-node">永兴队</div>
<!-- 添加更多节点 -->
</div>
</div>
<div slot="footer" class="dialog-footer">
<el-button @click="infoGraphDialogVisible = false">关闭</el-button>
</div>
</el-dialog>
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 操作按钮区域样式
.action-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.info-summary-container {
  display: flex;
  height: 100%;
  padding-top: 60px; // 为新建方案按钮留出空间
}

// 左侧导航栏样式
.left-sidebar {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-menu {
  flex: 1;
}

.el-menu-vertical {
  border-right: none;
}

// 右侧内容区域样式
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.entity-name {
  margin-right: 5px;
}

// 标签页样式
.tabs-container {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-tabs {
  display: flex;
  align-items: center;
}

// 筛选区域样式
.filter-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.filter-row {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #606266;
  line-height: 28px;
}

.filter-actions {
  margin-top: 10px;
  padding-left: 80px;
}

// 操作栏样式
.action-bar {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

// 信息列表样式
.info-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.info-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.item-checkbox {
  margin-right: 10px;
  margin-top: 3px;
}

.info-content {
  flex: 1;
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-summary {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-footer {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.info-source, .info-time, .info-sentiment, .info-views, .info-comments {
  margin-right: 15px;
}

.info-index {
  margin-left: auto;
}

.sentiment-positive {
  color: #67c23a;
}

.sentiment-neutral {
  color: #909399;
}

.sentiment-negative {
  color: #f56c6c;
}

.info-images {
  display: flex;
  margin-top: 10px;
}

.info-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 4px;
}

// 高亮样式
:deep(.highlight) {
  color: #409eff;
  font-weight: bold;
}

// 新建方案对话框样式
.create-plan-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }
}

.monitor-object-select {
  display: flex;
  align-items: center;
  position: relative;

  .el-dropdown {
    position: absolute;
    right: 10px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.location-select {
  display: flex;
  align-items: center;
}

.location-btn {
  margin-left: 10px;
}

.industry-select-btn {
  width: 100%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }
}

.theme-row, .industry-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.theme-input, .industry-input {
  width: 100px;
  margin-left: 10px;
  vertical-align: bottom;
}

.theme-button, .industry-button {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.channels-row {
  display: flex;
  margin-bottom: 10px;

  .el-checkbox {
    margin-right: 20px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px;
  text-align: right;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
}

// 行业分类弹窗样式
.industry-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .industry-dialog-content {
    display: flex;
    height: 400px;
  }

  .industry-tree-container {
    flex: 1;
    padding: 15px;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
  }

  .industry-selected-container {
    width: 200px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }

  .industry-selected-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
}

// 发送预警弹窗样式
.send-alert-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .send-alert-content {
    padding: 20px;
  }

  .receiver-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .receiver-item {
    display: flex;
    align-items: center;
  }

  .receiver-type {
    width: 80px;
    margin-right: 10px;
  }

  .receiver-select {
    width: 100%;
  }
}

// 正面情感弹窗样式
.positive-sentiment-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .el-dialog__body {
    padding: 20px;
  }
}
</style>

<el-button type="success" size="mini" @click="handlePositiveClick">正面</el-button>,
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 操作按钮区域样式
.action-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.info-summary-container {
  display: flex;
  height: 100%;
  padding-top: 60px; // 为新建方案按钮留出空间
}

// 左侧导航栏样式
.left-sidebar {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-menu {
  flex: 1;
}

.el-menu-vertical {
  border-right: none;
}

// 右侧内容区域样式
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.entity-name {
  margin-right: 5px;
}

// 标签页样式
.tabs-container {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-tabs {
  display: flex;
  align-items: center;
}

// 筛选区域样式
.filter-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.filter-row {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #606266;
  line-height: 28px;
}

.filter-actions {
  margin-top: 10px;
  padding-left: 80px;
}

// 操作栏样式
.action-bar {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

// 信息列表样式
.info-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.info-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.item-checkbox {
  margin-right: 10px;
  margin-top: 3px;
}

.info-content {
  flex: 1;
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-summary {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-footer {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.info-source, .info-time, .info-sentiment, .info-views, .info-comments {
  margin-right: 15px;
}

.info-index {
  margin-left: auto;
}

.sentiment-positive {
  color: #67c23a;
}

.sentiment-neutral {
  color: #909399;
}

.sentiment-negative {
  color: #f56c6c;
}

.info-images {
  display: flex;
  margin-top: 10px;
}

.info-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 4px;
}

// 高亮样式
:deep(.highlight) {
  color: #409eff;
  font-weight: bold;
}

// 新建方案对话框样式
.create-plan-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
