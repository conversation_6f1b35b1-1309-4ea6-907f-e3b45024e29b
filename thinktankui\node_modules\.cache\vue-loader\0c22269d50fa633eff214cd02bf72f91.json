{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\monitor\\cache\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-monitor\"></i> 基本信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Redis版本</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行模式</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">端口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">客户端数</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时间(天)</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用CPU</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">内存配置</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">AOF是否开启</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">RDB是否成功</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Key数量</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">网络入口/出口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-pie-chart\"></i> 命令统计</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"commandstats\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-odometer\"></i> 内存信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getCache } from \"@/api/monitor/cache\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: \"Cache\",\n  data() {\n    return {\n      // 统计命令信息\n      commandstats: null,\n      // 使用内存\n      usedmemory: null,\n      // cache信息\n      cache: []\n    }\n  },\n  created() {\n    this.getList();\n    this.openLoading();\n  },\n  methods: {\n    /** 查缓存询信息 */\n    getList() {\n      getCache().then((response) => {\n        this.cache = response.data;\n        this.$modal.closeLoading();\n\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\n        this.commandstats.setOption({\n          tooltip: {\n            trigger: \"item\",\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          },\n          series: [\n            {\n              name: \"命令\",\n              type: \"pie\",\n              roseType: \"radius\",\n              radius: [15, 95],\n              center: [\"50%\", \"38%\"],\n              data: response.data.commandStats,\n              animationEasing: \"cubicInOut\",\n              animationDuration: 1000,\n            }\n          ]\n        });\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\n        this.usedmemory.setOption({\n          tooltip: {\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\n          },\n          series: [\n            {\n              name: \"峰值\",\n              type: \"gauge\",\n              min: 0,\n              max: 1000,\n              detail: {\n                formatter: this.cache.info.used_memory_human,\n              },\n              data: [\n                {\n                  value: parseFloat(this.cache.info.used_memory_human),\n                  name: \"内存消耗\",\n                }\n              ]\n            }\n          ]\n        });\n        window.addEventListener(\"resize\", () => {\n          this.commandstats.resize();\n          this.usedmemory.resize();\n        });\n      });\n    },\n    // 打开加载层\n    openLoading() {\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\");\n    }\n  }\n};\n</script>\n"]}]}