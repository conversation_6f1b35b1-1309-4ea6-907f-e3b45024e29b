{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\IconSelect\\index.vue?vue&type=style&index=0&id=6504d548&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\IconSelect\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmljb24tYm9keSB7CiAgd2lkdGg6IDEwMCU7CiAgcGFkZGluZzogMTBweDsKICAuaWNvbi1zZWFyY2ggewogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgbWFyZ2luLWJvdHRvbTogNXB4OwogIH0KICAuaWNvbi1saXN0IHsKICAgIGhlaWdodDogMjAwcHg7CiAgICBvdmVyZmxvdzogYXV0bzsKICAgIC5saXN0LWNvbnRhaW5lciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtd3JhcDogd3JhcDsKICAgICAgLmljb24taXRlbS13cmFwcGVyIHsKICAgICAgICB3aWR0aDogY2FsYygxMDAlIC8gMyk7CiAgICAgICAgaGVpZ2h0OiAyNXB4OwogICAgICAgIGxpbmUtaGVpZ2h0OiAyNXB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIC5pY29uLWl0ZW0gewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIG1heC13aWR0aDogMTAwJTsKICAgICAgICAgIGhlaWdodDogMTAwJTsKICAgICAgICAgIHBhZGRpbmc6IDAgNXB4OwogICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlY2VjZWM7CiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsKICAgICAgICAgIH0KICAgICAgICAgIC5pY29uIHsKICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7CiAgICAgICAgICB9CiAgICAgICAgICBzcGFuIHsKICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgICAgICB2ZXJ0aWNhbC1hbGlnbjogLTAuMTVlbTsKICAgICAgICAgICAgZmlsbDogY3VycmVudENvbG9yOwogICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDJweDsKICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIC5pY29uLWl0ZW0uYWN0aXZlIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNlY2VjZWM7CiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div class=\"list-container\">\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\n            <span>{{ item }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  props: {\n    activeIcon: {\n      type: String\n    }\n  },\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-search {\n      position: relative;\n      margin-bottom: 5px;\n    }\n    .icon-list {\n      height: 200px;\n      overflow: auto;\n      .list-container {\n        display: flex;\n        flex-wrap: wrap;\n        .icon-item-wrapper {\n          width: calc(100% / 3);\n          height: 25px;\n          line-height: 25px;\n          cursor: pointer;\n          display: flex;\n          .icon-item {\n            display: flex;\n            max-width: 100%;\n            height: 100%;\n            padding: 0 5px;\n            &:hover {\n              background: #ececec;\n              border-radius: 5px;\n            }\n            .icon {\n              flex-shrink: 0;\n            }\n            span {\n              display: inline-block;\n              vertical-align: -0.15em;\n              fill: currentColor;\n              padding-left: 2px;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n          }\n          .icon-item.active {\n            background: #ececec;\n            border-radius: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n"]}]}