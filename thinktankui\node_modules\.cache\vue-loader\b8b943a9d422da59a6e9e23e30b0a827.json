{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\DraggableItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\DraggableItem.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DraggableItem.vue"], "names": [], "mappings": ";AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DraggableItem.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<script>\nimport draggable from 'vuedraggable'\nimport render from '@/utils/generator/render'\n\nconst components = {\n  itemBtns(h, element, index, parent) {\n    const { copyItem, deleteItem } = this.$listeners\n    return [\n      <span class=\"drawing-item-copy\" title=\"复制\" onClick={event => {\n        copyItem(element, parent); event.stopPropagation()\n      }}>\n        <i class=\"el-icon-copy-document\" />\n      </span>,\n      <span class=\"drawing-item-delete\" title=\"删除\" onClick={event => {\n        deleteItem(index, parent); event.stopPropagation()\n      }}>\n        <i class=\"el-icon-delete\" />\n      </span>\n    ]\n  }\n}\nconst layouts = {\n  colFormItem(h, element, index, parent) {\n    const { activeItem } = this.$listeners\n    let className = this.activeId === element.formId ? 'drawing-item active-from-item' : 'drawing-item'\n    if (this.formConf.unFocusedComponentBorder) className += ' unfocus-bordered'\n    return (\n      <el-col span={element.span} class={className}\n        nativeOnClick={event => { activeItem(element); event.stopPropagation() }}>\n        <el-form-item label-width={element.labelWidth ? `${element.labelWidth}px` : null}\n          label={element.label} required={element.required}>\n          <render key={element.renderKey} conf={element} onInput={ event => {\n            this.$set(element, 'defaultValue', event)\n          }} />\n        </el-form-item>\n        {components.itemBtns.apply(this, arguments)}\n      </el-col>\n    )\n  },\n  rowFormItem(h, element, index, parent) {\n    const { activeItem } = this.$listeners\n    const className = this.activeId === element.formId ? 'drawing-row-item active-from-item' : 'drawing-row-item'\n    let child = renderChildren.apply(this, arguments)\n    if (element.type === 'flex') {\n      child = <el-row type={element.type} justify={element.justify} align={element.align}>\n              {child}\n            </el-row>\n    }\n    return (\n      <el-col span={element.span}>\n        <el-row gutter={element.gutter} class={className}\n          nativeOnClick={event => { activeItem(element); event.stopPropagation() }}>\n          <span class=\"component-name\">{element.componentName}</span>\n          <draggable list={element.children} animation={340} group=\"componentsGroup\" class=\"drag-wrapper\">\n            {child}\n          </draggable>\n          {components.itemBtns.apply(this, arguments)}\n        </el-row>\n      </el-col>\n    )\n  }\n}\n\nfunction renderChildren(h, element, index, parent) {\n  if (!Array.isArray(element.children)) return null\n  return element.children.map((el, i) => {\n    const layout = layouts[el.layout]\n    if (layout) {\n      return layout.call(this, h, el, i, element.children)\n    }\n    return layoutIsNotFound()\n  })\n}\n\nfunction layoutIsNotFound() {\n  throw new Error(`没有与${this.element.layout}匹配的layout`)\n}\n\nexport default {\n  components: {\n    render,\n    draggable\n  },\n  props: [\n    'element',\n    'index',\n    'drawingList',\n    'activeId',\n    'formConf'\n  ],\n  render(h) {\n    const layout = layouts[this.element.layout]\n\n    if (layout) {\n      return layout.call(this, h, this.element, this.index, this.drawingList)\n    }\n    return layoutIsNotFound()\n  }\n}\n</script>\n"]}]}