{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\CodeTypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\CodeTypeDialog.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["inheritAttrs", "props", "data", "formData", "fileName", "undefined", "type", "rules", "required", "message", "trigger", "typeOptions", "label", "value", "computed", "watch", "mounted", "methods", "onOpen", "showFileName", "concat", "Date", "onClose", "close", "e", "$emit", "handleConfirm", "_this", "$refs", "elForm", "validate", "valid", "_objectSpread2", "default"], "sources": ["src/views/tool/build/CodeTypeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"15\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"medium\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item label=\"生成类型\" prop=\"type\">\n              <el-radio-group v-model=\"formData.type\">\n                <el-radio-button\n                  v-for=\"(item, index) in typeOptions\"\n                  :key=\"index\"\n                  :label=\"item.value\"\n                  :disabled=\"item.disabled\"\n                >\n                  {{ item.label }}\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\n              <el-input v-model=\"formData.fileName\" placeholder=\"请输入文件名\" clearable />\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n\n      <div slot=\"footer\">\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  inheritAttrs: false,\n  props: ['showFileName'],\n  data() {\n    return {\n      formData: {\n        fileName: undefined,\n        type: 'file'\n      },\n      rules: {\n        fileName: [{\n          required: true,\n          message: '请输入文件名',\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '生成类型不能为空',\n          trigger: 'change'\n        }]\n      },\n      typeOptions: [{\n        label: '页面',\n        value: 'file'\n      }, {\n        label: '弹窗',\n        value: 'dialog'\n      }]\n    }\n  },\n  computed: {\n  },\n  watch: {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      if (this.showFileName) {\n        this.formData.fileName = `${+new Date()}.vue`\n      }\n    },\n    onClose() {\n    },\n    close(e) {\n      this.$emit('update:visible', false)\n    },\n    handleConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        this.$emit('confirm', { ...this.formData })\n        this.close()\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAmDA;EACAA,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAH,QAAA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QACAJ,IAAA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA,GACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,SAAAC,YAAA;QACA,KAAAhB,QAAA,CAAAC,QAAA,MAAAgB,MAAA,MAAAC,IAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAAC,CAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAJ,KAAA,CAAAF,KAAA,gBAAAO,cAAA,CAAAC,OAAA,MAAAN,KAAA,CAAAxB,QAAA;QACAwB,KAAA,CAAAJ,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}