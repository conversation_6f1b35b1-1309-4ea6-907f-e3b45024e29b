{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\TopNav\\index.vue?vue&type=style&index=0&id=35f3a2c1&lang=scss", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\TopNav\\index.vue", "mtime": 1747908152292}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWwgPiAuZWwtbWVudS1pdGVtIHsKICBmbG9hdDogbGVmdDsKICBoZWlnaHQ6IDUwcHggIWltcG9ydGFudDsKICBsaW5lLWhlaWdodDogNTBweCAhaW1wb3J0YW50OwogIGNvbG9yOiAjOTk5MDkzICFpbXBvcnRhbnQ7CiAgcGFkZGluZzogMCA1cHggIWltcG9ydGFudDsKICBtYXJnaW46IDAgMTBweCAhaW1wb3J0YW50Owp9CgoudG9wbWVudS1jb250YWluZXIuZWwtbWVudS0taG9yaXpvbnRhbCA+IC5lbC1tZW51LWl0ZW0uaXMtYWN0aXZlLCAuZWwtbWVudS0taG9yaXpvbnRhbCA+IC5lbC1zdWJtZW51LmlzLWFjdGl2ZSAuZWwtc3VibWVudV9fdGl0bGUgewogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjeyd2YXIoLS10aGVtZSknfSAhaW1wb3J0YW50OwogIGNvbG9yOiAjMzAzMTMzOwp9CgovKiBzdWJtZW51IGl0ZW0gKi8KLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWwgPiAuZWwtc3VibWVudSAuZWwtc3VibWVudV9fdGl0bGUgewogIGZsb2F0OiBsZWZ0OwogIGhlaWdodDogNTBweCAhaW1wb3J0YW50OwogIGxpbmUtaGVpZ2h0OiA1MHB4ICFpbXBvcnRhbnQ7CiAgY29sb3I6ICM5OTkwOTMgIWltcG9ydGFudDsKICBwYWRkaW5nOiAwIDVweCAhaW1wb3J0YW50OwogIG1hcmdpbjogMCAxMHB4ICFpbXBvcnRhbnQ7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\n  <el-menu\n    :default-active=\"activeMenu\"\n    mode=\"horizontal\"\n    @select=\"handleSelect\"\n  >\n    <template v-for=\"(item, index) in topMenus\">\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\">\n        <svg-icon\n        v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\n        :icon-class=\"item.meta.icon\"/>\n        {{ item.meta.title }}\n      </el-menu-item>\n    </template>\n\n    <!-- 顶部菜单超出数量折叠 -->\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" :key=\"visibleNumber\" v-if=\"topMenus.length > visibleNumber\">\n      <template slot=\"title\">更多菜单</template>\n      <template v-for=\"(item, index) in topMenus\">\n        <el-menu-item\n          :index=\"item.path\"\n          :key=\"index\"\n          v-if=\"index >= visibleNumber\">\n          <svg-icon\n            v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\n            :icon-class=\"item.meta.icon\"/>\n          {{ item.meta.title }}\n        </el-menu-item>\n      </template>\n    </el-submenu>\n  </el-menu>\n</template>\n\n<script>\nimport { constantRoutes } from \"@/router\";\nimport { isHttp } from \"@/utils/validate\";\n\n// 隐藏侧边栏路由\nconst hideList = ['/index', '/user/profile'];\n\nexport default {\n  data() {\n    return {\n      // 顶部栏初始数\n      visibleNumber: 5,\n      // 当前激活菜单的 index\n      currentIndex: undefined\n    };\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme;\n    },\n    // 顶部显示菜单\n    topMenus() {\n      let topMenus = [];\n      this.routers.map((menu) => {\n        if (menu.hidden !== true) {\n          // 兼容顶部栏一级菜单内部跳转\n          if (menu.path === \"/\") {\n            topMenus.push(menu.children[0]);\n          } else {\n            topMenus.push(menu);\n          }\n        }\n      });\n      return topMenus;\n    },\n    // 所有的路由信息\n    routers() {\n      return this.$store.state.permission.topbarRouters;\n    },\n    // 设置子路由\n    childrenMenus() {\n      var childrenMenus = [];\n      this.routers.map((router) => {\n        for (var item in router.children) {\n          if (router.children[item].parentPath === undefined) {\n            if(router.path === \"/\") {\n              router.children[item].path = \"/\" + router.children[item].path;\n            } else {\n              if(!isHttp(router.children[item].path)) {\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\n              }\n            }\n            router.children[item].parentPath = router.path;\n          }\n          childrenMenus.push(router.children[item]);\n        }\n      });\n      return constantRoutes.concat(childrenMenus);\n    },\n    // 默认激活的菜单\n    activeMenu() {\n      const path = this.$route.path;\n      let activePath = path;\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\n        const tmpPath = path.substring(1, path.length);\n        if (!this.$route.meta.link) {\n          activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\n        }\n      } else if(!this.$route.children) {\n        activePath = path;\n        this.$store.dispatch('app/toggleSideBarHide', true);\n      }\n      this.activeRoutes(activePath);\n      return activePath;\n    },\n  },\n  beforeMount() {\n    window.addEventListener('resize', this.setVisibleNumber)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.setVisibleNumber)\n  },\n  mounted() {\n    this.setVisibleNumber();\n  },\n  methods: {\n    // 根据宽度计算设置显示栏数\n    setVisibleNumber() {\n      const width = document.body.getBoundingClientRect().width / 3;\n      this.visibleNumber = parseInt(width / 85);\n    },\n    // 菜单选择事件\n    handleSelect(key, keyPath) {\n      this.currentIndex = key;\n      const route = this.routers.find(item => item.path === key);\n      if (isHttp(key)) {\n        // http(s):// 路径新窗口打开\n        window.open(key, \"_blank\");\n      } else if (!route || !route.children) {\n        // 没有子路由路径内部打开\n        const routeMenu = this.childrenMenus.find(item => item.path === key);\n        if (routeMenu && routeMenu.query) {\n          let query = JSON.parse(routeMenu.query);\n          this.$router.push({ path: key, query: query });\n        } else {\n          this.$router.push({ path: key });\n        }\n        this.$store.dispatch('app/toggleSideBarHide', true);\n      } else {\n        // 显示左侧联动菜单\n        this.activeRoutes(key);\n        this.$store.dispatch('app/toggleSideBarHide', false);\n      }\n    },\n    // 当前激活的路由\n    activeRoutes(key) {\n      var routes = [];\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\n        this.childrenMenus.map((item) => {\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\n            routes.push(item);\n          }\n        });\n      }\n      if(routes.length > 0) {\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\n      } else {\n        this.$store.dispatch('app/toggleSideBarHide', true);\n      }\n    }\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.topmenu-container.el-menu--horizontal > .el-menu-item {\n  float: left;\n  height: 50px !important;\n  line-height: 50px !important;\n  color: #999093 !important;\n  padding: 0 5px !important;\n  margin: 0 10px !important;\n}\n\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\n  border-bottom: 2px solid #{'var(--theme)'} !important;\n  color: #303133;\n}\n\n/* submenu item */\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\n  float: left;\n  height: 50px !important;\n  line-height: 50px !important;\n  color: #999093 !important;\n  padding: 0 5px !important;\n  margin: 0 10px !important;\n}\n</style>\n"]}]}