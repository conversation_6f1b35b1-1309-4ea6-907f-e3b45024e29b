{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\TreeNodeDialog.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzTnVtYmVyU3RyIH0gZnJvbSAnQC91dGlscy9pbmRleCcKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7fSwKICBpbmhlcml0QXR0cnM6IGZhbHNlLAogIHByb3BzOiBbXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaWQ6IDEwMCwKICAgICAgZm9ybURhdGE6IHsKICAgICAgICBsYWJlbDogdW5kZWZpbmVkLAogICAgICAgIHZhbHVlOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICBsYWJlbDogWwogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpemAiemhueWQjScsCiAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgdmFsdWU6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXpgInpobnlgLwnLAogICAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIGRhdGFUeXBlOiAnc3RyaW5nJywKICAgICAgZGF0YVR5cGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflrZfnrKbkuLInLAogICAgICAgICAgdmFsdWU6ICdzdHJpbmcnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+aVsOWtlycsCiAgICAgICAgICB2YWx1ZTogJ251bWJlcicKICAgICAgICB9CiAgICAgIF0KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7fSwKICB3YXRjaDogewogICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGZ1bmMtbmFtZXMKICAgICdmb3JtRGF0YS52YWx1ZSc6IGZ1bmN0aW9uICh2YWwpIHsKICAgICAgdGhpcy5kYXRhVHlwZSA9IGlzTnVtYmVyU3RyKHZhbCkgPyAnbnVtYmVyJyA6ICdzdHJpbmcnCiAgICB9CiAgfSwKICBjcmVhdGVkKCkge30sCiAgbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIG9uT3BlbigpIHsKICAgICAgdGhpcy5mb3JtRGF0YSA9IHsKICAgICAgICBsYWJlbDogdW5kZWZpbmVkLAogICAgICAgIHZhbHVlOiB1bmRlZmluZWQKICAgICAgfQogICAgfSwKICAgIG9uQ2xvc2UoKSB7fSwKICAgIGNsb3NlKCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIGZhbHNlKQogICAgfSwKICAgIGhhbmRsZUNvbmZpcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMuZWxGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4KICAgICAgICBpZiAodGhpcy5kYXRhVHlwZSA9PT0gJ251bWJlcicpIHsKICAgICAgICAgIHRoaXMuZm9ybURhdGEudmFsdWUgPSBwYXJzZUZsb2F0KHRoaXMuZm9ybURhdGEudmFsdWUpCiAgICAgICAgfQogICAgICAgIHRoaXMuZm9ybURhdGEuaWQgPSB0aGlzLmlkKysKICAgICAgICB0aGlzLiRlbWl0KCdjb21taXQnLCB0aGlzLmZvcm1EYXRhKQogICAgICAgIHRoaXMuY2xvc2UoKQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["TreeNodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TreeNodeDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"0\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"small\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项名\"\n              prop=\"label\"\n            >\n              <el-input\n                v-model=\"formData.label\"\n                placeholder=\"请输入选项名\"\n                clearable\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项值\"\n              prop=\"value\"\n            >\n              <el-input\n                v-model=\"formData.value\"\n                placeholder=\"请输入选项值\"\n                clearable\n              >\n                <el-select\n                  slot=\"append\"\n                  v-model=\"dataType\"\n                  :style=\"{width: '100px'}\"\n                >\n                  <el-option\n                    v-for=\"(item, index) in dataTypeOptions\"\n                    :key=\"index\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    :disabled=\"item.disabled\"\n                  />\n                </el-select>\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"handleConfirm\"\n        >\n          确定\n        </el-button>\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { isNumberStr } from '@/utils/index'\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: [],\n  data() {\n    return {\n      id: 100,\n      formData: {\n        label: undefined,\n        value: undefined\n      },\n      rules: {\n        label: [\n          {\n            required: true,\n            message: '请输入选项名',\n            trigger: 'blur'\n          }\n        ],\n        value: [\n          {\n            required: true,\n            message: '请输入选项值',\n            trigger: 'blur'\n          }\n        ]\n      },\n      dataType: 'string',\n      dataTypeOptions: [\n        {\n          label: '字符串',\n          value: 'string'\n        },\n        {\n          label: '数字',\n          value: 'number'\n        }\n      ]\n    }\n  },\n  computed: {},\n  watch: {\n    // eslint-disable-next-line func-names\n    'formData.value': function (val) {\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.formData = {\n        label: undefined,\n        value: undefined\n      }\n    },\n    onClose() {},\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handleConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        if (this.dataType === 'number') {\n          this.formData.value = parseFloat(this.formData.value)\n        }\n        this.formData.id = this.id++\n        this.$emit('commit', this.formData)\n        this.close()\n      })\n    }\n  }\n}\n</script>\n"]}]}