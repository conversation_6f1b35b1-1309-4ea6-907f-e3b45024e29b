{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Navbar.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" v-if=\"!topNav\"/>\n    <top-nav id=\"topmenu-container\" class=\"topmenu-container\" v-if=\"topNav\"/>\n\n    <div class=\"right-menu\">\n      <template v-if=\"device!=='mobile'\">\n        <search id=\"header-search\" class=\"right-menu-item\" />\n\n        <el-tooltip content=\"源码地址\" effect=\"dark\" placement=\"bottom\">\n          <ruo-yi-git id=\"ruoyi-git\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n        <el-tooltip content=\"文档地址\" effect=\"dark\" placement=\"bottom\">\n          <ruo-yi-doc id=\"ruoyi-doc\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\n\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n      </template>\n\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar\" class=\"user-avatar\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <router-link to=\"/user/profile\">\n            <el-dropdown-item>个人中心</el-dropdown-item>\n          </router-link>\n          <el-dropdown-item @click.native=\"setting = true\">\n            <span>布局设置</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span>退出登录</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport TopNav from '@/components/TopNav'\nimport Hamburger from '@/components/Hamburger'\nimport Screenfull from '@/components/Screenfull'\nimport SizeSelect from '@/components/SizeSelect'\nimport Search from '@/components/HeaderSearch'\nimport RuoYiGit from '@/components/RuoYi/Git'\nimport RuoYiDoc from '@/components/RuoYi/Doc'\n\nexport default {\n  components: {\n    Breadcrumb,\n    TopNav,\n    Hamburger,\n    Screenfull,\n    SizeSelect,\n    Search,\n    RuoYiGit,\n    RuoYiDoc\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar',\n      'device'\n    ]),\n    setting: {\n      get() {\n        return this.$store.state.settings.showSettings\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'showSettings',\n          value: val\n        })\n      }\n    },\n    topNav: {\n      get() {\n        return this.$store.state.settings.topNav\n      }\n    }\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      this.$confirm('确定注销并退出系统吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/index';\n        })\n      }).catch(() => {});\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .topmenu-container {\n    position: absolute;\n    left: 50px;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 30px;\n\n      .avatar-wrapper {\n        // margin-top: 5px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 40px;\n          height: 40px;\n          border-radius: 10px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}