<template>
  <div>
    <div class="page-header-content">
      <div class="avatar">
        <a-avatar size="large" :src="currentUser.avatar" />
      </div>
      <div class="content">
        <div class="content-title">
          {{ currentUser.name }}<span class="welcome-text">，欢迎使用舆情监控系统</span>
        </div>
        <div>{{ currentUser.title }} | {{ currentUser.group }}</div>
      </div>
      <div class="extra-content">
        <div class="stat-item">
          <a-statistic title="今日热点" :value="hotTopicsCount" />
        </div>
        <div class="stat-item">
          <a-statistic title="负面信息" :value="negativeCount" />
        </div>
        <div class="stat-item">
          <a-statistic title="总监控量" :value="totalMonitorCount" />
        </div>
      </div>
    </div>

    <div>
      <a-row :gutter="24">
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card
            :loading="loading"
            style="margin-bottom: 24px"
            :bordered="false"
            title="24小时传播趋势"
          >
            <div style="height: 400px; position: relative;">
              <v-chart :forceFit="true" height="400" :data="trendData" :scale="trendScale">
                <v-tooltip crosshairs></v-tooltip>
                <v-axis dataKey="time"></v-axis>
                <v-axis dataKey="value"></v-axis>
                <v-legend></v-legend>
                <v-line position="time*value" color="type" :size="2"></v-line>
                <v-point position="time*value" color="type" :size="4" shape="circle"></v-point>
              </v-chart>
            </div>
          </a-card>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-card
                :loading="loading"
                style="margin-bottom: 24px"
                :bordered="false"
                title="平台来源占比"
              >
                <div style="height: 300px;">
                  <v-chart :forceFit="true" height="300" :data="platformData" :scale="platformScale">
                    <v-tooltip></v-tooltip>
                    <v-legend dataKey="type"></v-legend>
                    <v-coord type="theta" radius="0.75" innerRadius="0.6"></v-coord>
                    <v-pie position="percent" color="type" :label="labelConfig"></v-pie>
                  </v-chart>
                </div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card
                :loading="loading"
                style="margin-bottom: 24px"
                :bordered="false"
                title="情感分布占比"
              >
                <div style="height: 300px;">
                  <v-chart :forceFit="true" height="300" :data="sentimentData" :scale="sentimentScale">
                    <v-tooltip></v-tooltip>
                    <v-axis dataKey="time"></v-axis>
                    <v-axis dataKey="value"></v-axis>
                    <v-legend dataKey="type"></v-legend>
                    <v-area position="time*value" color="type" :opacity="0.6"></v-area>
                  </v-chart>
                </div>
              </a-card>
            </a-col>
          </a-row>

          <a-card :loading="loading" :bordered="false">
            <template slot="title">
              <i class="el-icon-hot" style="color: #F5222D; margin-right: 8px;"></i>
              热门文章
            </template>
            <a-list>
              <a-list-item :key="index" v-for="(item, index) in hotArticles">
                <a-list-item-meta>
                  <template slot="avatar">
                    <a-tag :color="item.tagColor">{{ item.tag }}</a-tag>
                  </template>
                  <div slot="title">
                    <a :href="item.link">{{ item.title }}</a>
                  </div>
                  <div slot="description">
                    <span>{{ item.source }}</span>
                    <span style="float: right;">{{ item.publishTime }}</span>
                  </div>
                </a-list-item-meta>
              </a-list-item>
            </a-list>
          </a-card>
        </a-col>
        <a-col
          style="padding: 0 12px"
          :xl="8"
          :lg="24"
          :md="24"
          :sm="24"
          :xs="24"
        >
          <a-card
            style="margin-bottom: 24px"
            :bordered="false"
          >
            <template slot="title">
              <i class="el-icon-warning-outline" style="color: #FA8C16; margin-right: 8px;"></i>
              预警方案设置
            </template>
            <div class="setting-buttons">
              <a-button type="primary" icon="user" style="margin-right: 8px;">接收人设置</a-button>
              <a-button type="primary" icon="setting" style="margin-right: 8px;">预警设置</a-button>
              <a-button type="primary" icon="bell" style="margin-right: 8px;">关键词设置</a-button>
              <a-button type="primary" icon="search" @click="goToSearch">搜索测试</a-button>
            </div>
            <a-divider />
            <div class="switch-buttons">
              <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
                <span>预警开关</span>
                <a-switch checked></a-switch>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <a-button>自动预警</a-button>
                <a-button type="primary">人工预警</a-button>
              </div>
            </div>
          </a-card>
          <a-card
            style="margin-bottom: 24px"
            :bordered="false"
          >
            <template slot="title">
              <i class="el-icon-document" style="color: #52C41A; margin-right: 8px;"></i>
              报告模板
            </template>
            <a-list>
              <a-list-item v-for="(item, index) in reportTemplates" :key="index">
                <a-list-item-meta>
                  <div slot="title">{{ item.title }}</div>
                  <div slot="description">{{ item.createTime }}</div>
                </a-list-item-meta>
                <div>
                  <a-button type="link" icon="edit"></a-button>
                  <a-button type="link" icon="copy"></a-button>
                  <a-button type="link" icon="delete"></a-button>
                </div>
              </a-list-item>
            </a-list>
          </a-card>
          <a-card :loading="loading" :bordered="false">
            <template slot="title">
              <i class="el-icon-warning" style="color: #FF4D4F; margin-right: 8px;"></i>
              最新负面
            </template>
            <a-list>
              <a-list-item v-for="(item, index) in negativeNews" :key="index">
                <a-list-item-meta>
                  <template slot="avatar">
                    <a-badge status="error" />
                  </template>
                  <div slot="title">
                    <a :href="item.link">{{ item.title }}</a>
                  </div>
                  <div slot="description">{{ item.publishTime }}</div>
                </a-list-item-meta>
              </a-list-item>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import Viser from 'viser-vue';
import {
  Avatar,
  Button,
  Card,
  Col,
  List,
  Row,
  Statistic,
  Tag,
  Divider,
  Switch,
  Badge,
} from "ant-design-vue";
import 'ant-design-vue/dist/antd.css';
import Vue from "vue";

Vue.use(Viser);
Vue.component(Avatar.name, Avatar);
Vue.component(Button.name, Button);
Vue.component(Card.name, Card);
Vue.component(Card.Grid.name, Card.Grid);
Vue.component(Card.Meta.name, Card.Meta);
Vue.component(Col.name, Col);
Vue.component(List.name, List);
Vue.component(List.Item.name, List.Item);
Vue.component(List.Item.Meta.name, List.Item.Meta);
Vue.component(Row.name, Row);
Vue.component(Statistic.name, Statistic);
Vue.component(Tag.name, Tag);
Vue.component(Divider.name, Divider);
Vue.component(Switch.name, Switch);
Vue.component(Badge.name, Badge);

export default {
  name: "DashBoard",
  methods: {
    goToSearch() {
      // 跳转到搜索结果页面，并传递测试搜索关键词
      this.$router.push({
        path: '/search-results',
        query: {
          q: '方太 厨电'
        }
      });
    }
  },
  data() {
    return {
      originalTopNav: undefined, // 存储原始的topNav状态
      currentUser: {
        avatar:
          "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png",
        name: "方太",
        userid: "00000001",
        email: "<EMAIL>",
        signature: "舆情监控，实时把控",
        title: "舆情分析师",
        group: "舆情监控中心",
      },
      // 统计数据
      hotTopicsCount: 24,
      negativeCount: 8,
      totalMonitorCount: 1446,

      // 24小时传播趋势数据
      trendData: [
        { time: "00:00", value: 300, type: "总量" },
        { time: "01:00", value: 250, type: "总量" },
        { time: "02:00", value: 200, type: "总量" },
        { time: "03:00", value: 180, type: "总量" },
        { time: "04:00", value: 150, type: "总量" },
        { time: "05:00", value: 170, type: "总量" },
        { time: "06:00", value: 220, type: "总量" },
        { time: "07:00", value: 350, type: "总量" },
        { time: "08:00", value: 500, type: "总量" },
        { time: "09:00", value: 620, type: "总量" },
        { time: "10:00", value: 550, type: "总量" },
        { time: "11:00", value: 480, type: "总量" },
        { time: "12:00", value: 400, type: "总量" },
        { time: "13:00", value: 450, type: "总量" },
        { time: "14:00", value: 500, type: "总量" },
        { time: "15:00", value: 470, type: "总量" },
        { time: "16:00", value: 460, type: "总量" },
        { time: "17:00", value: 520, type: "总量" },
        { time: "18:00", value: 580, type: "总量" },
        { time: "19:00", value: 550, type: "总量" },
        { time: "20:00", value: 500, type: "总量" },
        { time: "21:00", value: 450, type: "总量" },
        { time: "22:00", value: 400, type: "总量" },
        { time: "23:00", value: 350, type: "总量" },

        { time: "00:00", value: 100, type: "微博" },
        { time: "01:00", value: 80, type: "微博" },
        { time: "02:00", value: 60, type: "微博" },
        { time: "03:00", value: 50, type: "微博" },
        { time: "04:00", value: 40, type: "微博" },
        { time: "05:00", value: 50, type: "微博" },
        { time: "06:00", value: 70, type: "微博" },
        { time: "07:00", value: 100, type: "微博" },
        { time: "08:00", value: 150, type: "微博" },
        { time: "09:00", value: 180, type: "微博" },
        { time: "10:00", value: 160, type: "微博" },
        { time: "11:00", value: 140, type: "微博" },
        { time: "12:00", value: 120, type: "微博" },
        { time: "13:00", value: 130, type: "微博" },
        { time: "14:00", value: 150, type: "微博" },
        { time: "15:00", value: 140, type: "微博" },
        { time: "16:00", value: 130, type: "微博" },
        { time: "17:00", value: 150, type: "微博" },
        { time: "18:00", value: 170, type: "微博" },
        { time: "19:00", value: 160, type: "微博" },
        { time: "20:00", value: 150, type: "微博" },
        { time: "21:00", value: 130, type: "微博" },
        { time: "22:00", value: 120, type: "微博" },
        { time: "23:00", value: 100, type: "微博" },

        { time: "00:00", value: 80, type: "视频" },
        { time: "01:00", value: 70, type: "视频" },
        { time: "02:00", value: 60, type: "视频" },
        { time: "03:00", value: 50, type: "视频" },
        { time: "04:00", value: 40, type: "视频" },
        { time: "05:00", value: 50, type: "视频" },
        { time: "06:00", value: 60, type: "视频" },
        { time: "07:00", value: 80, type: "视频" },
        { time: "08:00", value: 100, type: "视频" },
        { time: "09:00", value: 120, type: "视频" },
        { time: "10:00", value: 110, type: "视频" },
        { time: "11:00", value: 100, type: "视频" },
        { time: "12:00", value: 90, type: "视频" },
        { time: "13:00", value: 100, type: "视频" },
        { time: "14:00", value: 110, type: "视频" },
        { time: "15:00", value: 100, type: "视频" },
        { time: "16:00", value: 90, type: "视频" },
        { time: "17:00", value: 100, type: "视频" },
        { time: "18:00", value: 110, type: "视频" },
        { time: "19:00", value: 100, type: "视频" },
        { time: "20:00", value: 90, type: "视频" },
        { time: "21:00", value: 80, type: "视频" },
        { time: "22:00", value: 70, type: "视频" },
        { time: "23:00", value: 60, type: "视频" },
      ],
      trendScale: [
        {
          dataKey: 'value',
          min: 0,
        },
      ],

      // 平台来源占比数据
      platformData: [
        { type: '微博', percent: 31.2 },
        { type: '视频', percent: 17.9 },
        { type: '头条号', percent: 15.3 },
        { type: 'APP', percent: 12.7 },
        { type: '微信', percent: 9.8 },
        { type: '其他', percent: 13.1 },
      ],
      platformScale: [
        {
          dataKey: 'percent',
          min: 0,
          formatter: '.0%',
        },
      ],
      labelConfig: {
        offset: -20,
        textStyle: {
          fill: '#000',
          fontSize: 12,
          fontWeight: 'bold',
        },
        formatter: (text, item) => {
          return `${item.point.type}: ${item.point.percent}%`;
        },
      },

      // 情感分布占比数据
      sentimentData: [
        { time: '00:00', value: 300, type: '中性' },
        { time: '02:00', value: 280, type: '中性' },
        { time: '04:00', value: 250, type: '中性' },
        { time: '06:00', value: 260, type: '中性' },
        { time: '08:00', value: 280, type: '中性' },
        { time: '10:00', value: 300, type: '中性' },
        { time: '12:00', value: 280, type: '中性' },
        { time: '14:00', value: 250, type: '中性' },
        { time: '16:00', value: 260, type: '中性' },
        { time: '18:00', value: 280, type: '中性' },
        { time: '20:00', value: 300, type: '中性' },
        { time: '22:00', value: 280, type: '中性' },

        { time: '00:00', value: 100, type: '正面' },
        { time: '02:00', value: 120, type: '正面' },
        { time: '04:00', value: 140, type: '正面' },
        { time: '06:00', value: 130, type: '正面' },
        { time: '08:00', value: 120, type: '正面' },
        { time: '10:00', value: 100, type: '正面' },
        { time: '12:00', value: 120, type: '正面' },
        { time: '14:00', value: 140, type: '正面' },
        { time: '16:00', value: 130, type: '正面' },
        { time: '18:00', value: 120, type: '正面' },
        { time: '20:00', value: 100, type: '正面' },
        { time: '22:00', value: 120, type: '正面' },

        { time: '00:00', value: 50, type: '负面' },
        { time: '02:00', value: 60, type: '负面' },
        { time: '04:00', value: 70, type: '负面' },
        { time: '06:00', value: 65, type: '负面' },
        { time: '08:00', value: 60, type: '负面' },
        { time: '10:00', value: 50, type: '负面' },
        { time: '12:00', value: 60, type: '负面' },
        { time: '14:00', value: 70, type: '负面' },
        { time: '16:00', value: 65, type: '负面' },
        { time: '18:00', value: 60, type: '负面' },
        { time: '20:00', value: 50, type: '负面' },
        { time: '22:00', value: 60, type: '负面' },
      ],
      sentimentScale: [
        {
          dataKey: 'value',
          min: 0,
        },
      ],

      // 热门文章列表
      hotArticles: [
        {
          id: 1,
          title: '方太热水器(Fotile)官方旗舰店 方太热水器(Fotile)-111 方太热水器(Fotile) 400.6808.655...',
          tag: '热门',
          tagColor: '#f50',
          source: '电商平台',
          publishTime: '2025-04-29 20:24:00',
          link: 'https://example.com/article/1',
        },
        {
          id: 2,
          title: '近地铺30年地暖口碑，靠老字二全新设计住人。#成都二手房 #地暖电源',
          tag: '热门',
          tagColor: '#f50',
          source: '视频平台',
          publishTime: '2025-04-29 15:15:16',
          link: 'https://example.com/article/2',
        },
        {
          id: 3,
          title: '现在这个社会要靠脑袋吃饭，可能是一辈子的事，开玩笑 我一个实体人 更应该有品质。',
          tag: '热门',
          tagColor: '#f50',
          source: '视频平台',
          publishTime: '2025-04-29 14:29:09',
          link: 'https://example.com/article/3',
        },
        {
          id: 4,
          title: '（无标题）S2 V b ## b ### 限定2人方太全球首创一代燃气灶全球首发 03 ALOQAV 0 13 图A-5986 59...',
          tag: '热门',
          tagColor: '#f50',
          source: '视频平台',
          publishTime: '2025-04-29 14:19:23',
          link: 'https://example.com/article/4',
        },
        {
          id: 5,
          title: '你于过这事吗? 来看合了#方太官网 #南粤 #新桥口大厨的字写得 #同城姐妹的朋友看过来 #电影',
          tag: '热门',
          tagColor: '#f50',
          source: '视频平台',
          publishTime: '2025-04-29 12:48:04',
          link: 'https://example.com/article/5',
        },
      ],

      // 报告模板列表
      reportTemplates: [
        {
          id: 1,
          title: '舆情-周报表',
          createTime: '2019-11-16 18:02:00',
        },
        {
          id: 2,
          title: '舆情-月报表',
          createTime: '2019-11-18 18:06:52',
        },
        {
          id: 3,
          title: '舆情-季度表',
          createTime: '2021-09-12 10:15:00',
        },
      ],

      // 最新负面信息
      negativeNews: [
        {
          id: 1,
          title: '方太热水器出现质量问题，多名用户投诉无人处理',
          publishTime: '2025-04-29 19:03:00',
          link: 'https://example.com/negative/1',
        },
        {
          id: 2,
          title: '方太厨电安装电话无人接听，客户投诉服务态度差',
          publishTime: '2025-04-29 18:22:43',
          link: 'https://example.com/negative/2',
        },
        {
          id: 3,
          title: '航空女友偷拍日记被礼，网友后，我在床上看到了电视一幕的她',
          publishTime: '2025-04-29 17:48:45',
          link: 'https://example.com/negative/3',
        },
        {
          id: 4,
          title: '某品牌大型抽油烟机噪音问题引发用户不满',
          publishTime: '2025-04-29 15:15:16',
          link: 'https://example.com/negative/4',
        },
        {
          id: 5,
          title: '家电售后服务调查：多品牌服务质量参差不齐',
          publishTime: '2025-04-29 12:26:04',
          link: 'https://example.com/negative/5',
        },
      ],

      loading: true
    };
  },

  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })

    setTimeout(() => {
      this.loading = false;
    }, 1000);
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
};
</script>

  <style lang="less" scoped>
@import "./Workplace.less";



.page-header-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.setting-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.switch-buttons {
  margin-top: 16px;
}

.ant-list-item {
  transition: all 0.3s;

  &:hover {
    background-color: rgba(24, 144, 255, 0.05);
  }
}

.ant-card {
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.ant-tag {
  margin-right: 0;
}

.ant-list-item-meta-title {
  margin-bottom: 4px;

  a {
    color: rgba(0, 0, 0, 0.85);

    &:hover {
      color: #1890ff;
    }
  }
}

.ant-list-item-meta-description {
  color: rgba(0, 0, 0, 0.45);
}

.mobile {
  .more-info {
    border: 0;
    padding-top: 16px;
    margin: 16px 0 16px;
  }

  .headerContent .title .welcome-text {
    display: none;
  }
}
</style>
