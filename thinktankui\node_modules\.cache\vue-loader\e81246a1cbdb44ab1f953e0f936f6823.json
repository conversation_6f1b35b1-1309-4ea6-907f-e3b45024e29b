{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMsIG1hcFN0YXRlIH0gZnJvbSAidnVleCI7CmltcG9ydCBMb2dvIGZyb20gIi4vTG9nbyI7CmltcG9ydCBTaWRlYmFySXRlbSBmcm9tICIuL1NpZGViYXJJdGVtIjsKaW1wb3J0IHZhcmlhYmxlcyBmcm9tICJAL2Fzc2V0cy9zdHlsZXMvdmFyaWFibGVzLnNjc3MiOwoKZXhwb3J0IGRlZmF1bHQgewogICAgY29tcG9uZW50czogeyBTaWRlYmFySXRlbSwgTG9nbyB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICAuLi5tYXBTdGF0ZShbInNldHRpbmdzIl0pLAogICAgICAgIC4uLm1hcEdldHRlcnMoWyJzaWRlYmFyUm91dGVycyIsICJzaWRlYmFyIl0pLAogICAgICAgIGFjdGl2ZU1lbnUoKSB7CiAgICAgICAgICAgIGNvbnN0IHJvdXRlID0gdGhpcy4kcm91dGU7CiAgICAgICAgICAgIGNvbnN0IHsgbWV0YSwgcGF0aCB9ID0gcm91dGU7CiAgICAgICAgICAgIC8vIGlmIHNldCBwYXRoLCB0aGUgc2lkZWJhciB3aWxsIGhpZ2hsaWdodCB0aGUgcGF0aCB5b3Ugc2V0CiAgICAgICAgICAgIGlmIChtZXRhLmFjdGl2ZU1lbnUpIHsKICAgICAgICAgICAgICAgIHJldHVybiBtZXRhLmFjdGl2ZU1lbnU7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIHBhdGg7CiAgICAgICAgfSwKICAgICAgICBzaG93TG9nbygpIHsKICAgICAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNpZGViYXJMb2dvOwogICAgICAgIH0sCiAgICAgICAgdmFyaWFibGVzKCkgewogICAgICAgICAgICByZXR1cm4gdmFyaWFibGVzOwogICAgICAgIH0sCiAgICAgICAgaXNDb2xsYXBzZSgpIHsKICAgICAgICAgICAgcmV0dXJuICF0aGlzLnNpZGViYXIub3BlbmVkOwogICAgICAgIH0KICAgIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n    <div :class=\"{'has-logo':showLogo}\" :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\n        <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n        <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\n            <el-menu\n                :default-active=\"activeMenu\"\n                :collapse=\"isCollapse\"\n                :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"\n                :text-color=\"settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor\"\n                :unique-opened=\"true\"\n                :active-text-color=\"settings.theme\"\n                :collapse-transition=\"false\"\n                mode=\"vertical\"\n            >\n                <sidebar-item\n                    v-for=\"(route, index) in sidebarRouters\"\n                    :key=\"route.path  + index\"\n                    :item=\"route\"\n                    :base-path=\"route.path\"\n                />\n            </el-menu>\n        </el-scrollbar>\n    </div>\n</template>\n\n<script>\nimport { mapGetters, mapState } from \"vuex\";\nimport Logo from \"./Logo\";\nimport SidebarItem from \"./SidebarItem\";\nimport variables from \"@/assets/styles/variables.scss\";\n\nexport default {\n    components: { SidebarItem, Logo },\n    computed: {\n        ...mapState([\"settings\"]),\n        ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\n        activeMenu() {\n            const route = this.$route;\n            const { meta, path } = route;\n            // if set path, the sidebar will highlight the path you set\n            if (meta.activeMenu) {\n                return meta.activeMenu;\n            }\n            return path;\n        },\n        showLogo() {\n            return this.$store.state.settings.sidebarLogo;\n        },\n        variables() {\n            return variables;\n        },\n        isCollapse() {\n            return !this.sidebar.opened;\n        }\n    }\n};\n</script>\n"]}]}