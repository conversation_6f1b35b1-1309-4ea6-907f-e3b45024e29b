{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\week.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\week.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["week.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "week.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n\t<el-form size='small'>\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\n\t\t\t\t周，允许的通配符[, - * ? / L #]\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\n\t\t\t\t不指定\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\n\t\t\t\t周期从星期\n\t\t\t\t<el-select clearable v-model=\"cycle01\">\n\t\t\t\t\t<el-option\n\t\t\t\t\t\tv-for=\"(item,index) of weekList\"\n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t:label=\"item.value\"\n\t\t\t\t\t\t:value=\"item.key\"\n\t\t\t\t\t\t:disabled=\"item.key === 1\"\n\t\t\t\t\t>{{item.value}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t\t-\n\t\t\t\t<el-select clearable v-model=\"cycle02\">\n\t\t\t\t\t<el-option\n\t\t\t\t\t\tv-for=\"(item,index) of weekList\"\n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t:label=\"item.value\"\n\t\t\t\t\t\t:value=\"item.key\"\n\t\t\t\t\t\t:disabled=\"item.key < cycle01 && item.key !== 1\"\n\t\t\t\t\t>{{item.value}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\n\t\t\t\t第\n\t\t\t\t<el-input-number v-model='average01' :min=\"1\" :max=\"4\" /> 周的星期\n\t\t\t\t<el-select clearable v-model=\"average02\">\n\t\t\t\t\t<el-option v-for=\"(item,index) of weekList\" :key=\"index\" :label=\"item.value\" :value=\"item.key\">{{item.value}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"5\">\n\t\t\t\t本月最后一个星期\n\t\t\t\t<el-select clearable v-model=\"weekday\">\n\t\t\t\t\t<el-option v-for=\"(item,index) of weekList\" :key=\"index\" :label=\"item.value\" :value=\"item.key\">{{item.value}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio v-model='radioValue' :label=\"6\">\n\t\t\t\t指定\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\n\t\t\t\t\t<el-option v-for=\"(item,index) of weekList\" :key=\"index\" :label=\"item.value\" :value=\"String(item.key)\">{{item.value}}</el-option>\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t</el-form>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tradioValue: 2,\n\t\t\tweekday: 2,\n\t\t\tcycle01: 2,\n\t\t\tcycle02: 3,\n\t\t\taverage01: 1,\n\t\t\taverage02: 2,\n\t\t\tcheckboxList: [],\n\t\t\tweekList: [\n\t\t\t\t{\n\t\t\t\t\tkey: 2,\n\t\t\t\t\tvalue: '星期一'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tkey: 3,\n\t\t\t\t\tvalue: '星期二'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tkey: 4,\n\t\t\t\t\tvalue: '星期三'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tkey: 5,\n\t\t\t\t\tvalue: '星期四'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tkey: 6,\n\t\t\t\t\tvalue: '星期五'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tkey: 7,\n\t\t\t\t\tvalue: '星期六'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tkey: 1,\n\t\t\t\t\tvalue: '星期日'\n\t\t\t\t}\n\t\t\t],\n\t\t\tcheckNum: this.$options.propsData.check\n\t\t}\n\t},\n\tname: 'crontab-week',\n\tprops: ['check', 'cron'],\n\tmethods: {\n\t\t// 单选按钮值变化时\n\t\tradioChange() {\n\t\t\tif (this.radioValue !== 2 && this.cron.day !== '?') {\n\t\t\t\tthis.$emit('update', 'day', '?', 'week');\n\t\t\t}\n\t\t\tswitch (this.radioValue) {\n\t\t\t\tcase 1:\n\t\t\t\t\tthis.$emit('update', 'week', '*');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tthis.$emit('update', 'week', '?');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tthis.$emit('update', 'week', this.cycleTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tthis.$emit('update', 'week', this.averageTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 5:\n\t\t\t\t\tthis.$emit('update', 'week', this.weekdayCheck + 'L');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 6:\n\t\t\t\t\tthis.$emit('update', 'week', this.checkboxString);\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\n\t\t// 周期两个值变化时\n\t\tcycleChange() {\n\t\t\tif (this.radioValue == '3') {\n\t\t\t\tthis.$emit('update', 'week', this.cycleTotal);\n\t\t\t}\n\t\t},\n\t\t// 平均两个值变化时\n\t\taverageChange() {\n\t\t\tif (this.radioValue == '4') {\n\t\t\t\tthis.$emit('update', 'week', this.averageTotal);\n\t\t\t}\n\t\t},\n\t\t// 最近工作日值变化时\n\t\tweekdayChange() {\n\t\t\tif (this.radioValue == '5') {\n\t\t\t\tthis.$emit('update', 'week', this.weekday + 'L');\n\t\t\t}\n\t\t},\n\t\t// checkbox值变化时\n\t\tcheckboxChange() {\n\t\t\tif (this.radioValue == '6') {\n\t\t\t\tthis.$emit('update', 'week', this.checkboxString);\n\t\t\t}\n\t\t},\n\t},\n\twatch: {\n\t\t'radioValue': 'radioChange',\n\t\t'cycleTotal': 'cycleChange',\n\t\t'averageTotal': 'averageChange',\n\t\t'weekdayCheck': 'weekdayChange',\n\t\t'checkboxString': 'checkboxChange',\n\t},\n\tcomputed: {\n\t\t// 计算两个周期值\n\t\tcycleTotal: function () {\n\t\t\tthis.cycle01 = this.checkNum(this.cycle01, 1, 7)\n\t\t\tthis.cycle02 = this.checkNum(this.cycle02, 1, 7)\n\t\t\treturn this.cycle01 + '-' + this.cycle02;\n\t\t},\n\t\t// 计算平均用到的值\n\t\taverageTotal: function () {\n\t\t\tthis.average01 = this.checkNum(this.average01, 1, 4)\n\t\t\tthis.average02 = this.checkNum(this.average02, 1, 7)\n\t\t\treturn this.average02 + '#' + this.average01;\n\t\t},\n\t\t// 最近的工作日（格式）\n\t\tweekdayCheck: function () {\n\t\t\tthis.weekday = this.checkNum(this.weekday, 1, 7)\n\t\t\treturn this.weekday;\n\t\t},\n\t\t// 计算勾选的checkbox值合集\n\t\tcheckboxString: function () {\n\t\t\tlet str = this.checkboxList.join();\n\t\t\treturn str == '' ? '*' : str;\n\t\t}\n\t}\n}\n</script>\n"]}]}