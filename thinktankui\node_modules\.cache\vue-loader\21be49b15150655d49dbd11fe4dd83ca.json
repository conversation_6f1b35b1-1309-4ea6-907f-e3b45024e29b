{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\tool\\build\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBkcmFnZ2FibGUgZnJvbSAndnVlZHJhZ2dhYmxlJwppbXBvcnQgYmVhdXRpZmllciBmcm9tICdqcy1iZWF1dGlmeScKaW1wb3J0IENsaXBib2FyZEpTIGZyb20gJ2NsaXBib2FyZCcKaW1wb3J0IHJlbmRlciBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9yZW5kZXInCmltcG9ydCBSaWdodFBhbmVsIGZyb20gJy4vUmlnaHRQYW5lbCcKaW1wb3J0IHsgaW5wdXRDb21wb25lbnRzLCBzZWxlY3RDb21wb25lbnRzLCBsYXlvdXRDb21wb25lbnRzLCBmb3JtQ29uZiB9IGZyb20gJ0AvdXRpbHMvZ2VuZXJhdG9yL2NvbmZpZycKaW1wb3J0IHsgYmVhdXRpZmllckNvbmYsIHRpdGxlQ2FzZSB9IGZyb20gJ0AvdXRpbHMvaW5kZXgnCmltcG9ydCB7IG1ha2VVcEh0bWwsIHZ1ZVRlbXBsYXRlLCB2dWVTY3JpcHQsIGNzc1N0eWxlIH0gZnJvbSAnQC91dGlscy9nZW5lcmF0b3IvaHRtbCcKaW1wb3J0IHsgbWFrZVVwSnMgfSBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9qcycKaW1wb3J0IHsgbWFrZVVwQ3NzIH0gZnJvbSAnQC91dGlscy9nZW5lcmF0b3IvY3NzJwppbXBvcnQgZHJhd2luZ0RlZmF1bHQgZnJvbSAnQC91dGlscy9nZW5lcmF0b3IvZHJhd2luZ0RlZmF1bHQnCmltcG9ydCBsb2dvIGZyb20gJ0AvYXNzZXRzL2xvZ28vbG9nby5wbmcnCmltcG9ydCBDb2RlVHlwZURpYWxvZyBmcm9tICcuL0NvZGVUeXBlRGlhbG9nJwppbXBvcnQgRHJhZ2dhYmxlSXRlbSBmcm9tICcuL0RyYWdnYWJsZUl0ZW0nCgpsZXQgb2xkQWN0aXZlSWQKbGV0IHRlbXBBY3RpdmVEYXRhCgpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgZHJhZ2dhYmxlLAogICAgcmVuZGVyLAogICAgUmlnaHRQYW5lbCwKICAgIENvZGVUeXBlRGlhbG9nLAogICAgRHJhZ2dhYmxlSXRlbQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvZ28sCiAgICAgIGlkR2xvYmFsOiAxMDAsCiAgICAgIGZvcm1Db25mLAogICAgICBpbnB1dENvbXBvbmVudHMsCiAgICAgIHNlbGVjdENvbXBvbmVudHMsCiAgICAgIGxheW91dENvbXBvbmVudHMsCiAgICAgIGxhYmVsV2lkdGg6IDEwMCwKICAgICAgZHJhd2luZ0xpc3Q6IGRyYXdpbmdEZWZhdWx0LAogICAgICBkcmF3aW5nRGF0YToge30sCiAgICAgIGFjdGl2ZUlkOiBkcmF3aW5nRGVmYXVsdFswXS5mb3JtSWQsCiAgICAgIGRyYXdlclZpc2libGU6IGZhbHNlLAogICAgICBmb3JtRGF0YToge30sCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBnZW5lcmF0ZUNvbmY6IG51bGwsCiAgICAgIHNob3dGaWxlTmFtZTogZmFsc2UsCiAgICAgIGFjdGl2ZURhdGE6IGRyYXdpbmdEZWZhdWx0WzBdCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgLy8g6Ziy5q2iIGZpcmVmb3gg5LiLIOaLluaLvSDkvJrmlrDmiZPljaHkuIDkuKrpgInpobnljaEKICAgIGRvY3VtZW50LmJvZHkub25kcm9wID0gZXZlbnQgPT4gewogICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpCiAgICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGZ1bmMtbmFtZXMKICAgICdhY3RpdmVEYXRhLmxhYmVsJzogZnVuY3Rpb24gKHZhbCwgb2xkVmFsKSB7CiAgICAgIGlmICgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEucGxhY2Vob2xkZXIgPT09IHVuZGVmaW5lZAogICAgICAgIHx8ICF0aGlzLmFjdGl2ZURhdGEudGFnCiAgICAgICAgfHwgb2xkQWN0aXZlSWQgIT09IHRoaXMuYWN0aXZlSWQKICAgICAgKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy5hY3RpdmVEYXRhLnBsYWNlaG9sZGVyID0gdGhpcy5hY3RpdmVEYXRhLnBsYWNlaG9sZGVyLnJlcGxhY2Uob2xkVmFsLCAnJykgKyB2YWwKICAgIH0sCiAgICBhY3RpdmVJZDogewogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIG9sZEFjdGl2ZUlkID0gdmFsCiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIGNvbnN0IGNsaXBib2FyZCA9IG5ldyBDbGlwYm9hcmRKUygnI2NvcHlOb2RlJywgewogICAgICB0ZXh0OiB0cmlnZ2VyID0+IHsKICAgICAgICBjb25zdCBjb2RlU3RyID0gdGhpcy5nZW5lcmF0ZUNvZGUoKQogICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICBtZXNzYWdlOiAn5Luj56CB5bey5aSN5Yi25Yiw5Ymq5YiH5p2/77yM5Y+v57KY6LS044CCJywKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuIGNvZGVTdHIKICAgICAgfQogICAgfSkKICAgIGNsaXBib2FyZC5vbignZXJyb3InLCBlID0+IHsKICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Luj56CB5aSN5Yi25aSx6LSlJykKICAgIH0pCiAgfSwKICBtZXRob2RzOiB7CiAgICBhY3RpdmVGb3JtSXRlbShlbGVtZW50KSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YSA9IGVsZW1lbnQKICAgICAgdGhpcy5hY3RpdmVJZCA9IGVsZW1lbnQuZm9ybUlkCiAgICB9LAogICAgb25FbmQob2JqLCBhKSB7CiAgICAgIGlmIChvYmouZnJvbSAhPT0gb2JqLnRvKSB7CiAgICAgICAgdGhpcy5hY3RpdmVEYXRhID0gdGVtcEFjdGl2ZURhdGEKICAgICAgICB0aGlzLmFjdGl2ZUlkID0gdGhpcy5pZEdsb2JhbAogICAgICB9CiAgICB9LAogICAgYWRkQ29tcG9uZW50KGl0ZW0pIHsKICAgICAgY29uc3QgY2xvbmUgPSB0aGlzLmNsb25lQ29tcG9uZW50KGl0ZW0pCiAgICAgIHRoaXMuZHJhd2luZ0xpc3QucHVzaChjbG9uZSkKICAgICAgdGhpcy5hY3RpdmVGb3JtSXRlbShjbG9uZSkKICAgIH0sCiAgICBjbG9uZUNvbXBvbmVudChvcmlnaW4pIHsKICAgICAgY29uc3QgY2xvbmUgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG9yaWdpbikpCiAgICAgIGNsb25lLmZvcm1JZCA9ICsrdGhpcy5pZEdsb2JhbAogICAgICBjbG9uZS5zcGFuID0gZm9ybUNvbmYuc3BhbgogICAgICBjbG9uZS5yZW5kZXJLZXkgPSArbmV3IERhdGUoKSAvLyDmlLnlj5hyZW5kZXJLZXnlkI7lj6/ku6Xlrp7njrDlvLrliLbmm7TmlrDnu4Tku7YKICAgICAgaWYgKCFjbG9uZS5sYXlvdXQpIGNsb25lLmxheW91dCA9ICdjb2xGb3JtSXRlbScKICAgICAgaWYgKGNsb25lLmxheW91dCA9PT0gJ2NvbEZvcm1JdGVtJykgewogICAgICAgIGNsb25lLnZNb2RlbCA9IGBmaWVsZCR7dGhpcy5pZEdsb2JhbH1gCiAgICAgICAgY2xvbmUucGxhY2Vob2xkZXIgIT09IHVuZGVmaW5lZCAmJiAoY2xvbmUucGxhY2Vob2xkZXIgKz0gY2xvbmUubGFiZWwpCiAgICAgICAgdGVtcEFjdGl2ZURhdGEgPSBjbG9uZQogICAgICB9IGVsc2UgaWYgKGNsb25lLmxheW91dCA9PT0gJ3Jvd0Zvcm1JdGVtJykgewogICAgICAgIGRlbGV0ZSBjbG9uZS5sYWJlbAogICAgICAgIGNsb25lLmNvbXBvbmVudE5hbWUgPSBgcm93JHt0aGlzLmlkR2xvYmFsfWAKICAgICAgICBjbG9uZS5ndXR0ZXIgPSB0aGlzLmZvcm1Db25mLmd1dHRlcgogICAgICAgIHRlbXBBY3RpdmVEYXRhID0gY2xvbmUKICAgICAgfQogICAgICByZXR1cm4gdGVtcEFjdGl2ZURhdGEKICAgIH0sCiAgICBBc3NlbWJsZUZvcm1EYXRhKCkgewogICAgICB0aGlzLmZvcm1EYXRhID0gewogICAgICAgIGZpZWxkczogSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmRyYXdpbmdMaXN0KSksCiAgICAgICAgLi4udGhpcy5mb3JtQ29uZgogICAgICB9CiAgICB9LAogICAgZ2VuZXJhdGUoZGF0YSkgewogICAgICBjb25zdCBmdW5jID0gdGhpc1tgZXhlYyR7dGl0bGVDYXNlKHRoaXMub3BlcmF0aW9uVHlwZSl9YF0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbmYgPSBkYXRhCiAgICAgIGZ1bmMgJiYgZnVuYyhkYXRhKQogICAgfSwKICAgIGV4ZWNSdW4oZGF0YSkgewogICAgICB0aGlzLkFzc2VtYmxlRm9ybURhdGEoKQogICAgICB0aGlzLmRyYXdlclZpc2libGUgPSB0cnVlCiAgICB9LAogICAgZXhlY0Rvd25sb2FkKGRhdGEpIHsKICAgICAgY29uc3QgY29kZVN0ciA9IHRoaXMuZ2VuZXJhdGVDb2RlKCkKICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtjb2RlU3RyXSwgeyB0eXBlOiAndGV4dC9wbGFpbjtjaGFyc2V0PXV0Zi04JyB9KQogICAgICB0aGlzLiRkb3dubG9hZC5zYXZlQXMoYmxvYiwgZGF0YS5maWxlTmFtZSkKICAgIH0sCiAgICBleGVjQ29weShkYXRhKSB7CiAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjb3B5Tm9kZScpLmNsaWNrKCkKICAgIH0sCiAgICBlbXB0eSgpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5riF56m65omA5pyJ57uE5Lu25ZCX77yfJywgJ+aPkOekuicsIHsgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oCiAgICAgICAgKCkgPT4gewogICAgICAgICAgdGhpcy5kcmF3aW5nTGlzdCA9IFtdCiAgICAgICAgfQogICAgICApCiAgICB9LAogICAgZHJhd2luZ0l0ZW1Db3B5KGl0ZW0sIHBhcmVudCkgewogICAgICBsZXQgY2xvbmUgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGl0ZW0pKQogICAgICBjbG9uZSA9IHRoaXMuY3JlYXRlSWRBbmRLZXkoY2xvbmUpCiAgICAgIHBhcmVudC5wdXNoKGNsb25lKQogICAgICB0aGlzLmFjdGl2ZUZvcm1JdGVtKGNsb25lKQogICAgfSwKICAgIGNyZWF0ZUlkQW5kS2V5KGl0ZW0pIHsKICAgICAgaXRlbS5mb3JtSWQgPSArK3RoaXMuaWRHbG9iYWwKICAgICAgaXRlbS5yZW5kZXJLZXkgPSArbmV3IERhdGUoKQogICAgICBpZiAoaXRlbS5sYXlvdXQgPT09ICdjb2xGb3JtSXRlbScpIHsKICAgICAgICBpdGVtLnZNb2RlbCA9IGBmaWVsZCR7dGhpcy5pZEdsb2JhbH1gCiAgICAgIH0gZWxzZSBpZiAoaXRlbS5sYXlvdXQgPT09ICdyb3dGb3JtSXRlbScpIHsKICAgICAgICBpdGVtLmNvbXBvbmVudE5hbWUgPSBgcm93JHt0aGlzLmlkR2xvYmFsfWAKICAgICAgfQogICAgICBpZiAoQXJyYXkuaXNBcnJheShpdGVtLmNoaWxkcmVuKSkgewogICAgICAgIGl0ZW0uY2hpbGRyZW4gPSBpdGVtLmNoaWxkcmVuLm1hcChjaGlsZEl0ZW0gPT4gdGhpcy5jcmVhdGVJZEFuZEtleShjaGlsZEl0ZW0pKQogICAgICB9CiAgICAgIHJldHVybiBpdGVtCiAgICB9LAogICAgZHJhd2luZ0l0ZW1EZWxldGUoaW5kZXgsIHBhcmVudCkgewogICAgICBwYXJlbnQuc3BsaWNlKGluZGV4LCAxKQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgY29uc3QgbGVuID0gdGhpcy5kcmF3aW5nTGlzdC5sZW5ndGgKICAgICAgICBpZiAobGVuKSB7CiAgICAgICAgICB0aGlzLmFjdGl2ZUZvcm1JdGVtKHRoaXMuZHJhd2luZ0xpc3RbbGVuIC0gMV0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGdlbmVyYXRlQ29kZSgpIHsKICAgICAgY29uc3QgeyB0eXBlIH0gPSB0aGlzLmdlbmVyYXRlQ29uZgogICAgICB0aGlzLkFzc2VtYmxlRm9ybURhdGEoKQogICAgICBjb25zdCBzY3JpcHQgPSB2dWVTY3JpcHQobWFrZVVwSnModGhpcy5mb3JtRGF0YSwgdHlwZSkpCiAgICAgIGNvbnN0IGh0bWwgPSB2dWVUZW1wbGF0ZShtYWtlVXBIdG1sKHRoaXMuZm9ybURhdGEsIHR5cGUpKQogICAgICBjb25zdCBjc3MgPSBjc3NTdHlsZShtYWtlVXBDc3ModGhpcy5mb3JtRGF0YSkpCiAgICAgIHJldHVybiBiZWF1dGlmaWVyLmh0bWwoaHRtbCArIHNjcmlwdCArIGNzcywgYmVhdXRpZmllckNvbmYuaHRtbCkKICAgIH0sCiAgICBkb3dubG9hZCgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLnNob3dGaWxlTmFtZSA9IHRydWUKICAgICAgdGhpcy5vcGVyYXRpb25UeXBlID0gJ2Rvd25sb2FkJwogICAgfSwKICAgIHJ1bigpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLnNob3dGaWxlTmFtZSA9IGZhbHNlCiAgICAgIHRoaXMub3BlcmF0aW9uVHlwZSA9ICdydW4nCiAgICB9LAogICAgY29weSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLnNob3dGaWxlTmFtZSA9IGZhbHNlCiAgICAgIHRoaXMub3BlcmF0aW9uVHlwZSA9ICdjb3B5JwogICAgfSwKICAgIHRhZ0NoYW5nZShuZXdUYWcpIHsKICAgICAgbmV3VGFnID0gdGhpcy5jbG9uZUNvbXBvbmVudChuZXdUYWcpCiAgICAgIG5ld1RhZy52TW9kZWwgPSB0aGlzLmFjdGl2ZURhdGEudk1vZGVsCiAgICAgIG5ld1RhZy5mb3JtSWQgPSB0aGlzLmFjdGl2ZUlkCiAgICAgIG5ld1RhZy5zcGFuID0gdGhpcy5hY3RpdmVEYXRhLnNwYW4KICAgICAgZGVsZXRlIHRoaXMuYWN0aXZlRGF0YS50YWcKICAgICAgZGVsZXRlIHRoaXMuYWN0aXZlRGF0YS50YWdJY29uCiAgICAgIGRlbGV0ZSB0aGlzLmFjdGl2ZURhdGEuZG9jdW1lbnQKICAgICAgT2JqZWN0LmtleXMobmV3VGFnKS5mb3JFYWNoKGtleSA9PiB7CiAgICAgICAgaWYgKHRoaXMuYWN0aXZlRGF0YVtrZXldICE9PSB1bmRlZmluZWQKICAgICAgICAgICYmIHR5cGVvZiB0aGlzLmFjdGl2ZURhdGFba2V5XSA9PT0gdHlwZW9mIG5ld1RhZ1trZXldKSB7CiAgICAgICAgICBuZXdUYWdba2V5XSA9IHRoaXMuYWN0aXZlRGF0YVtrZXldCiAgICAgICAgfQogICAgICB9KQogICAgICB0aGlzLmFjdGl2ZURhdGEgPSBuZXdUYWcKICAgICAgdGhpcy51cGRhdGVEcmF3aW5nTGlzdChuZXdUYWcsIHRoaXMuZHJhd2luZ0xpc3QpCiAgICB9LAogICAgdXBkYXRlRHJhd2luZ0xpc3QobmV3VGFnLCBsaXN0KSB7CiAgICAgIGNvbnN0IGluZGV4ID0gbGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLmZvcm1JZCA9PT0gdGhpcy5hY3RpdmVJZCkKICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICBsaXN0LnNwbGljZShpbmRleCwgMSwgbmV3VGFnKQogICAgICB9IGVsc2UgewogICAgICAgIGxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGl0ZW0uY2hpbGRyZW4pKSB0aGlzLnVwZGF0ZURyYXdpbmdMaXN0KG5ld1RhZywgaXRlbS5jaGlsZHJlbikKICAgICAgICB9KQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <div class=\"left-board\">\n      <div class=\"logo-wrapper\">\n        <div class=\"logo\">\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\n        </div>\n      </div>\n      <el-scrollbar class=\"left-scrollbar\">\n        <div class=\"components-list\">\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />输入型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"inputComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />选择型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"selectComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in selectComponents\"\n              :key=\"index\"\n              class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" /> 布局型组件\n          </div>\n          <draggable\n            class=\"components-draggable\" :list=\"layoutComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n        </div>\n      </el-scrollbar>\n    </div>\n\n    <div class=\"center-board\">\n      <div class=\"action-bar\">\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\n          导出vue文件\n        </el-button>\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\n          复制代码\n        </el-button>\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\n          清空\n        </el-button>\n      </div>\n      <el-scrollbar class=\"center-scrollbar\">\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\n          <el-form\n            :size=\"formConf.size\"\n            :label-position=\"formConf.labelPosition\"\n            :disabled=\"formConf.disabled\"\n            :label-width=\"formConf.labelWidth + 'px'\"\n          >\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\n              <draggable-item\n                v-for=\"(element, index) in drawingList\"\n                :key=\"element.renderKey\"\n                :drawing-list=\"drawingList\"\n                :element=\"element\"\n                :index=\"index\"\n                :active-id=\"activeId\"\n                :form-conf=\"formConf\"\n                @activeItem=\"activeFormItem\"\n                @copyItem=\"drawingItemCopy\"\n                @deleteItem=\"drawingItemDelete\"\n              />\n            </draggable>\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\n              从左侧拖入或点选组件进行表单设计\n            </div>\n          </el-form>\n        </el-row>\n      </el-scrollbar>\n    </div>\n\n    <right-panel\n      :active-data=\"activeData\"\n      :form-conf=\"formConf\"\n      :show-field=\"!!drawingList.length\"\n      @tag-change=\"tagChange\"\n    />\n\n    <code-type-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"选择生成类型\"\n      :show-file-name=\"showFileName\"\n      @confirm=\"generate\"\n    />\n    <input id=\"copyNode\" type=\"hidden\">\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport beautifier from 'js-beautify'\nimport ClipboardJS from 'clipboard'\nimport render from '@/utils/generator/render'\nimport RightPanel from './RightPanel'\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\nimport { beautifierConf, titleCase } from '@/utils/index'\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport drawingDefault from '@/utils/generator/drawingDefault'\nimport logo from '@/assets/logo/logo.png'\nimport CodeTypeDialog from './CodeTypeDialog'\nimport DraggableItem from './DraggableItem'\n\nlet oldActiveId\nlet tempActiveData\n\nexport default {\n  components: {\n    draggable,\n    render,\n    RightPanel,\n    CodeTypeDialog,\n    DraggableItem\n  },\n  data() {\n    return {\n      logo,\n      idGlobal: 100,\n      formConf,\n      inputComponents,\n      selectComponents,\n      layoutComponents,\n      labelWidth: 100,\n      drawingList: drawingDefault,\n      drawingData: {},\n      activeId: drawingDefault[0].formId,\n      drawerVisible: false,\n      formData: {},\n      dialogVisible: false,\n      generateConf: null,\n      showFileName: false,\n      activeData: drawingDefault[0]\n    }\n  },\n  created() {\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\n    document.body.ondrop = event => {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n  },\n  watch: {\n    // eslint-disable-next-line func-names\n    'activeData.label': function (val, oldVal) {\n      if (\n        this.activeData.placeholder === undefined\n        || !this.activeData.tag\n        || oldActiveId !== this.activeId\n      ) {\n        return\n      }\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\n    },\n    activeId: {\n      handler(val) {\n        oldActiveId = val\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    const clipboard = new ClipboardJS('#copyNode', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  methods: {\n    activeFormItem(element) {\n      this.activeData = element\n      this.activeId = element.formId\n    },\n    onEnd(obj, a) {\n      if (obj.from !== obj.to) {\n        this.activeData = tempActiveData\n        this.activeId = this.idGlobal\n      }\n    },\n    addComponent(item) {\n      const clone = this.cloneComponent(item)\n      this.drawingList.push(clone)\n      this.activeFormItem(clone)\n    },\n    cloneComponent(origin) {\n      const clone = JSON.parse(JSON.stringify(origin))\n      clone.formId = ++this.idGlobal\n      clone.span = formConf.span\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\n      if (!clone.layout) clone.layout = 'colFormItem'\n      if (clone.layout === 'colFormItem') {\n        clone.vModel = `field${this.idGlobal}`\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\n        tempActiveData = clone\n      } else if (clone.layout === 'rowFormItem') {\n        delete clone.label\n        clone.componentName = `row${this.idGlobal}`\n        clone.gutter = this.formConf.gutter\n        tempActiveData = clone\n      }\n      return tempActiveData\n    },\n    AssembleFormData() {\n      this.formData = {\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\n        ...this.formConf\n      }\n    },\n    generate(data) {\n      const func = this[`exec${titleCase(this.operationType)}`]\n      this.generateConf = data\n      func && func(data)\n    },\n    execRun(data) {\n      this.AssembleFormData()\n      this.drawerVisible = true\n    },\n    execDownload(data) {\n      const codeStr = this.generateCode()\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n      this.$download.saveAs(blob, data.fileName)\n    },\n    execCopy(data) {\n      document.getElementById('copyNode').click()\n    },\n    empty() {\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\n        () => {\n          this.drawingList = []\n        }\n      )\n    },\n    drawingItemCopy(item, parent) {\n      let clone = JSON.parse(JSON.stringify(item))\n      clone = this.createIdAndKey(clone)\n      parent.push(clone)\n      this.activeFormItem(clone)\n    },\n    createIdAndKey(item) {\n      item.formId = ++this.idGlobal\n      item.renderKey = +new Date()\n      if (item.layout === 'colFormItem') {\n        item.vModel = `field${this.idGlobal}`\n      } else if (item.layout === 'rowFormItem') {\n        item.componentName = `row${this.idGlobal}`\n      }\n      if (Array.isArray(item.children)) {\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\n      }\n      return item\n    },\n    drawingItemDelete(index, parent) {\n      parent.splice(index, 1)\n      this.$nextTick(() => {\n        const len = this.drawingList.length\n        if (len) {\n          this.activeFormItem(this.drawingList[len - 1])\n        }\n      })\n    },\n    generateCode() {\n      const { type } = this.generateConf\n      this.AssembleFormData()\n      const script = vueScript(makeUpJs(this.formData, type))\n      const html = vueTemplate(makeUpHtml(this.formData, type))\n      const css = cssStyle(makeUpCss(this.formData))\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    download() {\n      this.dialogVisible = true\n      this.showFileName = true\n      this.operationType = 'download'\n    },\n    run() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'run'\n    },\n    copy() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'copy'\n    },\n    tagChange(newTag) {\n      newTag = this.cloneComponent(newTag)\n      newTag.vModel = this.activeData.vModel\n      newTag.formId = this.activeId\n      newTag.span = this.activeData.span\n      delete this.activeData.tag\n      delete this.activeData.tagIcon\n      delete this.activeData.document\n      Object.keys(newTag).forEach(key => {\n        if (this.activeData[key] !== undefined\n          && typeof this.activeData[key] === typeof newTag[key]) {\n          newTag[key] = this.activeData[key]\n        }\n      })\n      this.activeData = newTag\n      this.updateDrawingList(newTag, this.drawingList)\n    },\n    updateDrawingList(newTag, list) {\n      const index = list.findIndex(item => item.formId === this.activeId)\n      if (index > -1) {\n        list.splice(index, 1, newTag)\n      } else {\n        list.forEach(item => {\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang='scss'>\n.editor-tabs{\n  background: #121315;\n  .el-tabs__header{\n    margin: 0;\n    border-bottom-color: #121315;\n    .el-tabs__nav{\n      border-color: #121315;\n    }\n  }\n  .el-tabs__item{\n    height: 32px;\n    line-height: 32px;\n    color: #888a8e;\n    border-left: 1px solid #121315 !important;\n    background: #363636;\n    margin-right: 5px;\n    user-select: none;\n  }\n  .el-tabs__item.is-active{\n    background: #1e1e1e;\n    border-bottom-color: #1e1e1e!important;\n    color: #fff;\n  }\n  .el-icon-edit{\n    color: #f1fa8c;\n  }\n  .el-icon-document{\n    color: #a95812;\n  }\n}\n\n// home\n.right-scrollbar {\n  .el-scrollbar__view {\n    padding: 12px 18px 15px 15px;\n  }\n}\n.left-scrollbar .el-scrollbar__wrap {\n  box-sizing: border-box;\n  overflow-x: hidden !important;\n  margin-bottom: 0 !important;\n}\n.center-tabs{\n  .el-tabs__header{\n    margin-bottom: 0!important;\n  }\n  .el-tabs__item{\n    width: 50%;\n    text-align: center;\n  }\n  .el-tabs__nav{\n    width: 100%;\n  }\n}\n.reg-item{\n  padding: 12px 6px;\n  background: #f8f8f8;\n  position: relative;\n  border-radius: 4px;\n  .close-btn{\n    position: absolute;\n    right: -6px;\n    top: -6px;\n    display: block;\n    width: 16px;\n    height: 16px;\n    line-height: 16px;\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 50%;\n    color: #fff;\n    text-align: center;\n    z-index: 1;\n    cursor: pointer;\n    font-size: 12px;\n    &:hover{\n      background: rgba(210, 23, 23, 0.5)\n    }\n  }\n  & + .reg-item{\n    margin-top: 18px;\n  }\n}\n.action-bar{\n  & .el-button+.el-button {\n    margin-left: 15px;\n  }\n  & i {\n    font-size: 20px;\n    vertical-align: middle;\n    position: relative;\n    top: -1px;\n  }\n}\n\n.custom-tree-node{\n  width: 100%;\n  font-size: 14px;\n  .node-operation{\n    float: right;\n  }\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\n    margin-left: 6px;\n  }\n  .el-icon-plus{\n    color: #409EFF;\n  }\n  .el-icon-delete{\n    color: #157a0c;\n  }\n}\n\n.left-scrollbar .el-scrollbar__view{\n  overflow-x: hidden;\n}\n\n.el-rate{\n  display: inline-block;\n  vertical-align: text-top;\n}\n.el-upload__tip{\n  line-height: 1.2;\n}\n\n$selectedColor: #f6f7ff;\n$lighterBlue: #409EFF;\n\n.container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.components-list {\n  padding: 8px;\n  box-sizing: border-box;\n  height: 100%;\n  .components-item {\n    display: inline-block;\n    width: 48%;\n    margin: 1%;\n    transition: transform 0ms !important;\n  }\n}\n.components-draggable{\n  padding-bottom: 20px;\n}\n.components-title{\n  font-size: 14px;\n  color: #222;\n  margin: 6px 2px;\n  .svg-icon{\n    color: #666;\n    font-size: 18px;\n  }\n}\n\n.components-body {\n  padding: 8px 10px;\n  background: $selectedColor;\n  font-size: 12px;\n  cursor: move;\n  border: 1px dashed $selectedColor;\n  border-radius: 3px;\n  .svg-icon{\n    color: #777;\n    font-size: 15px;\n  }\n  &:hover {\n    border: 1px dashed #787be8;\n    color: #787be8;\n    .svg-icon {\n      color: #787be8;\n    }\n  }\n}\n\n.left-board {\n  width: 260px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100vh;\n}\n.left-scrollbar{\n  height: calc(100vh - 42px);\n  overflow: hidden;\n}\n.center-scrollbar {\n  height: calc(100vh - 42px);\n  overflow: hidden;\n  border-left: 1px solid #f1e8e8;\n  border-right: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.center-board {\n  height: 100vh;\n  width: auto;\n  margin: 0 350px 0 260px;\n  box-sizing: border-box;\n}\n.empty-info{\n  position: absolute;\n  top: 46%;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 18px;\n  color: #ccb1ea;\n  letter-spacing: 4px;\n}\n.action-bar{\n  position: relative;\n  height: 42px;\n  text-align: right;\n  padding: 0 15px;\n  box-sizing: border-box;;\n  border: 1px solid #f1e8e8;\n  border-top: none;\n  border-left: none;\n  .delete-btn{\n    color: #F56C6C;\n  }\n}\n.logo-wrapper{\n  position: relative;\n  height: 42px;\n  background: #fff;\n  border-bottom: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.logo{\n  position: absolute;\n  left: 12px;\n  top: 6px;\n  line-height: 30px;\n  color: #00afff;\n  font-weight: 600;\n  font-size: 17px;\n  white-space: nowrap;\n  > img{\n    width: 30px;\n    height: 30px;\n    vertical-align: top;\n  }\n  .github{\n    display: inline-block;\n    vertical-align: sub;\n    margin-left: 15px;\n    > img{\n      height: 22px;\n    }\n  }\n}\n\n.center-board-row {\n  padding: 12px 12px 15px 12px;\n  box-sizing: border-box;\n  & > .el-form {\n    // 69 = 12+15+42\n    height: calc(100vh - 69px);\n  }\n}\n.drawing-board {\n  height: 100%;\n  position: relative;\n  .components-body {\n    padding: 0;\n    margin: 0;\n    font-size: 0;\n  }\n  .sortable-ghost {\n    position: relative;\n    display: block;\n    overflow: hidden;\n    &::before {\n      content: \" \";\n      position: absolute;\n      left: 0;\n      right: 0;\n      top: 0;\n      height: 3px;\n      background: rgb(89, 89, 223);\n      z-index: 2;\n    }\n  }\n  .components-item.sortable-ghost {\n    width: 100%;\n    height: 60px;\n    background-color: $selectedColor;\n  }\n  .active-from-item {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n    & > .component-name{\n      color: $lighterBlue;\n    }\n  }\n  .el-form-item{\n    margin-bottom: 15px;\n  }\n}\n.drawing-item{\n  position: relative;\n  cursor: move;\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\n    border: 1px dashed #ccc;\n  }\n  .el-form-item{\n    padding: 12px 10px;\n  }\n}\n.drawing-row-item{\n  position: relative;\n  cursor: move;\n  box-sizing: border-box;\n  border: 1px dashed #ccc;\n  border-radius: 3px;\n  padding: 0 2px;\n  margin-bottom: 15px;\n  .drawing-row-item {\n    margin-bottom: 2px;\n  }\n  .el-col{\n    margin-top: 22px;\n  }\n  .el-form-item{\n    margin-bottom: 0;\n  }\n  .drag-wrapper{\n    min-height: 80px;\n  }\n  &.active-from-item{\n    border: 1px dashed $lighterBlue;\n  }\n  .component-name{\n    position: absolute;\n    top: 0;\n    left: 0;\n    font-size: 12px;\n    color: #bbb;\n    display: inline-block;\n    padding: 0 6px;\n  }\n}\n.drawing-item, .drawing-row-item{\n  &:hover {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n  }\n  & > .drawing-item-copy, & > .drawing-item-delete{\n    display: none;\n    position: absolute;\n    top: -10px;\n    width: 22px;\n    height: 22px;\n    line-height: 22px;\n    text-align: center;\n    border-radius: 50%;\n    font-size: 12px;\n    border: 1px solid;\n    cursor: pointer;\n    z-index: 1;\n  }\n  & > .drawing-item-copy{\n    right: 56px;\n    border-color: $lighterBlue;\n    color: $lighterBlue;\n    background: #fff;\n    &:hover{\n      background: $lighterBlue;\n      color: #fff;\n    }\n  }\n  & > .drawing-item-delete{\n    right: 24px;\n    border-color: #F56C6C;\n    color: #F56C6C;\n    background: #fff;\n    &:hover{\n      background: #F56C6C;\n      color: #fff;\n    }\n  }\n}\n</style>\n"]}]}