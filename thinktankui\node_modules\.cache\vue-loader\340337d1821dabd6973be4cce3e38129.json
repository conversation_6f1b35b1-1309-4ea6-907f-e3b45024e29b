{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\IframeToggle\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\IframeToggle\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBJbm5lckxpbmsgZnJvbSAiLi4vSW5uZXJMaW5rL2luZGV4IjsKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7IElubmVyTGluayB9LAogIGNvbXB1dGVkOiB7CiAgICBpZnJhbWVWaWV3cygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRhZ3NWaWV3LmlmcmFtZVZpZXdzOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaWZyYW1lVXJsKHVybCwgcXVlcnkpIHsKICAgICAgaWYgKE9iamVjdC5rZXlzKHF1ZXJ5KS5sZW5ndGggPiAwKSB7CiAgICAgICAgbGV0IHBhcmFtcyA9IE9iamVjdC5rZXlzKHF1ZXJ5KS5tYXAoKGtleSkgPT4ga2V5ICsgIj0iICsgcXVlcnlba2V5XSkuam9pbigiJiIpOwogICAgICAgIHJldHVybiB1cmwgKyAiPyIgKyBwYXJhbXM7CiAgICAgIH0KICAgICAgcmV0dXJuIHVybDsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/IframeToggle", "sourcesContent": ["<template>\n  <transition-group name=\"fade-transform\" mode=\"out-in\">\n    <inner-link\n      v-for=\"(item, index) in iframeViews\"\n      :key=\"item.path\"\n      :iframeId=\"'iframe' + index\"\n      v-show=\"$route.path === item.path\"\n      :src=\"iframeUrl(item.meta.link, item.query)\"\n    ></inner-link>\n  </transition-group>\n</template>\n\n<script>\nimport InnerLink from \"../InnerLink/index\";\n\nexport default {\n  components: { InnerLink },\n  computed: {\n    iframeViews() {\n      return this.$store.state.tagsView.iframeViews;\n    }\n  },\n  methods: {\n    iframeUrl(url, query) {\n      if (Object.keys(query).length > 0) {\n        let params = Object.keys(query).map((key) => key + \"=\" + query[key]).join(\"&\");\n        return url + \"?\" + params;\n      }\n      return url;\n    }\n  }\n}\n</script>\n"]}]}