{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\modules\\keyboard.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\modules\\keyboard.js", "mtime": 1749105929561}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireWildcard", "_parchment", "_quill", "_interopRequireDefault", "_logger", "_module", "debug", "logger", "SHORTKEY", "exports", "test", "navigator", "platform", "Keyboard", "default", "_Module", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "bindings", "Object", "keys", "for<PERSON>ach", "name", "addBinding", "key", "shift<PERSON>ey", "handleEnter", "metaKey", "ctrl<PERSON>ey", "altKey", "userAgent", "collapsed", "handleBackspace", "handleDelete", "prefix", "suffix", "handleDeleteRange", "offset", "listen", "_inherits2", "_createClass2", "value", "keyBinding", "_this2", "context", "arguments", "length", "undefined", "handler", "binding", "normalize", "warn", "Array", "isArray", "singleBinding", "_objectSpread2", "push", "_this3", "root", "addEventListener", "evt", "defaultPrevented", "isComposing", "keyCode", "concat", "which", "matches", "filter", "match", "blot", "<PERSON><PERSON><PERSON>", "find", "target", "scroll", "range", "getSelection", "hasFocus", "_this3$quill$getLine", "getLine", "index", "_this3$quill$getLine2", "_slicedToArray2", "line", "_this3$quill$getLeaf", "<PERSON><PERSON><PERSON><PERSON>", "_this3$quill$getLeaf2", "leafStart", "offsetStart", "_ref3", "_ref4", "leafEnd", "offsetEnd", "prefixText", "TextBlot", "slice", "suffixText", "cur<PERSON><PERSON><PERSON><PERSON>", "empty", "format", "getFormat", "event", "prevented", "some", "every", "_typeof2", "isEqual", "call", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "formats", "_this$quill$getLine", "_this$quill$getLine2", "delta", "Delta", "retain", "delete", "_this$quill$getLine3", "_this$quill$getLine4", "prev", "isPrevLineEmpty", "statics", "blotName", "curFormats", "prevFormats", "AttributeMap", "diff", "formatDelta", "compose", "updateContents", "sources", "USER", "focus", "_this$quill$getLine5", "_this$quill$getLine6", "_this$quill$getLine7", "_this$quill$getLine8", "next", "nextFormats", "deleteRange", "_this4", "lineFormats", "reduce", "query", "<PERSON><PERSON>", "BLOCK", "insert", "setSelection", "SILENT", "<PERSON><PERSON><PERSON>", "defaultOptions", "bold", "makeFormatHandler", "italic", "underline", "indent", "outdent", "list", "makeCodeBlockHandler", "deleteText", "tab", "table", "history", "cutoff", "formatLine", "_this$quill$getLine9", "_this$quill$getLine0", "scrollSelectionIntoView", "_this$quill$getLine1", "_this$quill$getLine10", "header", "module", "getModule", "_module$getTable", "getTable", "_module$getTable2", "row", "cell", "shift", "tableSide", "blockquote", "_this$quill$getLine11", "_this$quill$getLine12", "trim", "insertText", "_this$quill$getLine13", "_this$quill$getLine14", "numLines", "cur", "makeEmbedArrowHandler", "makeTableArrowHandler", "DEFAULTS", "_ref", "CodeBlock", "TAB", "lines", "getLines", "i", "insertAt", "domNode", "textContent", "startsWith", "deleteAt", "update", "where", "_defineProperty2", "_this$quill$getLeaf", "_this$quill$getLeaf2", "leaf", "EmbedBlot", "<PERSON><PERSON><PERSON>", "up", "targetRow", "parent", "targetCell", "children", "head", "Math", "min", "targetLine", "cloneDeep", "_ref2", "firstFormats", "lastFormats", "_table"], "sources": ["../../src/modules/keyboard.ts"], "sourcesContent": ["import { cloneDeep, isEqual } from 'lodash-es';\nimport Delta, { AttributeMap } from 'quill-delta';\nimport { EmbedBlot, Scope, TextBlot } from 'parchment';\nimport type { Blot, BlockBlot } from 'parchment';\nimport Quill from '../core/quill.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport type { BlockEmbed } from '../blots/block.js';\nimport type { Range } from '../core/selection.js';\n\nconst debug = logger('quill:keyboard');\n\nconst SHORTKEY = /Mac/i.test(navigator.platform) ? 'metaKey' : 'ctrlKey';\n\nexport interface Context {\n  collapsed: boolean;\n  empty: boolean;\n  offset: number;\n  prefix: string;\n  suffix: string;\n  format: Record<string, unknown>;\n  event: KeyboardEvent;\n  line: BlockEmbed | BlockBlot;\n}\n\ninterface BindingObject\n  extends Partial<Omit<Context, 'prefix' | 'suffix' | 'format'>> {\n  key: number | string | string[];\n  shortKey?: boolean | null;\n  shiftKey?: boolean | null;\n  altKey?: boolean | null;\n  metaKey?: boolean | null;\n  ctrlKey?: boolean | null;\n  prefix?: RegExp;\n  suffix?: RegExp;\n  format?: Record<string, unknown> | string[];\n  handler?: (\n    this: { quill: Quill },\n    range: Range,\n    curContext: Context,\n    // eslint-disable-next-line no-use-before-define\n    binding: NormalizedBinding,\n  ) => boolean | void;\n}\n\ntype Binding = BindingObject | string | number;\n\ninterface NormalizedBinding extends Omit<BindingObject, 'key' | 'shortKey'> {\n  key: string | number;\n}\n\ninterface KeyboardOptions {\n  bindings: Record<string, Binding>;\n}\n\ninterface KeyboardOptions {\n  bindings: Record<string, Binding>;\n}\n\nclass Keyboard extends Module<KeyboardOptions> {\n  static DEFAULTS: KeyboardOptions;\n\n  static match(evt: KeyboardEvent, binding: BindingObject) {\n    if (\n      (['altKey', 'ctrlKey', 'metaKey', 'shiftKey'] as const).some((key) => {\n        return !!binding[key] !== evt[key] && binding[key] !== null;\n      })\n    ) {\n      return false;\n    }\n    return binding.key === evt.key || binding.key === evt.which;\n  }\n\n  bindings: Record<string, NormalizedBinding[]>;\n\n  constructor(quill: Quill, options: Partial<KeyboardOptions>) {\n    super(quill, options);\n    this.bindings = {};\n    // @ts-expect-error Fix me later\n    Object.keys(this.options.bindings).forEach((name) => {\n      // @ts-expect-error Fix me later\n      if (this.options.bindings[name]) {\n        // @ts-expect-error Fix me later\n        this.addBinding(this.options.bindings[name]);\n      }\n    });\n    this.addBinding({ key: 'Enter', shiftKey: null }, this.handleEnter);\n    this.addBinding(\n      { key: 'Enter', metaKey: null, ctrlKey: null, altKey: null },\n      () => {},\n    );\n    if (/Firefox/i.test(navigator.userAgent)) {\n      // Need to handle delete and backspace for Firefox in the general case #1171\n      this.addBinding(\n        { key: 'Backspace' },\n        { collapsed: true },\n        this.handleBackspace,\n      );\n      this.addBinding(\n        { key: 'Delete' },\n        { collapsed: true },\n        this.handleDelete,\n      );\n    } else {\n      this.addBinding(\n        { key: 'Backspace' },\n        { collapsed: true, prefix: /^.?$/ },\n        this.handleBackspace,\n      );\n      this.addBinding(\n        { key: 'Delete' },\n        { collapsed: true, suffix: /^.?$/ },\n        this.handleDelete,\n      );\n    }\n    this.addBinding(\n      { key: 'Backspace' },\n      { collapsed: false },\n      this.handleDeleteRange,\n    );\n    this.addBinding(\n      { key: 'Delete' },\n      { collapsed: false },\n      this.handleDeleteRange,\n    );\n    this.addBinding(\n      {\n        key: 'Backspace',\n        altKey: null,\n        ctrlKey: null,\n        metaKey: null,\n        shiftKey: null,\n      },\n      { collapsed: true, offset: 0 },\n      this.handleBackspace,\n    );\n    this.listen();\n  }\n\n  addBinding(\n    keyBinding: Binding,\n    context:\n      | Required<BindingObject['handler']>\n      | Partial<Omit<BindingObject, 'key' | 'handler'>> = {},\n    handler:\n      | Required<BindingObject['handler']>\n      | Partial<Omit<BindingObject, 'key' | 'handler'>> = {},\n  ) {\n    const binding = normalize(keyBinding);\n    if (binding == null) {\n      debug.warn('Attempted to add invalid keyboard binding', binding);\n      return;\n    }\n    if (typeof context === 'function') {\n      context = { handler: context };\n    }\n    if (typeof handler === 'function') {\n      handler = { handler };\n    }\n    const keys = Array.isArray(binding.key) ? binding.key : [binding.key];\n    keys.forEach((key) => {\n      const singleBinding = {\n        ...binding,\n        key,\n        ...context,\n        ...handler,\n      };\n      this.bindings[singleBinding.key] = this.bindings[singleBinding.key] || [];\n      this.bindings[singleBinding.key].push(singleBinding);\n    });\n  }\n\n  listen() {\n    this.quill.root.addEventListener('keydown', (evt) => {\n      if (evt.defaultPrevented || evt.isComposing) return;\n\n      // evt.isComposing is false when pressing Enter/Backspace when composing in Safari\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      const isComposing =\n        evt.keyCode === 229 && (evt.key === 'Enter' || evt.key === 'Backspace');\n      if (isComposing) return;\n\n      const bindings = (this.bindings[evt.key] || []).concat(\n        this.bindings[evt.which] || [],\n      );\n      const matches = bindings.filter((binding) =>\n        Keyboard.match(evt, binding),\n      );\n      if (matches.length === 0) return;\n      // @ts-expect-error\n      const blot = Quill.find(evt.target, true);\n      if (blot && blot.scroll !== this.quill.scroll) return;\n      const range = this.quill.getSelection();\n      if (range == null || !this.quill.hasFocus()) return;\n      const [line, offset] = this.quill.getLine(range.index);\n      const [leafStart, offsetStart] = this.quill.getLeaf(range.index);\n      const [leafEnd, offsetEnd] =\n        range.length === 0\n          ? [leafStart, offsetStart]\n          : this.quill.getLeaf(range.index + range.length);\n      const prefixText =\n        leafStart instanceof TextBlot\n          ? leafStart.value().slice(0, offsetStart)\n          : '';\n      const suffixText =\n        leafEnd instanceof TextBlot ? leafEnd.value().slice(offsetEnd) : '';\n      const curContext = {\n        collapsed: range.length === 0,\n        // @ts-expect-error Fix me later\n        empty: range.length === 0 && line.length() <= 1,\n        format: this.quill.getFormat(range),\n        line,\n        offset,\n        prefix: prefixText,\n        suffix: suffixText,\n        event: evt,\n      };\n      const prevented = matches.some((binding) => {\n        if (\n          binding.collapsed != null &&\n          binding.collapsed !== curContext.collapsed\n        ) {\n          return false;\n        }\n        if (binding.empty != null && binding.empty !== curContext.empty) {\n          return false;\n        }\n        if (binding.offset != null && binding.offset !== curContext.offset) {\n          return false;\n        }\n        if (Array.isArray(binding.format)) {\n          // any format is present\n          if (binding.format.every((name) => curContext.format[name] == null)) {\n            return false;\n          }\n        } else if (typeof binding.format === 'object') {\n          // all formats must match\n          if (\n            !Object.keys(binding.format).every((name) => {\n              // @ts-expect-error Fix me later\n              if (binding.format[name] === true)\n                return curContext.format[name] != null;\n              // @ts-expect-error Fix me later\n              if (binding.format[name] === false)\n                return curContext.format[name] == null;\n              // @ts-expect-error Fix me later\n              return isEqual(binding.format[name], curContext.format[name]);\n            })\n          ) {\n            return false;\n          }\n        }\n        if (binding.prefix != null && !binding.prefix.test(curContext.prefix)) {\n          return false;\n        }\n        if (binding.suffix != null && !binding.suffix.test(curContext.suffix)) {\n          return false;\n        }\n        // @ts-expect-error Fix me later\n        return binding.handler.call(this, range, curContext, binding) !== true;\n      });\n      if (prevented) {\n        evt.preventDefault();\n      }\n    });\n  }\n\n  handleBackspace(range: Range, context: Context) {\n    // Check for astral symbols\n    const length = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]$/.test(context.prefix)\n      ? 2\n      : 1;\n    if (range.index === 0 || this.quill.getLength() <= 1) return;\n    let formats = {};\n    const [line] = this.quill.getLine(range.index);\n    let delta = new Delta().retain(range.index - length).delete(length);\n    if (context.offset === 0) {\n      // Always deleting newline here, length always 1\n      const [prev] = this.quill.getLine(range.index - 1);\n      if (prev) {\n        const isPrevLineEmpty =\n          prev.statics.blotName === 'block' && prev.length() <= 1;\n        if (!isPrevLineEmpty) {\n          // @ts-expect-error Fix me later\n          const curFormats = line.formats();\n          const prevFormats = this.quill.getFormat(range.index - 1, 1);\n          formats = AttributeMap.diff(curFormats, prevFormats) || {};\n          if (Object.keys(formats).length > 0) {\n            // line.length() - 1 targets \\n in line, another -1 for newline being deleted\n            const formatDelta = new Delta()\n              // @ts-expect-error Fix me later\n              .retain(range.index + line.length() - 2)\n              .retain(1, formats);\n            delta = delta.compose(formatDelta);\n          }\n        }\n      }\n    }\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.focus();\n  }\n\n  handleDelete(range: Range, context: Context) {\n    // Check for astral symbols\n    const length = /^[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/.test(context.suffix)\n      ? 2\n      : 1;\n    if (range.index >= this.quill.getLength() - length) return;\n    let formats = {};\n    const [line] = this.quill.getLine(range.index);\n    let delta = new Delta().retain(range.index).delete(length);\n    // @ts-expect-error Fix me later\n    if (context.offset >= line.length() - 1) {\n      const [next] = this.quill.getLine(range.index + 1);\n      if (next) {\n        // @ts-expect-error Fix me later\n        const curFormats = line.formats();\n        const nextFormats = this.quill.getFormat(range.index, 1);\n        formats = AttributeMap.diff(curFormats, nextFormats) || {};\n        if (Object.keys(formats).length > 0) {\n          delta = delta.retain(next.length() - 1).retain(1, formats);\n        }\n      }\n    }\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.focus();\n  }\n\n  handleDeleteRange(range: Range) {\n    deleteRange({ range, quill: this.quill });\n    this.quill.focus();\n  }\n\n  handleEnter(range: Range, context: Context) {\n    const lineFormats = Object.keys(context.format).reduce(\n      (formats: Record<string, unknown>, format) => {\n        if (\n          this.quill.scroll.query(format, Scope.BLOCK) &&\n          !Array.isArray(context.format[format])\n        ) {\n          formats[format] = context.format[format];\n        }\n        return formats;\n      },\n      {},\n    );\n    const delta = new Delta()\n      .retain(range.index)\n      .delete(range.length)\n      .insert('\\n', lineFormats);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n    this.quill.focus();\n  }\n}\n\nconst defaultOptions: KeyboardOptions = {\n  bindings: {\n    bold: makeFormatHandler('bold'),\n    italic: makeFormatHandler('italic'),\n    underline: makeFormatHandler('underline'),\n    indent: {\n      // highlight tab or tab at beginning of list, indent or blockquote\n      key: 'Tab',\n      format: ['blockquote', 'indent', 'list'],\n      handler(range, context) {\n        if (context.collapsed && context.offset !== 0) return true;\n        this.quill.format('indent', '+1', Quill.sources.USER);\n        return false;\n      },\n    },\n    outdent: {\n      key: 'Tab',\n      shiftKey: true,\n      format: ['blockquote', 'indent', 'list'],\n      // highlight tab or tab at beginning of list, indent or blockquote\n      handler(range, context) {\n        if (context.collapsed && context.offset !== 0) return true;\n        this.quill.format('indent', '-1', Quill.sources.USER);\n        return false;\n      },\n    },\n    'outdent backspace': {\n      key: 'Backspace',\n      collapsed: true,\n      shiftKey: null,\n      metaKey: null,\n      ctrlKey: null,\n      altKey: null,\n      format: ['indent', 'list'],\n      offset: 0,\n      handler(range, context) {\n        if (context.format.indent != null) {\n          this.quill.format('indent', '-1', Quill.sources.USER);\n        } else if (context.format.list != null) {\n          this.quill.format('list', false, Quill.sources.USER);\n        }\n      },\n    },\n    'indent code-block': makeCodeBlockHandler(true),\n    'outdent code-block': makeCodeBlockHandler(false),\n    'remove tab': {\n      key: 'Tab',\n      shiftKey: true,\n      collapsed: true,\n      prefix: /\\t$/,\n      handler(range) {\n        this.quill.deleteText(range.index - 1, 1, Quill.sources.USER);\n      },\n    },\n    tab: {\n      key: 'Tab',\n      handler(range, context) {\n        if (context.format.table) return true;\n        this.quill.history.cutoff();\n        const delta = new Delta()\n          .retain(range.index)\n          .delete(range.length)\n          .insert('\\t');\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.history.cutoff();\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        return false;\n      },\n    },\n    'blockquote empty enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['blockquote'],\n      empty: true,\n      handler() {\n        this.quill.format('blockquote', false, Quill.sources.USER);\n      },\n    },\n    'list empty enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['list'],\n      empty: true,\n      handler(range, context) {\n        const formats: Record<string, unknown> = { list: false };\n        if (context.format.indent) {\n          formats.indent = false;\n        }\n        this.quill.formatLine(\n          range.index,\n          range.length,\n          formats,\n          Quill.sources.USER,\n        );\n      },\n    },\n    'checklist enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: { list: 'checked' },\n      handler(range) {\n        const [line, offset] = this.quill.getLine(range.index);\n        const formats = {\n          // @ts-expect-error Fix me later\n          ...line.formats(),\n          list: 'checked',\n        };\n        const delta = new Delta()\n          .retain(range.index)\n          .insert('\\n', formats)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - offset - 1)\n          .retain(1, { list: 'unchecked' });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        this.quill.scrollSelectionIntoView();\n      },\n    },\n    'header enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['header'],\n      suffix: /^$/,\n      handler(range, context) {\n        const [line, offset] = this.quill.getLine(range.index);\n        const delta = new Delta()\n          .retain(range.index)\n          .insert('\\n', context.format)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - offset - 1)\n          .retain(1, { header: null });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        this.quill.scrollSelectionIntoView();\n      },\n    },\n    'table backspace': {\n      key: 'Backspace',\n      format: ['table'],\n      collapsed: true,\n      offset: 0,\n      handler() {},\n    },\n    'table delete': {\n      key: 'Delete',\n      format: ['table'],\n      collapsed: true,\n      suffix: /^$/,\n      handler() {},\n    },\n    'table enter': {\n      key: 'Enter',\n      shiftKey: null,\n      format: ['table'],\n      handler(range) {\n        const module = this.quill.getModule('table');\n        if (module) {\n          // @ts-expect-error\n          const [table, row, cell, offset] = module.getTable(range);\n          const shift = tableSide(table, row, cell, offset);\n          if (shift == null) return;\n          let index = table.offset();\n          if (shift < 0) {\n            const delta = new Delta().retain(index).insert('\\n');\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(\n              range.index + 1,\n              range.length,\n              Quill.sources.SILENT,\n            );\n          } else if (shift > 0) {\n            index += table.length();\n            const delta = new Delta().retain(index).insert('\\n');\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(index, Quill.sources.USER);\n          }\n        }\n      },\n    },\n    'table tab': {\n      key: 'Tab',\n      shiftKey: null,\n      format: ['table'],\n      handler(range, context) {\n        const { event, line: cell } = context;\n        const offset = cell.offset(this.quill.scroll);\n        if (event.shiftKey) {\n          this.quill.setSelection(offset - 1, Quill.sources.USER);\n        } else {\n          this.quill.setSelection(offset + cell.length(), Quill.sources.USER);\n        }\n      },\n    },\n    'list autofill': {\n      key: ' ',\n      shiftKey: null,\n      collapsed: true,\n      format: {\n        'code-block': false,\n        blockquote: false,\n        table: false,\n      },\n      prefix: /^\\s*?(\\d+\\.|-|\\*|\\[ ?\\]|\\[x\\])$/,\n      handler(range, context) {\n        if (this.quill.scroll.query('list') == null) return true;\n        const { length } = context.prefix;\n        const [line, offset] = this.quill.getLine(range.index);\n        if (offset > length) return true;\n        let value;\n        switch (context.prefix.trim()) {\n          case '[]':\n          case '[ ]':\n            value = 'unchecked';\n            break;\n          case '[x]':\n            value = 'checked';\n            break;\n          case '-':\n          case '*':\n            value = 'bullet';\n            break;\n          default:\n            value = 'ordered';\n        }\n        this.quill.insertText(range.index, ' ', Quill.sources.USER);\n        this.quill.history.cutoff();\n        const delta = new Delta()\n          .retain(range.index - offset)\n          .delete(length + 1)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - 2 - offset)\n          .retain(1, { list: value });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.history.cutoff();\n        this.quill.setSelection(range.index - length, Quill.sources.SILENT);\n        return false;\n      },\n    },\n    'code exit': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['code-block'],\n      prefix: /^$/,\n      suffix: /^\\s*$/,\n      handler(range) {\n        const [line, offset] = this.quill.getLine(range.index);\n        let numLines = 2;\n        let cur = line;\n        while (\n          cur != null &&\n          cur.length() <= 1 &&\n          cur.formats()['code-block']\n        ) {\n          // @ts-expect-error\n          cur = cur.prev;\n          numLines -= 1;\n          // Requisite prev lines are empty\n          if (numLines <= 0) {\n            const delta = new Delta()\n              // @ts-expect-error Fix me later\n              .retain(range.index + line.length() - offset - 2)\n              .retain(1, { 'code-block': null })\n              .delete(1);\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(range.index - 1, Quill.sources.SILENT);\n            return false;\n          }\n        }\n        return true;\n      },\n    },\n    'embed left': makeEmbedArrowHandler('ArrowLeft', false),\n    'embed left shift': makeEmbedArrowHandler('ArrowLeft', true),\n    'embed right': makeEmbedArrowHandler('ArrowRight', false),\n    'embed right shift': makeEmbedArrowHandler('ArrowRight', true),\n    'table down': makeTableArrowHandler(false),\n    'table up': makeTableArrowHandler(true),\n  },\n};\n\nKeyboard.DEFAULTS = defaultOptions;\n\nfunction makeCodeBlockHandler(indent: boolean): BindingObject {\n  return {\n    key: 'Tab',\n    shiftKey: !indent,\n    format: { 'code-block': true },\n    handler(range, { event }) {\n      const CodeBlock = this.quill.scroll.query('code-block');\n      // @ts-expect-error\n      const { TAB } = CodeBlock;\n      if (range.length === 0 && !event.shiftKey) {\n        this.quill.insertText(range.index, TAB, Quill.sources.USER);\n        this.quill.setSelection(range.index + TAB.length, Quill.sources.SILENT);\n        return;\n      }\n\n      const lines =\n        range.length === 0\n          ? this.quill.getLines(range.index, 1)\n          : this.quill.getLines(range);\n      let { index, length } = range;\n      lines.forEach((line, i) => {\n        if (indent) {\n          line.insertAt(0, TAB);\n          if (i === 0) {\n            index += TAB.length;\n          } else {\n            length += TAB.length;\n          }\n          // @ts-expect-error Fix me later\n        } else if (line.domNode.textContent.startsWith(TAB)) {\n          line.deleteAt(0, TAB.length);\n          if (i === 0) {\n            index -= TAB.length;\n          } else {\n            length -= TAB.length;\n          }\n        }\n      });\n      this.quill.update(Quill.sources.USER);\n      this.quill.setSelection(index, length, Quill.sources.SILENT);\n    },\n  };\n}\n\nfunction makeEmbedArrowHandler(\n  key: string,\n  shiftKey: boolean | null,\n): BindingObject {\n  const where = key === 'ArrowLeft' ? 'prefix' : 'suffix';\n  return {\n    key,\n    shiftKey,\n    altKey: null,\n    [where]: /^$/,\n    handler(range) {\n      let { index } = range;\n      if (key === 'ArrowRight') {\n        index += range.length + 1;\n      }\n      const [leaf] = this.quill.getLeaf(index);\n      if (!(leaf instanceof EmbedBlot)) return true;\n      if (key === 'ArrowLeft') {\n        if (shiftKey) {\n          this.quill.setSelection(\n            range.index - 1,\n            range.length + 1,\n            Quill.sources.USER,\n          );\n        } else {\n          this.quill.setSelection(range.index - 1, Quill.sources.USER);\n        }\n      } else if (shiftKey) {\n        this.quill.setSelection(\n          range.index,\n          range.length + 1,\n          Quill.sources.USER,\n        );\n      } else {\n        this.quill.setSelection(\n          range.index + range.length + 1,\n          Quill.sources.USER,\n        );\n      }\n      return false;\n    },\n  };\n}\n\nfunction makeFormatHandler(format: string): BindingObject {\n  return {\n    key: format[0],\n    shortKey: true,\n    handler(range, context) {\n      this.quill.format(format, !context.format[format], Quill.sources.USER);\n    },\n  };\n}\n\nfunction makeTableArrowHandler(up: boolean): BindingObject {\n  return {\n    key: up ? 'ArrowUp' : 'ArrowDown',\n    collapsed: true,\n    format: ['table'],\n    handler(range, context) {\n      // TODO move to table module\n      const key = up ? 'prev' : 'next';\n      const cell = context.line;\n      const targetRow = cell.parent[key];\n      if (targetRow != null) {\n        if (targetRow.statics.blotName === 'table-row') {\n          // @ts-expect-error\n          let targetCell = targetRow.children.head;\n          let cur = cell;\n          while (cur.prev != null) {\n            // @ts-expect-error\n            cur = cur.prev;\n            targetCell = targetCell.next;\n          }\n          const index =\n            targetCell.offset(this.quill.scroll) +\n            Math.min(context.offset, targetCell.length() - 1);\n          this.quill.setSelection(index, 0, Quill.sources.USER);\n        }\n      } else {\n        // @ts-expect-error\n        const targetLine = cell.table()[key];\n        if (targetLine != null) {\n          if (up) {\n            this.quill.setSelection(\n              targetLine.offset(this.quill.scroll) + targetLine.length() - 1,\n              0,\n              Quill.sources.USER,\n            );\n          } else {\n            this.quill.setSelection(\n              targetLine.offset(this.quill.scroll),\n              0,\n              Quill.sources.USER,\n            );\n          }\n        }\n      }\n      return false;\n    },\n  };\n}\n\nfunction normalize(binding: Binding): BindingObject | null {\n  if (typeof binding === 'string' || typeof binding === 'number') {\n    binding = { key: binding };\n  } else if (typeof binding === 'object') {\n    binding = cloneDeep(binding);\n  } else {\n    return null;\n  }\n  if (binding.shortKey) {\n    binding[SHORTKEY] = binding.shortKey;\n    delete binding.shortKey;\n  }\n  return binding;\n}\n\n// TODO: Move into quill.ts or editor.ts\nfunction deleteRange({ quill, range }: { quill: Quill; range: Range }) {\n  const lines = quill.getLines(range);\n  let formats = {};\n  if (lines.length > 1) {\n    const firstFormats = lines[0].formats();\n    const lastFormats = lines[lines.length - 1].formats();\n    formats = AttributeMap.diff(lastFormats, firstFormats) || {};\n  }\n  quill.deleteText(range, Quill.sources.USER);\n  if (Object.keys(formats).length > 0) {\n    quill.formatLine(range.index, 1, formats, Quill.sources.USER);\n  }\n  quill.setSelection(range.index, Quill.sources.SILENT);\n}\n\nfunction tableSide(_table: unknown, row: Blot, cell: Blot, offset: number) {\n  if (row.prev == null && row.next == null) {\n    if (cell.prev == null && cell.next == null) {\n      return offset === 0 ? -1 : 1;\n    }\n    return cell.prev == null ? -1 : 1;\n  }\n  if (row.prev == null) {\n    return -1;\n  }\n  if (row.next == null) {\n    return 1;\n  }\n  return null;\n}\n\nexport { Keyboard as default, SHORTKEY, normalize, deleteRange };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,OAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,OAAA,GAAAF,sBAAA,CAAAL,OAAA;AAIA,IAAMQ,KAAK,GAAG,IAAAC,eAAM,EAAC,gBAAgB,CAAC;AAEtC,IAAMC,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG,MAAM,CAACE,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,GAAG,SAAS,GAAG,SAAS;AAAA,IA+ClEC,QAAQ,GAAAJ,OAAA,CAAAK,OAAA,0BAAAC,OAAA;EAgBZ,SAAAF,SAAYG,KAAY,EAAEC,OAAiC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAD,QAAA;IAC3DK,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAD,QAAA,GAAMG,KAAK,EAAEC,OAAO;IACpBC,KAAA,CAAKG,QAAQ,GAAG,CAAC,CAAC;IAClB;IACAC,MAAM,CAACC,IAAI,CAACL,KAAA,CAAKD,OAAO,CAACI,QAAQ,CAAC,CAACG,OAAO,CAAE,UAAAC,IAAI,EAAK;MACnD;MACA,IAAIP,KAAA,CAAKD,OAAO,CAACI,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC/B;QACAP,KAAA,CAAKQ,UAAU,CAACR,KAAA,CAAKD,OAAO,CAACI,QAAQ,CAACI,IAAI,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC;IACFP,KAAA,CAAKQ,UAAU,CAAC;MAAEC,GAAG,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAAEV,KAAA,CAAKW,WAAW,CAAC;IACnEX,KAAA,CAAKQ,UAAU,CACb;MAAEC,GAAG,EAAE,OAAO;MAAEG,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,EAC5D,YAAM,CAAC,CACT,CAAC;IACD,IAAI,UAAU,CAACtB,IAAI,CAACC,SAAS,CAACsB,SAAS,CAAC,EAAE;MACxC;MACAf,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAY,CAAC,EACpB;QAAEO,SAAS,EAAE;MAAK,CAAC,EACnBhB,KAAA,CAAKiB,eACP,CAAC;MACDjB,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAS,CAAC,EACjB;QAAEO,SAAS,EAAE;MAAK,CAAC,EACnBhB,KAAA,CAAKkB,YACP,CAAC;IACH,CAAC,MAAM;MACLlB,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAY,CAAC,EACpB;QAAEO,SAAS,EAAE,IAAI;QAAEG,MAAM,EAAE;MAAO,CAAC,EACnCnB,KAAA,CAAKiB,eACP,CAAC;MACDjB,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAS,CAAC,EACjB;QAAEO,SAAS,EAAE,IAAI;QAAEI,MAAM,EAAE;MAAO,CAAC,EACnCpB,KAAA,CAAKkB,YACP,CAAC;IACH;IACAlB,KAAA,CAAKQ,UAAU,CACb;MAAEC,GAAG,EAAE;IAAY,CAAC,EACpB;MAAEO,SAAS,EAAE;IAAM,CAAC,EACpBhB,KAAA,CAAKqB,iBACP,CAAC;IACDrB,KAAA,CAAKQ,UAAU,CACb;MAAEC,GAAG,EAAE;IAAS,CAAC,EACjB;MAAEO,SAAS,EAAE;IAAM,CAAC,EACpBhB,KAAA,CAAKqB,iBACP,CAAC;IACDrB,KAAA,CAAKQ,UAAU,CACb;MACEC,GAAG,EAAE,WAAW;MAChBK,MAAM,EAAE,IAAI;MACZD,OAAO,EAAE,IAAI;MACbD,OAAO,EAAE,IAAI;MACbF,QAAQ,EAAE;IACZ,CAAC,EACD;MAAEM,SAAS,EAAE,IAAI;MAAEM,MAAM,EAAE;IAAE,CAAC,EAC9BtB,KAAA,CAAKiB,eACP,CAAC;IACDjB,KAAA,CAAKuB,MAAM,CAAC,CAAC;IAAA,OAAAvB,KAAA;EACf;EAAA,IAAAwB,UAAA,CAAA5B,OAAA,EAAAD,QAAA,EAAAE,OAAA;EAAA,WAAA4B,aAAA,CAAA7B,OAAA,EAAAD,QAAA;IAAAc,GAAA;IAAAiB,KAAA,EAEA,SAAAlB,UAAUA,CACRmB,UAAmB,EAOnB;MAAA,IAAAC,MAAA;MAAA,IANAC,OAEmD,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IACxDG,OAEmD,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAExD,IAAMI,OAAO,GAAGC,SAAS,CAACR,UAAU,CAAC;MACrC,IAAIO,OAAO,IAAI,IAAI,EAAE;QACnB9C,KAAK,CAACgD,IAAI,CAAC,2CAA2C,EAAEF,OAAO,CAAC;QAChE;MACF;MACA,IAAI,OAAOL,OAAO,KAAK,UAAU,EAAE;QACjCA,OAAO,GAAG;UAAEI,OAAO,EAAEJ;QAAQ,CAAC;MAChC;MACA,IAAI,OAAOI,OAAO,KAAK,UAAU,EAAE;QACjCA,OAAO,GAAG;UAAEA,OAAA,EAAAA;QAAQ,CAAC;MACvB;MACA,IAAM5B,IAAI,GAAGgC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACzB,GAAG,CAAC,GAAGyB,OAAO,CAACzB,GAAG,GAAG,CAACyB,OAAO,CAACzB,GAAG,CAAC;MACrEJ,IAAI,CAACC,OAAO,CAAE,UAAAG,GAAG,EAAK;QACpB,IAAM8B,aAAa,OAAAC,cAAA,CAAA5C,OAAA,MAAA4C,cAAA,CAAA5C,OAAA,MAAA4C,cAAA,CAAA5C,OAAA,MACdsC,OAAO;UACVzB,GAAG,EAAHA;QAAG,GACAoB,OAAO,GACPI,OAAA,CACJ;QACDL,MAAI,CAACzB,QAAQ,CAACoC,aAAa,CAAC9B,GAAG,CAAC,GAAGmB,MAAI,CAACzB,QAAQ,CAACoC,aAAa,CAAC9B,GAAG,CAAC,IAAI,EAAE;QACzEmB,MAAI,CAACzB,QAAQ,CAACoC,aAAa,CAAC9B,GAAG,CAAC,CAACgC,IAAI,CAACF,aAAa,CAAC;MACtD,CAAC,CAAC;IACJ;EAAA;IAAA9B,GAAA;IAAAiB,KAAA,EAEA,SAAAH,MAAMA,CAAA,EAAG;MAAA,IAAAmB,MAAA;MACP,IAAI,CAAC5C,KAAK,CAAC6C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAG,UAAAC,GAAG,EAAK;QACnD,IAAIA,GAAG,CAACC,gBAAgB,IAAID,GAAG,CAACE,WAAW,EAAE;;QAE7C;QACA;QACA,IAAMA,WAAW,GACfF,GAAG,CAACG,OAAO,KAAK,GAAG,KAAKH,GAAG,CAACpC,GAAG,KAAK,OAAO,IAAIoC,GAAG,CAACpC,GAAG,KAAK,WAAW,CAAC;QACzE,IAAIsC,WAAW,EAAE;QAEjB,IAAM5C,QAAQ,GAAG,CAACuC,MAAI,CAACvC,QAAQ,CAAC0C,GAAG,CAACpC,GAAG,CAAC,IAAI,EAAE,EAAEwC,MAAM,CACpDP,MAAI,CAACvC,QAAQ,CAAC0C,GAAG,CAACK,KAAK,CAAC,IAAI,EAC9B,CAAC;QACD,IAAMC,OAAO,GAAGhD,QAAQ,CAACiD,MAAM,CAAE,UAAAlB,OAAO;UAAA,OACtCvC,QAAQ,CAAC0D,KAAK,CAACR,GAAG,EAAEX,OAAO,CAC7B;QAAA,EAAC;QACD,IAAIiB,OAAO,CAACpB,MAAM,KAAK,CAAC,EAAE;QAC1B;QACA,IAAMuB,IAAI,GAAGC,cAAK,CAACC,IAAI,CAACX,GAAG,CAACY,MAAM,EAAE,IAAI,CAAC;QACzC,IAAIH,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKhB,MAAI,CAAC5C,KAAK,CAAC4D,MAAM,EAAE;QAC/C,IAAMC,KAAK,GAAGjB,MAAI,CAAC5C,KAAK,CAAC8D,YAAY,CAAC,CAAC;QACvC,IAAID,KAAK,IAAI,IAAI,IAAI,CAACjB,MAAI,CAAC5C,KAAK,CAAC+D,QAAQ,CAAC,CAAC,EAAE;QAC7C,IAAAC,oBAAA,GAAuBpB,MAAI,CAAC5C,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAC,qBAAA,OAAAC,eAAA,CAAAtE,OAAA,EAAAkE,oBAAA;UAA/CK,IAAI,GAAAF,qBAAA;UAAE3C,MAAM,GAAA2C,qBAAA;QACnB,IAAAG,oBAAA,GAAiC1B,MAAI,CAAC5C,KAAK,CAACuE,OAAO,CAACV,KAAK,CAACK,KAAK,CAAC;UAAAM,qBAAA,OAAAJ,eAAA,CAAAtE,OAAA,EAAAwE,oBAAA;UAAzDG,SAAS,GAAAD,qBAAA;UAAEE,WAAW,GAAAF,qBAAA;QAC7B,IAAAG,KAAA,GACEd,KAAK,CAAC5B,MAAM,KAAK,CAAC,GACd,CAACwC,SAAS,EAAEC,WAAW,CAAC,GACxB9B,MAAI,CAAC5C,KAAK,CAACuE,OAAO,CAACV,KAAK,CAACK,KAAK,GAAGL,KAAK,CAAC5B,MAAM,CAAC;UAAA2C,KAAA,OAAAR,eAAA,CAAAtE,OAAA,EAAA6E,KAAA;UAH7CE,OAAO,GAAAD,KAAA;UAAEE,SAAS,GAAAF,KAAA;QAIzB,IAAMG,UAAU,GACdN,SAAS,YAAYO,mBAAQ,GACzBP,SAAS,CAAC7C,KAAK,CAAC,CAAC,CAACqD,KAAK,CAAC,CAAC,EAAEP,WAAW,CAAC,GACvC,EAAE;QACR,IAAMQ,UAAU,GACdL,OAAO,YAAYG,mBAAQ,GAAGH,OAAO,CAACjD,KAAK,CAAC,CAAC,CAACqD,KAAK,CAACH,SAAS,CAAC,GAAG,EAAE;QACrE,IAAMK,UAAU,GAAG;UACjBjE,SAAS,EAAE2C,KAAK,CAAC5B,MAAM,KAAK,CAAC;UAC7B;UACAmD,KAAK,EAAEvB,KAAK,CAAC5B,MAAM,KAAK,CAAC,IAAIoC,IAAI,CAACpC,MAAM,CAAC,CAAC,IAAI,CAAC;UAC/CoD,MAAM,EAAEzC,MAAI,CAAC5C,KAAK,CAACsF,SAAS,CAACzB,KAAK,CAAC;UACnCQ,IAAI,EAAJA,IAAI;UACJ7C,MAAM,EAANA,MAAM;UACNH,MAAM,EAAE0D,UAAU;UAClBzD,MAAM,EAAE4D,UAAU;UAClBK,KAAK,EAAExC;QACT,CAAC;QACD,IAAMyC,SAAS,GAAGnC,OAAO,CAACoC,IAAI,CAAE,UAAArD,OAAO,EAAK;UAC1C,IACEA,OAAO,CAAClB,SAAS,IAAI,IAAI,IACzBkB,OAAO,CAAClB,SAAS,KAAKiE,UAAU,CAACjE,SAAS,EAC1C;YACA,OAAO,KAAK;UACd;UACA,IAAIkB,OAAO,CAACgD,KAAK,IAAI,IAAI,IAAIhD,OAAO,CAACgD,KAAK,KAAKD,UAAU,CAACC,KAAK,EAAE;YAC/D,OAAO,KAAK;UACd;UACA,IAAIhD,OAAO,CAACZ,MAAM,IAAI,IAAI,IAAIY,OAAO,CAACZ,MAAM,KAAK2D,UAAU,CAAC3D,MAAM,EAAE;YAClE,OAAO,KAAK;UACd;UACA,IAAIe,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACiD,MAAM,CAAC,EAAE;YACjC;YACA,IAAIjD,OAAO,CAACiD,MAAM,CAACK,KAAK,CAAE,UAAAjF,IAAI;cAAA,OAAK0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,IAAI,IAAI;YAAA,EAAC,EAAE;cACnE,OAAO,KAAK;YACd;UACF,CAAC,MAAM,IAAI,IAAAkF,QAAA,CAAA7F,OAAA,EAAOsC,OAAO,CAACiD,MAAM,MAAK,QAAQ,EAAE;YAC7C;YACA,IACE,CAAC/E,MAAM,CAACC,IAAI,CAAC6B,OAAO,CAACiD,MAAM,CAAC,CAACK,KAAK,CAAE,UAAAjF,IAAI,EAAK;cAC3C;cACA,IAAI2B,OAAO,CAACiD,MAAM,CAAC5E,IAAI,CAAC,KAAK,IAAI,EAC/B,OAAO0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,IAAI,IAAI;cACxC;cACA,IAAI2B,OAAO,CAACiD,MAAM,CAAC5E,IAAI,CAAC,KAAK,KAAK,EAChC,OAAO0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,IAAI,IAAI;cACxC;cACA,OAAO,IAAAmF,iBAAO,EAACxD,OAAO,CAACiD,MAAM,CAAC5E,IAAI,CAAC,EAAE0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,CAAC;YAC/D,CAAC,CAAC,EACF;cACA,OAAO,KAAK;YACd;UACF;UACA,IAAI2B,OAAO,CAACf,MAAM,IAAI,IAAI,IAAI,CAACe,OAAO,CAACf,MAAM,CAAC3B,IAAI,CAACyF,UAAU,CAAC9D,MAAM,CAAC,EAAE;YACrE,OAAO,KAAK;UACd;UACA,IAAIe,OAAO,CAACd,MAAM,IAAI,IAAI,IAAI,CAACc,OAAO,CAACd,MAAM,CAAC5B,IAAI,CAACyF,UAAU,CAAC7D,MAAM,CAAC,EAAE;YACrE,OAAO,KAAK;UACd;UACA;UACA,OAAOc,OAAO,CAACD,OAAO,CAAC0D,IAAI,CAACjD,MAAI,EAAEiB,KAAK,EAAEsB,UAAU,EAAE/C,OAAO,CAAC,KAAK,IAAI;QACxE,CAAC,CAAC;QACF,IAAIoD,SAAS,EAAE;UACbzC,GAAG,CAAC+C,cAAc,CAAC,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;EAAA;IAAAnF,GAAA;IAAAiB,KAAA,EAEA,SAAAT,eAAeA,CAAC0C,KAAY,EAAE9B,OAAgB,EAAE;MAC9C;MACA,IAAME,MAAM,GAAG,iCAAiC,CAACvC,IAAI,CAACqC,OAAO,CAACV,MAAM,CAAC,GACjE,CAAC,GACD,CAAC;MACL,IAAIwC,KAAK,CAACK,KAAK,KAAK,CAAC,IAAI,IAAI,CAAClE,KAAK,CAAC+F,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;MACtD,IAAIC,OAAO,GAAG,CAAC,CAAC;MAChB,IAAAC,mBAAA,GAAe,IAAI,CAACjG,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QAAAgC,oBAAA,OAAA9B,eAAA,CAAAtE,OAAA,EAAAmG,mBAAA;QAAvC5B,IAAI,GAAA6B,oBAAA;MACX,IAAIC,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAGjC,MAAM,CAAC,CAACqE,MAAM,CAACrE,MAAM,CAAC;MACnE,IAAIF,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE;QACxB;QACA,IAAA+E,oBAAA,GAAe,IAAI,CAACvG,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC;UAAAsC,oBAAA,OAAApC,eAAA,CAAAtE,OAAA,EAAAyG,oBAAA;UAA3CE,IAAI,GAAAD,oBAAA;QACX,IAAIC,IAAI,EAAE;UACR,IAAMC,eAAe,GACnBD,IAAI,CAACE,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIH,IAAI,CAACxE,MAAM,CAAC,CAAC,IAAI,CAAC;UACzD,IAAI,CAACyE,eAAe,EAAE;YACpB;YACA,IAAMG,UAAU,GAAGxC,IAAI,CAAC2B,OAAO,CAAC,CAAC;YACjC,IAAMc,WAAW,GAAG,IAAI,CAAC9G,KAAK,CAACsF,SAAS,CAACzB,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5D8B,OAAO,GAAGe,wBAAY,CAACC,IAAI,CAACH,UAAU,EAAEC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAIxG,MAAM,CAACC,IAAI,CAACyF,OAAO,CAAC,CAAC/D,MAAM,GAAG,CAAC,EAAE;cACnC;cACA,IAAMgF,WAAW,GAAG,IAAIb,mBAAK,CAAC;cAC5B;cAAA,CACCC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAGG,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CACvCoE,MAAM,CAAC,CAAC,EAAEL,OAAO,CAAC;cACrBG,KAAK,GAAGA,KAAK,CAACe,OAAO,CAACD,WAAW,CAAC;YACpC;UACF;QACF;MACF;MACA,IAAI,CAACjH,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrH,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAEA,SAAAR,YAAYA,CAACyC,KAAY,EAAE9B,OAAgB,EAAE;MAC3C;MACA,IAAME,MAAM,GAAG,iCAAiC,CAACvC,IAAI,CAACqC,OAAO,CAACT,MAAM,CAAC,GACjE,CAAC,GACD,CAAC;MACL,IAAIuC,KAAK,CAACK,KAAK,IAAI,IAAI,CAAClE,KAAK,CAAC+F,SAAS,CAAC,CAAC,GAAG9D,MAAM,EAAE;MACpD,IAAI+D,OAAO,GAAG,CAAC,CAAC;MAChB,IAAAuB,oBAAA,GAAe,IAAI,CAACvH,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QAAAsD,oBAAA,OAAApD,eAAA,CAAAtE,OAAA,EAAAyH,oBAAA;QAAvClD,IAAI,GAAAmD,oBAAA;MACX,IAAIrB,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CAACoC,MAAM,CAACrE,MAAM,CAAC;MAC1D;MACA,IAAIF,OAAO,CAACP,MAAM,IAAI6C,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;QACvC,IAAAwF,oBAAA,GAAe,IAAI,CAACzH,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC;UAAAwD,oBAAA,OAAAtD,eAAA,CAAAtE,OAAA,EAAA2H,oBAAA;UAA3CE,IAAI,GAAAD,oBAAA;QACX,IAAIC,IAAI,EAAE;UACR;UACA,IAAMd,UAAU,GAAGxC,IAAI,CAAC2B,OAAO,CAAC,CAAC;UACjC,IAAM4B,WAAW,GAAG,IAAI,CAAC5H,KAAK,CAACsF,SAAS,CAACzB,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC;UACxD8B,OAAO,GAAGe,wBAAY,CAACC,IAAI,CAACH,UAAU,EAAEe,WAAW,CAAC,IAAI,CAAC,CAAC;UAC1D,IAAItH,MAAM,CAACC,IAAI,CAACyF,OAAO,CAAC,CAAC/D,MAAM,GAAG,CAAC,EAAE;YACnCkE,KAAK,GAAGA,KAAK,CAACE,MAAM,CAACsB,IAAI,CAAC1F,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACoE,MAAM,CAAC,CAAC,EAAEL,OAAO,CAAC;UAC5D;QACF;MACF;MACA,IAAI,CAAChG,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrH,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAEA,SAAAL,iBAAiBA,CAACsC,KAAY,EAAE;MAC9BgE,WAAW,CAAC;QAAEhE,KAAK,EAALA,KAAK;QAAE7D,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;MACzC,IAAI,CAACA,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAEA,SAAAf,WAAWA,CAACgD,KAAY,EAAE9B,OAAgB,EAAE;MAAA,IAAA+F,MAAA;MAC1C,IAAMC,WAAW,GAAGzH,MAAM,CAACC,IAAI,CAACwB,OAAO,CAACsD,MAAM,CAAC,CAAC2C,MAAM,CACpD,UAAChC,OAAgC,EAAEX,MAAM,EAAK;QAC5C,IACEyC,MAAI,CAAC9H,KAAK,CAAC4D,MAAM,CAACqE,KAAK,CAAC5C,MAAM,EAAE6C,gBAAK,CAACC,KAAK,CAAC,IAC5C,CAAC5F,KAAK,CAACC,OAAO,CAACT,OAAO,CAACsD,MAAM,CAACA,MAAM,CAAC,CAAC,EACtC;UACAW,OAAO,CAACX,MAAM,CAAC,GAAGtD,OAAO,CAACsD,MAAM,CAACA,MAAM,CAAC;QAC1C;QACA,OAAOW,OAAO;MAChB,CAAC,EACD,CAAC,CACH,CAAC;MACD,IAAMG,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBoC,MAAM,CAACzC,KAAK,CAAC5B,MAAM,CAAC,CACpBmG,MAAM,CAAC,IAAI,EAAEL,WAAW,CAAC;MAC5B,IAAI,CAAC/H,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;MAC9D,IAAI,CAACtI,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAnSA,SAAO2B,KAAKA,CAACR,GAAkB,EAAEX,OAAsB,EAAE;MACvD,IACG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAWqD,IAAI,CAAE,UAAA9E,GAAG,EAAK;QACpE,OAAO,CAAC,CAACyB,OAAO,CAACzB,GAAG,CAAC,KAAKoC,GAAG,CAACpC,GAAG,CAAC,IAAIyB,OAAO,CAACzB,GAAG,CAAC,KAAK,IAAI;MAC7D,CAAC,CAAC,EACF;QACA,OAAO,KAAK;MACd;MACA,OAAOyB,OAAO,CAACzB,GAAG,KAAKoC,GAAG,CAACpC,GAAG,IAAIyB,OAAO,CAACzB,GAAG,KAAKoC,GAAG,CAACK,KAAK;IAC7D;EAAA;AAAA,EAZqBmF,eAAM;AAyS7B,IAAMC,cAA+B,GAAG;EACtCnI,QAAQ,EAAE;IACRoI,IAAI,EAAEC,iBAAiB,CAAC,MAAM,CAAC;IAC/BC,MAAM,EAAED,iBAAiB,CAAC,QAAQ,CAAC;IACnCE,SAAS,EAAEF,iBAAiB,CAAC,WAAW,CAAC;IACzCG,MAAM,EAAE;MACN;MACAlI,GAAG,EAAE,KAAK;MACV0E,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;MACxClD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACb,SAAS,IAAIa,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC1D,IAAI,CAACxB,KAAK,CAACqF,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC;IACDyB,OAAO,EAAE;MACPnI,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,IAAI;MACdyE,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;MACxC;MACAlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACb,SAAS,IAAIa,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC1D,IAAI,CAACxB,KAAK,CAACqF,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC;IACD,mBAAmB,EAAE;MACnB1G,GAAG,EAAE,WAAW;MAChBO,SAAS,EAAE,IAAI;MACfN,QAAQ,EAAE,IAAI;MACdE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI;MACZqE,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;MAC1B7D,MAAM,EAAE,CAAC;MACTW,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACsD,MAAM,CAACwD,MAAM,IAAI,IAAI,EAAE;UACjC,IAAI,CAAC7I,KAAK,CAACqF,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACvD,CAAC,MAAM,IAAItF,OAAO,CAACsD,MAAM,CAAC0D,IAAI,IAAI,IAAI,EAAE;UACtC,IAAI,CAAC/I,KAAK,CAACqF,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACtD;MACF;IACF,CAAC;IACD,mBAAmB,EAAE2B,oBAAoB,CAAC,IAAI,CAAC;IAC/C,oBAAoB,EAAEA,oBAAoB,CAAC,KAAK,CAAC;IACjD,YAAY,EAAE;MACZrI,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,IAAI;MACdM,SAAS,EAAE,IAAI;MACfG,MAAM,EAAE,KAAK;MACbc,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAI,CAAC7D,KAAK,CAACiJ,UAAU,CAACpF,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MAC/D;IACF,CAAC;IACD6B,GAAG,EAAE;MACHvI,GAAG,EAAE,KAAK;MACVwB,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACsD,MAAM,CAAC8D,KAAK,EAAE,OAAO,IAAI;QACrC,IAAI,CAACnJ,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAMlD,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBoC,MAAM,CAACzC,KAAK,CAAC5B,MAAM,CAAC,CACpBmG,MAAM,CAAC,IAAI,CAAC;QACf,IAAI,CAACpI,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAACrJ,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QAC9D,OAAO,KAAK;MACd;IACF,CAAC;IACD,wBAAwB,EAAE;MACxB3H,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBD,KAAK,EAAE,IAAI;MACXjD,OAAO,WAAPA,OAAOA,CAAA,EAAG;QACR,IAAI,CAACnC,KAAK,CAACqF,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MAC5D;IACF,CAAC;IACD,kBAAkB,EAAE;MAClB1G,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,MAAM,CAAC;MAChBD,KAAK,EAAE,IAAI;MACXjD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAMiE,OAAgC,GAAG;UAAE+C,IAAI,EAAE;QAAM,CAAC;QACxD,IAAIhH,OAAO,CAACsD,MAAM,CAACwD,MAAM,EAAE;UACzB7C,OAAO,CAAC6C,MAAM,GAAG,KAAK;QACxB;QACA,IAAI,CAAC7I,KAAK,CAACsJ,UAAU,CACnBzF,KAAK,CAACK,KAAK,EACXL,KAAK,CAAC5B,MAAM,EACZ+D,OAAO,EACPvC,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;MACH;IACF,CAAC;IACD,iBAAiB,EAAE;MACjB1G,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE;QAAE0D,IAAI,EAAE;MAAU,CAAC;MAC3B5G,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAA0F,oBAAA,GAAuB,IAAI,CAACvJ,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAsF,oBAAA,OAAApF,eAAA,CAAAtE,OAAA,EAAAyJ,oBAAA;UAA/ClF,IAAI,GAAAmF,oBAAA;UAAEhI,MAAM,GAAAgI,oBAAA;QACnB,IAAMxD,OAAO,OAAAtD,cAAA,CAAA5C,OAAA,MAAA4C,cAAA,CAAA5C,OAAA,MAERuE,IAAI,CAAC2B,OAAO,CAAC,CAAC;UACjB+C,IAAI,EAAE;QAAA,EACP;QACD,IAAM5C,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBkE,MAAM,CAAC,IAAI,EAAEpC,OAAO;QACrB;QAAA,CACCK,MAAM,CAAChC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAGT,MAAM,GAAG,CAAC,CAAC,CAClC6E,MAAM,CAAC,CAAC,EAAE;UAAE0C,IAAI,EAAE;QAAY,CAAC,CAAC;QACnC,IAAI,CAAC/I,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QAC9D,IAAI,CAACtI,KAAK,CAACyJ,uBAAuB,CAAC,CAAC;MACtC;IACF,CAAC;IACD,cAAc,EAAE;MACd9I,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClB/D,MAAM,EAAE,IAAI;MACZa,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAA2H,oBAAA,GAAuB,IAAI,CAAC1J,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAyF,qBAAA,OAAAvF,eAAA,CAAAtE,OAAA,EAAA4J,oBAAA;UAA/CrF,IAAI,GAAAsF,qBAAA;UAAEnI,MAAM,GAAAmI,qBAAA;QACnB,IAAMxD,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBkE,MAAM,CAAC,IAAI,EAAErG,OAAO,CAACsD,MAAM;QAC5B;QAAA,CACCgB,MAAM,CAAChC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAGT,MAAM,GAAG,CAAC,CAAC,CAClC6E,MAAM,CAAC,CAAC,EAAE;UAAEuD,MAAM,EAAE;QAAK,CAAC,CAAC;QAC9B,IAAI,CAAC5J,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QAC9D,IAAI,CAACtI,KAAK,CAACyJ,uBAAuB,CAAC,CAAC;MACtC;IACF,CAAC;IACD,iBAAiB,EAAE;MACjB9I,GAAG,EAAE,WAAW;MAChB0E,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBnE,SAAS,EAAE,IAAI;MACfM,MAAM,EAAE,CAAC;MACTW,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;IACb,CAAC;IACD,cAAc,EAAE;MACdxB,GAAG,EAAE,QAAQ;MACb0E,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBnE,SAAS,EAAE,IAAI;MACfI,MAAM,EAAE,IAAI;MACZa,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;IACb,CAAC;IACD,aAAa,EAAE;MACbxB,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,IAAI;MACdyE,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAMgG,MAAM,GAAG,IAAI,CAAC7J,KAAK,CAAC8J,SAAS,CAAC,OAAO,CAAC;QAC5C,IAAID,MAAM,EAAE;UACV;UACA,IAAAE,gBAAA,GAAmCF,MAAM,CAACG,QAAQ,CAACnG,KAAK,CAAC;YAAAoG,iBAAA,OAAA7F,eAAA,CAAAtE,OAAA,EAAAiK,gBAAA;YAAlDZ,KAAK,GAAAc,iBAAA;YAAEC,GAAG,GAAAD,iBAAA;YAAEE,IAAI,GAAAF,iBAAA;YAAEzI,MAAM,GAAAyI,iBAAA;UAC/B,IAAMG,KAAK,GAAGC,SAAS,CAAClB,KAAK,EAAEe,GAAG,EAAEC,IAAI,EAAE3I,MAAM,CAAC;UACjD,IAAI4I,KAAK,IAAI,IAAI,EAAE;UACnB,IAAIlG,KAAK,GAAGiF,KAAK,CAAC3H,MAAM,CAAC,CAAC;UAC1B,IAAI4I,KAAK,GAAG,CAAC,EAAE;YACb,IAAMjE,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAC,CAACkE,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAACpI,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,GAAG,CAAC,EACfL,KAAK,CAAC5B,MAAM,EACZwB,cAAK,CAAC2D,OAAO,CAACkB,MAChB,CAAC;UACH,CAAC,MAAM,IAAI8B,KAAK,GAAG,CAAC,EAAE;YACpBlG,KAAK,IAAIiF,KAAK,CAAClH,MAAM,CAAC,CAAC;YACvB,IAAMkE,MAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAC,CAACkE,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAACpI,KAAK,CAACmH,cAAc,CAAChB,MAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACnE,KAAK,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;UACpD;QACF;MACF;IACF,CAAC;IACD,WAAW,EAAE;MACX1G,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,IAAI;MACdyE,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAQwD,KAAK,GAAiBxD,OAAO,CAA7BwD,KAAK;UAAQ4E,IAAA,GAASpI,OAAO,CAAtBsC,IAAI;QACnB,IAAM7C,MAAM,GAAG2I,IAAI,CAAC3I,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC;QAC7C,IAAI2B,KAAK,CAAC3E,QAAQ,EAAE;UAClB,IAAI,CAACZ,KAAK,CAACqI,YAAY,CAAC7G,MAAM,GAAG,CAAC,EAAEiC,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACzD,CAAC,MAAM;UACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAAC7G,MAAM,GAAG2I,IAAI,CAAClI,MAAM,CAAC,CAAC,EAAEwB,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACrE;MACF;IACF,CAAC;IACD,eAAe,EAAE;MACf1G,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,IAAI;MACdM,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE;QACN,YAAY,EAAE,KAAK;QACnBiF,UAAU,EAAE,KAAK;QACjBnB,KAAK,EAAE;MACT,CAAC;MACD9H,MAAM,EAAE,iCAAiC;MACzCc,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAI,IAAI,CAAC/B,KAAK,CAAC4D,MAAM,CAACqE,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI;QACxD,IAAQhG,MAAA,GAAWF,OAAO,CAACV,MAAM,CAAzBY,MAAA;QACR,IAAAsI,qBAAA,GAAuB,IAAI,CAACvK,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAsG,qBAAA,OAAApG,eAAA,CAAAtE,OAAA,EAAAyK,qBAAA;UAA/ClG,IAAI,GAAAmG,qBAAA;UAAEhJ,MAAM,GAAAgJ,qBAAA;QACnB,IAAIhJ,MAAM,GAAGS,MAAM,EAAE,OAAO,IAAI;QAChC,IAAIL,KAAK;QACT,QAAQG,OAAO,CAACV,MAAM,CAACoJ,IAAI,CAAC,CAAC;UAC3B,KAAK,IAAI;UACT,KAAK,KAAK;YACR7I,KAAK,GAAG,WAAW;YACnB;UACF,KAAK,KAAK;YACRA,KAAK,GAAG,SAAS;YACjB;UACF,KAAK,GAAG;UACR,KAAK,GAAG;YACNA,KAAK,GAAG,QAAQ;YAChB;UACF;YACEA,KAAK,GAAG,SAAS;QACrB;QACA,IAAI,CAAC5B,KAAK,CAAC0K,UAAU,CAAC7G,KAAK,CAACK,KAAK,EAAE,GAAG,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QAC3D,IAAI,CAACrH,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAMlD,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAG1C,MAAM,CAAC,CAC5B8E,MAAM,CAACrE,MAAM,GAAG,CAAC;QAClB;QAAA,CACCoE,MAAM,CAAChC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAGT,MAAM,CAAC,CAClC6E,MAAM,CAAC,CAAC,EAAE;UAAE0C,IAAI,EAAEnH;QAAM,CAAC,CAAC;QAC7B,IAAI,CAAC5B,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAACrJ,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAGjC,MAAM,EAAEwB,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QACnE,OAAO,KAAK;MACd;IACF,CAAC;IACD,WAAW,EAAE;MACX3H,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBhE,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,OAAO;MACfa,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAA8G,qBAAA,GAAuB,IAAI,CAAC3K,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAA0G,qBAAA,OAAAxG,eAAA,CAAAtE,OAAA,EAAA6K,qBAAA;UAA/CtG,IAAI,GAAAuG,qBAAA;UAAEpJ,MAAM,GAAAoJ,qBAAA;QACnB,IAAIC,QAAQ,GAAG,CAAC;QAChB,IAAIC,GAAG,GAAGzG,IAAI;QACd,OACEyG,GAAG,IAAI,IAAI,IACXA,GAAG,CAAC7I,MAAM,CAAC,CAAC,IAAI,CAAC,IACjB6I,GAAG,CAAC9E,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,EAC3B;UACA;UACA8E,GAAG,GAAGA,GAAG,CAACrE,IAAI;UACdoE,QAAQ,IAAI,CAAC;UACb;UACA,IAAIA,QAAQ,IAAI,CAAC,EAAE;YACjB,IAAM1E,KAAK,GAAG,IAAIC,mBAAK,CAAC;YACtB;YAAA,CACCC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAGG,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAGT,MAAM,GAAG,CAAC,CAAC,CAChD6E,MAAM,CAAC,CAAC,EAAE;cAAE,YAAY,EAAE;YAAK,CAAC,CAAC,CACjCC,MAAM,CAAC,CAAC,CAAC;YACZ,IAAI,CAACtG,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;YAC9D,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC;IACD,YAAY,EAAEyC,qBAAqB,CAAC,WAAW,EAAE,KAAK,CAAC;IACvD,kBAAkB,EAAEA,qBAAqB,CAAC,WAAW,EAAE,IAAI,CAAC;IAC5D,aAAa,EAAEA,qBAAqB,CAAC,YAAY,EAAE,KAAK,CAAC;IACzD,mBAAmB,EAAEA,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;IAC9D,YAAY,EAAEC,qBAAqB,CAAC,KAAK,CAAC;IAC1C,UAAU,EAAEA,qBAAqB,CAAC,IAAI;EACxC;AACF,CAAC;AAEDnL,QAAQ,CAACoL,QAAQ,GAAGzC,cAAc;AAElC,SAASQ,oBAAoBA,CAACH,MAAe,EAAiB;EAC5D,OAAO;IACLlI,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE,CAACiI,MAAM;IACjBxD,MAAM,EAAE;MAAE,YAAY,EAAE;IAAK,CAAC;IAC9BlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAAqH,IAAA,EAAa;MAAA,IAAT3F,KAAA,GAAO2F,IAAA,CAAP3F,KAAA;MACf,IAAM4F,SAAS,GAAG,IAAI,CAACnL,KAAK,CAAC4D,MAAM,CAACqE,KAAK,CAAC,YAAY,CAAC;MACvD;MACA,IAAQmD,GAAA,GAAQD,SAAS,CAAjBC,GAAA;MACR,IAAIvH,KAAK,CAAC5B,MAAM,KAAK,CAAC,IAAI,CAACsD,KAAK,CAAC3E,QAAQ,EAAE;QACzC,IAAI,CAACZ,KAAK,CAAC0K,UAAU,CAAC7G,KAAK,CAACK,KAAK,EAAEkH,GAAG,EAAE3H,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QAC3D,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAGkH,GAAG,CAACnJ,MAAM,EAAEwB,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QACvE;MACF;MAEA,IAAM+C,KAAK,GACTxH,KAAK,CAAC5B,MAAM,KAAK,CAAC,GACd,IAAI,CAACjC,KAAK,CAACsL,QAAQ,CAACzH,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC,GACnC,IAAI,CAAClE,KAAK,CAACsL,QAAQ,CAACzH,KAAK,CAAC;MAChC,IAAMK,KAAK,GAAaL,KAAK,CAAvBK,KAAK;QAAEjC,MAAA,GAAW4B,KAAK,CAAhB5B,MAAA;MACboJ,KAAK,CAAC7K,OAAO,CAAC,UAAC6D,IAAI,EAAEkH,CAAC,EAAK;QACzB,IAAI1C,MAAM,EAAE;UACVxE,IAAI,CAACmH,QAAQ,CAAC,CAAC,EAAEJ,GAAG,CAAC;UACrB,IAAIG,CAAC,KAAK,CAAC,EAAE;YACXrH,KAAK,IAAIkH,GAAG,CAACnJ,MAAM;UACrB,CAAC,MAAM;YACLA,MAAM,IAAImJ,GAAG,CAACnJ,MAAM;UACtB;UACA;QACF,CAAC,MAAM,IAAIoC,IAAI,CAACoH,OAAO,CAACC,WAAW,CAACC,UAAU,CAACP,GAAG,CAAC,EAAE;UACnD/G,IAAI,CAACuH,QAAQ,CAAC,CAAC,EAAER,GAAG,CAACnJ,MAAM,CAAC;UAC5B,IAAIsJ,CAAC,KAAK,CAAC,EAAE;YACXrH,KAAK,IAAIkH,GAAG,CAACnJ,MAAM;UACrB,CAAC,MAAM;YACLA,MAAM,IAAImJ,GAAG,CAACnJ,MAAM;UACtB;QACF;MACF,CAAC,CAAC;MACF,IAAI,CAACjC,KAAK,CAAC6L,MAAM,CAACpI,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACrC,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACnE,KAAK,EAAEjC,MAAM,EAAEwB,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;IAC9D;EACF,CAAC;AACH;AAEA,SAASyC,qBAAqBA,CAC5BpK,GAAW,EACXC,QAAwB,EACT;EACf,IAAMkL,KAAK,GAAGnL,GAAG,KAAK,WAAW,GAAG,QAAQ,GAAG,QAAQ;EACvD,WAAAoL,gBAAA,CAAAjM,OAAA,MAAAiM,gBAAA,CAAAjM,OAAA;IACEa,GAAG,EAAHA,GAAG;IACHC,QAAQ,EAARA,QAAQ;IACRI,MAAM,EAAE;EAAI,GACX8K,KAAK,EAAG,IAAI,uBACb3J,OAAOA,CAAC0B,KAAK,EAAE;IACb,IAAMK,KAAA,GAAUL,KAAK,CAAfK,KAAA;IACN,IAAIvD,GAAG,KAAK,YAAY,EAAE;MACxBuD,KAAK,IAAIL,KAAK,CAAC5B,MAAM,GAAG,CAAC;IAC3B;IACA,IAAA+J,mBAAA,GAAe,IAAI,CAAChM,KAAK,CAACuE,OAAO,CAACL,KAAK,CAAC;MAAA+H,oBAAA,OAAA7H,eAAA,CAAAtE,OAAA,EAAAkM,mBAAA;MAAjCE,IAAI,GAAAD,oBAAA;IACX,IAAI,EAAEC,IAAI,YAAYC,oBAAS,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAIxL,GAAG,KAAK,WAAW,EAAE;MACvB,IAAIC,QAAQ,EAAE;QACZ,IAAI,CAACZ,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,GAAG,CAAC,EACfL,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAChBwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MAC9D;IACF,CAAC,MAAM,IAAIzG,QAAQ,EAAE;MACnB,IAAI,CAACZ,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,EACXL,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAChBwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,GAAGL,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAC9BwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;IACH;IACA,OAAO,KAAK;EACd;AAEJ;AAEA,SAASqB,iBAAiBA,CAACrD,MAAc,EAAiB;EACxD,OAAO;IACL1E,GAAG,EAAE0E,MAAM,CAAC,CAAC,CAAC;IACd+G,QAAQ,EAAE,IAAI;IACdjK,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;MACtB,IAAI,CAAC/B,KAAK,CAACqF,MAAM,CAACA,MAAM,EAAE,CAACtD,OAAO,CAACsD,MAAM,CAACA,MAAM,CAAC,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;IACxE;EACF,CAAC;AACH;AAEA,SAAS2D,qBAAqBA,CAACqB,EAAW,EAAiB;EACzD,OAAO;IACL1L,GAAG,EAAE0L,EAAE,GAAG,SAAS,GAAG,WAAW;IACjCnL,SAAS,EAAE,IAAI;IACfmE,MAAM,EAAE,CAAC,OAAO,CAAC;IACjBlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;MACtB;MACA,IAAMpB,GAAG,GAAG0L,EAAE,GAAG,MAAM,GAAG,MAAM;MAChC,IAAMlC,IAAI,GAAGpI,OAAO,CAACsC,IAAI;MACzB,IAAMiI,SAAS,GAAGnC,IAAI,CAACoC,MAAM,CAAC5L,GAAG,CAAC;MAClC,IAAI2L,SAAS,IAAI,IAAI,EAAE;QACrB,IAAIA,SAAS,CAAC3F,OAAO,CAACC,QAAQ,KAAK,WAAW,EAAE;UAC9C;UACA,IAAI4F,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAACC,IAAI;UACxC,IAAI5B,GAAG,GAAGX,IAAI;UACd,OAAOW,GAAG,CAACrE,IAAI,IAAI,IAAI,EAAE;YACvB;YACAqE,GAAG,GAAGA,GAAG,CAACrE,IAAI;YACd+F,UAAU,GAAGA,UAAU,CAAC7E,IAAI;UAC9B;UACA,IAAMzD,KAAK,GACTsI,UAAU,CAAChL,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC,GACpC+I,IAAI,CAACC,GAAG,CAAC7K,OAAO,CAACP,MAAM,EAAEgL,UAAU,CAACvK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;UACnD,IAAI,CAACjC,KAAK,CAACqI,YAAY,CAACnE,KAAK,EAAE,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACvD;MACF,CAAC,MAAM;QACL;QACA,IAAMwF,UAAU,GAAG1C,IAAI,CAAChB,KAAK,CAAC,CAAC,CAACxI,GAAG,CAAC;QACpC,IAAIkM,UAAU,IAAI,IAAI,EAAE;UACtB,IAAIR,EAAE,EAAE;YACN,IAAI,CAACrM,KAAK,CAACqI,YAAY,CACrBwE,UAAU,CAACrL,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC,GAAGiJ,UAAU,CAAC5K,MAAM,CAAC,CAAC,GAAG,CAAC,EAC9D,CAAC,EACDwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;UACH,CAAC,MAAM;YACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CACrBwE,UAAU,CAACrL,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC,EACpC,CAAC,EACDH,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;UACH;QACF;MACF;MACA,OAAO,KAAK;IACd;EACF,CAAC;AACH;AAEA,SAAShF,SAASA,CAACD,OAAgB,EAAwB;EACzD,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC9DA,OAAO,GAAG;MAAEzB,GAAG,EAAEyB;IAAQ,CAAC;EAC5B,CAAC,MAAM,IAAI,IAAAuD,QAAA,CAAA7F,OAAA,EAAOsC,OAAO,MAAK,QAAQ,EAAE;IACtCA,OAAO,GAAG,IAAA0K,mBAAS,EAAC1K,OAAO,CAAC;EAC9B,CAAC,MAAM;IACL,OAAO,IAAI;EACb;EACA,IAAIA,OAAO,CAACgK,QAAQ,EAAE;IACpBhK,OAAO,CAAC5C,QAAQ,CAAC,GAAG4C,OAAO,CAACgK,QAAQ;IACpC,OAAOhK,OAAO,CAACgK,QAAQ;EACzB;EACA,OAAOhK,OAAO;AAChB;;AAEA;AACA,SAASyF,WAAWA,CAAAkF,KAAA,EAAmD;EAAA,IAAhD/M,KAAK,GAAyC+M,KAAA,CAA9C/M,KAAK;IAAE6D,KAAA,GAAuCkJ,KAAA,CAAvClJ,KAAA;EAC5B,IAAMwH,KAAK,GAAGrL,KAAK,CAACsL,QAAQ,CAACzH,KAAK,CAAC;EACnC,IAAImC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIqF,KAAK,CAACpJ,MAAM,GAAG,CAAC,EAAE;IACpB,IAAM+K,YAAY,GAAG3B,KAAK,CAAC,CAAC,CAAC,CAACrF,OAAO,CAAC,CAAC;IACvC,IAAMiH,WAAW,GAAG5B,KAAK,CAACA,KAAK,CAACpJ,MAAM,GAAG,CAAC,CAAC,CAAC+D,OAAO,CAAC,CAAC;IACrDA,OAAO,GAAGe,wBAAY,CAACC,IAAI,CAACiG,WAAW,EAAED,YAAY,CAAC,IAAI,CAAC,CAAC;EAC9D;EACAhN,KAAK,CAACiJ,UAAU,CAACpF,KAAK,EAAEJ,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;EAC3C,IAAI/G,MAAM,CAACC,IAAI,CAACyF,OAAO,CAAC,CAAC/D,MAAM,GAAG,CAAC,EAAE;IACnCjC,KAAK,CAACsJ,UAAU,CAACzF,KAAK,CAACK,KAAK,EAAE,CAAC,EAAE8B,OAAO,EAAEvC,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;EAC/D;EACArH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;AACvD;AAEA,SAAS+B,SAASA,CAAC6C,MAAe,EAAEhD,GAAS,EAAEC,IAAU,EAAE3I,MAAc,EAAE;EACzE,IAAI0I,GAAG,CAACzD,IAAI,IAAI,IAAI,IAAIyD,GAAG,CAACvC,IAAI,IAAI,IAAI,EAAE;IACxC,IAAIwC,IAAI,CAAC1D,IAAI,IAAI,IAAI,IAAI0D,IAAI,CAACxC,IAAI,IAAI,IAAI,EAAE;MAC1C,OAAOnG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9B;IACA,OAAO2I,IAAI,CAAC1D,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EACnC;EACA,IAAIyD,GAAG,CAACzD,IAAI,IAAI,IAAI,EAAE;IACpB,OAAO,CAAC,CAAC;EACX;EACA,IAAIyD,GAAG,CAACvC,IAAI,IAAI,IAAI,EAAE;IACpB,OAAO,CAAC;EACV;EACA,OAAO,IAAI;AACb", "ignoreList": []}]}