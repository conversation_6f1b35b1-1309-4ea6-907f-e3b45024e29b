{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSb2xlLCBnZXRSb2xlLCBkZWxSb2xlLCBhZGRSb2xlLCB1cGRhdGVSb2xlLCBkYXRhU2NvcGUsIGNoYW5nZVJvbGVTdGF0dXMsIGRlcHRUcmVlU2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL3JvbGUiOwppbXBvcnQgeyB0cmVlc2VsZWN0IGFzIG1lbnVUcmVlc2VsZWN0LCByb2xlTWVudVRyZWVzZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vbWVudSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJvbGUiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KeS6Imy6KGo5qC85pWw5o2uCiAgICAgIHJvbGVMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjmlbDmja7mnYPpmZDvvIkKICAgICAgb3BlbkRhdGFTY29wZTogZmFsc2UsCiAgICAgIG1lbnVFeHBhbmQ6IGZhbHNlLAogICAgICBtZW51Tm9kZUFsbDogZmFsc2UsCiAgICAgIGRlcHRFeHBhbmQ6IHRydWUsCiAgICAgIGRlcHROb2RlQWxsOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOaVsOaNruiMg+WbtOmAiemhuQogICAgICBkYXRhU2NvcGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIxIiwKICAgICAgICAgIGxhYmVsOiAi5YWo6YOo5pWw5o2u5p2D6ZmQIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIyIiwKICAgICAgICAgIGxhYmVsOiAi6Ieq5a6a5pWw5o2u5p2D6ZmQIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIzIiwKICAgICAgICAgIGxhYmVsOiAi5pys6YOo6Zeo5pWw5o2u5p2D6ZmQIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICI0IiwKICAgICAgICAgIGxhYmVsOiAi5pys6YOo6Zeo5Y+K5Lul5LiL5pWw5o2u5p2D6ZmQIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICI1IiwKICAgICAgICAgIGxhYmVsOiAi5LuF5pys5Lq65pWw5o2u5p2D6ZmQIgogICAgICAgIH0KICAgICAgXSwKICAgICAgLy8g6I+c5Y2V5YiX6KGoCiAgICAgIG1lbnVPcHRpb25zOiBbXSwKICAgICAgLy8g6YOo6Zeo5YiX6KGoCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgICByb2xlS2V5OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICByb2xlTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuinkuiJsuWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICByb2xlS2V5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5p2D6ZmQ5a2X56ym5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHJvbGVTb3J0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6KeS6Imy6aG65bqP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6KeS6Imy5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Um9sZSh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5yb2xlTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICk7CiAgICB9LAogICAgLyoqIOafpeivouiPnOWNleagkee7k+aehCAqLwogICAgZ2V0TWVudVRyZWVzZWxlY3QoKSB7CiAgICAgIG1lbnVUcmVlc2VsZWN0KCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tZW51T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaJgOacieiPnOWNleiKgueCueaVsOaNrgogICAgZ2V0TWVudUFsbENoZWNrZWRLZXlzKCkgewogICAgICAvLyDnm67liY3ooqvpgInkuK3nmoToj5zljZXoioLngrkKICAgICAgbGV0IGNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldENoZWNrZWRLZXlzKCk7CiAgICAgIC8vIOWNiumAieS4reeahOiPnOWNleiKgueCuQogICAgICBsZXQgaGFsZkNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldEhhbGZDaGVja2VkS2V5cygpOwogICAgICBjaGVja2VkS2V5cy51bnNoaWZ0LmFwcGx5KGNoZWNrZWRLZXlzLCBoYWxmQ2hlY2tlZEtleXMpOwogICAgICByZXR1cm4gY2hlY2tlZEtleXM7CiAgICB9LAogICAgLy8g5omA5pyJ6YOo6Zeo6IqC54K55pWw5o2uCiAgICBnZXREZXB0QWxsQ2hlY2tlZEtleXMoKSB7CiAgICAgIC8vIOebruWJjeiiq+mAieS4reeahOmDqOmXqOiKgueCuQogICAgICBsZXQgY2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLmRlcHQuZ2V0Q2hlY2tlZEtleXMoKTsKICAgICAgLy8g5Y2K6YCJ5Lit55qE6YOo6Zeo6IqC54K5CiAgICAgIGxldCBoYWxmQ2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLmRlcHQuZ2V0SGFsZkNoZWNrZWRLZXlzKCk7CiAgICAgIGNoZWNrZWRLZXlzLnVuc2hpZnQuYXBwbHkoY2hlY2tlZEtleXMsIGhhbGZDaGVja2VkS2V5cyk7CiAgICAgIHJldHVybiBjaGVja2VkS2V5czsKICAgIH0sCiAgICAvKiog5qC55o2u6KeS6ImySUTmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi8KICAgIGdldFJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpIHsKICAgICAgcmV0dXJuIHJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubWVudU9wdGlvbnMgPSByZXNwb25zZS5tZW51czsKICAgICAgICByZXR1cm4gcmVzcG9uc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivoumDqOmXqOagkee7k+aehCAqLwogICAgZ2V0RGVwdFRyZWUocm9sZUlkKSB7CiAgICAgIHJldHVybiBkZXB0VHJlZVNlbGVjdChyb2xlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kZXB0czsKICAgICAgICByZXR1cm4gcmVzcG9uc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOinkuiJsueKtuaAgeS/ruaUuQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlKHJvdykgewogICAgICBsZXQgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cucm9sZU5hbWUgKyAnIuinkuiJsuWQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGNoYW5nZVJvbGVTdGF0dXMocm93LnJvbGVJZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24oKSB7CiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKu77yI5pWw5o2u5p2D6ZmQ77yJCiAgICBjYW5jZWxEYXRhU2NvcGUoKSB7CiAgICAgIHRoaXMub3BlbkRhdGFTY29wZSA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMubWVudSAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZEtleXMoW10pOwogICAgICB9CiAgICAgIHRoaXMubWVudUV4cGFuZCA9IGZhbHNlLAogICAgICB0aGlzLm1lbnVOb2RlQWxsID0gZmFsc2UsCiAgICAgIHRoaXMuZGVwdEV4cGFuZCA9IHRydWUsCiAgICAgIHRoaXMuZGVwdE5vZGVBbGwgPSBmYWxzZSwKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHJvbGVJZDogdW5kZWZpbmVkLAogICAgICAgIHJvbGVOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcm9sZUtleTogdW5kZWZpbmVkLAogICAgICAgIHJvbGVTb3J0OiAwLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIG1lbnVJZHM6IFtdLAogICAgICAgIGRlcHRJZHM6IFtdLAogICAgICAgIG1lbnVDaGVja1N0cmljdGx5OiB0cnVlLAogICAgICAgIGRlcHRDaGVja1N0cmljdGx5OiB0cnVlLAogICAgICAgIHJlbWFyazogdW5kZWZpbmVkCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnJvbGVJZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8vIOabtOWkmuaTjeS9nOinpuWPkQogICAgaGFuZGxlQ29tbWFuZChjb21tYW5kLCByb3cpIHsKICAgICAgc3dpdGNoIChjb21tYW5kKSB7CiAgICAgICAgY2FzZSAiaGFuZGxlRGF0YVNjb3BlIjoKICAgICAgICAgIHRoaXMuaGFuZGxlRGF0YVNjb3BlKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJoYW5kbGVBdXRoVXNlciI6CiAgICAgICAgICB0aGlzLmhhbmRsZUF1dGhVc2VyKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoJHmnYPpmZDvvIjlsZXlvIAv5oqY5Y+g77yJCiAgICBoYW5kbGVDaGVja2VkVHJlZUV4cGFuZCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICBsZXQgdHJlZUxpc3QgPSB0aGlzLm1lbnVPcHRpb25zOwogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHRoaXMuJHJlZnMubWVudS5zdG9yZS5ub2Rlc01hcFt0cmVlTGlzdFtpXS5pZF0uZXhwYW5kZWQgPSB2YWx1ZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsKICAgICAgICBsZXQgdHJlZUxpc3QgPSB0aGlzLmRlcHRPcHRpb25zOwogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHRoaXMuJHJlZnMuZGVwdC5zdG9yZS5ub2Rlc01hcFt0cmVlTGlzdFtpXS5pZF0uZXhwYW5kZWQgPSB2YWx1ZTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoJHmnYPpmZDvvIjlhajpgIkv5YWo5LiN6YCJ77yJCiAgICBoYW5kbGVDaGVja2VkVHJlZU5vZGVBbGwodmFsdWUsIHR5cGUpIHsKICAgICAgaWYgKHR5cGUgPT0gJ21lbnUnKSB7CiAgICAgICAgdGhpcy4kcmVmcy5tZW51LnNldENoZWNrZWROb2Rlcyh2YWx1ZSA/IHRoaXMubWVudU9wdGlvbnM6IFtdKTsKICAgICAgfSBlbHNlIGlmICh0eXBlID09ICdkZXB0JykgewogICAgICAgIHRoaXMuJHJlZnMuZGVwdC5zZXRDaGVja2VkTm9kZXModmFsdWUgPyB0aGlzLmRlcHRPcHRpb25zOiBbXSk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoJHmnYPpmZDvvIjniLblrZDogZTliqjvvIkKICAgIGhhbmRsZUNoZWNrZWRUcmVlQ29ubmVjdCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICB0aGlzLmZvcm0ubWVudUNoZWNrU3RyaWN0bHkgPSB2YWx1ZSA/IHRydWU6IGZhbHNlOwogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gJ2RlcHQnKSB7CiAgICAgICAgdGhpcy5mb3JtLmRlcHRDaGVja1N0cmljdGx5ID0gdmFsdWUgPyB0cnVlOiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmdldE1lbnVUcmVlc2VsZWN0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6KeS6ImyIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3Qgcm9sZUlkID0gcm93LnJvbGVJZCB8fCB0aGlzLmlkcwogICAgICBjb25zdCByb2xlTWVudSA9IHRoaXMuZ2V0Um9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCk7CiAgICAgIGdldFJvbGUocm9sZUlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgcm9sZU1lbnUudGhlbihyZXMgPT4gewogICAgICAgICAgICBsZXQgY2hlY2tlZEtleXMgPSByZXMuY2hlY2tlZEtleXMKICAgICAgICAgICAgY2hlY2tlZEtleXMuZm9yRWFjaCgodikgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCk9PnsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZCh2LCB0cnVlICxmYWxzZSk7CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KQogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueinkuiJsiI7CiAgICB9LAogICAgLyoqIOmAieaLqeinkuiJsuadg+mZkOiMg+WbtOinpuWPkSAqLwogICAgZGF0YVNjb3BlU2VsZWN0Q2hhbmdlKHZhbHVlKSB7CiAgICAgIGlmKHZhbHVlICE9PSAnMicpIHsKICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc2V0Q2hlY2tlZEtleXMoW10pOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIhumFjeaVsOaNruadg+mZkOaTjeS9nCAqLwogICAgaGFuZGxlRGF0YVNjb3BlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGRlcHRUcmVlU2VsZWN0ID0gdGhpcy5nZXREZXB0VHJlZShyb3cucm9sZUlkKTsKICAgICAgZ2V0Um9sZShyb3cucm9sZUlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbkRhdGFTY29wZSA9IHRydWU7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgZGVwdFRyZWVTZWxlY3QudGhlbihyZXMgPT4gewogICAgICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc2V0Q2hlY2tlZEtleXMocmVzLmNoZWNrZWRLZXlzKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgdGhpcy50aXRsZSA9ICLliIbphY3mlbDmja7mnYPpmZAiOwogICAgfSwKICAgIC8qKiDliIbphY3nlKjmiLfmk43kvZwgKi8KICAgIGhhbmRsZUF1dGhVc2VyOiBmdW5jdGlvbihyb3cpIHsKICAgICAgY29uc3Qgcm9sZUlkID0gcm93LnJvbGVJZDsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9zeXN0ZW0vcm9sZS1hdXRoL3VzZXIvIiArIHJvbGVJZCk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLnJvbGVJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLm1lbnVJZHMgPSB0aGlzLmdldE1lbnVBbGxDaGVja2VkS2V5cygpOwogICAgICAgICAgICB1cGRhdGVSb2xlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5mb3JtLm1lbnVJZHMgPSB0aGlzLmdldE1lbnVBbGxDaGVja2VkS2V5cygpOwogICAgICAgICAgICBhZGRSb2xlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSru+8iOaVsOaNruadg+mZkO+8iSAqLwogICAgc3VibWl0RGF0YVNjb3BlOiBmdW5jdGlvbigpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy5mb3JtLmRlcHRJZHMgPSB0aGlzLmdldERlcHRBbGxDaGVja2VkS2V5cygpOwogICAgICAgIGRhdGFTY29wZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCByb2xlSWRzID0gcm93LnJvbGVJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6KeS6Imy57yW5Y+35Li6IicgKyByb2xlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxSb2xlKHJvbGVJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3JvbGUvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYHJvbGVfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\n        <el-input\n          v-model=\"queryParams.roleName\"\n          placeholder=\"请输入角色名称\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\n        <el-input\n          v-model=\"queryParams.roleKey\"\n          placeholder=\"请输入权限字符\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"角色状态\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:role:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:role:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:role:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:role:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"0\"\n            inactive-value=\"1\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\" v-if=\"scope.row.roleId !== 1\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:role:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:role:remove']\"\n          >删除</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:role:edit']\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"handleDataScope\" icon=\"el-icon-circle-check\"\n                v-hasPermi=\"['system:role:edit']\">数据权限</el-dropdown-item>\n              <el-dropdown-item command=\"handleAuthUser\" icon=\"el-icon-user\"\n                v-hasPermi=\"['system:role:edit']\">分配用户</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改角色配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\n        </el-form-item>\n        <el-form-item prop=\"roleKey\">\n          <span slot=\"label\">\n            <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n            权限字符\n          </span>\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\n        </el-form-item>\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"菜单权限\">\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"menuOptions\"\n            show-checkbox\n            ref=\"menu\"\n            node-key=\"id\"\n            :check-strictly=\"!form.menuCheckStrictly\"\n            empty-text=\"加载中，请稍候\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分配角色数据权限对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"角色名称\">\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限字符\">\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限范围\">\n          <el-select v-model=\"form.dataScope\" @change=\"dataScopeSelectChange\">\n            <el-option\n              v-for=\"item in dataScopeOptions\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"deptOptions\"\n            show-checkbox\n            default-expand-all\n            ref=\"dept\"\n            node-key=\"id\"\n            :check-strictly=\"!form.deptCheckStrictly\"\n            empty-text=\"加载中，请稍候\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from \"@/api/system/role\";\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\";\n\nexport default {\n  name: \"Role\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 角色表格数据\n      roleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示弹出层（数据权限）\n      openDataScope: false,\n      menuExpand: false,\n      menuNodeAll: false,\n      deptExpand: true,\n      deptNodeAll: false,\n      // 日期范围\n      dateRange: [],\n      // 数据范围选项\n      dataScopeOptions: [\n        {\n          value: \"1\",\n          label: \"全部数据权限\"\n        },\n        {\n          value: \"2\",\n          label: \"自定数据权限\"\n        },\n        {\n          value: \"3\",\n          label: \"本部门数据权限\"\n        },\n        {\n          value: \"4\",\n          label: \"本部门及以下数据权限\"\n        },\n        {\n          value: \"5\",\n          label: \"仅本人数据权限\"\n        }\n      ],\n      // 菜单列表\n      menuOptions: [],\n      // 部门列表\n      deptOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleName: undefined,\n        roleKey: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 表单校验\n      rules: {\n        roleName: [\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\n        ],\n        roleKey: [\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\n        ],\n        roleSort: [\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询角色列表 */\n    getList() {\n      this.loading = true;\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.roleList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 查询菜单树结构 */\n    getMenuTreeselect() {\n      menuTreeselect().then(response => {\n        this.menuOptions = response.data;\n      });\n    },\n    // 所有菜单节点数据\n    getMenuAllCheckedKeys() {\n      // 目前被选中的菜单节点\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\n      // 半选中的菜单节点\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    // 所有部门节点数据\n    getDeptAllCheckedKeys() {\n      // 目前被选中的部门节点\n      let checkedKeys = this.$refs.dept.getCheckedKeys();\n      // 半选中的部门节点\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    /** 根据角色ID查询菜单树结构 */\n    getRoleMenuTreeselect(roleId) {\n      return roleMenuTreeselect(roleId).then(response => {\n        this.menuOptions = response.menus;\n        return response;\n      });\n    },\n    /** 根据角色ID查询部门树结构 */\n    getDeptTree(roleId) {\n      return deptTreeSelect(roleId).then(response => {\n        this.deptOptions = response.depts;\n        return response;\n      });\n    },\n    // 角色状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗？').then(function() {\n        return changeRoleStatus(row.roleId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 取消按钮（数据权限）\n    cancelDataScope() {\n      this.openDataScope = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      if (this.$refs.menu != undefined) {\n        this.$refs.menu.setCheckedKeys([]);\n      }\n      this.menuExpand = false,\n      this.menuNodeAll = false,\n      this.deptExpand = true,\n      this.deptNodeAll = false,\n      this.form = {\n        roleId: undefined,\n        roleName: undefined,\n        roleKey: undefined,\n        roleSort: 0,\n        status: \"0\",\n        menuIds: [],\n        deptIds: [],\n        menuCheckStrictly: true,\n        deptCheckStrictly: true,\n        remark: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.roleId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleDataScope\":\n          this.handleDataScope(row);\n          break;\n        case \"handleAuthUser\":\n          this.handleAuthUser(row);\n          break;\n        default:\n          break;\n      }\n    },\n    // 树权限（展开/折叠）\n    handleCheckedTreeExpand(value, type) {\n      if (type == 'menu') {\n        let treeList = this.menuOptions;\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      } else if (type == 'dept') {\n        let treeList = this.deptOptions;\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      }\n    },\n    // 树权限（全选/全不选）\n    handleCheckedTreeNodeAll(value, type) {\n      if (type == 'menu') {\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);\n      } else if (type == 'dept') {\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);\n      }\n    },\n    // 树权限（父子联动）\n    handleCheckedTreeConnect(value, type) {\n      if (type == 'menu') {\n        this.form.menuCheckStrictly = value ? true: false;\n      } else if (type == 'dept') {\n        this.form.deptCheckStrictly = value ? true: false;\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.getMenuTreeselect();\n      this.open = true;\n      this.title = \"添加角色\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const roleId = row.roleId || this.ids\n      const roleMenu = this.getRoleMenuTreeselect(roleId);\n      getRole(roleId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.$nextTick(() => {\n          roleMenu.then(res => {\n            let checkedKeys = res.checkedKeys\n            checkedKeys.forEach((v) => {\n                this.$nextTick(()=>{\n                    this.$refs.menu.setChecked(v, true ,false);\n                })\n            })\n          });\n        });\n      });\n      this.title = \"修改角色\";\n    },\n    /** 选择角色权限范围触发 */\n    dataScopeSelectChange(value) {\n      if(value !== '2') {\n        this.$refs.dept.setCheckedKeys([]);\n      }\n    },\n    /** 分配数据权限操作 */\n    handleDataScope(row) {\n      this.reset();\n      const deptTreeSelect = this.getDeptTree(row.roleId);\n      getRole(row.roleId).then(response => {\n        this.form = response.data;\n        this.openDataScope = true;\n        this.$nextTick(() => {\n          deptTreeSelect.then(res => {\n            this.$refs.dept.setCheckedKeys(res.checkedKeys);\n          });\n        });\n      });\n      this.title = \"分配数据权限\";\n    },\n    /** 分配用户操作 */\n    handleAuthUser: function(row) {\n      const roleId = row.roleId;\n      this.$router.push(\"/system/role-auth/user/\" + roleId);\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.roleId != undefined) {\n            this.form.menuIds = this.getMenuAllCheckedKeys();\n            updateRole(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            this.form.menuIds = this.getMenuAllCheckedKeys();\n            addRole(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 提交按钮（数据权限） */\n    submitDataScope: function() {\n      if (this.form.roleId != undefined) {\n        this.form.deptIds = this.getDeptAllCheckedKeys();\n        dataScope(this.form).then(response => {\n          this.$modal.msgSuccess(\"修改成功\");\n          this.openDataScope = false;\n          this.getList();\n        });\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const roleIds = row.roleId || this.ids;\n      this.$modal.confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项？').then(function() {\n        return delRole(roleIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/role/export', {\n        ...this.queryParams\n      }, `role_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>"]}]}