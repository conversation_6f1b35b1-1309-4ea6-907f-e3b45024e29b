{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Settings\\index.vue?vue&type=style&index=0&id=126b135a&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zZXR0aW5nLWRyYXdlci1jb250ZW50IHsKICAuc2V0dGluZy1kcmF3ZXItdGl0bGUgewogICAgbWFyZ2luLWJvdHRvbTogMTJweDsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC44NSk7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBsaW5lLWhlaWdodDogMjJweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KCiAgLnNldHRpbmctZHJhd2VyLWJsb2NrLWNoZWNib3ggewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tdG9wOiAxMHB4OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAuc2V0dGluZy1kcmF3ZXItYmxvY2stY2hlY2JveC1pdGVtIHsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDJweDsKICAgICAgY3Vyc29yOiBwb2ludGVyOwoKICAgICAgaW1nIHsKICAgICAgICB3aWR0aDogNDhweDsKICAgICAgICBoZWlnaHQ6IDQ4cHg7CiAgICAgIH0KCiAgICAgIC5zZXR0aW5nLWRyYXdlci1ibG9jay1jaGVjYm94LXNlbGVjdEljb24gewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICB0b3A6IDA7CiAgICAgICAgcmlnaHQ6IDA7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgIHBhZGRpbmctdG9wOiAxNXB4OwogICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICBmb250LXdlaWdodDogNzAwOwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgfQogICAgfQogIH0KfQoKLmRyYXdlci1jb250YWluZXIgewogIHBhZGRpbmc6IDIwcHg7CiAgZm9udC1zaXplOiAxNHB4OwogIGxpbmUtaGVpZ2h0OiAxLjU7CiAgd29yZC13cmFwOiBicmVhay13b3JkOwoKICAuZHJhd2VyLXRpdGxlIHsKICAgIG1hcmdpbi1ib3R0b206IDEycHg7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAuODUpOwogICAgZm9udC1zaXplOiAxNHB4OwogICAgbGluZS1oZWlnaHQ6IDIycHg7CiAgfQoKICAuZHJhd2VyLWl0ZW0gewogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjY1KTsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIHBhZGRpbmc6IDEycHggMDsKICB9CgogIC5kcmF3ZXItc3dpdGNoIHsKICAgIGZsb2F0OiByaWdodAogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\n  <el-drawer size=\"280px\" :visible=\"visible\" :with-header=\"false\" :append-to-body=\"true\" :show-close=\"false\">\n    <div class=\"drawer-container\">\n      <div>\n        <div class=\"setting-drawer-content\">\n          <div class=\"setting-drawer-title\">\n            <h3 class=\"drawer-title\">主题风格设置</h3>\n          </div>\n          <div class=\"setting-drawer-block-checbox\">\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\n              <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\n              <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                  </svg>\n                </i>\n              </div>\n            </div>\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\n              <img src=\"@/assets/images/light.svg\" alt=\"light\">\n              <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                  </svg>\n                </i>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"drawer-item\">\n            <span>主题颜色</span>\n            <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n          </div>\n        </div>\n\n        <el-divider/>\n\n        <h3 class=\"drawer-title\">系统布局配置</h3>\n\n        <div class=\"drawer-item\">\n          <span>开启 TopNav</span>\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>开启 Tags-Views</span>\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>固定 Header</span>\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>显示 Logo</span>\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>动态标题</span>\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\" />\n        </div>\n\n        <el-divider/>\n\n        <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\n        <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\n      </div>\n    </div>\n  </el-drawer>\n</template>\n\n<script>\nimport ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: { ThemePicker },\n  data() {\n    return {\n      theme: this.$store.state.settings.theme,\n      sideTheme: this.$store.state.settings.sideTheme\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.$store.state.settings.showSettings\n      }\n    },\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    topNav: {\n      get() {\n        return this.$store.state.settings.topNav\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'topNav',\n          value: val\n        })\n        if (!val) {\n          this.$store.dispatch('app/toggleSideBarHide', false);\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes);\n        }\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    },\n    dynamicTitle: {\n      get() {\n        return this.$store.state.settings.dynamicTitle\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'dynamicTitle',\n          value: val\n        })\n      }\n    },\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n      this.theme = val;\n    },\n    handleTheme(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'sideTheme',\n        value: val\n      })\n      this.sideTheme = val;\n    },\n    saveSetting() {\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\n      this.$cache.local.set(\n        \"layout-setting\",\n        `{\n            \"topNav\":${this.topNav},\n            \"tagsView\":${this.tagsView},\n            \"fixedHeader\":${this.fixedHeader},\n            \"sidebarLogo\":${this.sidebarLogo},\n            \"dynamicTitle\":${this.dynamicTitle},\n            \"sideTheme\":\"${this.sideTheme}\",\n            \"theme\":\"${this.theme}\"\n          }`\n      );\n      setTimeout(this.$modal.closeLoading(), 1000)\n    },\n    resetSetting() {\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\n      this.$cache.local.remove(\"layout-setting\")\n      setTimeout(\"window.location.reload()\", 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .setting-drawer-content {\n    .setting-drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n      font-weight: bold;\n    }\n\n    .setting-drawer-block-checbox {\n      display: flex;\n      justify-content: flex-start;\n      align-items: center;\n      margin-top: 10px;\n      margin-bottom: 20px;\n\n      .setting-drawer-block-checbox-item {\n        position: relative;\n        margin-right: 16px;\n        border-radius: 2px;\n        cursor: pointer;\n\n        img {\n          width: 48px;\n          height: 48px;\n        }\n\n        .setting-drawer-block-checbox-selectIcon {\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 100%;\n          height: 100%;\n          padding-top: 15px;\n          padding-left: 24px;\n          color: #1890ff;\n          font-weight: 700;\n          font-size: 14px;\n        }\n      }\n    }\n  }\n\n  .drawer-container {\n    padding: 20px;\n    font-size: 14px;\n    line-height: 1.5;\n    word-wrap: break-word;\n\n    .drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n    }\n\n    .drawer-item {\n      color: rgba(0, 0, 0, .65);\n      font-size: 14px;\n      padding: 12px 0;\n    }\n\n    .drawer-switch {\n      float: right\n    }\n  }\n</style>\n"]}]}