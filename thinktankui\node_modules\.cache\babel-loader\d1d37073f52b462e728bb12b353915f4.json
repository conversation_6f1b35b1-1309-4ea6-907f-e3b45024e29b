{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\utils\\generator\\js.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\utils\\generator\\js.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "require", "_config", "units", "KB", "MB", "GB", "confGlobal", "inheritAttrs", "file", "dialog", "makeUpJs", "conf", "type", "JSON", "parse", "stringify", "dataList", "ruleList", "optionsList", "propsList", "methodList", "mixinMethod", "uploadVarList", "fields", "for<PERSON>ach", "el", "buildAttributes", "script", "buildexport", "join", "buildData", "buildRules", "options", "length", "buildOptions", "dataType", "model", "concat", "vModel", "titleCase", "buildOptionMethod", "props", "buildProps", "action", "tag", "push", "buildBeforeUpload", "buildSubmitUpload", "children", "el2", "list", "minxins", "formBtns", "submitForm", "formRef", "resetForm", "onOpen", "onClose", "close", "handleConfirm", "methods", "Object", "keys", "key", "undefined", "defaultValue", "multiple", "rules", "trigger", "required", "Array", "isArray", "message", "placeholder", "label", "regList", "item", "pattern", "eval", "str", "valueKey", "value", "labelKey", "<PERSON><PERSON><PERSON>", "unitNum", "sizeUnit", "rightSizeCode", "acceptCode", "returnList", "fileSize", "accept", "methodName", "data", "selectOptions", "uploadVar", "exportDefault", "formModel", "formRules"], "sources": ["H:/项目/金刚/3/thinktankui/src/utils/generator/js.js"], "sourcesContent": ["import { exportDefault, titleCase } from '@/utils/index'\nimport { trigger } from './config'\n\nconst units = {\n  KB: '1024',\n  MB: '1024 / 1024',\n  GB: '1024 / 1024 / 1024'\n}\nlet confGlobal\nconst inheritAttrs = {\n  file: '',\n  dialog: 'inheritAttrs: false,'\n}\n\n\nexport function makeUpJs(conf, type) {\n  confGlobal = conf = JSON.parse(JSON.stringify(conf))\n  const dataList = []\n  const ruleList = []\n  const optionsList = []\n  const propsList = []\n  const methodList = mixinMethod(type)\n  const uploadVarList = []\n\n  conf.fields.forEach(el => {\n    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\n  })\n\n  const script = buildexport(\n    conf,\n    type,\n    dataList.join('\\n'),\n    ruleList.join('\\n'),\n    optionsList.join('\\n'),\n    uploadVarList.join('\\n'),\n    propsList.join('\\n'),\n    methodList.join('\\n')\n  )\n  confGlobal = null\n  return script\n}\n\nfunction buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList) {\n  buildData(el, dataList)\n  buildRules(el, ruleList)\n\n  if (el.options && el.options.length) {\n    buildOptions(el, optionsList)\n    if (el.dataType === 'dynamic') {\n      const model = `${el.vModel}Options`\n      const options = titleCase(model)\n      buildOptionMethod(`get${options}`, model, methodList)\n    }\n  }\n\n  if (el.props && el.props.props) {\n    buildProps(el, propsList)\n  }\n\n  if (el.action && el.tag === 'el-upload') {\n    uploadVarList.push(\n      `${el.vModel}Action: '${el.action}',\n      ${el.vModel}fileList: [],`\n    )\n    methodList.push(buildBeforeUpload(el))\n    if (!el['auto-upload']) {\n      methodList.push(buildSubmitUpload(el))\n    }\n  }\n\n  if (el.children) {\n    el.children.forEach(el2 => {\n      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\n    })\n  }\n}\n\nfunction mixinMethod(type) {\n  const list = []; const\n    minxins = {\n      file: confGlobal.formBtns ? {\n        submitForm: `submitForm() {\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\n          if(!valid) return\n          // TODO 提交表单\n        })\n      },`,\n        resetForm: `resetForm() {\n        this.$refs['${confGlobal.formRef}'].resetFields()\n      },`\n      } : null,\n      dialog: {\n        onOpen: 'onOpen() {},',\n        onClose: `onClose() {\n        this.$refs['${confGlobal.formRef}'].resetFields()\n      },`,\n        close: `close() {\n        this.$emit('update:visible', false)\n      },`,\n        handleConfirm: `handleConfirm() {\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\n          if(!valid) return\n          this.close()\n        })\n      },`\n      }\n    }\n\n  const methods = minxins[type]\n  if (methods) {\n    Object.keys(methods).forEach(key => {\n      list.push(methods[key])\n    })\n  }\n\n  return list\n}\n\nfunction buildData(conf, dataList) {\n  if (conf.vModel === undefined) return\n  let defaultValue\n  if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {\n    defaultValue = `'${conf.defaultValue}'`\n  } else {\n    defaultValue = `${JSON.stringify(conf.defaultValue)}`\n  }\n  dataList.push(`${conf.vModel}: ${defaultValue},`)\n}\n\nfunction buildRules(conf, ruleList) {\n  if (conf.vModel === undefined) return\n  const rules = []\n  if (trigger[conf.tag]) {\n    if (conf.required) {\n      const type = Array.isArray(conf.defaultValue) ? 'type: \\'array\\',' : ''\n      let message = Array.isArray(conf.defaultValue) ? `请至少选择一个${conf.vModel}` : conf.placeholder\n      if (message === undefined) message = `${conf.label}不能为空`\n      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)\n    }\n    if (conf.regList && Array.isArray(conf.regList)) {\n      conf.regList.forEach(item => {\n        if (item.pattern) {\n          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)\n        }\n      })\n    }\n    ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)\n  }\n}\n\nfunction buildOptions(conf, optionsList) {\n  if (conf.vModel === undefined) return\n  if (conf.dataType === 'dynamic') { conf.options = [] }\n  const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`\n  optionsList.push(str)\n}\n\nfunction buildProps(conf, propsList) {\n  if (conf.dataType === 'dynamic') {\n    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)\n    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)\n    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)\n  }\n  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`\n  propsList.push(str)\n}\n\nfunction buildBeforeUpload(conf) {\n  const unitNum = units[conf.sizeUnit]; let rightSizeCode = ''; let acceptCode = ''; const\n    returnList = []\n  if (conf.fileSize) {\n    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}\n    if(!isRightSize){\n      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')\n    }`\n    returnList.push('isRightSize')\n  }\n  if (conf.accept) {\n    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)\n    if(!isAccept){\n      this.$message.error('应该选择${conf.accept}类型的文件')\n    }`\n    returnList.push('isAccept')\n  }\n  const str = `${conf.vModel}BeforeUpload(file) {\n    ${rightSizeCode}\n    ${acceptCode}\n    return ${returnList.join('&&')}\n  },`\n  return returnList.length ? str : ''\n}\n\nfunction buildSubmitUpload(conf) {\n  const str = `submitUpload() {\n    this.$refs['${conf.vModel}'].submit()\n  },`\n  return str\n}\n\nfunction buildOptionMethod(methodName, model, methodList) {\n  const str = `${methodName}() {\n    // TODO 发起请求获取数据\n    this.${model}\n  },`\n  methodList.push(str)\n}\n\nfunction buildexport(conf, type, data, rules, selectOptions, uploadVar, props, methods) {\n  const str = `${exportDefault}{\n  ${inheritAttrs[type]}\n  components: {},\n  props: [],\n  data () {\n    return {\n      ${conf.formModel}: {\n        ${data}\n      },\n      ${conf.formRules}: {\n        ${rules}\n      },\n      ${uploadVar}\n      ${selectOptions}\n      ${props}\n    }\n  },\n  computed: {},\n  watch: {},\n  created () {},\n  mounted () {},\n  methods: {\n    ${methods}\n  }\n}`\n  return str\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAEA,IAAME,KAAK,GAAG;EACZC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,UAAU;AACd,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACV,CAAC;AAGM,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACnCN,UAAU,GAAGK,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC;EACpD,IAAMK,QAAQ,GAAG,EAAE;EACnB,IAAMC,QAAQ,GAAG,EAAE;EACnB,IAAMC,WAAW,GAAG,EAAE;EACtB,IAAMC,SAAS,GAAG,EAAE;EACpB,IAAMC,UAAU,GAAGC,WAAW,CAACT,IAAI,CAAC;EACpC,IAAMU,aAAa,GAAG,EAAE;EAExBX,IAAI,CAACY,MAAM,CAACC,OAAO,CAAC,UAAAC,EAAE,EAAI;IACxBC,eAAe,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;EAC5F,CAAC,CAAC;EAEF,IAAMK,MAAM,GAAGC,WAAW,CACxBjB,IAAI,EACJC,IAAI,EACJI,QAAQ,CAACa,IAAI,CAAC,IAAI,CAAC,EACnBZ,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC,EACnBX,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC,EACtBP,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EACxBV,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC,EACpBT,UAAU,CAACS,IAAI,CAAC,IAAI,CACtB,CAAC;EACDvB,UAAU,GAAG,IAAI;EACjB,OAAOqB,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,EAAE;EAClGQ,SAAS,CAACL,EAAE,EAAET,QAAQ,CAAC;EACvBe,UAAU,CAACN,EAAE,EAAER,QAAQ,CAAC;EAExB,IAAIQ,EAAE,CAACO,OAAO,IAAIP,EAAE,CAACO,OAAO,CAACC,MAAM,EAAE;IACnCC,YAAY,CAACT,EAAE,EAAEP,WAAW,CAAC;IAC7B,IAAIO,EAAE,CAACU,QAAQ,KAAK,SAAS,EAAE;MAC7B,IAAMC,KAAK,MAAAC,MAAA,CAAMZ,EAAE,CAACa,MAAM,YAAS;MACnC,IAAMN,OAAO,GAAG,IAAAO,gBAAS,EAACH,KAAK,CAAC;MAChCI,iBAAiB,OAAAH,MAAA,CAAOL,OAAO,GAAII,KAAK,EAAEhB,UAAU,CAAC;IACvD;EACF;EAEA,IAAIK,EAAE,CAACgB,KAAK,IAAIhB,EAAE,CAACgB,KAAK,CAACA,KAAK,EAAE;IAC9BC,UAAU,CAACjB,EAAE,EAAEN,SAAS,CAAC;EAC3B;EAEA,IAAIM,EAAE,CAACkB,MAAM,IAAIlB,EAAE,CAACmB,GAAG,KAAK,WAAW,EAAE;IACvCtB,aAAa,CAACuB,IAAI,IAAAR,MAAA,CACbZ,EAAE,CAACa,MAAM,eAAAD,MAAA,CAAYZ,EAAE,CAACkB,MAAM,gBAAAN,MAAA,CAC/BZ,EAAE,CAACa,MAAM,kBACb,CAAC;IACDlB,UAAU,CAACyB,IAAI,CAACC,iBAAiB,CAACrB,EAAE,CAAC,CAAC;IACtC,IAAI,CAACA,EAAE,CAAC,aAAa,CAAC,EAAE;MACtBL,UAAU,CAACyB,IAAI,CAACE,iBAAiB,CAACtB,EAAE,CAAC,CAAC;IACxC;EACF;EAEA,IAAIA,EAAE,CAACuB,QAAQ,EAAE;IACfvB,EAAE,CAACuB,QAAQ,CAACxB,OAAO,CAAC,UAAAyB,GAAG,EAAI;MACzBvB,eAAe,CAACuB,GAAG,EAAEjC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;IAC7F,CAAC,CAAC;EACJ;AACF;AAEA,SAASD,WAAWA,CAACT,IAAI,EAAE;EACzB,IAAMsC,IAAI,GAAG,EAAE;EAAE,IACfC,OAAO,GAAG;IACR3C,IAAI,EAAEF,UAAU,CAAC8C,QAAQ,GAAG;MAC1BC,UAAU,yCAAAhB,MAAA,CACI/B,UAAU,CAACgD,OAAO,0HAI/B;MACDC,SAAS,wCAAAlB,MAAA,CACK/B,UAAU,CAACgD,OAAO;IAElC,CAAC,GAAG,IAAI;IACR7C,MAAM,EAAE;MACN+C,MAAM,EAAE,cAAc;MACtBC,OAAO,sCAAApB,MAAA,CACO/B,UAAU,CAACgD,OAAO,+BAC/B;MACDI,KAAK,oEAEJ;MACDC,aAAa,4CAAAtB,MAAA,CACC/B,UAAU,CAACgD,OAAO;IAKlC;EACF,CAAC;EAEH,IAAMM,OAAO,GAAGT,OAAO,CAACvC,IAAI,CAAC;EAC7B,IAAIgD,OAAO,EAAE;IACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACpC,OAAO,CAAC,UAAAuC,GAAG,EAAI;MAClCb,IAAI,CAACL,IAAI,CAACe,OAAO,CAACG,GAAG,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA,OAAOb,IAAI;AACb;AAEA,SAASpB,SAASA,CAACnB,IAAI,EAAEK,QAAQ,EAAE;EACjC,IAAIL,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAIC,YAAY;EAChB,IAAI,OAAQtD,IAAI,CAACsD,YAAa,KAAK,QAAQ,IAAI,CAACtD,IAAI,CAACuD,QAAQ,EAAE;IAC7DD,YAAY,OAAA5B,MAAA,CAAO1B,IAAI,CAACsD,YAAY,MAAG;EACzC,CAAC,MAAM;IACLA,YAAY,MAAA5B,MAAA,CAAMxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACsD,YAAY,CAAC,CAAE;EACvD;EACAjD,QAAQ,CAAC6B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,QAAAD,MAAA,CAAK4B,YAAY,MAAG,CAAC;AACnD;AAEA,SAASlC,UAAUA,CAACpB,IAAI,EAAEM,QAAQ,EAAE;EAClC,IAAIN,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAMG,KAAK,GAAG,EAAE;EAChB,IAAIC,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,EAAE;IACrB,IAAIjC,IAAI,CAAC0D,QAAQ,EAAE;MACjB,IAAMzD,IAAI,GAAG0D,KAAK,CAACC,OAAO,CAAC5D,IAAI,CAACsD,YAAY,CAAC,GAAG,kBAAkB,GAAG,EAAE;MACvE,IAAIO,OAAO,GAAGF,KAAK,CAACC,OAAO,CAAC5D,IAAI,CAACsD,YAAY,CAAC,gDAAA5B,MAAA,CAAa1B,IAAI,CAAC2B,MAAM,IAAK3B,IAAI,CAAC8D,WAAW;MAC3F,IAAID,OAAO,KAAKR,SAAS,EAAEQ,OAAO,MAAAnC,MAAA,CAAM1B,IAAI,CAAC+D,KAAK,6BAAM;MACxDP,KAAK,CAACtB,IAAI,sBAAAR,MAAA,CAAsBzB,IAAI,iBAAAyB,MAAA,CAAcmC,OAAO,mBAAAnC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;IAClG;IACA,IAAIjC,IAAI,CAACgE,OAAO,IAAIL,KAAK,CAACC,OAAO,CAAC5D,IAAI,CAACgE,OAAO,CAAC,EAAE;MAC/ChE,IAAI,CAACgE,OAAO,CAACnD,OAAO,CAAC,UAAAoD,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBV,KAAK,CAACtB,IAAI,eAAAR,MAAA,CAAeyC,IAAI,CAACF,IAAI,CAACC,OAAO,CAAC,kBAAAxC,MAAA,CAAeuC,IAAI,CAACJ,OAAO,mBAAAnC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;QAC/G;MACF,CAAC,CAAC;IACJ;IACA3B,QAAQ,CAAC4B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,SAAAD,MAAA,CAAM8B,KAAK,CAACtC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC;EACxD;AACF;AAEA,SAASK,YAAYA,CAACvB,IAAI,EAAEO,WAAW,EAAE;EACvC,IAAIP,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAIrD,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAAExB,IAAI,CAACqB,OAAO,GAAG,EAAE;EAAC;EACrD,IAAM+C,GAAG,MAAA1C,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,eAAAD,MAAA,CAAYxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACqB,OAAO,CAAC,MAAG;EACrEd,WAAW,CAAC2B,IAAI,CAACkC,GAAG,CAAC;AACvB;AAEA,SAASrC,UAAUA,CAAC/B,IAAI,EAAEQ,SAAS,EAAE;EACnC,IAAIR,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAC/BxB,IAAI,CAACqE,QAAQ,KAAK,OAAO,KAAKrE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACwC,KAAK,GAAGtE,IAAI,CAACqE,QAAQ,CAAC;IACrErE,IAAI,CAACuE,QAAQ,KAAK,OAAO,KAAKvE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACiC,KAAK,GAAG/D,IAAI,CAACuE,QAAQ,CAAC;IACrEvE,IAAI,CAACwE,WAAW,KAAK,UAAU,KAAKxE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACO,QAAQ,GAAGrC,IAAI,CAACwE,WAAW,CAAC;EACnF;EACA,IAAMJ,GAAG,MAAA1C,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,aAAAD,MAAA,CAAUxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAAC,MAAG;EACvEtB,SAAS,CAAC0B,IAAI,CAACkC,GAAG,CAAC;AACrB;AAEA,SAASjC,iBAAiBA,CAACnC,IAAI,EAAE;EAC/B,IAAMyE,OAAO,GAAGlF,KAAK,CAACS,IAAI,CAAC0E,QAAQ,CAAC;EAAE,IAAIC,aAAa,GAAG,EAAE;EAAE,IAAIC,UAAU,GAAG,EAAE;EAAE,IACjFC,UAAU,GAAG,EAAE;EACjB,IAAI7E,IAAI,CAAC8E,QAAQ,EAAE;IACjBH,aAAa,oCAAAjD,MAAA,CAAoC+C,OAAO,SAAA/C,MAAA,CAAM1B,IAAI,CAAC8E,QAAQ,+FAAApD,MAAA,CAE3C1B,IAAI,CAAC8E,QAAQ,EAAApD,MAAA,CAAG1B,IAAI,CAAC0E,QAAQ,cAC3D;IACFG,UAAU,CAAC3C,IAAI,CAAC,aAAa,CAAC;EAChC;EACA,IAAIlC,IAAI,CAAC+E,MAAM,EAAE;IACfH,UAAU,iCAAAlD,MAAA,CAAiC1B,IAAI,CAAC+E,MAAM,iGAAArD,MAAA,CAEzB1B,IAAI,CAAC+E,MAAM,4CACtC;IACFF,UAAU,CAAC3C,IAAI,CAAC,UAAU,CAAC;EAC7B;EACA,IAAMkC,GAAG,MAAA1C,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,gCAAAD,MAAA,CACtBiD,aAAa,YAAAjD,MAAA,CACbkD,UAAU,mBAAAlD,MAAA,CACHmD,UAAU,CAAC3D,IAAI,CAAC,IAAI,CAAC,WAC7B;EACH,OAAO2D,UAAU,CAACvD,MAAM,GAAG8C,GAAG,GAAG,EAAE;AACrC;AAEA,SAAShC,iBAAiBA,CAACpC,IAAI,EAAE;EAC/B,IAAMoE,GAAG,wCAAA1C,MAAA,CACO1B,IAAI,CAAC2B,MAAM,sBACxB;EACH,OAAOyC,GAAG;AACZ;AAEA,SAASvC,iBAAiBA,CAACmD,UAAU,EAAEvD,KAAK,EAAEhB,UAAU,EAAE;EACxD,IAAM2D,GAAG,MAAA1C,MAAA,CAAMsD,UAAU,mFAAAtD,MAAA,CAEhBD,KAAK,WACX;EACHhB,UAAU,CAACyB,IAAI,CAACkC,GAAG,CAAC;AACtB;AAEA,SAASnD,WAAWA,CAACjB,IAAI,EAAEC,IAAI,EAAEgF,IAAI,EAAEzB,KAAK,EAAE0B,aAAa,EAAEC,SAAS,EAAErD,KAAK,EAAEmB,OAAO,EAAE;EACtF,IAAMmB,GAAG,MAAA1C,MAAA,CAAM0D,oBAAa,WAAA1D,MAAA,CAC1B9B,YAAY,CAACK,IAAI,CAAC,0EAAAyB,MAAA,CAKd1B,IAAI,CAACqF,SAAS,mBAAA3D,MAAA,CACZuD,IAAI,wBAAAvD,MAAA,CAEN1B,IAAI,CAACsF,SAAS,mBAAA5D,MAAA,CACZ8B,KAAK,wBAAA9B,MAAA,CAEPyD,SAAS,cAAAzD,MAAA,CACTwD,aAAa,cAAAxD,MAAA,CACbI,KAAK,0GAAAJ,MAAA,CAQPuB,OAAO,aAEX;EACA,OAAOmB,GAAG;AACZ", "ignoreList": []}]}