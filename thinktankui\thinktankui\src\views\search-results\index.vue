<template>
  <div class="search-results-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-header">
        <el-button type="text" icon="el-icon-arrow-left" @click="goBack" class="back-button">
          返回
        </el-button>
        <div class="search-tabs">
          <div class="tab-item active">全文检索</div>
          <div class="tab-item">可视化</div>
        </div>
      </div>

      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="请输入搜索关键词"
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
        </el-input>
      </div>
    </div>

    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <!-- 时间筛选 -->
      <div class="filter-row">
        <span class="filter-label">时间范围:</span>
        <div class="filter-options">
          <el-button
            v-for="time in timeOptions"
            :key="time.value"
            :type="selectedTime === time.value ? 'primary' : ''"
            size="small"
            @click="selectTime(time.value)"
          >
            {{ time.label }}
          </el-button>
        </div>
      </div>

      <!-- 平台筛选 -->
      <div class="filter-row">
        <span class="filter-label">平台类型:</span>
        <div class="filter-options">
          <el-button
            v-for="platform in platformOptions"
            :key="platform.value"
            :type="selectedPlatform === platform.value ? 'primary' : ''"
            size="small"
            @click="selectPlatform(platform.value)"
          >
            {{ platform.label }}
            <span v-if="platform.count" class="count">({{ platform.count }})</span>
          </el-button>
        </div>
      </div>

      <!-- 情感筛选 -->
      <div class="filter-row">
        <span class="filter-label">情感类型:</span>
        <div class="filter-options">
          <el-button
            v-for="emotion in emotionOptions"
            :key="emotion.value"
            :type="selectedEmotion === emotion.value ? 'primary' : ''"
            size="small"
            @click="selectEmotion(emotion.value)"
          >
            {{ emotion.label }}
            <span v-if="emotion.count" class="count">({{ emotion.count }})</span>
          </el-button>
        </div>
      </div>

      <!-- 其他筛选 -->
      <div class="filter-row">
        <span class="filter-label">其他筛选:</span>
        <div class="filter-options">
          <el-button size="small">作者类型</el-button>
          <el-button size="small">地域</el-button>
          <el-button size="small">影响力</el-button>
          <el-button size="small">传播量</el-button>
        </div>
      </div>
    </div>

    <!-- 结果统计 -->
    <div class="result-stats">
      <span>共{{ totalResults }}条结果</span>
      <div class="action-buttons">
        <el-button size="small">导出</el-button>
        <el-button type="primary" size="small">分析</el-button>
      </div>
    </div>

    <!-- 搜索结果列表 -->
    <div class="results-list">
      <div v-for="(item, index) in searchResults" :key="index" class="result-item">
        <div class="result-header">
          <h3 class="result-title">{{ item.title }}</h3>
          <div class="result-actions">
            <el-button type="text" icon="el-icon-view"></el-button>
          </div>
        </div>

        <div class="result-meta">
          <span class="meta-item">{{ item.source }}</span>
          <span class="meta-item">{{ item.publishTime }}</span>
          <span class="meta-item">{{ item.author }}</span>
          <span class="meta-item">{{ item.platform }}</span>
          <span class="meta-item">阅读量: {{ item.readCount }}</span>
          <span class="meta-item">{{ item.location }}</span>
          <span class="meta-item">{{ item.category }}</span>
        </div>

        <div class="result-content">
          {{ item.content }}
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="totalResults"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        @current-change="handlePageChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "SearchResults",
  data() {
    return {
      originalTopNav: undefined,
      searchQuery: "今日 万元",
      selectedTime: "24h",
      selectedPlatform: "all",
      selectedEmotion: "all",
      currentPage: 1,
      pageSize: 10,
      totalResults: 10000,

      timeOptions: [
        { label: "24小时", value: "24h" },
        { label: "一周", value: "1w" },
        { label: "半年", value: "6m" },
        { label: "一年", value: "1y" },
        { label: "自定义", value: "custom" }
      ],

      platformOptions: [
        { label: "全部", value: "all", count: 10540 },
        { label: "微信", value: "wechat", count: 1847 },
        { label: "微博", value: "weibo", count: 2008 },
        { label: "客户端", value: "app", count: 1748 },
        { label: "论坛", value: "forum", count: 673 }
      ],

      emotionOptions: [
        { label: "全部", value: "all" },
        { label: "正面", value: "positive" },
        { label: "负面", value: "negative" },
        { label: "中性", value: "neutral" }
      ],

      searchResults: [
        {
          title: "从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...",
          source: "新华网",
          publishTime: "2022-06-29 20:07:04",
          author: "77人讨论",
          platform: "平台来源",
          readCount: "无",
          location: "无所在地",
          category: "新闻",
          content: "从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家..."
        },
        {
          title: "中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文",
          source: "中大论文发表",
          publishTime: "2022-06-29 20:07:04",
          author: "77人讨论",
          platform: "平台来源",
          readCount: "无",
          location: "无所在地",
          category: "论文",
          content: "中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文..."
        },
        {
          title: "转发微博#中#大学生，人情世故。",
          source: "微博",
          publishTime: "2022-06-29 20:07:04",
          author: "77人讨论",
          platform: "微博",
          readCount: "1000",
          location: "北京",
          category: "社交媒体",
          content: "转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容..."
        }
      ]
    };
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })

    // 获取URL参数中的搜索关键词
    if (this.$route.query.q) {
      this.searchQuery = this.$route.query.q;
      this.handleSearch();
    }
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
  methods: {
    handleSearch() {
      this.$message.success(`搜索: ${this.searchQuery}`);
      // 实际搜索逻辑
    },

    selectTime(value) {
      this.selectedTime = value;
      this.handleSearch();
    },

    selectPlatform(value) {
      this.selectedPlatform = value;
      this.handleSearch();
    },

    selectEmotion(value) {
      this.selectedEmotion = value;
      this.handleSearch();
    },

    handlePageChange(page) {
      this.currentPage = page;
      // 加载对应页面数据
    },

    goBack() {
      // 返回上一页，如果没有历史记录则返回信息汇总页面
      if (window.history.length > 1) {
        this.$router.go(-1);
      } else {
        this.$router.push('/info-summary');
      }
    }
  }
};
</script>

<style scoped>
.search-results-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.search-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  margin-right: 20px;
  color: #409EFF;
  font-size: 14px;
}

.back-button:hover {
  color: #66b1ff;
}

.search-tabs {
  display: flex;
}

.tab-item {
  padding: 8px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #666;
}

.tab-item.active {
  color: #409EFF;
  border-bottom-color: #409EFF;
}

.search-box {
  max-width: 600px;
}

.search-input {
  width: 100%;
}

.filter-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.count {
  color: #999;
  font-size: 12px;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 15px 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.results-list {
  background: white;
  border-radius: 4px;
  margin-bottom: 20px;
}

.result-item {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.result-title {
  font-size: 16px;
  color: #333;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 20px;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #999;
}

.meta-item {
  white-space: nowrap;
}

.result-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 4px;
}
</style>
