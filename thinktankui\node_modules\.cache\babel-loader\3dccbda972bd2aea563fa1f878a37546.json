{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\source-monitoring\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\source-monitoring\\index.vue", "mtime": 1747909704000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnU291cmNlTW9uaXRvcmluZycsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZVRhYjogJ3BlcnNvbm5lbCcsCiAgICAgIG1vbml0b3JTdGF0dXM6IHRydWUsCiAgICAgIHBhZ2VTaXplOiAnNycsCiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIHNvcnRCeTogJ2FsbCcsCiAgICAgIHNlYXJjaEtleXdvcmQ6ICcnLAogICAgICB0YWJsZURhdGE6IFtdIC8vIOaaguaXoOaVsOaNru+8jOaJgOS7peS4uuepuuaVsOe7hAogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOWQhOenjeaWueazlQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "activeTab", "monitorStatus", "pageSize", "date<PERSON><PERSON><PERSON>", "sortBy", "searchKeyword", "tableData", "methods"], "sources": ["src/views/source-monitoring/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <h2>信源监测</h2>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 标签页导航 -->\n      <el-tabs v-model=\"activeTab\" class=\"source-tabs\">\n        <el-tab-pane label=\"信源人员\" name=\"personnel\">\n          <div class=\"tab-content\">\n            <!-- 搜索和筛选区域 -->\n            <div class=\"filter-section\">\n              <div class=\"filter-row\">\n                <div class=\"filter-left\">\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\">接收人设置</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-warning\">预警设置</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-key\">关键词设置</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-star-off\">信源设置</el-button>\n                </div>\n                <div class=\"filter-right\">\n                  <span class=\"status-text\">预警开关</span>\n                  <el-switch v-model=\"monitorStatus\" active-color=\"#13ce66\"></el-switch>\n                </div>\n              </div>\n\n              <div class=\"search-row\">\n                <div class=\"search-left\">\n                  <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\n                  <el-button size=\"small\" icon=\"el-icon-download\">下载</el-button>\n                </div>\n                <div class=\"search-right\">\n                  <span class=\"total-text\">共0条记录</span>\n                  <el-select v-model=\"pageSize\" size=\"small\" style=\"width: 80px; margin: 0 10px;\">\n                    <el-option label=\"7条\" value=\"7\"></el-option>\n                    <el-option label=\"10条\" value=\"10\"></el-option>\n                    <el-option label=\"20条\" value=\"20\"></el-option>\n                  </el-select>\n                  <el-date-picker\n                    v-model=\"dateRange\"\n                    type=\"datetimerange\"\n                    range-separator=\"~\"\n                    start-placeholder=\"2023/04/23 00:00:00\"\n                    end-placeholder=\"2023/04/25 20:47:46\"\n                    size=\"small\"\n                    style=\"width: 350px; margin-right: 10px;\"\n                  ></el-date-picker>\n                  <el-select v-model=\"sortBy\" size=\"small\" style=\"width: 100px; margin-right: 10px;\">\n                    <el-option label=\"全部\" value=\"all\"></el-option>\n                  </el-select>\n                  <el-input\n                    v-model=\"searchKeyword\"\n                    placeholder=\"请输入关键词搜索\"\n                    size=\"small\"\n                    style=\"width: 200px;\"\n                    suffix-icon=\"el-icon-search\"\n                  ></el-input>\n                </div>\n              </div>\n            </div>\n\n            <!-- 表格区域 -->\n            <div class=\"table-section\">\n              <el-table\n                :data=\"tableData\"\n                style=\"width: 100%\"\n                height=\"400\"\n                empty-text=\"暂无数据\"\n              >\n                <el-table-column prop=\"media\" label=\"媒体\" width=\"120\"></el-table-column>\n                <el-table-column prop=\"platform\" label=\"平台\" width=\"100\"></el-table-column>\n                <el-table-column prop=\"publisher\" label=\"发布人\" width=\"120\"></el-table-column>\n                <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\"></el-table-column>\n                <el-table-column prop=\"content\" label=\"内容\" min-width=\"200\"></el-table-column>\n                <el-table-column prop=\"readCount\" label=\"阅读量\" width=\"100\"></el-table-column>\n                <el-table-column label=\"操作\" width=\"120\">\n                  <template slot-scope=\"scope\">\n                    <el-button type=\"text\" size=\"small\">查看</el-button>\n                    <el-button type=\"text\" size=\"small\">编辑</el-button>\n                  </template>\n                </el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"信源媒体\" name=\"media\">\n          <div class=\"tab-content\">\n            <div class=\"empty-state\">\n              <p>暂无数据</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"媒体监控\" name=\"monitoring\">\n          <div class=\"tab-content\">\n            <div class=\"empty-state\">\n              <p>暂无数据</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"媒体反馈\" name=\"feedback\">\n          <div class=\"tab-content\">\n            <div class=\"empty-state\">\n              <p>暂无数据</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SourceMonitoring',\n  data() {\n    return {\n      activeTab: 'personnel',\n      monitorStatus: true,\n      pageSize: '7',\n      dateRange: [],\n      sortBy: 'all',\n      searchKeyword: '',\n      tableData: [] // 暂无数据，所以为空数组\n    }\n  },\n  methods: {\n    // 这里可以添加各种方法\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: calc(100vh - 84px);\n}\n\n.page-header {\n  margin-bottom: 20px;\n\n  h2 {\n    margin: 0;\n    font-size: 20px;\n    font-weight: 500;\n  }\n}\n\n.main-content {\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  min-height: calc(100vh - 150px);\n}\n\n.source-tabs {\n  padding: 20px;\n\n  .tab-content {\n    margin-top: 20px;\n  }\n}\n\n.filter-section {\n  margin-bottom: 20px;\n\n  .filter-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .filter-left {\n      display: flex;\n      gap: 10px;\n    }\n\n    .filter-right {\n      display: flex;\n      align-items: center;\n      gap: 10px;\n\n      .status-text {\n        font-size: 14px;\n        color: #606266;\n      }\n    }\n  }\n\n  .search-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .search-left {\n      display: flex;\n      gap: 10px;\n    }\n\n    .search-right {\n      display: flex;\n      align-items: center;\n\n      .total-text {\n        font-size: 14px;\n        color: #606266;\n        margin-right: 15px;\n      }\n    }\n  }\n}\n\n.table-section {\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 0;\n  color: #909399;\n  font-size: 14px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAqHA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,aAAA;MACAC,QAAA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACA;EAAA;AAEA", "ignoreList": []}]}