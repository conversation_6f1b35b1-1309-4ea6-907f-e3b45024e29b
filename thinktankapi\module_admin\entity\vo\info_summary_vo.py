from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank, Size, Xss
from typing import Literal, Optional, List
from module_admin.annotation.pydantic_annotation import as_query


class InfoSummaryModel(BaseModel):
    """
    信息汇总表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='信息ID')
    title: Optional[str] = Field(default=None, description='信息标题')
    content: Optional[str] = Field(default=None, description='信息内容')
    summary: Optional[str] = Field(default=None, description='信息摘要')
    platform_type: Optional[Literal['news', 'weibo', 'wechat', 'video', 'app', 'forum', 'ecommerce', 'qa', 'other']] = Field(default=None, description='平台类型')
    source_name: Optional[str] = Field(default=None, description='来源名称')
    source_url: Optional[str] = Field(default=None, description='原始链接')
    publish_time: Optional[datetime] = Field(default=None, description='发布时间')
    sentiment: Optional[Literal['positive', 'neutral', 'negative']] = Field(default='neutral', description='情感倾向')
    info_attribute: Optional[Literal['official', 'media', 'user', 'competitor', 'industry', 'policy']] = Field(default=None, description='信息属性')
    views_count: Optional[int] = Field(default=0, description='浏览量')
    comments_count: Optional[int] = Field(default=0, description='评论数')
    shares_count: Optional[int] = Field(default=0, description='转发数')
    entity_type: Optional[Literal['brand', 'person', 'organization', 'product', 'event', 'topic']] = Field(default=None, description='关联实体类型')
    entity_name: Optional[str] = Field(default=None, description='关联实体名称')
    keywords: Optional[str] = Field(default=None, description='关键词(JSON格式)')
    images: Optional[str] = Field(default=None, description='图片链接(JSON格式)')
    status: Optional[Literal['0', '1']] = Field(default='1', description='状态(0停用 1正常)')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')

    @Xss(field_name='title', message='信息标题不能包含脚本字符')
    @NotBlank(field_name='title', message='信息标题不能为空')
    @Size(field_name='title', min_length=0, max_length=500, message='信息标题不能超过500个字符')
    def get_title(self):
        return self.title

    def validate_fields(self):
        self.get_title()


class InfoSummaryQueryModel(InfoSummaryModel):
    """
    信息汇总管理不分页查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
    platform_types: Optional[List[str]] = Field(default=None, description='平台类型列表')
    sentiment_types: Optional[List[str]] = Field(default=None, description='情感类型列表')
    info_attributes: Optional[List[str]] = Field(default=None, description='信息属性列表')
    keyword: Optional[str] = Field(default=None, description='搜索关键词')


@as_query
class InfoSummaryPageQueryModel(InfoSummaryQueryModel):
    """
    信息汇总管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteInfoSummaryModel(BaseModel):
    """
    删除信息汇总模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    info_ids: str = Field(description='需要删除的信息ID')


class InfoSummaryStatisticsModel(BaseModel):
    """
    信息汇总统计模型
    """

    model_config = ConfigDict(from_attributes=True)

    platform_type: str = Field(description='平台类型')
    total_count: int = Field(description='总数量')
    today_count: int = Field(description='今日数量')
    positive_count: int = Field(description='正面数量')
    neutral_count: int = Field(description='中性数量')
    negative_count: int = Field(description='负面数量')
