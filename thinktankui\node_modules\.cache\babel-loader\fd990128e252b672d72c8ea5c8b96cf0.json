{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core\\logger.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core\\logger.js", "mtime": 1749105929563}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5yZWR1Y2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIGxldmVscyA9IFsnZXJyb3InLCAnd2FybicsICdsb2cnLCAnaW5mbyddOwp2YXIgbGV2ZWwgPSAnd2Fybic7CmZ1bmN0aW9uIGRlYnVnKG1ldGhvZCkgewogIGlmIChsZXZlbCkgewogICAgaWYgKGxldmVscy5pbmRleE9mKG1ldGhvZCkgPD0gbGV2ZWxzLmluZGV4T2YobGV2ZWwpKSB7CiAgICAgIHZhciBfY29uc29sZTsKICAgICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiA+IDEgPyBfbGVuIC0gMSA6IDApLCBfa2V5ID0gMTsgX2tleSA8IF9sZW47IF9rZXkrKykgewogICAgICAgIGFyZ3NbX2tleSAtIDFdID0gYXJndW1lbnRzW19rZXldOwogICAgICB9CiAgICAgIChfY29uc29sZSA9IGNvbnNvbGUpW21ldGhvZF0uYXBwbHkoX2NvbnNvbGUsIGFyZ3MpOyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIG5vLWNvbnNvbGUKICAgIH0KICB9Cn0KZnVuY3Rpb24gbmFtZXNwYWNlKG5zKSB7CiAgcmV0dXJuIGxldmVscy5yZWR1Y2UoZnVuY3Rpb24gKGxvZ2dlciwgbWV0aG9kKSB7CiAgICBsb2dnZXJbbWV0aG9kXSA9IGRlYnVnLmJpbmQoY29uc29sZSwgbWV0aG9kLCBucyk7CiAgICByZXR1cm4gbG9nZ2VyOwogIH0sIHt9KTsKfQpuYW1lc3BhY2UubGV2ZWwgPSBmdW5jdGlvbiAobmV3TGV2ZWwpIHsKICBsZXZlbCA9IG5ld0xldmVsOwp9OwpkZWJ1Zy5sZXZlbCA9IG5hbWVzcGFjZS5sZXZlbDsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gbmFtZXNwYWNlOw=="}, {"version": 3, "names": ["levels", "level", "debug", "method", "indexOf", "_console", "_len", "arguments", "length", "args", "Array", "_key", "console", "apply", "namespace", "ns", "reduce", "logger", "bind", "newLevel", "_default", "exports", "default"], "sources": ["../../src/core/logger.ts"], "sourcesContent": ["const levels = ['error', 'warn', 'log', 'info'] as const;\nexport type DebugLevel = (typeof levels)[number];\nlet level: DebugLevel | false = 'warn';\n\nfunction debug(method: DebugLevel, ...args: unknown[]) {\n  if (level) {\n    if (levels.indexOf(method) <= levels.indexOf(level)) {\n      console[method](...args); // eslint-disable-line no-console\n    }\n  }\n}\n\nfunction namespace(\n  ns: string,\n): Record<DebugLevel, (...args: unknown[]) => void> {\n  return levels.reduce(\n    (logger, method) => {\n      logger[method] = debug.bind(console, method, ns);\n      return logger;\n    },\n    {} as Record<DebugLevel, (...args: unknown[]) => void>,\n  );\n}\n\nnamespace.level = (newLevel: DebugLevel | false) => {\n  level = newLevel;\n};\ndebug.level = namespace.level;\n\nexport default namespace;\n"], "mappings": ";;;;;;;;AAAA,IAAMA,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAU;AAExD,IAAIC,KAAyB,GAAG,MAAM;AAEtC,SAASC,KAAKA,CAACC,MAAkB,EAAsB;EACrD,IAAIF,KAAK,EAAE;IACT,IAAID,MAAM,CAACI,OAAO,CAACD,MAAM,CAAC,IAAIH,MAAM,CAACI,OAAO,CAACH,KAAK,CAAC,EAAE;MAAA,IAAAI,QAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAFnBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAGpC,CAAAN,QAAA,GAAAO,OAAO,EAACT,MAAM,CAAC,CAAAU,KAAA,CAAAR,QAAA,EAAII,IAAI,CAAC,CAAC,CAAC;IAC5B;EACF;AACF;AAEA,SAASK,SAASA,CAChBC,EAAU,EACwC;EAClD,OAAOf,MAAM,CAACgB,MAAM,CAClB,UAACC,MAAM,EAAEd,MAAM,EAAK;IAClBc,MAAM,CAACd,MAAM,CAAC,GAAGD,KAAK,CAACgB,IAAI,CAACN,OAAO,EAAET,MAAM,EAAEY,EAAE,CAAC;IAChD,OAAOE,MAAM;EACf,CAAC,EACD,CAAC,CACH,CAAC;AACH;AAEAH,SAAS,CAACb,KAAK,GAAI,UAAAkB,QAA4B,EAAK;EAClDlB,KAAK,GAAGkB,QAAQ;AAClB,CAAC;AACDjB,KAAK,CAACD,KAAK,GAAGa,SAAS,CAACb,KAAK;AAAA,IAAAmB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEdR,SAAS", "ignoreList": []}]}