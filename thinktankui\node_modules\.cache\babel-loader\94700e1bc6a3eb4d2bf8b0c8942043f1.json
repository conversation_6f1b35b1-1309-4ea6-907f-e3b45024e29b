{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\notice.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\system\\notice.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZE5vdGljZSA9IGFkZE5vdGljZTsKZXhwb3J0cy5kZWxOb3RpY2UgPSBkZWxOb3RpY2U7CmV4cG9ydHMuZ2V0Tm90aWNlID0gZ2V0Tm90aWNlOwpleHBvcnRzLmxpc3ROb3RpY2UgPSBsaXN0Tm90aWNlOwpleHBvcnRzLnVwZGF0ZU5vdGljZSA9IHVwZGF0ZU5vdGljZTsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouWFrOWRiuWIl+ihqApmdW5jdGlvbiBsaXN0Tm90aWNlKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL25vdGljZS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWFrOWRiuivpue7hgpmdW5jdGlvbiBnZXROb3RpY2Uobm90aWNlSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vbm90aWNlLycgKyBub3RpY2VJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5YWs5ZGKCmZ1bmN0aW9uIGFkZE5vdGljZShkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL25vdGljZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55YWs5ZGKCmZ1bmN0aW9uIHVwZGF0ZU5vdGljZShkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL25vdGljZScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlhazlkYoKZnVuY3Rpb24gZGVsTm90aWNlKG5vdGljZUlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL25vdGljZS8nICsgbm90aWNlSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listNotice", "query", "request", "url", "method", "params", "getNotice", "noticeId", "addNotice", "data", "updateNotice", "delNotice"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/system/notice.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询公告列表\nexport function listNotice(query) {\n  return request({\n    url: '/system/notice/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询公告详细\nexport function getNotice(noticeId) {\n  return request({\n    url: '/system/notice/' + noticeId,\n    method: 'get'\n  })\n}\n\n// 新增公告\nexport function addNotice(data) {\n  return request({\n    url: '/system/notice',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改公告\nexport function updateNotice(data) {\n  return request({\n    url: '/system/notice',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除公告\nexport function delNotice(noticeId) {\n  return request({\n    url: '/system/notice/' + noticeId,\n    method: 'delete'\n  })\n}"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACJ,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}