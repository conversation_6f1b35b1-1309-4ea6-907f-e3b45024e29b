{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\search-results\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\search-results\\index.vue", "mtime": 1748095893923}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2VhcmNoUmVzdWx0cyIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG9yaWdpbmFsVG9wTmF2OiB1bmRlZmluZWQsCiAgICAgIHNlYXJjaFF1ZXJ5OiAi5LuK5pelIOS4h+WFgyIsCiAgICAgIHNlbGVjdGVkVGltZTogIjI0aCIsCiAgICAgIHNlbGVjdGVkUGxhdGZvcm06ICJhbGwiLAogICAgICBzZWxlY3RlZEVtb3Rpb246ICJhbGwiLAogICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgcGFnZVNpemU6IDEwLAogICAgICB0b3RhbFJlc3VsdHM6IDEwMDAwLAoKICAgICAgdGltZU9wdGlvbnM6IFsKICAgICAgICB7IGxhYmVsOiAiMjTlsI/ml7YiLCB2YWx1ZTogIjI0aCIgfSwKICAgICAgICB7IGxhYmVsOiAi5LiA5ZGoIiwgdmFsdWU6ICIxdyIgfSwKICAgICAgICB7IGxhYmVsOiAi5Y2K5bm0IiwgdmFsdWU6ICI2bSIgfSwKICAgICAgICB7IGxhYmVsOiAi5LiA5bm0IiwgdmFsdWU6ICIxeSIgfSwKICAgICAgICB7IGxhYmVsOiAi6Ieq5a6a5LmJIiwgdmFsdWU6ICJjdXN0b20iIH0KICAgICAgXSwKCiAgICAgIHBsYXRmb3JtT3B0aW9uczogWwogICAgICAgIHsgbGFiZWw6ICLlhajpg6giLCB2YWx1ZTogImFsbCIsIGNvdW50OiAxMDU0MCB9LAogICAgICAgIHsgbGFiZWw6ICLlvq7kv6EiLCB2YWx1ZTogIndlY2hhdCIsIGNvdW50OiAxODQ3IH0sCiAgICAgICAgeyBsYWJlbDogIuW+ruWNmiIsIHZhbHVlOiAid2VpYm8iLCBjb3VudDogMjAwOCB9LAogICAgICAgIHsgbGFiZWw6ICLlrqLmiLfnq68iLCB2YWx1ZTogImFwcCIsIGNvdW50OiAxNzQ4IH0sCiAgICAgICAgeyBsYWJlbDogIuiuuuWdmyIsIHZhbHVlOiAiZm9ydW0iLCBjb3VudDogNjczIH0KICAgICAgXSwKCiAgICAgIGVtb3Rpb25PcHRpb25zOiBbCiAgICAgICAgeyBsYWJlbDogIuWFqOmDqCIsIHZhbHVlOiAiYWxsIiB9LAogICAgICAgIHsgbGFiZWw6ICLmraPpnaIiLCB2YWx1ZTogInBvc2l0aXZlIiB9LAogICAgICAgIHsgbGFiZWw6ICLotJ/pnaIiLCB2YWx1ZTogIm5lZ2F0aXZlIiB9LAogICAgICAgIHsgbGFiZWw6ICLkuK3mgKciLCB2YWx1ZTogIm5ldXRyYWwiIH0KICAgICAgXSwKCiAgICAgIHNlYXJjaFJlc3VsdHM6IFsKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogIuS7juaUv+W6nOmDqOmXqDQ05LiJ5Liq5Liq5L2T55qE54Ot54K56Zeu6aKY77yM5Yiw5aqS5L2TMe+8jOimhueblueahO+8iOWQq+S5ie+8ie+8jOaWl+S6ieS4u+S5ieWPo+WPt++8jOeJm+W5tOS4u+imgeeahOmXrumimO+8jOWFseWQjOS6uuW3peaZuuiDveeahOmXrumimOS6uuWRmO+8jOWmguWxseWxseeahOmXrumimOeahOS4u+imgemXrumimO+8jOaWsOW3peS6uu+8jOaJk+W3pe+8jOeUqOWPi+S4ieWuti4uLiIsCiAgICAgICAgICBzb3VyY2U6ICLmlrDljY7nvZEiLAogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDIyLTA2LTI5IDIwOjA3OjA0IiwKICAgICAgICAgIGF1dGhvcjogIjc35Lq66K6o6K66IiwKICAgICAgICAgIHBsYXRmb3JtOiAi5bmz5Y+w5p2l5rqQIiwKICAgICAgICAgIHJlYWRDb3VudDogIuaXoCIsCiAgICAgICAgICBsb2NhdGlvbjogIuaXoOaJgOWcqOWcsCIsCiAgICAgICAgICBjYXRlZ29yeTogIuaWsOmXuyIsCiAgICAgICAgICBjb250ZW50OiAi5LuO5pS/5bqc6YOo6ZeoNDTkuInkuKrkuKrkvZPnmoTng63ngrnpl67popjvvIzliLDlqpLkvZMx77yM6KaG55uW55qE77yI5ZCr5LmJ77yJ77yM5paX5LqJ5Li75LmJ5Y+j5Y+377yM54mb5bm05Li76KaB55qE6Zeu6aKY77yM5YWx5ZCM5Lq65bel5pm66IO955qE6Zeu6aKY5Lq65ZGY77yM5aaC5bGx5bGx55qE6Zeu6aKY55qE5Li76KaB6Zeu6aKY77yM5paw5bel5Lq677yM5omT5bel77yM55So5Y+L5LiJ5a62Li4uIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICLkuK3lpKct6K665paH5Y+R6KGoKDIwMjXlubTkuK3lpKforrrmloflj5Hooagp6Ieq54S25oyH5pWw5Lit5aSnLeiuuuaWh+WPkeihqOS4lueVjOWcsOS9jeiuuuWdmy0yMDI15bm055qE5Lu35YC85Lit5Zu96K665paHIiwKICAgICAgICAgIHNvdXJjZTogIuS4reWkp+iuuuaWh+WPkeihqCIsCiAgICAgICAgICBwdWJsaXNoVGltZTogIjIwMjItMDYtMjkgMjA6MDc6MDQiLAogICAgICAgICAgYXV0aG9yOiAiNzfkurrorqjorroiLAogICAgICAgICAgcGxhdGZvcm06ICLlubPlj7DmnaXmupAiLAogICAgICAgICAgcmVhZENvdW50OiAi5pegIiwKICAgICAgICAgIGxvY2F0aW9uOiAi5peg5omA5Zyo5ZywIiwKICAgICAgICAgIGNhdGVnb3J5OiAi6K665paHIiwKICAgICAgICAgIGNvbnRlbnQ6ICLkuK3lpKct6K665paH5Y+R6KGoKDIwMjXlubTkuK3lpKforrrmloflj5Hooagp6Ieq54S25oyH5pWw5Lit5aSnLeiuuuaWh+WPkeihqOS4lueVjOWcsOS9jeiuuuWdmy0yMDI15bm055qE5Lu35YC85Lit5Zu96K665paHLi4uIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICLovazlj5Hlvq7ljZoj5LitI+Wkp+WtpueUn++8jOS6uuaDheS4luaVheOAgiIsCiAgICAgICAgICBzb3VyY2U6ICLlvq7ljZoiLAogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDIyLTA2LTI5IDIwOjA3OjA0IiwKICAgICAgICAgIGF1dGhvcjogIjc35Lq66K6o6K66IiwKICAgICAgICAgIHBsYXRmb3JtOiAi5b6u5Y2aIiwKICAgICAgICAgIHJlYWRDb3VudDogIjEwMDAiLAogICAgICAgICAgbG9jYXRpb246ICLljJfkuqwiLAogICAgICAgICAgY2F0ZWdvcnk6ICLnpL7kuqTlqpLkvZMiLAogICAgICAgICAgY29udGVudDogIui9rOWPkeW+ruWNmiPkuK0j5aSn5a2m55Sf77yM5Lq65oOF5LiW5pWF44CC6L+Z5piv5LiA5p2h5YWz5LqO5aSn5a2m55Sf5Lq66ZmF5YWz57O755qE5b6u5Y2a5YaF5a65Li4uIgogICAgICAgIH0KICAgICAgXQogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvLyDpmpDol4/pobbpg6jlr7zoiKrmoI8KICAgIHRoaXMub3JpZ2luYWxUb3BOYXYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50b3BOYXYKICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICBrZXk6ICd0b3BOYXYnLAogICAgICB2YWx1ZTogZmFsc2UKICAgIH0pCgogICAgLy8g6I635Y+WVVJM5Y+C5pWw5Lit55qE5pCc57Si5YWz6ZSu6K+NCiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkucSkgewogICAgICB0aGlzLnNlYXJjaFF1ZXJ5ID0gdGhpcy4kcm91dGUucXVlcnkucTsKICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsKICAgIH0KICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4KICAgIGlmICh0aGlzLm9yaWdpbmFsVG9wTmF2ICE9PSB1bmRlZmluZWQpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAga2V5OiAndG9wTmF2JywKICAgICAgICB2YWx1ZTogdGhpcy5vcmlnaW5hbFRvcE5hdgogICAgICB9KQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlU2VhcmNoKCkgewogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaQnOe0ojogJHt0aGlzLnNlYXJjaFF1ZXJ5fWApOwogICAgICAvLyDlrp7pmYXmkJzntKLpgLvovpEKICAgIH0sCgogICAgc2VsZWN0VGltZSh2YWx1ZSkgewogICAgICB0aGlzLnNlbGVjdGVkVGltZSA9IHZhbHVlOwogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgfSwKCiAgICBzZWxlY3RQbGF0Zm9ybSh2YWx1ZSkgewogICAgICB0aGlzLnNlbGVjdGVkUGxhdGZvcm0gPSB2YWx1ZTsKICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsKICAgIH0sCgogICAgc2VsZWN0RW1vdGlvbih2YWx1ZSkgewogICAgICB0aGlzLnNlbGVjdGVkRW1vdGlvbiA9IHZhbHVlOwogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgfSwKCiAgICBoYW5kbGVQYWdlQ2hhbmdlKHBhZ2UpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IHBhZ2U7CiAgICAgIC8vIOWKoOi9veWvueW6lOmhtemdouaVsOaNrgogICAgfSwKCiAgICBnb0JhY2soKSB7CiAgICAgIC8vIOi/lOWbnuS4iuS4gOmhte+8jOWmguaenOayoeacieWOhuWPsuiusOW9leWImei/lOWbnuS/oeaBr+axh+aAu+mhtemdogogICAgICBpZiAod2luZG93Lmhpc3RvcnkubGVuZ3RoID4gMSkgewogICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9pbmZvLXN1bW1hcnknKTsKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/search-results", "sourcesContent": ["<template>\n  <div class=\"search-results-container\">\n    <!-- 搜索区域 -->\n    <div class=\"search-section\">\n      <div class=\"search-header\">\n        <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\" class=\"back-button\">\n          返回\n        </el-button>\n        <div class=\"search-tabs\">\n          <div class=\"tab-item active\">全文检索</div>\n          <div class=\"tab-item\">可视化</div>\n        </div>\n      </div>\n\n      <div class=\"search-box\">\n        <el-input\n          v-model=\"searchQuery\"\n          placeholder=\"请输入搜索关键词\"\n          class=\"search-input\"\n          @keyup.enter=\"handleSearch\"\n        >\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\n        </el-input>\n      </div>\n    </div>\n\n    <!-- 筛选条件区域 -->\n    <div class=\"filter-section\">\n      <!-- 时间筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">时间范围:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"time in timeOptions\"\n            :key=\"time.value\"\n            :type=\"selectedTime === time.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectTime(time.value)\"\n          >\n            {{ time.label }}\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 平台筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">平台类型:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"platform in platformOptions\"\n            :key=\"platform.value\"\n            :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectPlatform(platform.value)\"\n          >\n            {{ platform.label }}\n            <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 情感筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">情感类型:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"emotion in emotionOptions\"\n            :key=\"emotion.value\"\n            :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectEmotion(emotion.value)\"\n          >\n            {{ emotion.label }}\n            <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 其他筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">其他筛选:</span>\n        <div class=\"filter-options\">\n          <el-button size=\"small\">作者类型</el-button>\n          <el-button size=\"small\">地域</el-button>\n          <el-button size=\"small\">影响力</el-button>\n          <el-button size=\"small\">传播量</el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 结果统计 -->\n    <div class=\"result-stats\">\n      <span>共{{ totalResults }}条结果</span>\n      <div class=\"action-buttons\">\n        <el-button size=\"small\">导出</el-button>\n        <el-button type=\"primary\" size=\"small\">分析</el-button>\n      </div>\n    </div>\n\n    <!-- 搜索结果列表 -->\n    <div class=\"results-list\">\n      <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\n        <div class=\"result-header\">\n          <h3 class=\"result-title\">{{ item.title }}</h3>\n          <div class=\"result-actions\">\n            <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\n          </div>\n        </div>\n\n        <div class=\"result-meta\">\n          <span class=\"meta-item\">{{ item.source }}</span>\n          <span class=\"meta-item\">{{ item.publishTime }}</span>\n          <span class=\"meta-item\">{{ item.author }}</span>\n          <span class=\"meta-item\">{{ item.platform }}</span>\n          <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\n          <span class=\"meta-item\">{{ item.location }}</span>\n          <span class=\"meta-item\">{{ item.category }}</span>\n        </div>\n\n        <div class=\"result-content\">\n          {{ item.content }}\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        background\n        layout=\"prev, pager, next\"\n        :total=\"totalResults\"\n        :current-page.sync=\"currentPage\"\n        :page-size=\"pageSize\"\n        @current-change=\"handlePageChange\"\n      ></el-pagination>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"SearchResults\",\n  data() {\n    return {\n      originalTopNav: undefined,\n      searchQuery: \"今日 万元\",\n      selectedTime: \"24h\",\n      selectedPlatform: \"all\",\n      selectedEmotion: \"all\",\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 10000,\n\n      timeOptions: [\n        { label: \"24小时\", value: \"24h\" },\n        { label: \"一周\", value: \"1w\" },\n        { label: \"半年\", value: \"6m\" },\n        { label: \"一年\", value: \"1y\" },\n        { label: \"自定义\", value: \"custom\" }\n      ],\n\n      platformOptions: [\n        { label: \"全部\", value: \"all\", count: 10540 },\n        { label: \"微信\", value: \"wechat\", count: 1847 },\n        { label: \"微博\", value: \"weibo\", count: 2008 },\n        { label: \"客户端\", value: \"app\", count: 1748 },\n        { label: \"论坛\", value: \"forum\", count: 673 }\n      ],\n\n      emotionOptions: [\n        { label: \"全部\", value: \"all\" },\n        { label: \"正面\", value: \"positive\" },\n        { label: \"负面\", value: \"negative\" },\n        { label: \"中性\", value: \"neutral\" }\n      ],\n\n      searchResults: [\n        {\n          title: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\",\n          source: \"新华网\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"平台来源\",\n          readCount: \"无\",\n          location: \"无所在地\",\n          category: \"新闻\",\n          content: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\"\n        },\n        {\n          title: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文\",\n          source: \"中大论文发表\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"平台来源\",\n          readCount: \"无\",\n          location: \"无所在地\",\n          category: \"论文\",\n          content: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...\"\n        },\n        {\n          title: \"转发微博#中#大学生，人情世故。\",\n          source: \"微博\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"微博\",\n          readCount: \"1000\",\n          location: \"北京\",\n          category: \"社交媒体\",\n          content: \"转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...\"\n        }\n      ]\n    };\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n\n    // 获取URL参数中的搜索关键词\n    if (this.$route.query.q) {\n      this.searchQuery = this.$route.query.q;\n      this.handleSearch();\n    }\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.$message.success(`搜索: ${this.searchQuery}`);\n      // 实际搜索逻辑\n    },\n\n    selectTime(value) {\n      this.selectedTime = value;\n      this.handleSearch();\n    },\n\n    selectPlatform(value) {\n      this.selectedPlatform = value;\n      this.handleSearch();\n    },\n\n    selectEmotion(value) {\n      this.selectedEmotion = value;\n      this.handleSearch();\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page;\n      // 加载对应页面数据\n    },\n\n    goBack() {\n      // 返回上一页，如果没有历史记录则返回信息汇总页面\n      if (window.history.length > 1) {\n        this.$router.go(-1);\n      } else {\n        this.$router.push('/info-summary');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.search-results-container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.search-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.search-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.back-button {\n  margin-right: 20px;\n  color: #409EFF;\n  font-size: 14px;\n}\n\n.back-button:hover {\n  color: #66b1ff;\n}\n\n.search-tabs {\n  display: flex;\n}\n\n.tab-item {\n  padding: 8px 20px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  color: #666;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  border-bottom-color: #409EFF;\n}\n\n.search-box {\n  max-width: 600px;\n}\n\n.search-input {\n  width: 100%;\n}\n\n.filter-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.count {\n  color: #999;\n  font-size: 12px;\n}\n\n.result-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 15px 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.results-list {\n  background: white;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n\n.result-item {\n  padding: 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.result-item:last-child {\n  border-bottom: none;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.result-title {\n  font-size: 16px;\n  color: #333;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n  margin-right: 20px;\n}\n\n.result-meta {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: #999;\n}\n\n.meta-item {\n  white-space: nowrap;\n}\n\n.result-content {\n  color: #666;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  background: white;\n  padding: 20px;\n  border-radius: 4px;\n}\n</style>\n"]}]}