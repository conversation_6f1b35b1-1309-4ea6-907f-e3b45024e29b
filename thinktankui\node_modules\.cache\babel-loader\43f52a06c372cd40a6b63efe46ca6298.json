{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\search-results\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\search-results\\index.vue", "mtime": 1748095893923}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "originalTopNav", "undefined", "searchQuery", "selectedTime", "selectedPlatform", "selectedEmotion", "currentPage", "pageSize", "totalResults", "timeOptions", "label", "value", "platformOptions", "count", "emotionOptions", "searchResults", "title", "source", "publishTime", "author", "platform", "readCount", "location", "category", "content", "mounted", "$store", "state", "settings", "topNav", "dispatch", "key", "$route", "query", "q", "handleSearch", "<PERSON><PERSON><PERSON><PERSON>", "methods", "$message", "success", "concat", "selectTime", "selectPlatform", "selectEmotion", "handlePageChange", "page", "goBack", "window", "history", "length", "$router", "go", "push"], "sources": ["src/views/search-results/index.vue"], "sourcesContent": ["<template>\n  <div class=\"search-results-container\">\n    <!-- 搜索区域 -->\n    <div class=\"search-section\">\n      <div class=\"search-header\">\n        <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\" class=\"back-button\">\n          返回\n        </el-button>\n        <div class=\"search-tabs\">\n          <div class=\"tab-item active\">全文检索</div>\n          <div class=\"tab-item\">可视化</div>\n        </div>\n      </div>\n\n      <div class=\"search-box\">\n        <el-input\n          v-model=\"searchQuery\"\n          placeholder=\"请输入搜索关键词\"\n          class=\"search-input\"\n          @keyup.enter=\"handleSearch\"\n        >\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\n        </el-input>\n      </div>\n    </div>\n\n    <!-- 筛选条件区域 -->\n    <div class=\"filter-section\">\n      <!-- 时间筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">时间范围:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"time in timeOptions\"\n            :key=\"time.value\"\n            :type=\"selectedTime === time.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectTime(time.value)\"\n          >\n            {{ time.label }}\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 平台筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">平台类型:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"platform in platformOptions\"\n            :key=\"platform.value\"\n            :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectPlatform(platform.value)\"\n          >\n            {{ platform.label }}\n            <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 情感筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">情感类型:</span>\n        <div class=\"filter-options\">\n          <el-button\n            v-for=\"emotion in emotionOptions\"\n            :key=\"emotion.value\"\n            :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\n            size=\"small\"\n            @click=\"selectEmotion(emotion.value)\"\n          >\n            {{ emotion.label }}\n            <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 其他筛选 -->\n      <div class=\"filter-row\">\n        <span class=\"filter-label\">其他筛选:</span>\n        <div class=\"filter-options\">\n          <el-button size=\"small\">作者类型</el-button>\n          <el-button size=\"small\">地域</el-button>\n          <el-button size=\"small\">影响力</el-button>\n          <el-button size=\"small\">传播量</el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 结果统计 -->\n    <div class=\"result-stats\">\n      <span>共{{ totalResults }}条结果</span>\n      <div class=\"action-buttons\">\n        <el-button size=\"small\">导出</el-button>\n        <el-button type=\"primary\" size=\"small\">分析</el-button>\n      </div>\n    </div>\n\n    <!-- 搜索结果列表 -->\n    <div class=\"results-list\">\n      <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\n        <div class=\"result-header\">\n          <h3 class=\"result-title\">{{ item.title }}</h3>\n          <div class=\"result-actions\">\n            <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\n          </div>\n        </div>\n\n        <div class=\"result-meta\">\n          <span class=\"meta-item\">{{ item.source }}</span>\n          <span class=\"meta-item\">{{ item.publishTime }}</span>\n          <span class=\"meta-item\">{{ item.author }}</span>\n          <span class=\"meta-item\">{{ item.platform }}</span>\n          <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\n          <span class=\"meta-item\">{{ item.location }}</span>\n          <span class=\"meta-item\">{{ item.category }}</span>\n        </div>\n\n        <div class=\"result-content\">\n          {{ item.content }}\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        background\n        layout=\"prev, pager, next\"\n        :total=\"totalResults\"\n        :current-page.sync=\"currentPage\"\n        :page-size=\"pageSize\"\n        @current-change=\"handlePageChange\"\n      ></el-pagination>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"SearchResults\",\n  data() {\n    return {\n      originalTopNav: undefined,\n      searchQuery: \"今日 万元\",\n      selectedTime: \"24h\",\n      selectedPlatform: \"all\",\n      selectedEmotion: \"all\",\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 10000,\n\n      timeOptions: [\n        { label: \"24小时\", value: \"24h\" },\n        { label: \"一周\", value: \"1w\" },\n        { label: \"半年\", value: \"6m\" },\n        { label: \"一年\", value: \"1y\" },\n        { label: \"自定义\", value: \"custom\" }\n      ],\n\n      platformOptions: [\n        { label: \"全部\", value: \"all\", count: 10540 },\n        { label: \"微信\", value: \"wechat\", count: 1847 },\n        { label: \"微博\", value: \"weibo\", count: 2008 },\n        { label: \"客户端\", value: \"app\", count: 1748 },\n        { label: \"论坛\", value: \"forum\", count: 673 }\n      ],\n\n      emotionOptions: [\n        { label: \"全部\", value: \"all\" },\n        { label: \"正面\", value: \"positive\" },\n        { label: \"负面\", value: \"negative\" },\n        { label: \"中性\", value: \"neutral\" }\n      ],\n\n      searchResults: [\n        {\n          title: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\",\n          source: \"新华网\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"平台来源\",\n          readCount: \"无\",\n          location: \"无所在地\",\n          category: \"新闻\",\n          content: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\"\n        },\n        {\n          title: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文\",\n          source: \"中大论文发表\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"平台来源\",\n          readCount: \"无\",\n          location: \"无所在地\",\n          category: \"论文\",\n          content: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...\"\n        },\n        {\n          title: \"转发微博#中#大学生，人情世故。\",\n          source: \"微博\",\n          publishTime: \"2022-06-29 20:07:04\",\n          author: \"77人讨论\",\n          platform: \"微博\",\n          readCount: \"1000\",\n          location: \"北京\",\n          category: \"社交媒体\",\n          content: \"转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...\"\n        }\n      ]\n    };\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n\n    // 获取URL参数中的搜索关键词\n    if (this.$route.query.q) {\n      this.searchQuery = this.$route.query.q;\n      this.handleSearch();\n    }\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.$message.success(`搜索: ${this.searchQuery}`);\n      // 实际搜索逻辑\n    },\n\n    selectTime(value) {\n      this.selectedTime = value;\n      this.handleSearch();\n    },\n\n    selectPlatform(value) {\n      this.selectedPlatform = value;\n      this.handleSearch();\n    },\n\n    selectEmotion(value) {\n      this.selectedEmotion = value;\n      this.handleSearch();\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page;\n      // 加载对应页面数据\n    },\n\n    goBack() {\n      // 返回上一页，如果没有历史记录则返回信息汇总页面\n      if (window.history.length > 1) {\n        this.$router.go(-1);\n      } else {\n        this.$router.push('/info-summary');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.search-results-container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.search-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.search-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.back-button {\n  margin-right: 20px;\n  color: #409EFF;\n  font-size: 14px;\n}\n\n.back-button:hover {\n  color: #66b1ff;\n}\n\n.search-tabs {\n  display: flex;\n}\n\n.tab-item {\n  padding: 8px 20px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  color: #666;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  border-bottom-color: #409EFF;\n}\n\n.search-box {\n  max-width: 600px;\n}\n\n.search-input {\n  width: 100%;\n}\n\n.filter-section {\n  background: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.count {\n  color: #999;\n  font-size: 12px;\n}\n\n.result-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 15px 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.results-list {\n  background: white;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n\n.result-item {\n  padding: 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.result-item:last-child {\n  border-bottom: none;\n}\n\n.result-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.result-title {\n  font-size: 16px;\n  color: #333;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n  margin-right: 20px;\n}\n\n.result-meta {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: #999;\n}\n\n.meta-item {\n  white-space: nowrap;\n}\n\n.result-content {\n  color: #666;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  background: white;\n  padding: 20px;\n  border-radius: 4px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA4IA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA,EAAAC,SAAA;MACAC,WAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,QAAA;MACAC,YAAA;MAEAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MAEAC,eAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,EACA;MAEAC,cAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MAEAI,aAAA,GACA;QACAC,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;MACA,GACA;QACAR,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;MACA,GACA;QACAR,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAzB,cAAA,QAAA0B,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACApB,KAAA;IACA;;IAEA;IACA,SAAAqB,MAAA,CAAAC,KAAA,CAAAC,CAAA;MACA,KAAAhC,WAAA,QAAA8B,MAAA,CAAAC,KAAA,CAAAC,CAAA;MACA,KAAAC,YAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAApC,cAAA,KAAAC,SAAA;MACA,KAAAyB,MAAA,CAAAI,QAAA;QACAC,GAAA;QACApB,KAAA,OAAAX;MACA;IACA;EACA;EACAqC,OAAA;IACAF,YAAA,WAAAA,aAAA;MACA,KAAAG,QAAA,CAAAC,OAAA,kBAAAC,MAAA,MAAAtC,WAAA;MACA;IACA;IAEAuC,UAAA,WAAAA,WAAA9B,KAAA;MACA,KAAAR,YAAA,GAAAQ,KAAA;MACA,KAAAwB,YAAA;IACA;IAEAO,cAAA,WAAAA,eAAA/B,KAAA;MACA,KAAAP,gBAAA,GAAAO,KAAA;MACA,KAAAwB,YAAA;IACA;IAEAQ,aAAA,WAAAA,cAAAhC,KAAA;MACA,KAAAN,eAAA,GAAAM,KAAA;MACA,KAAAwB,YAAA;IACA;IAEAS,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAvC,WAAA,GAAAuC,IAAA;MACA;IACA;IAEAC,MAAA,WAAAA,OAAA;MACA;MACA,IAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA;QACA,KAAAC,OAAA,CAAAC,EAAA;MACA;QACA,KAAAD,OAAA,CAAAE,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}