{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\user\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RVc2VyLCBnZXRVc2VyLCBkZWxVc2VyLCBhZGRVc2VyLCB1cGRhdGVVc2VyLCByZXNldFVzZXJQd2QsIGNoYW5nZVVzZXJTdGF0dXMsIGRlcHRUcmVlU2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCB7IFNwbGl0cGFuZXMsIFBhbmUgfSBmcm9tICJzcGxpdHBhbmVzIjsKaW1wb3J0ICJzcGxpdHBhbmVzL2Rpc3Qvc3BsaXRwYW5lcy5jc3MiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJVc2VyIiwKICBkaWN0czogWydzeXNfbm9ybWFsX2Rpc2FibGUnLCAnc3lzX3VzZXJfc2V4J10sCiAgY29tcG9uZW50czogeyBUcmVlc2VsZWN0LCBTcGxpdHBhbmVzLCBQYW5lIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnlKjmiLfooajmoLzmlbDmja4KICAgICAgdXNlckxpc3Q6IG51bGwsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaJgOaciemDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogdW5kZWZpbmVkLAogICAgICAvLyDov4fmu6Tmjonlt7LnpoHnlKjpg6jpl6jmoJHpgInpobkKICAgICAgZW5hYmxlZERlcHRPcHRpb25zOiB1bmRlZmluZWQsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOm7mOiupOWvhueggQogICAgICBpbml0UGFzc3dvcmQ6IHVuZGVmaW5lZCwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOWyl+S9jemAiemhuQogICAgICBwb3N0T3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIKICAgICAgfSwKICAgICAgLy8g55So5oi35a+85YWl5Y+C5pWwCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKAKICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE55So5oi35pWw5o2uCiAgICAgICAgdXBkYXRlU3VwcG9ydDogZmFsc2UsCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sCiAgICAgICAgLy8g5LiK5Lyg55qE5Zyw5Z2ACiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9zeXN0ZW0vdXNlci9pbXBvcnREYXRhIgogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g5YiX5L+h5oGvCiAgICAgIGNvbHVtbnM6IFsKICAgICAgICB7IGtleTogMCwgbGFiZWw6IGDnlKjmiLfnvJblj7dgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6IDEsIGxhYmVsOiBg55So5oi35ZCN56ewYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAyLCBsYWJlbDogYOeUqOaIt+aYteensGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogMywgbGFiZWw6IGDpg6jpl6hgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6IDQsIGxhYmVsOiBg5omL5py65Y+356CBYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiA1LCBsYWJlbDogYOeKtuaAgWAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogNiwgbGFiZWw6IGDliJvlu7rml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0KICAgICAgXSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdXNlck5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn55So5oi35ZCN56ew6ZW/5bqm5b+F6aG75LuL5LqOIDIg5ZKMIDIwIOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBuaWNrTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+aYteensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+WvhueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDUsIG1heDogMjAsIG1lc3NhZ2U6ICfnlKjmiLflr4bnoIHplb/luqblv4Xpobvku4vkuo4gNSDlkowgMjAg5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IHBhdHRlcm46IC9eW148PiInfFxcXSskLywgbWVzc2FnZTogIuS4jeiDveWMheWQq+mdnuazleWtl+espu+8mjwgPiBcIiAnIFxcXCB8IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGVtYWlsOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICJlbWFpbCIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTpgq7nrrHlnLDlnYAiLAogICAgICAgICAgICB0cmlnZ2VyOiBbImJsdXIiLCAiY2hhbmdlIl0KICAgICAgICAgIH0KICAgICAgICBdLAogICAgICAgIHBob25lbnVtYmVyOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICAvLyDmoLnmja7lkI3np7DnrZvpgInpg6jpl6jmoJEKICAgIGRlcHROYW1lKHZhbCkgewogICAgICB0aGlzLiRyZWZzLnRyZWUuZmlsdGVyKHZhbCk7CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERlcHRUcmVlKCk7CiAgICB0aGlzLmdldENvbmZpZ0tleSgic3lzLnVzZXIuaW5pdFBhc3N3b3JkIikudGhlbihyZXNwb25zZSA9PiB7CiAgICAgIHRoaXMuaW5pdFBhc3N3b3JkID0gcmVzcG9uc2UubXNnOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i55So5oi35YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0VXNlcih0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICk7CiAgICB9LAogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqLwogICAgZ2V0RGVwdFRyZWUoKSB7CiAgICAgIGRlcHRUcmVlU2VsZWN0KCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5lbmFibGVkRGVwdE9wdGlvbnMgPSB0aGlzLmZpbHRlckRpc2FibGVkRGVwdChKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLmRhdGEpKSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi/h+a7pOemgeeUqOeahOmDqOmXqAogICAgZmlsdGVyRGlzYWJsZWREZXB0KGRlcHRMaXN0KSB7CiAgICAgIHJldHVybiBkZXB0TGlzdC5maWx0ZXIoZGVwdCA9PiB7CiAgICAgICAgaWYgKGRlcHQuZGlzYWJsZWQpIHsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgICAgaWYgKGRlcHQuY2hpbGRyZW4gJiYgZGVwdC5jaGlsZHJlbi5sZW5ndGgpIHsKICAgICAgICAgIGRlcHQuY2hpbGRyZW4gPSB0aGlzLmZpbHRlckRpc2FibGVkRGVwdChkZXB0LmNoaWxkcmVuKTsKICAgICAgICB9CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOetm+mAieiKgueCuQogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgfSwKICAgIC8vIOiKgueCueWNleWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBkYXRhLmlkOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g55So5oi354q25oCB5L+u5pS5CiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy51c2VyTmFtZSArICci55So5oi35ZCX77yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gY2hhbmdlVXNlclN0YXR1cyhyb3cudXNlcklkLCByb3cuc3RhdHVzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbigpIHsKICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsCiAgICAgICAgcGhvbmVudW1iZXI6IHVuZGVmaW5lZCwKICAgICAgICBlbWFpbDogdW5kZWZpbmVkLAogICAgICAgIHNleDogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIHJlbWFyazogdW5kZWZpbmVkLAogICAgICAgIHBvc3RJZHM6IFtdLAogICAgICAgIHJvbGVJZHM6IFtdCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuJHJlZnMudHJlZS5zZXRDdXJyZW50S2V5KG51bGwpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udXNlcklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvLyDmm7TlpJrmk43kvZzop6blj5EKICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCwgcm93KSB7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgImhhbmRsZVJlc2V0UHdkIjoKICAgICAgICAgIHRoaXMuaGFuZGxlUmVzZXRQd2Qocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImhhbmRsZUF1dGhSb2xlIjoKICAgICAgICAgIHRoaXMuaGFuZGxlQXV0aFJvbGUocm93KTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBnZXRVc2VyKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOwogICAgICAgIHRoaXMucm9sZU9wdGlvbnMgPSByZXNwb25zZS5yb2xlczsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg55So5oi3IjsKICAgICAgICB0aGlzLmZvcm0ucGFzc3dvcmQgPSB0aGlzLmluaXRQYXNzd29yZDsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IHVzZXJJZCA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldFVzZXIodXNlcklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMucG9zdE9wdGlvbnMgPSByZXNwb25zZS5wb3N0czsKICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzcG9uc2Uucm9sZXM7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInBvc3RJZHMiLCByZXNwb25zZS5wb3N0SWRzKTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAicm9sZUlkcyIsIHJlc3BvbnNlLnJvbGVJZHMpOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnlKjmiLciOwogICAgICAgIHRoaXMuZm9ybS5wYXNzd29yZCA9IHVuZGVmaW5lZDsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOmHjee9ruWvhueggeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUmVzZXRQd2Qocm93KSB7CiAgICAgIHRoaXMuJHByb21wdCgn6K+36L6T5YWlIicgKyByb3cudXNlck5hbWUgKyAnIueahOaWsOWvhueggScsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIGNsb3NlT25DbGlja01vZGFsOiBmYWxzZSwKICAgICAgICBpbnB1dFBhdHRlcm46IC9eLns1LDIwfSQvLAogICAgICAgIGlucHV0RXJyb3JNZXNzYWdlOiAi55So5oi35a+G56CB6ZW/5bqm5b+F6aG75LuL5LqOIDUg5ZKMIDIwIOS5i+mXtCIsCiAgICAgICAgaW5wdXRWYWxpZGF0b3I6ICh2YWx1ZSkgPT4gewogICAgICAgICAgaWYgKC88fD58InwnfFx8fFxcLy50ZXN0KHZhbHVlKSkgewogICAgICAgICAgICByZXR1cm4gIuS4jeiDveWMheWQq+mdnuazleWtl+espu+8mjwgPiBcIiAnIFxcXCB8IgogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgIH0pLnRoZW4oKHsgdmFsdWUgfSkgPT4gewogICAgICAgICAgcmVzZXRVc2VyUHdkKHJvdy51c2VySWQsIHZhbHVlKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5Yqf77yM5paw5a+G56CB5piv77yaIiArIHZhbHVlKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5YiG6YWN6KeS6Imy5pON5L2cICovCiAgICBoYW5kbGVBdXRoUm9sZTogZnVuY3Rpb24ocm93KSB7CiAgICAgIGNvbnN0IHVzZXJJZCA9IHJvdy51c2VySWQ7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvc3lzdGVtL3VzZXItYXV0aC9yb2xlLyIgKyB1c2VySWQpOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS51c2VySWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHVwZGF0ZVVzZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRVc2VyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCB1c2VySWRzID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk55So5oi357yW5Y+35Li6IicgKyB1c2VySWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxVc2VyKHVzZXJJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3VzZXIvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYHVzZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLnlKjmiLflr7zlhaUiOwogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5LiL6L295qih5p2/5pON5L2cICovCiAgICBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3VzZXIvaW1wb3J0VGVtcGxhdGUnLCB7CiAgICAgIH0sIGB1c2VyX3RlbXBsYXRlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhgogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgIHRoaXMuJGFsZXJ0KCI8ZGl2IHN0eWxlPSdvdmVyZmxvdzogYXV0bztvdmVyZmxvdy14OiBoaWRkZW47bWF4LWhlaWdodDogNzB2aDtwYWRkaW5nOiAxMHB4IDIwcHggMDsnPiIgKyByZXNwb25zZS5tc2cgKyAiPC9kaXY+IiwgIuWvvOWFpee7k+aenCIsIHsgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlIH0pOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YKICAgIHN1Ym1pdEZpbGVGb3JtKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2VA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <splitpanes :horizontal=\"this.$store.getters.device === 'mobile'\" class=\"default-theme\">\n        <!--部门数据-->\n        <pane size=\"16\">\n          <div class=\"head-container\">\n            <el-input\n              v-model=\"deptName\"\n              placeholder=\"请输入部门名称\"\n              clearable\n              size=\"small\"\n              prefix-icon=\"el-icon-search\"\n              style=\"margin-bottom: 20px\"\n            />\n          </div>\n          <div class=\"head-container\">\n            <el-tree\n              :data=\"deptOptions\"\n              :props=\"defaultProps\"\n              :expand-on-click-node=\"false\"\n              :filter-node-method=\"filterNode\"\n              ref=\"tree\"\n              node-key=\"id\"\n              default-expand-all\n              highlight-current\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </pane>\n        <!--用户数据-->\n        <pane size=\"84\">\n          <el-col>\n            <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n              <el-form-item label=\"用户名称\" prop=\"userName\">\n                <el-input\n                  v-model=\"queryParams.userName\"\n                  placeholder=\"请输入用户名称\"\n                  clearable\n                  style=\"width: 240px\"\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n              <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n                <el-input\n                  v-model=\"queryParams.phonenumber\"\n                  placeholder=\"请输入手机号码\"\n                  clearable\n                  style=\"width: 240px\"\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n              <el-form-item label=\"状态\" prop=\"status\">\n                <el-select\n                  v-model=\"queryParams.status\"\n                  placeholder=\"用户状态\"\n                  clearable\n                  style=\"width: 240px\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.sys_normal_disable\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"创建时间\">\n                <el-date-picker\n                  v-model=\"dateRange\"\n                  style=\"width: 240px\"\n                  value-format=\"yyyy-MM-dd\"\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                ></el-date-picker>\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-form>\n\n            <el-row :gutter=\"10\" class=\"mb8\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  plain\n                  icon=\"el-icon-plus\"\n                  size=\"mini\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['system:user:add']\"\n                >新增</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"success\"\n                  plain\n                  icon=\"el-icon-edit\"\n                  size=\"mini\"\n                  :disabled=\"single\"\n                  @click=\"handleUpdate\"\n                  v-hasPermi=\"['system:user:edit']\"\n                >修改</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"danger\"\n                  plain\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['system:user:remove']\"\n                >删除</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"info\"\n                  plain\n                  icon=\"el-icon-upload2\"\n                  size=\"mini\"\n                  @click=\"handleImport\"\n                  v-hasPermi=\"['system:user:import']\"\n                >导入</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"warning\"\n                  plain\n                  icon=\"el-icon-download\"\n                  size=\"mini\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:user:export']\"\n                >导出</el-button>\n              </el-col>\n              <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\n            </el-row>\n\n            <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n              <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" />\n              <el-table-column label=\"用户名称\" align=\"center\" key=\"userName\" prop=\"userName\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\n              <el-table-column label=\"用户昵称\" align=\"center\" key=\"nickName\" prop=\"nickName\" v-if=\"columns[2].visible\" :show-overflow-tooltip=\"true\" />\n              <el-table-column label=\"部门\" align=\"center\" key=\"deptName\" prop=\"dept.deptName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\n              <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\n              <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[5].visible\">\n                <template slot-scope=\"scope\">\n                  <el-switch\n                    v-model=\"scope.row.status\"\n                    active-value=\"0\"\n                    inactive-value=\"1\"\n                    @change=\"handleStatusChange(scope.row)\"\n                  ></el-switch>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[6].visible\" width=\"160\">\n                <template slot-scope=\"scope\">\n                  <span>{{ parseTime(scope.row.createTime) }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column\n                label=\"操作\"\n                align=\"center\"\n                width=\"160\"\n                class-name=\"small-padding fixed-width\"\n              >\n                <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-edit\"\n                    @click=\"handleUpdate(scope.row)\"\n                    v-hasPermi=\"['system:user:edit']\"\n                  >修改</el-button>\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    @click=\"handleDelete(scope.row)\"\n                    v-hasPermi=\"['system:user:remove']\"\n                  >删除</el-button>\n                  <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:user:resetPwd', 'system:user:edit']\">\n                    <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\n                    <el-dropdown-menu slot=\"dropdown\">\n                      <el-dropdown-item command=\"handleResetPwd\" icon=\"el-icon-key\"\n                        v-hasPermi=\"['system:user:resetPwd']\">重置密码</el-dropdown-item>\n                      <el-dropdown-item command=\"handleAuthRole\" icon=\"el-icon-circle-check\"\n                        v-hasPermi=\"['system:user:edit']\">分配角色</el-dropdown-item>\n                    </el-dropdown-menu>\n                  </el-dropdown>\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <pagination\n              v-show=\"total>0\"\n              :total=\"total\"\n              :page.sync=\"queryParams.pageNum\"\n              :limit.sync=\"queryParams.pageSize\"\n              @pagination=\"getList\"\n            />\n          </el-col>\n        </pane>\n      </splitpanes>\n    </el-row>\n\n    <!-- 添加或修改用户配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户昵称\" prop=\"nickName\">\n              <el-input v-model=\"form.nickName\" placeholder=\"请输入用户昵称\" maxlength=\"30\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"归属部门\" prop=\"deptId\">\n              <treeselect v-model=\"form.deptId\" :options=\"enabledDeptOptions\" :show-count=\"true\" placeholder=\"请选择归属部门\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n              <el-input v-model=\"form.phonenumber\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"邮箱\" prop=\"email\">\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户名称\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户名称\" maxlength=\"30\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户密码\" prop=\"password\">\n              <el-input v-model=\"form.password\" placeholder=\"请输入用户密码\" type=\"password\" maxlength=\"20\" show-password/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_user_sex\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"岗位\">\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择岗位\">\n                <el-option\n                  v-for=\"item in postOptions\"\n                  :key=\"item.postId\"\n                  :label=\"item.postName\"\n                  :value=\"item.postId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择角色\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 用户导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\n          <div class=\"el-upload__tip\" slot=\"tip\">\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\n          </div>\n          <span>仅允许导入xls、xlsx格式文件。</span>\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from \"@/api/system/user\";\nimport { getToken } from \"@/utils/auth\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport { Splitpanes, Pane } from \"splitpanes\";\nimport \"splitpanes/dist/splitpanes.css\";\n\nexport default {\n  name: \"User\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  components: { Treeselect, Splitpanes, Pane },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 所有部门树选项\n      deptOptions: undefined,\n      // 过滤掉已禁用部门树选项\n      enabledDeptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 岗位选项\n      postOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: false,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\"\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: undefined,\n        phonenumber: undefined,\n        status: undefined,\n        deptId: undefined\n      },\n      // 列信息\n      columns: [\n        { key: 0, label: `用户编号`, visible: true },\n        { key: 1, label: `用户名称`, visible: true },\n        { key: 2, label: `用户昵称`, visible: true },\n        { key: 3, label: `部门`, visible: true },\n        { key: 4, label: `手机号码`, visible: true },\n        { key: 5, label: `状态`, visible: true },\n        { key: 6, label: `创建时间`, visible: true }\n      ],\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"用户名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        nickName: [\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\n        ],\n        password: [\n          { required: true, message: \"用户密码不能为空\", trigger: \"blur\" },\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        email: [\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phonenumber: [\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  watch: {\n    // 根据名称筛选部门树\n    deptName(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.getList();\n    this.getDeptTree();\n    this.getConfigKey(\"sys.user.initPassword\").then(response => {\n      this.initPassword = response.msg;\n    });\n  },\n  methods: {\n    /** 查询用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 查询部门下拉树结构 */\n    getDeptTree() {\n      deptTreeSelect().then(response => {\n        this.deptOptions = response.data;\n        this.enabledDeptOptions = this.filterDisabledDept(JSON.parse(JSON.stringify(response.data)));\n      });\n    },\n    // 过滤禁用的部门\n    filterDisabledDept(deptList) {\n      return deptList.filter(dept => {\n        if (dept.disabled) {\n          return false;\n        }\n        if (dept.children && dept.children.length) {\n          dept.children = this.filterDisabledDept(dept.children);\n        }\n        return true;\n      });\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      this.queryParams.deptId = data.id;\n      this.handleQuery();\n    },\n    // 用户状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.userName + '\"用户吗？').then(function() {\n        return changeUserStatus(row.userId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        password: undefined,\n        phonenumber: undefined,\n        email: undefined,\n        sex: undefined,\n        status: \"0\",\n        remark: undefined,\n        postIds: [],\n        roleIds: []\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.queryParams.deptId = undefined;\n      this.$refs.tree.setCurrentKey(null);\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.userId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleResetPwd\":\n          this.handleResetPwd(row);\n          break;\n        case \"handleAuthRole\":\n          this.handleAuthRole(row);\n          break;\n        default:\n          break;\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      getUser().then(response => {\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.open = true;\n        this.title = \"添加用户\";\n        this.form.password = this.initPassword;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const userId = row.userId || this.ids;\n      getUser(userId).then(response => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.$set(this.form, \"postIds\", response.postIds);\n        this.$set(this.form, \"roleIds\", response.roleIds);\n        this.open = true;\n        this.title = \"修改用户\";\n        this.form.password = undefined;\n      });\n    },\n    /** 重置密码按钮操作 */\n    handleResetPwd(row) {\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        closeOnClickModal: false,\n        inputPattern: /^.{5,20}$/,\n        inputErrorMessage: \"用户密码长度必须介于 5 和 20 之间\",\n        inputValidator: (value) => {\n          if (/<|>|\"|'|\\||\\\\/.test(value)) {\n            return \"不能包含非法字符：< > \\\" ' \\\\\\ |\"\n          }\n        },\n      }).then(({ value }) => {\n          resetUserPwd(row.userId, value).then(response => {\n            this.$modal.msgSuccess(\"修改成功，新密码是：\" + value);\n          });\n        }).catch(() => {});\n    },\n    /** 分配角色操作 */\n    handleAuthRole: function(row) {\n      const userId = row.userId;\n      this.$router.push(\"/system/user-auth/role/\" + userId);\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.userId != undefined) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const userIds = row.userId || this.ids;\n      this.$modal.confirm('是否确认删除用户编号为\"' + userIds + '\"的数据项？').then(function() {\n        return delUser(userIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/user/export', {\n        ...this.queryParams\n      }, `user_${new Date().getTime()}.xlsx`)\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.download('system/user/importTemplate', {\n      }, `user_template_${new Date().getTime()}.xlsx`)\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    }\n  }\n};\n</script>"]}]}