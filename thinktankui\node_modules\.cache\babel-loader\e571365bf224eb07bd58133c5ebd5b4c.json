{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_role", "require", "_menu", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "roleList", "title", "open", "openDataScope", "menuExpand", "menuNodeAll", "deptExpand", "deptNodeAll", "date<PERSON><PERSON><PERSON>", "dataScopeOptions", "value", "label", "menuOptions", "deptOptions", "queryParams", "pageNum", "pageSize", "<PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "status", "form", "defaultProps", "children", "rules", "required", "message", "trigger", "roleSort", "created", "getList", "methods", "_this", "listRole", "addDateRange", "then", "response", "rows", "getMenuTreeselect", "_this2", "menuTreeselect", "getMenuAllCheckedKeys", "checked<PERSON>eys", "$refs", "menu", "getChe<PERSON><PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "unshift", "apply", "getDeptAllCheckedKeys", "dept", "getRoleMenuTreeselect", "roleId", "_this3", "roleMenuTreeselect", "menus", "getDeptTree", "_this4", "deptTreeSelect", "depts", "handleStatusChange", "row", "_this5", "text", "$modal", "confirm", "changeRoleStatus", "msgSuccess", "catch", "cancel", "reset", "cancelDataScope", "set<PERSON><PERSON><PERSON><PERSON>eys", "menuIds", "deptIds", "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleCommand", "command", "handleDataScope", "handleAuthUser", "handleCheckedTreeExpand", "type", "treeList", "i", "store", "nodesMap", "id", "expanded", "handleCheckedTreeNodeAll", "setCheckedNodes", "handleCheckedTreeConnect", "handleAdd", "handleUpdate", "_this6", "roleMenu", "getRole", "$nextTick", "res", "for<PERSON>ach", "v", "setChecked", "dataScopeSelectChange", "_this7", "$router", "push", "submitForm", "_this8", "validate", "valid", "updateRole", "addRole", "submitDataScope", "_this9", "dataScope", "handleDelete", "_this0", "roleIds", "delRole", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/system/role/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\n        <el-input\n          v-model=\"queryParams.roleName\"\n          placeholder=\"请输入角色名称\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\n        <el-input\n          v-model=\"queryParams.roleKey\"\n          placeholder=\"请输入权限字符\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"角色状态\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:role:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:role:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:role:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:role:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"0\"\n            inactive-value=\"1\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\" v-if=\"scope.row.roleId !== 1\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:role:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:role:remove']\"\n          >删除</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:role:edit']\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"handleDataScope\" icon=\"el-icon-circle-check\"\n                v-hasPermi=\"['system:role:edit']\">数据权限</el-dropdown-item>\n              <el-dropdown-item command=\"handleAuthUser\" icon=\"el-icon-user\"\n                v-hasPermi=\"['system:role:edit']\">分配用户</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改角色配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\n        </el-form-item>\n        <el-form-item prop=\"roleKey\">\n          <span slot=\"label\">\n            <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n            权限字符\n          </span>\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\n        </el-form-item>\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"菜单权限\">\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"menuOptions\"\n            show-checkbox\n            ref=\"menu\"\n            node-key=\"id\"\n            :check-strictly=\"!form.menuCheckStrictly\"\n            empty-text=\"加载中，请稍候\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分配角色数据权限对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"角色名称\">\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限字符\">\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限范围\">\n          <el-select v-model=\"form.dataScope\" @change=\"dataScopeSelectChange\">\n            <el-option\n              v-for=\"item in dataScopeOptions\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"deptOptions\"\n            show-checkbox\n            default-expand-all\n            ref=\"dept\"\n            node-key=\"id\"\n            :check-strictly=\"!form.deptCheckStrictly\"\n            empty-text=\"加载中，请稍候\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from \"@/api/system/role\";\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\";\n\nexport default {\n  name: \"Role\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 角色表格数据\n      roleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示弹出层（数据权限）\n      openDataScope: false,\n      menuExpand: false,\n      menuNodeAll: false,\n      deptExpand: true,\n      deptNodeAll: false,\n      // 日期范围\n      dateRange: [],\n      // 数据范围选项\n      dataScopeOptions: [\n        {\n          value: \"1\",\n          label: \"全部数据权限\"\n        },\n        {\n          value: \"2\",\n          label: \"自定数据权限\"\n        },\n        {\n          value: \"3\",\n          label: \"本部门数据权限\"\n        },\n        {\n          value: \"4\",\n          label: \"本部门及以下数据权限\"\n        },\n        {\n          value: \"5\",\n          label: \"仅本人数据权限\"\n        }\n      ],\n      // 菜单列表\n      menuOptions: [],\n      // 部门列表\n      deptOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleName: undefined,\n        roleKey: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 表单校验\n      rules: {\n        roleName: [\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\n        ],\n        roleKey: [\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\n        ],\n        roleSort: [\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询角色列表 */\n    getList() {\n      this.loading = true;\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.roleList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    /** 查询菜单树结构 */\n    getMenuTreeselect() {\n      menuTreeselect().then(response => {\n        this.menuOptions = response.data;\n      });\n    },\n    // 所有菜单节点数据\n    getMenuAllCheckedKeys() {\n      // 目前被选中的菜单节点\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\n      // 半选中的菜单节点\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    // 所有部门节点数据\n    getDeptAllCheckedKeys() {\n      // 目前被选中的部门节点\n      let checkedKeys = this.$refs.dept.getCheckedKeys();\n      // 半选中的部门节点\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    /** 根据角色ID查询菜单树结构 */\n    getRoleMenuTreeselect(roleId) {\n      return roleMenuTreeselect(roleId).then(response => {\n        this.menuOptions = response.menus;\n        return response;\n      });\n    },\n    /** 根据角色ID查询部门树结构 */\n    getDeptTree(roleId) {\n      return deptTreeSelect(roleId).then(response => {\n        this.deptOptions = response.depts;\n        return response;\n      });\n    },\n    // 角色状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗？').then(function() {\n        return changeRoleStatus(row.roleId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 取消按钮（数据权限）\n    cancelDataScope() {\n      this.openDataScope = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      if (this.$refs.menu != undefined) {\n        this.$refs.menu.setCheckedKeys([]);\n      }\n      this.menuExpand = false,\n      this.menuNodeAll = false,\n      this.deptExpand = true,\n      this.deptNodeAll = false,\n      this.form = {\n        roleId: undefined,\n        roleName: undefined,\n        roleKey: undefined,\n        roleSort: 0,\n        status: \"0\",\n        menuIds: [],\n        deptIds: [],\n        menuCheckStrictly: true,\n        deptCheckStrictly: true,\n        remark: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.roleId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleDataScope\":\n          this.handleDataScope(row);\n          break;\n        case \"handleAuthUser\":\n          this.handleAuthUser(row);\n          break;\n        default:\n          break;\n      }\n    },\n    // 树权限（展开/折叠）\n    handleCheckedTreeExpand(value, type) {\n      if (type == 'menu') {\n        let treeList = this.menuOptions;\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      } else if (type == 'dept') {\n        let treeList = this.deptOptions;\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      }\n    },\n    // 树权限（全选/全不选）\n    handleCheckedTreeNodeAll(value, type) {\n      if (type == 'menu') {\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);\n      } else if (type == 'dept') {\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);\n      }\n    },\n    // 树权限（父子联动）\n    handleCheckedTreeConnect(value, type) {\n      if (type == 'menu') {\n        this.form.menuCheckStrictly = value ? true: false;\n      } else if (type == 'dept') {\n        this.form.deptCheckStrictly = value ? true: false;\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.getMenuTreeselect();\n      this.open = true;\n      this.title = \"添加角色\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const roleId = row.roleId || this.ids\n      const roleMenu = this.getRoleMenuTreeselect(roleId);\n      getRole(roleId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.$nextTick(() => {\n          roleMenu.then(res => {\n            let checkedKeys = res.checkedKeys\n            checkedKeys.forEach((v) => {\n                this.$nextTick(()=>{\n                    this.$refs.menu.setChecked(v, true ,false);\n                })\n            })\n          });\n        });\n      });\n      this.title = \"修改角色\";\n    },\n    /** 选择角色权限范围触发 */\n    dataScopeSelectChange(value) {\n      if(value !== '2') {\n        this.$refs.dept.setCheckedKeys([]);\n      }\n    },\n    /** 分配数据权限操作 */\n    handleDataScope(row) {\n      this.reset();\n      const deptTreeSelect = this.getDeptTree(row.roleId);\n      getRole(row.roleId).then(response => {\n        this.form = response.data;\n        this.openDataScope = true;\n        this.$nextTick(() => {\n          deptTreeSelect.then(res => {\n            this.$refs.dept.setCheckedKeys(res.checkedKeys);\n          });\n        });\n      });\n      this.title = \"分配数据权限\";\n    },\n    /** 分配用户操作 */\n    handleAuthUser: function(row) {\n      const roleId = row.roleId;\n      this.$router.push(\"/system/role-auth/user/\" + roleId);\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.roleId != undefined) {\n            this.form.menuIds = this.getMenuAllCheckedKeys();\n            updateRole(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            this.form.menuIds = this.getMenuAllCheckedKeys();\n            addRole(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 提交按钮（数据权限） */\n    submitDataScope: function() {\n      if (this.form.roleId != undefined) {\n        this.form.deptIds = this.getDeptAllCheckedKeys();\n        dataScope(this.form).then(response => {\n          this.$modal.msgSuccess(\"修改成功\");\n          this.openDataScope = false;\n          this.getList();\n        });\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const roleIds = row.roleId || this.ids;\n      this.$modal.confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项？').then(function() {\n        return delRole(roleIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/role/export', {\n        ...this.queryParams\n      }, `role_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;;;;AA8PA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACAC,YAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACA;MACAa,KAAA;QACAP,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,OAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtC,OAAA;MACA,IAAAuC,cAAA,OAAAC,YAAA,MAAApB,WAAA,OAAAN,SAAA,GAAA2B,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAhC,QAAA,GAAAoC,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAjC,KAAA,GAAAqC,QAAA,CAAArC,KAAA;QACAiC,KAAA,CAAAtC,OAAA;MACA,CACA;IACA;IACA,cACA4C,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,IAAAL,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAA3B,WAAA,GAAAwB,QAAA,CAAA3C,IAAA;MACA;IACA;IACA;IACAgD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAC,WAAA,QAAAC,KAAA,CAAAC,IAAA,CAAAC,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAC,IAAA,CAAAG,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA;IACAQ,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAR,WAAA,QAAAC,KAAA,CAAAQ,IAAA,CAAAN,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAQ,IAAA,CAAAJ,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA,oBACAU,qBAAA,WAAAA,sBAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,wBAAA,EAAAF,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAA1C,WAAA,GAAAwB,QAAA,CAAAoB,KAAA;QACA,OAAApB,QAAA;MACA;IACA;IACA,oBACAqB,WAAA,WAAAA,YAAAJ,MAAA;MAAA,IAAAK,MAAA;MACA,WAAAC,oBAAA,EAAAN,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAA7C,WAAA,GAAAuB,QAAA,CAAAwB,KAAA;QACA,OAAAxB,QAAA;MACA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA1C,MAAA;MACA,KAAA6C,MAAA,CAAAC,OAAA,UAAAF,IAAA,UAAAF,GAAA,CAAA7C,QAAA,YAAAkB,IAAA;QACA,WAAAgC,sBAAA,EAAAL,GAAA,CAAAT,MAAA,EAAAS,GAAA,CAAA1C,MAAA;MACA,GAAAe,IAAA;QACA4B,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAJ,IAAA;MACA,GAAAK,KAAA;QACAP,GAAA,CAAA1C,MAAA,GAAA0C,GAAA,CAAA1C,MAAA;MACA;IACA;IACA;IACAkD,MAAA,WAAAA,OAAA;MACA,KAAApE,IAAA;MACA,KAAAqE,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAArE,aAAA;MACA,KAAAoE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAA5B,KAAA,CAAAC,IAAA,IAAA1B,SAAA;QACA,KAAAyB,KAAA,CAAAC,IAAA,CAAA6B,cAAA;MACA;MACA,KAAArE,UAAA,UACA,KAAAC,WAAA,UACA,KAAAC,UAAA,SACA,KAAAC,WAAA,UACA,KAAAc,IAAA;QACAgC,MAAA,EAAAnC,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAU,QAAA;QACAR,MAAA;QACAsD,OAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,MAAA,EAAA5D;MACA;MACA,KAAA6D,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAmD,UAAA,WAAAA,WAAA;MACA,KAAAzE,SAAA;MACA,KAAAuE,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxF,GAAA,GAAAwF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhC,MAAA;MAAA;MACA,KAAAzD,MAAA,GAAAuF,SAAA,CAAAG,MAAA;MACA,KAAAzF,QAAA,IAAAsF,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAC,OAAA,EAAA1B,GAAA;MACA,QAAA0B,OAAA;QACA;UACA,KAAAC,eAAA,CAAA3B,GAAA;UACA;QACA;UACA,KAAA4B,cAAA,CAAA5B,GAAA;UACA;QACA;UACA;MACA;IACA;IACA;IACA6B,uBAAA,WAAAA,wBAAAjF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,IAAAC,QAAA,QAAAjF,WAAA;QACA,SAAAkF,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAAP,MAAA,EAAAQ,CAAA;UACA,KAAAnD,KAAA,CAAAC,IAAA,CAAAmD,KAAA,CAAAC,QAAA,CAAAH,QAAA,CAAAC,CAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA,WAAAkF,IAAA;QACA,IAAAC,SAAA,QAAAhF,WAAA;QACA,SAAAiF,EAAA,MAAAA,EAAA,GAAAD,SAAA,CAAAP,MAAA,EAAAQ,EAAA;UACA,KAAAnD,KAAA,CAAAQ,IAAA,CAAA4C,KAAA,CAAAC,QAAA,CAAAH,SAAA,CAAAC,EAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA;IACA;IACA;IACAyF,wBAAA,WAAAA,yBAAAzF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAjD,KAAA,CAAAC,IAAA,CAAAwD,eAAA,CAAA1F,KAAA,QAAAE,WAAA;MACA,WAAAgF,IAAA;QACA,KAAAjD,KAAA,CAAAQ,IAAA,CAAAiD,eAAA,CAAA1F,KAAA,QAAAG,WAAA;MACA;IACA;IACA;IACAwF,wBAAA,WAAAA,yBAAA3F,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAvE,IAAA,CAAAuD,iBAAA,GAAAlE,KAAA;MACA,WAAAkF,IAAA;QACA,KAAAvE,IAAA,CAAAwD,iBAAA,GAAAnE,KAAA;MACA;IACA;IACA,aACA4F,SAAA,WAAAA,UAAA;MACA,KAAA/B,KAAA;MACA,KAAAjC,iBAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsG,YAAA,WAAAA,aAAAzC,GAAA;MAAA,IAAA0C,MAAA;MACA,KAAAjC,KAAA;MACA,IAAAlB,MAAA,GAAAS,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,IAAA8G,QAAA,QAAArD,qBAAA,CAAAC,MAAA;MACA,IAAAqD,aAAA,EAAArD,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAoE,MAAA,CAAAnF,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACA+G,MAAA,CAAAtG,IAAA;QACAsG,MAAA,CAAAG,SAAA;UACAF,QAAA,CAAAtE,IAAA,WAAAyE,GAAA;YACA,IAAAlE,WAAA,GAAAkE,GAAA,CAAAlE,WAAA;YACAA,WAAA,CAAAmE,OAAA,WAAAC,CAAA;cACAN,MAAA,CAAAG,SAAA;gBACAH,MAAA,CAAA7D,KAAA,CAAAC,IAAA,CAAAmE,UAAA,CAAAD,CAAA;cACA;YACA;UACA;QACA;MACA;MACA,KAAA7G,KAAA;IACA;IACA,iBACA+G,qBAAA,WAAAA,sBAAAtG,KAAA;MACA,IAAAA,KAAA;QACA,KAAAiC,KAAA,CAAAQ,IAAA,CAAAsB,cAAA;MACA;IACA;IACA,eACAgB,eAAA,WAAAA,gBAAA3B,GAAA;MAAA,IAAAmD,MAAA;MACA,KAAA1C,KAAA;MACA,IAAAZ,cAAA,QAAAF,WAAA,CAAAK,GAAA,CAAAT,MAAA;MACA,IAAAqD,aAAA,EAAA5C,GAAA,CAAAT,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACA6E,MAAA,CAAA5F,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACAwH,MAAA,CAAA9G,aAAA;QACA8G,MAAA,CAAAN,SAAA;UACAhD,cAAA,CAAAxB,IAAA,WAAAyE,GAAA;YACAK,MAAA,CAAAtE,KAAA,CAAAQ,IAAA,CAAAsB,cAAA,CAAAmC,GAAA,CAAAlE,WAAA;UACA;QACA;MACA;MACA,KAAAzC,KAAA;IACA;IACA;IACAyF,cAAA,WAAAA,eAAA5B,GAAA;MACA,IAAAT,MAAA,GAAAS,GAAA,CAAAT,MAAA;MACA,KAAA6D,OAAA,CAAAC,IAAA,6BAAA9D,MAAA;IACA;IACA;IACA+D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA1E,KAAA,SAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAhG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;YACAmG,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAA+E,gBAAA,EAAAH,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;YACAuF,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAAgF,aAAA,EAAAJ,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA4F,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;QACA,KAAAG,IAAA,CAAAsD,OAAA,QAAAzB,qBAAA;QACA,IAAA0E,eAAA,OAAAvG,IAAA,EAAAc,IAAA,WAAAC,QAAA;UACAuF,MAAA,CAAA1D,MAAA,CAAAG,UAAA;UACAuD,MAAA,CAAAxH,aAAA;UACAwH,MAAA,CAAA7F,OAAA;QACA;MACA;IACA;IACA,aACA+F,YAAA,WAAAA,aAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAC,OAAA,GAAAjE,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,KAAAsE,MAAA,CAAAC,OAAA,kBAAA6D,OAAA,aAAA5F,IAAA;QACA,WAAA6F,aAAA,EAAAD,OAAA;MACA,GAAA5F,IAAA;QACA2F,MAAA,CAAAhG,OAAA;QACAgG,MAAA,CAAA7D,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAtH,WAAA,WAAAuH,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}