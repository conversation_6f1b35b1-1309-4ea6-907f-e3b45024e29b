{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\menu.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\menu.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmdldFJvdXRlcnMgPSB2b2lkIDA7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDojrflj5bot6/nlLEKdmFyIGdldFJvdXRlcnMgPSBleHBvcnRzLmdldFJvdXRlcnMgPSBmdW5jdGlvbiBnZXRSb3V0ZXJzKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2dldFJvdXRlcnMnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9Ow=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getRouters", "exports", "request", "url", "method"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/menu.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取路由\nexport const getRouters = () => {\n  return request({\n    url: '/getRouters',\n    method: 'get'\n  })\n}"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,IAAMC,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAbA,UAAUA,CAAA,EAAS;EAC9B,OAAO,IAAAE,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}]}