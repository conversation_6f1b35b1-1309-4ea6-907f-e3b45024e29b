{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\index.vue", "mtime": 1749114975231}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnZhciBfbmV3cyA9IHJlcXVpcmUoIkAvYXBpL25ld3MiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdOZXdzJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOaWsOmXu+WIl+ihqAogICAgICBuZXdzTGlzdDogW10sCiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICBzdGF0aXN0aWNzOiBudWxsLAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdGl0bGU6IG51bGwsCiAgICAgICAga2V5d29yZDogbnVsbCwKICAgICAgICBzZW50aW1lbnQ6IG51bGwsCiAgICAgICAgaW5mb0F0dHJpYnV0ZTogbnVsbCwKICAgICAgICBlbnRpdHlOYW1lOiBudWxsLAogICAgICAgIGJlZ2luVGltZTogbnVsbCwKICAgICAgICBlbmRUaW1lOiBudWxsLAogICAgICAgIG9yZGVyQnk6ICdwdWJsaXNoVGltZScsCiAgICAgICAgb3JkZXJEaXJlY3Rpb246ICdkZXNjJwogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRTdGF0aXN0aWNzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5paw6Ze75YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgLy8g5aSE55CG5pe26Ze06IyD5Zu0CiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJlZ2luVGltZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYmVnaW5UaW1lID0gbnVsbDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBudWxsOwogICAgICB9CiAgICAgICgwLCBfbmV3cy5nZXROZXdzTGlzdCkodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5uZXdzTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5bnu5/orqHmlbDmja4gKi9nZXRTdGF0aXN0aWNzOiBmdW5jdGlvbiBnZXRTdGF0aXN0aWNzKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgKDAsIF9uZXdzLmdldE5ld3NTdGF0aXN0aWNzKSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLnN0YXRpc3RpY3MgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLyoqIOWIt+aWsOaMiemSruaTjeS9nCAqL2hhbmRsZVJlZnJlc2g6IGZ1bmN0aW9uIGhhbmRsZVJlZnJlc2goKSB7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKTsKICAgIH0sCiAgICAvKiog5paw6Ze754K55Ye75LqL5Lu2ICovaGFuZGxlTmV3c0NsaWNrOiBmdW5jdGlvbiBoYW5kbGVOZXdzQ2xpY2sobmV3c0lkKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAiL25ld3MvZGV0YWlsLyIuY29uY2F0KG5ld3NJZCkKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOagvOW8j+WMluaXtumXtCAqL2Zvcm1hdFRpbWU6IGZ1bmN0aW9uIGZvcm1hdFRpbWUodGltZSkgewogICAgICBpZiAoIXRpbWUpIHJldHVybiAnJzsKICAgICAgcmV0dXJuIHRoaXMucGFyc2VUaW1lKHRpbWUsICd7eX0te219LXtkfSB7aH06e2l9Jyk7CiAgICB9LAogICAgLyoqIOiOt+WPluaDheaEn+aWh+acrCAqL2dldFNlbnRpbWVudFRleHQ6IGZ1bmN0aW9uIGdldFNlbnRpbWVudFRleHQoc2VudGltZW50KSB7CiAgICAgIHZhciBzZW50aW1lbnRNYXAgPSB7CiAgICAgICAgcG9zaXRpdmU6ICfmraPpnaInLAogICAgICAgIG5ldXRyYWw6ICfkuK3mgKcnLAogICAgICAgIG5lZ2F0aXZlOiAn6LSf6Z2iJwogICAgICB9OwogICAgICByZXR1cm4gc2VudGltZW50TWFwW3NlbnRpbWVudF0gfHwgJ+acquefpSc7CiAgICB9LAogICAgLyoqIOWbvueJh+WKoOi9vemUmeivr+WkhOeQhiAqL2hhbmRsZUltYWdlRXJyb3I6IGZ1bmN0aW9uIGhhbmRsZUltYWdlRXJyb3IoZXZlbnQpIHsKICAgICAgZXZlbnQudGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_news", "require", "name", "data", "loading", "showSearch", "total", "newsList", "statistics", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "title", "keyword", "sentiment", "infoAttribute", "entityName", "beginTime", "endTime", "orderBy", "orderDirection", "created", "getList", "getStatistics", "methods", "_this", "length", "getNewsList", "then", "response", "rows", "_this2", "getNewsStatistics", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleRefresh", "handleNewsClick", "newsId", "$router", "push", "path", "concat", "formatTime", "time", "parseTime", "getSentimentText", "sentimentMap", "positive", "neutral", "negative", "handleImageError", "event", "target", "style", "display"], "sources": ["src/views/news/index.vue"], "sourcesContent": ["<template>\n  <div class=\"news-container\">\n    <!-- 搜索区域 -->\n    <div class=\"search-section\">\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n        <el-form-item label=\"新闻标题\" prop=\"title\">\n          <el-input\n            v-model=\"queryParams.title\"\n            placeholder=\"请输入新闻标题\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"关键词\" prop=\"keyword\">\n          <el-input\n            v-model=\"queryParams.keyword\"\n            placeholder=\"请输入关键词\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"情感倾向\" prop=\"sentiment\">\n          <el-select v-model=\"queryParams.sentiment\" placeholder=\"请选择情感倾向\" clearable>\n            <el-option label=\"正面\" value=\"positive\" />\n            <el-option label=\"中性\" value=\"neutral\" />\n            <el-option label=\"负面\" value=\"negative\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"信息属性\" prop=\"infoAttribute\">\n          <el-select v-model=\"queryParams.infoAttribute\" placeholder=\"请选择信息属性\" clearable>\n            <el-option label=\"官方发布\" value=\"official\" />\n            <el-option label=\"媒体报道\" value=\"media\" />\n            <el-option label=\"用户评价\" value=\"user\" />\n            <el-option label=\"竞品信息\" value=\"competitor\" />\n            <el-option label=\"行业动态\" value=\"industry\" />\n            <el-option label=\"政策法规\" value=\"policy\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"实体名称\" prop=\"entityName\">\n          <el-input\n            v-model=\"queryParams.entityName\"\n            placeholder=\"请输入实体名称\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"发布时间\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n          ></el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <!-- 操作区域 -->\n    <div class=\"toolbar-section\">\n      <div class=\"toolbar-left\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-refresh\"\n          size=\"mini\"\n          @click=\"handleRefresh\"\n        >刷新</el-button>\n      </div>\n      <div class=\"toolbar-right\">\n        <el-tooltip class=\"item\" effect=\"dark\" content=\"显示/隐藏搜索\" placement=\"top\">\n          <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"showSearch=!showSearch\" />\n        </el-tooltip>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"statistics-section\" v-if=\"statistics\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-number\">{{ statistics.totalCount }}</div>\n            <div class=\"stat-label\">总新闻数</div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-number\">{{ statistics.todayCount }}</div>\n            <div class=\"stat-label\">今日新增</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"stat-card positive\">\n            <div class=\"stat-number\">{{ statistics.positiveCount }}</div>\n            <div class=\"stat-label\">正面</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"stat-card neutral\">\n            <div class=\"stat-number\">{{ statistics.neutralCount }}</div>\n            <div class=\"stat-label\">中性</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"stat-card negative\">\n            <div class=\"stat-number\">{{ statistics.negativeCount }}</div>\n            <div class=\"stat-label\">负面</div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 新闻列表 -->\n    <div class=\"news-list-section\">\n      <div class=\"news-list\">\n        <div \n          v-for=\"news in newsList\" \n          :key=\"news.id\" \n          class=\"news-item\"\n          @click=\"handleNewsClick(news.id)\"\n        >\n          <div class=\"news-content\">\n            <div class=\"news-header\">\n              <h3 class=\"news-title\" v-html=\"news.title\"></h3>\n              <div class=\"news-meta\">\n                <span class=\"news-source\">{{ news.sourceName }}</span>\n                <span class=\"news-time\">{{ formatTime(news.publishTime) }}</span>\n                <span class=\"sentiment-tag\" :class=\"'sentiment-' + news.sentiment\">\n                  {{ getSentimentText(news.sentiment) }}\n                </span>\n              </div>\n            </div>\n            <div class=\"news-summary\" v-if=\"news.summary\" v-html=\"news.summary\"></div>\n            <div class=\"news-footer\">\n              <div class=\"news-stats\">\n                <span><i class=\"el-icon-view\"></i> {{ news.viewsCount }}</span>\n                <span><i class=\"el-icon-chat-line-square\"></i> {{ news.commentsCount }}</span>\n              </div>\n              <div class=\"news-images\" v-if=\"news.images && news.images.length > 0\">\n                <img \n                  v-for=\"(img, index) in news.images.slice(0, 3)\" \n                  :key=\"index\" \n                  :src=\"img\" \n                  class=\"news-image\"\n                  @error=\"handleImageError\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <pagination\n        v-show=\"total > 0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getNewsList, getNewsStatistics } from '@/api/news'\n\nexport default {\n  name: 'News',\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 新闻列表\n      newsList: [],\n      // 统计数据\n      statistics: null,\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null,\n        keyword: null,\n        sentiment: null,\n        infoAttribute: null,\n        entityName: null,\n        beginTime: null,\n        endTime: null,\n        orderBy: 'publishTime',\n        orderDirection: 'desc'\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getStatistics()\n  },\n  methods: {\n    /** 查询新闻列表 */\n    getList() {\n      this.loading = true\n      // 处理时间范围\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.beginTime = this.dateRange[0]\n        this.queryParams.endTime = this.dateRange[1]\n      } else {\n        this.queryParams.beginTime = null\n        this.queryParams.endTime = null\n      }\n\n      getNewsList(this.queryParams).then(response => {\n        this.newsList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 获取统计数据 */\n    getStatistics() {\n      getNewsStatistics().then(response => {\n        this.statistics = response.data\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = []\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    /** 刷新按钮操作 */\n    handleRefresh() {\n      this.getList()\n      this.getStatistics()\n    },\n    /** 新闻点击事件 */\n    handleNewsClick(newsId) {\n      this.$router.push({ path: `/news/detail/${newsId}` })\n    },\n    /** 格式化时间 */\n    formatTime(time) {\n      if (!time) return ''\n      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}')\n    },\n    /** 获取情感文本 */\n    getSentimentText(sentiment) {\n      const sentimentMap = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      }\n      return sentimentMap[sentiment] || '未知'\n    },\n    /** 图片加载错误处理 */\n    handleImageError(event) {\n      event.target.style.display = 'none'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.news-container {\n  padding: 20px;\n}\n\n.search-section {\n  background: #fff;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.1);\n}\n\n.toolbar-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.statistics-section {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: #fff;\n    padding: 20px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0,0,0,0.1);\n    text-align: center;\n    \n    .stat-number {\n      font-size: 24px;\n      font-weight: bold;\n      color: #409eff;\n    }\n    \n    .stat-label {\n      font-size: 14px;\n      color: #666;\n      margin-top: 5px;\n    }\n    \n    &.positive .stat-number {\n      color: #67c23a;\n    }\n    \n    &.neutral .stat-number {\n      color: #909399;\n    }\n    \n    &.negative .stat-number {\n      color: #f56c6c;\n    }\n  }\n}\n\n.news-list-section {\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.1);\n}\n\n.news-list {\n  .news-item {\n    padding: 20px;\n    border-bottom: 1px solid #ebeef5;\n    cursor: pointer;\n    transition: background-color 0.3s;\n    \n    &:hover {\n      background-color: #f5f7fa;\n    }\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n  \n  .news-header {\n    margin-bottom: 10px;\n    \n    .news-title {\n      font-size: 18px;\n      font-weight: 500;\n      color: #303133;\n      margin: 0 0 8px 0;\n      line-height: 1.4;\n    }\n    \n    .news-meta {\n      display: flex;\n      align-items: center;\n      font-size: 13px;\n      color: #909399;\n      \n      span {\n        margin-right: 15px;\n      }\n      \n      .sentiment-tag {\n        padding: 2px 8px;\n        border-radius: 12px;\n        font-size: 12px;\n        \n        &.sentiment-positive {\n          background: #f0f9ff;\n          color: #67c23a;\n        }\n        \n        &.sentiment-neutral {\n          background: #f4f4f5;\n          color: #909399;\n        }\n        \n        &.sentiment-negative {\n          background: #fef0f0;\n          color: #f56c6c;\n        }\n      }\n    }\n  }\n  \n  .news-summary {\n    color: #606266;\n    line-height: 1.6;\n    margin-bottom: 10px;\n  }\n  \n  .news-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    \n    .news-stats {\n      display: flex;\n      align-items: center;\n      font-size: 13px;\n      color: #909399;\n      \n      span {\n        margin-right: 15px;\n        \n        i {\n          margin-right: 4px;\n        }\n      }\n    }\n    \n    .news-images {\n      display: flex;\n      \n      .news-image {\n        width: 60px;\n        height: 60px;\n        object-fit: cover;\n        border-radius: 4px;\n        margin-left: 8px;\n      }\n    }\n  }\n}\n\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"], "mappings": ";;;;;;;AA2KA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,aAAA;QACAC,UAAA;QACAC,SAAA;QACAC,OAAA;QACAC,OAAA;QACAC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAtB,OAAA;MACA;MACA,SAAAK,SAAA,SAAAA,SAAA,CAAAkB,MAAA;QACA,KAAAjB,WAAA,CAAAQ,SAAA,QAAAT,SAAA;QACA,KAAAC,WAAA,CAAAS,OAAA,QAAAV,SAAA;MACA;QACA,KAAAC,WAAA,CAAAQ,SAAA;QACA,KAAAR,WAAA,CAAAS,OAAA;MACA;MAEA,IAAAS,iBAAA,OAAAlB,WAAA,EAAAmB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAnB,QAAA,GAAAuB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAApB,KAAA,GAAAwB,QAAA,CAAAxB,KAAA;QACAoB,KAAA,CAAAtB,OAAA;MACA;IACA;IACA,aACAoB,aAAA,WAAAA,cAAA;MAAA,IAAAQ,MAAA;MACA,IAAAC,uBAAA,IAAAJ,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAxB,UAAA,GAAAsB,QAAA,CAAA3B,IAAA;MACA;IACA;IACA,aACA+B,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAY,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAA1B,SAAA;MACA,KAAA2B,SAAA;MACA,KAAAF,WAAA;IACA;IACA,aACAG,aAAA,WAAAA,cAAA;MACA,KAAAd,OAAA;MACA,KAAAC,aAAA;IACA;IACA,aACAc,eAAA,WAAAA,gBAAAC,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA,kBAAAC,MAAA,CAAAJ,MAAA;MAAA;IACA;IACA,YACAK,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,YAAAC,SAAA,CAAAD,IAAA;IACA;IACA,aACAE,gBAAA,WAAAA,iBAAAhC,SAAA;MACA,IAAAiC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,OAAAH,YAAA,CAAAjC,SAAA;IACA;IACA,eACAqC,gBAAA,WAAAA,iBAAAC,KAAA;MACAA,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}