{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\modules\\input.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\modules\\input.js", "mtime": 1749105929559}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_module", "_quill", "_keyboard", "INSERT_TYPES", "Input", "_Module", "quill", "options", "_this", "_classCallCheck2", "default", "_callSuper2", "root", "addEventListener", "event", "handleBeforeInput", "test", "navigator", "userAgent", "on", "<PERSON><PERSON><PERSON>", "events", "COMPOSITION_BEFORE_START", "handleCompositionStart", "_inherits2", "_createClass2", "key", "value", "deleteRange", "range", "replaceText", "text", "arguments", "length", "undefined", "formats", "getFormat", "index", "updateContents", "Delta", "retain", "insert", "sources", "USER", "setSelection", "SILENT", "composition", "isComposing", "defaultPrevented", "includes", "inputType", "staticRange", "getTargetRanges", "collapsed", "getPlainTextFromInputEvent", "normalized", "selection", "normalizeNative", "normalizedToRange", "preventDefault", "getSelection", "<PERSON><PERSON><PERSON>", "_event$dataTransfer", "data", "dataTransfer", "types", "getData", "_default", "exports"], "sources": ["../../src/modules/input.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type { Range } from '../core/selection.js';\nimport { deleteRange } from './keyboard.js';\n\nconst INSERT_TYPES = ['insertText', 'insertReplacementText'];\n\nclass Input extends Module {\n  constructor(quill: Quill, options: Record<string, never>) {\n    super(quill, options);\n\n    quill.root.addEventListener('beforeinput', (event) => {\n      this.handleBeforeInput(event);\n    });\n\n    // Gboard with English input on Android triggers `compositionstart` sometimes even\n    // users are not going to type anything.\n    if (!/Android/i.test(navigator.userAgent)) {\n      quill.on(Quill.events.COMPOSITION_BEFORE_START, () => {\n        this.handleCompositionStart();\n      });\n    }\n  }\n\n  private deleteRange(range: Range) {\n    deleteRange({ range, quill: this.quill });\n  }\n\n  private replaceText(range: Range, text = '') {\n    if (range.length === 0) return false;\n\n    if (text) {\n      // Follow the native behavior that inherits the formats of the first character\n      const formats = this.quill.getFormat(range.index, 1);\n      this.deleteRange(range);\n      this.quill.updateContents(\n        new Delta().retain(range.index).insert(text, formats),\n        Quill.sources.USER,\n      );\n    } else {\n      this.deleteRange(range);\n    }\n\n    this.quill.setSelection(range.index + text.length, 0, Quill.sources.SILENT);\n    return true;\n  }\n\n  private handleBeforeInput(event: InputEvent) {\n    if (\n      this.quill.composition.isComposing ||\n      event.defaultPrevented ||\n      !INSERT_TYPES.includes(event.inputType)\n    ) {\n      return;\n    }\n\n    const staticRange = event.getTargetRanges\n      ? event.getTargetRanges()[0]\n      : null;\n    if (!staticRange || staticRange.collapsed === true) {\n      return;\n    }\n\n    const text = getPlainTextFromInputEvent(event);\n    if (text == null) {\n      return;\n    }\n    const normalized = this.quill.selection.normalizeNative(staticRange);\n    const range = normalized\n      ? this.quill.selection.normalizedToRange(normalized)\n      : null;\n    if (range && this.replaceText(range, text)) {\n      event.preventDefault();\n    }\n  }\n\n  private handleCompositionStart() {\n    const range = this.quill.getSelection();\n    if (range) {\n      this.replaceText(range);\n    }\n  }\n}\n\nfunction getPlainTextFromInputEvent(event: InputEvent) {\n  // When `inputType` is \"insertText\":\n  // - `event.data` should be string (Safari uses `event.dataTransfer`).\n  // - `event.dataTransfer` should be null.\n  // When `inputType` is \"insertReplacementText\":\n  // - `event.data` should be null.\n  // - `event.dataTransfer` should contain \"text/plain\" data.\n\n  if (typeof event.data === 'string') {\n    return event.data;\n  }\n  if (event.dataTransfer?.types.includes('text/plain')) {\n    return event.dataTransfer.getData('text/plain');\n  }\n  return null;\n}\n\nexport default Input;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAMI,YAAY,GAAG,CAAC,YAAY,EAAE,uBAAuB,CAAC;AAAA,IAEtDC,KAAK,0BAAAC,OAAA;EACT,SAAAD,MAAYE,KAAY,EAAEC,OAA8B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,KAAA;IACxDI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,KAAA,GAAME,KAAK,EAAEC,OAAO;IAEpBD,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAG,UAAAC,KAAK,EAAK;MACpDN,KAAA,CAAKO,iBAAiB,CAACD,KAAK,CAAC;IAC/B,CAAC,CAAC;;IAEF;IACA;IACA,IAAI,CAAC,UAAU,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;MACzCZ,KAAK,CAACa,EAAE,CAACC,cAAK,CAACC,MAAM,CAACC,wBAAwB,EAAE,YAAM;QACpDd,KAAA,CAAKe,sBAAsB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ;IAAA,OAAAf,KAAA;EACF;EAAA,IAAAgB,UAAA,CAAAd,OAAA,EAAAN,KAAA,EAAAC,OAAA;EAAA,WAAAoB,aAAA,CAAAf,OAAA,EAAAN,KAAA;IAAAsB,GAAA;IAAAC,KAAA,EAEQ,SAAAC,WAAWA,CAACC,KAAY,EAAE;MAChC,IAAAD,qBAAW,EAAC;QAAEC,KAAK,EAALA,KAAK;QAAEvB,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;IAC3C;EAAA;IAAAoB,GAAA;IAAAC,KAAA,EAEQ,SAAAG,WAAWA,CAACD,KAAY,EAAa;MAAA,IAAXE,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MACzC,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;MAEpC,IAAIF,IAAI,EAAE;QACR;QACA,IAAMI,OAAO,GAAG,IAAI,CAAC7B,KAAK,CAAC8B,SAAS,CAACP,KAAK,CAACQ,KAAK,EAAE,CAAC,CAAC;QACpD,IAAI,CAACT,WAAW,CAACC,KAAK,CAAC;QACvB,IAAI,CAACvB,KAAK,CAACgC,cAAc,CACvB,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACX,KAAK,CAACQ,KAAK,CAAC,CAACI,MAAM,CAACV,IAAI,EAAEI,OAAO,CAAC,EACrDf,cAAK,CAACsB,OAAO,CAACC,IAChB,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACf,WAAW,CAACC,KAAK,CAAC;MACzB;MAEA,IAAI,CAACvB,KAAK,CAACsC,YAAY,CAACf,KAAK,CAACQ,KAAK,GAAGN,IAAI,CAACE,MAAM,EAAE,CAAC,EAAEb,cAAK,CAACsB,OAAO,CAACG,MAAM,CAAC;MAC3E,OAAO,IAAI;IACb;EAAA;IAAAnB,GAAA;IAAAC,KAAA,EAEQ,SAAAZ,iBAAiBA,CAACD,KAAiB,EAAE;MAC3C,IACE,IAAI,CAACR,KAAK,CAACwC,WAAW,CAACC,WAAW,IAClCjC,KAAK,CAACkC,gBAAgB,IACtB,CAAC7C,YAAY,CAAC8C,QAAQ,CAACnC,KAAK,CAACoC,SAAS,CAAC,EACvC;QACA;MACF;MAEA,IAAMC,WAAW,GAAGrC,KAAK,CAACsC,eAAe,GACrCtC,KAAK,CAACsC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1B,IAAI;MACR,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,SAAS,KAAK,IAAI,EAAE;QAClD;MACF;MAEA,IAAMtB,IAAI,GAAGuB,0BAA0B,CAACxC,KAAK,CAAC;MAC9C,IAAIiB,IAAI,IAAI,IAAI,EAAE;QAChB;MACF;MACA,IAAMwB,UAAU,GAAG,IAAI,CAACjD,KAAK,CAACkD,SAAS,CAACC,eAAe,CAACN,WAAW,CAAC;MACpE,IAAMtB,KAAK,GAAG0B,UAAU,GACpB,IAAI,CAACjD,KAAK,CAACkD,SAAS,CAACE,iBAAiB,CAACH,UAAU,CAAC,GAClD,IAAI;MACR,IAAI1B,KAAK,IAAI,IAAI,CAACC,WAAW,CAACD,KAAK,EAAEE,IAAI,CAAC,EAAE;QAC1CjB,KAAK,CAAC6C,cAAc,CAAC,CAAC;MACxB;IACF;EAAA;IAAAjC,GAAA;IAAAC,KAAA,EAEQ,SAAAJ,sBAAsBA,CAAA,EAAG;MAC/B,IAAMM,KAAK,GAAG,IAAI,CAACvB,KAAK,CAACsD,YAAY,CAAC,CAAC;MACvC,IAAI/B,KAAK,EAAE;QACT,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;MACzB;IACF;EAAA;AAAA,EA1EkBgC,eAAM;AA6E1B,SAASP,0BAA0BA,CAACxC,KAAiB,EAAE;EAAA,IAAAgD,mBAAA;EACrD;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAI,OAAOhD,KAAK,CAACiD,IAAI,KAAK,QAAQ,EAAE;IAClC,OAAOjD,KAAK,CAACiD,IAAI;EACnB;EACA,KAAAD,mBAAA,GAAIhD,KAAK,CAACkD,YAAY,cAAAF,mBAAA,eAAlBA,mBAAA,CAAoBG,KAAK,CAAChB,QAAQ,CAAC,YAAY,CAAC,EAAE;IACpD,OAAOnC,KAAK,CAACkD,YAAY,CAACE,OAAO,CAAC,YAAY,CAAC;EACjD;EACA,OAAO,IAAI;AACb;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA1D,OAAA,GAEeN,KAAK", "ignoreList": []}]}