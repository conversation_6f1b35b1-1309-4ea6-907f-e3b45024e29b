{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\index.vue?vue&type=style&index=0&id=7427b2c6&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\index.vue", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICBtaW4taGVpZ2h0OiAxMDB2aDsKICBwYWRkaW5nOiAyMHB4Owp9CgoucGFnZS1sYXlvdXQgewogIGRpc3BsYXk6IGZsZXg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoubGVmdC1zaWRlYmFyIHsKICB3aWR0aDogMjAwcHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTZlNmU2Owp9CgoudXNlci1pbmZvIHsKICBwYWRkaW5nOiAyMHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U2ZTZlNjsKCiAgLmF2YXRhciB7CiAgICB3aWR0aDogNjBweDsKICAgIGhlaWdodDogNjBweDsKICAgIG1hcmdpbjogMCBhdXRvIDEwcHg7CiAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICBvdmVyZmxvdzogaGlkZGVuOwoKICAgIGltZyB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgICBoZWlnaHQ6IDEwMCU7CiAgICAgIG9iamVjdC1maXQ6IGNvdmVyOwogICAgfQogIH0KCiAgLnVzZXItaWQgewogICAgZm9udC1zaXplOiAxNnB4OwogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICBtYXJnaW4tYm90dG9tOiA1cHg7CiAgfQoKICAucmVnaXN0ZXItZGF0ZSB7CiAgICBmb250LXNpemU6IDEycHg7CiAgICBjb2xvcjogIzk5OTsKICB9Cn0KCi5zaWRlYmFyLW1lbnUgewogIC5zaWRlYmFyLW1lbnUtbGlzdCB7CiAgICBib3JkZXItcmlnaHQ6IG5vbmU7CiAgfQoKICAuZWwtbWVudS1pdGVtIHsKICAgIGhlaWdodDogNTBweDsKICAgIGxpbmUtaGVpZ2h0OiA1MHB4OwoKICAgIGkgewogICAgICBtYXJnaW4tcmlnaHQ6IDVweDsKICAgICAgY29sb3I6ICM2NjY7CiAgICB9CiAgfQoKICAuZWwtbWVudS1pdGVtLmlzLWFjdGl2ZSB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWViOwogICAgY29sb3I6ICM2N2MyM2E7CgogICAgaSB7CiAgICAgIGNvbG9yOiAjNjdjMjNhOwogICAgfQogIH0KfQoKLmNvbnRlbnQgewogIGZsZXg6IDE7CiAgcGFkZGluZzogMjBweDsKfQoKLmFjY291bnQtY29udGFpbmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwp9CgouYWNjb3VudC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZy1ib3R0b206IDIwcHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsKCiAgLnRpdGxlIHsKICAgIGZvbnQtc2l6ZTogMThweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KfQoKLmFjY291bnQtaW5mbyB7CiAgLmluZm8taXRlbSB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIGxpbmUtaGVpZ2h0OiAyNHB4OwoKICAgIC5sYWJlbCB7CiAgICAgIHdpZHRoOiAxMjBweDsKICAgICAgY29sb3I6ICM2NjY7CiAgICAgIHRleHQtYWxpZ246IHJpZ2h0OwogICAgICBwYWRkaW5nLXJpZ2h0OiAxMHB4OwogICAgfQoKICAgIC52YWx1ZSB7CiAgICAgIGZsZXg6IDE7CiAgICAgIGNvbG9yOiAjMzMzOwogICAgfQogIH0KfQoKLyog57O757uf6K6+572u5qC35byPICovCi5zZXR0aW5ncy1jb250YWluZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7Cn0KCi5zZXR0aW5ncy1oZWFkZXIgewogIHBhZGRpbmctYm90dG9tOiAxNXB4OwogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC50aXRsZSB7CiAgICBmb250LXNpemU6IDE4cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKICB9Cn0KCi5zZXR0aW5ncy1jb250ZW50IHsKICBkaXNwbGF5OiBmbGV4Owp9Cgouc2V0dGluZ3Mtc2lkZWJhciB7CiAgd2lkdGg6IDEyMHB4OwogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlZWU7CiAgcGFkZGluZy1yaWdodDogMTBweDsKfQoKLnNldHRpbmdzLW1lbnUgewogIC5tZW51LWl0ZW0gewogICAgcGFkZGluZzogMTBweCAwOwogICAgY3Vyc29yOiBwb2ludGVyOwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBjb2xvcjogIzY2NjsKICAgIGZvbnQtc2l6ZTogMTRweDsKCiAgICBpIHsKICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgfQoKICAgICYuYWN0aXZlIHsKICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgfQoKICAgICY6aG92ZXIgewogICAgICBjb2xvcjogIzE4OTBmZjsKICAgIH0KICB9Cn0KCi5zZXR0aW5ncy1tYWluIHsKICBmbGV4OiAxOwogIHBhZGRpbmctbGVmdDogMjBweDsKfQoKLnNldHRpbmdzLXNlY3Rpb24gewogIC5zZWN0aW9uLXRpdGxlIHsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgbWFyZ2luOiAxNXB4IDAgMTBweDsKICAgIGNvbG9yOiAjMzMzOwogICAgZm9udC1zaXplOiAxNHB4OwogIH0KfQoKLm9wdGlvbnMtZ3JvdXAgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC5lbC1jaGVja2JveCB7CiAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgbWluLXdpZHRoOiA4MHB4OwogIH0KfQoKLmRvd25sb2FkLXNldHRpbmdzIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAuc2V0dGluZy1pdGVtIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgZm9udC1zaXplOiAxNHB4OwoKICAgIC5sYWJlbCB7CiAgICAgIHdpZHRoOiA4MHB4OwogICAgICBjb2xvcjogIzY2NjsKICAgIH0KCiAgICAudmFsdWUgewogICAgICBmbGV4OiAxOwogICAgICBjb2xvcjogIzMzMzsKICAgIH0KICB9Cn0KCi5zYXZlLWJ1dHRvbiB7CiAgbWFyZ2luLXRvcDogMjBweDsKCiAgLmVsLWJ1dHRvbiB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTg5MGZmOwogICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgcGFkZGluZzogOHB4IDIwcHg7CiAgfQp9CgovKiDmiJHnmoTkuIvovb3moLflvI8gKi8KLmRvd25sb2FkLWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKfQoKLmRvd25sb2FkLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nLWJvdHRvbTogMTVweDsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAudGl0bGUgewogICAgZm9udC1zaXplOiAxOHB4OwogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgfQoKICAuYWN0aW9ucyB7CiAgICAuZWwtYnV0dG9uIHsKICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICB9CiAgfQp9CgouZG93bmxvYWQtY29udGVudCB7CiAgLmVsLXRhYmxlIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgfQoKICAuZG93bmxvYWQtc3RhdHVzIHsKICAgIGNvbG9yOiAjNjdjMjNhOwogIH0KfQoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogMjBweDsKCiAgc3BhbiB7CiAgICBtYXJnaW46IDAgNXB4OwogIH0KCiAgLmp1bXAtcGFnZS1pbnB1dCB7CiAgICB3aWR0aDogNTBweDsKICAgIG1hcmdpbjogMCA1cHg7CiAgfQp9CgovKiDkv67mlLnlr4bnoIHooajljZXmoLflvI8gKi8KLnBhc3N3b3JkLWZvcm0gewogIG1heC13aWR0aDogNTAwcHg7CiAgbWFyZ2luOiAyMHB4IDA7CgogIC5mb3JtLWdyb3VwIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAuZm9ybS1sYWJlbCB7CiAgICAgIHdpZHRoOiAxMDBweDsKICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICAgIG1hcmdpbi1yaWdodDogMTVweDsKICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICB9CgogICAgLmVsLWlucHV0IHsKICAgICAgd2lkdGg6IDMwMHB4OwogICAgfQogIH0KCiAgLmZvcm0tYWN0aW9ucyB7CiAgICBtYXJnaW4tdG9wOiAzMHB4OwogICAgcGFkZGluZy1sZWZ0OiAxMTVweDsKCiAgICAuZWwtYnV0dG9uIHsKICAgICAgd2lkdGg6IDEwMHB4OwogICAgfQogIH0KfQoKLyog5oiR55qE5pS26JeP5qC35byPICovCi5mYXZvcml0ZS1jb250YWluZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7Cn0KCi5mYXZvcml0ZS1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZy1ib3R0b206IDE1cHg7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsKCiAgLnRpdGxlIHsKICAgIGZvbnQtc2l6ZTogMThweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KfQoKLmZhdm9yaXRlLXRvb2xiYXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgLnRvb2xiYXItbGVmdCB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAuZWwtY2hlY2tib3ggewogICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgICB9CgogICAgLmVsLWJ1dHRvbiB7CiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgIH0KICB9CgogIC50b29sYmFyLXJpZ2h0IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgIC5kYXRlLWZpbHRlciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIG1hcmdpbi1yaWdodDogMTVweDsKCiAgICAgIC5kYXRlLXNlcGFyYXRvciB7CiAgICAgICAgbWFyZ2luOiAwIDVweDsKICAgICAgfQogICAgfQoKICAgIC5zZWFyY2gtYm94IHsKICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4OwogICAgfQogIH0KfQoKLmZhdm9yaXRlLWNvbnRlbnQgewogIG1hcmdpbi10b3A6IDIwcHg7CgogIC5mYXZvcml0ZS1saXN0IHsKICAgIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CgogICAgLmZhdm9yaXRlLWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBwYWRkaW5nOiAxNXB4OwogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsKCiAgICAgICY6bGFzdC1jaGlsZCB7CiAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsKICAgICAgfQoKICAgICAgLml0ZW0tY2hlY2tib3ggewogICAgICAgIG1hcmdpbi1yaWdodDogMTVweDsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIH0KCiAgICAgIC5pdGVtLWNvbnRlbnQgewogICAgICAgIGZsZXg6IDE7CgogICAgICAgIC5pdGVtLXRpdGxlIHsKICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsKCiAgICAgICAgICAudGl0bGUtdGFnIHsKICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgICAgICBwYWRkaW5nOiAycHggNnB4OwogICAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICAgIGNvbG9yOiAjZmZmOwogICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjU2YzZjOwogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7CiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4OwogICAgICAgICAgfQoKICAgICAgICAgIC50aXRsZS10ZXh0IHsKICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgICAgICBmb250LXdlaWdodDogNTAwOwogICAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC5pdGVtLWluZm8gewogICAgICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICAgICAgY29sb3I6ICM5MDkzOTk7CgogICAgICAgICAgLmluZm8tdGltZSB7CiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTVweDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5pdGVtLWFjdGlvbnMgewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBtYXJnaW4tbGVmdDogMTVweDsKCiAgICAgICAgLmNhbmNlbC1mYXZvcml0ZS1idG4gewogICAgICAgICAgY29sb3I6ICNmNTZjNmM7CgogICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgIGNvbG9yOiAjZmY3YzdjOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KCiAgLmZhdm9yaXRlLXBhZ2luYXRpb24gewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tdG9wOiAyMHB4OwogICAgcGFkZGluZzogMTBweCAwOwoKICAgIC5wYWdpbmF0aW9uLWluZm8gewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGNvbG9yOiAjNjA2MjY2OwoKICAgICAgc3BhbiB7CiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgY29sb3I6ICMzMDMxMzM7CiAgICAgIH0KICAgIH0KCiAgICAucGFnaW5hdGlvbi1jb250cm9scyB7CiAgICAgIGZsZXg6IDE7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIH0KCiAgICAucGFnaW5hdGlvbi1qdW1wIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAgIHNwYW4gewogICAgICAgIG1hcmdpbjogMCA1cHg7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjNjA2MjY2OwogICAgICB9CgogICAgICAuanVtcC1wYWdlLWlucHV0IHsKICAgICAgICB3aWR0aDogNTBweDsKICAgICAgICBtYXJnaW46IDAgNXB4OwogICAgICB9CgogICAgICAuZWwtYnV0dG9uIHsKICAgICAgICBtYXJnaW4tbGVmdDogNXB4OwogICAgICB9CiAgICB9CiAgfQp9CgovKiDmiJHnmoTogZTns7vkurrmoLflvI8gKi8KLmNvbnRhY3QtY29udGFpbmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogIHBhZGRpbmc6IDIwcHg7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgouY29udGFjdC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgLnRpdGxlIHsKICAgIGZvbnQtc2l6ZTogMThweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KCiAgLmNvbnRhY3QtdGFicyB7CiAgICAuZWwtcmFkaW8tZ3JvdXAgewogICAgICAuZWwtcmFkaW8tYnV0dG9uIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IC0xcHg7CgogICAgICAgICY6Zmlyc3QtY2hpbGQgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgewogICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4IDAgMCA0cHg7CiAgICAgICAgfQoKICAgICAgICAmOmxhc3QtY2hpbGQgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgewogICAgICAgICAgYm9yZGVyLXJhZGl1czogMCA0cHggNHB4IDA7CiAgICAgICAgfQoKICAgICAgICAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7CiAgICAgICAgICBwYWRkaW5nOiA4cHggMTVweDsKICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi5jb250YWN0LXRvb2xiYXIgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5jb250YWN0LWNvbnRlbnQgewogIC5lbC10YWJsZSB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIH0KCiAgLmRlbGV0ZS1idG4gewogICAgY29sb3I6ICNmNTZjNmM7CiAgfQp9CgovKiDntKDmnZDlupPmoLflvI8gKi8KLm1hdGVyaWFsLWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKfQoKLm1hdGVyaWFsLWhlYWRlciB7CiAgcGFkZGluZy1ib3R0b206IDE1cHg7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKCiAgLnRpdGxlIHsKICAgIGZvbnQtc2l6ZTogMThweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KfQoKLm1hdGVyaWFsLWNvbnRlbnQgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwoKICAubWF0ZXJpYWwtYWRkLWJveCB7CiAgICB3aWR0aDogMjAwcHg7CiAgICBoZWlnaHQ6IDE1MHB4OwogICAgYm9yZGVyOiAxcHggZGFzaGVkICNkOWQ5ZDk7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIG1hcmdpbi1yaWdodDogMjBweDsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CgogICAgJjpob3ZlciB7CiAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKCiAgICAgIC5hZGQtaWNvbiwgLmFkZC10ZXh0IHsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgfQogICAgfQoKICAgIC5hZGQtaWNvbiB7CiAgICAgIGZvbnQtc2l6ZTogMzBweDsKICAgICAgY29sb3I6ICM4YzhjOGM7CiAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICB9CgogICAgLmFkZC10ZXh0IHsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogIzhjOGM4YzsKICAgIH0KICB9Cn0KCi5tYXRlcmlhbC1kaWFsb2cgewogIC5lbC1kaWFsb2dfX2hlYWRlciB7CiAgICBwYWRkaW5nOiAxNXB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgfQoKICAubWF0ZXJpYWwtZm9ybSB7CiAgICBwYWRkaW5nOiAyMHB4IDA7CgogICAgLmZvcm0taXRlbSB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CgogICAgICAuZm9ybS1sYWJlbCB7CiAgICAgICAgd2lkdGg6IDgwcHg7CiAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICB9CiAgICB9CiAgfQoKICAuZGlhbG9nLWZvb3RlciB7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CgogICAgLmVsLWJ1dHRvbiB7CiAgICAgIHdpZHRoOiA4MHB4OwogICAgfQogIH0KfQoKLyog5pu05paw6K+05piO5qC35byPICovCi51cGRhdGVzLXNlY3Rpb24gewogIC51cGRhdGVzLWhlYWRlciB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAgIC52ZXJzaW9uLXRpdGxlIHsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgfQoKICAgIC52ZXJzaW9uLWRhdGUgewogICAgICBmb250LXNpemU6IDEycHg7CiAgICAgIGNvbG9yOiAjOTk5OwogICAgfQogIH0KCiAgLnVwZGF0ZXMtY29udGVudCB7CiAgICBkaXNwbGF5OiBmbGV4OwoKICAgIC52ZXJzaW9uLWxpc3QgewogICAgICB3aWR0aDogMTAwcHg7CiAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlZWU7CiAgICAgIHBhZGRpbmctcmlnaHQ6IDE1cHg7CiAgICAgIG1hcmdpbi1yaWdodDogMjBweDsKCiAgICAgIC52ZXJzaW9uLWl0ZW0gewogICAgICAgIHBhZGRpbmc6IDhweCAwOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBjb2xvcjogIzY2NjsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsKICAgICAgICB9CgogICAgICAgICYuYWN0aXZlIHsKICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmY7CiAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC51cGRhdGUtZGV0YWlscyB7CiAgICAgIGZsZXg6IDE7CgogICAgICAudXBkYXRlLW5vdGVzIHsKICAgICAgICAubm90ZS1pdGVtIHsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjsKCiAgICAgICAgICAubm90ZS1udW1iZXIgewogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDVweDsKICAgICAgICAgICAgY29sb3I6ICM2NjY7CiAgICAgICAgICB9CgogICAgICAgICAgLm5vdGUtY29udGVudCB7CiAgICAgICAgICAgIGZsZXg6IDE7CgogICAgICAgICAgICBiIHsKICAgICAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4tCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"page-layout\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"user-info\">\n          <div class=\"avatar\">\n            <img src=\"@/assets/images/profile.jpg\" alt=\"用户头像\">\n          </div>\n          <div class=\"user-id\">***********</div>\n          <div class=\"register-date\">2023-04-28注册</div>\n        </div>\n\n        <div class=\"sidebar-menu\">\n          <el-menu\n            :default-active=\"activeMenuItem\"\n            class=\"sidebar-menu-list\"\n            @select=\"handleMenuSelect\"\n          >\n            <el-menu-item index=\"account\">\n              <i class=\"el-icon-user\"></i>\n              <span>我的账号</span>\n            </el-menu-item>\n            <el-menu-item index=\"favorite\">\n              <i class=\"el-icon-star-on\"></i>\n              <span>我的收藏</span>\n            </el-menu-item>\n            <el-menu-item index=\"download\">\n              <i class=\"el-icon-download\"></i>\n              <span>我的下载</span>\n            </el-menu-item>\n            <el-menu-item index=\"contact\">\n              <i class=\"el-icon-notebook-1\"></i>\n              <span>我的联系人</span>\n            </el-menu-item>\n            <el-menu-item index=\"material\">\n              <i class=\"el-icon-chat-line-round\"></i>\n              <span>素材库</span>\n            </el-menu-item>\n            <el-menu-item index=\"privacy\">\n              <i class=\"el-icon-setting\"></i>\n              <span>系统设置</span>\n            </el-menu-item>\n            <el-menu-item index=\"user-management\">\n              <i class=\"el-icon-user\"></i>\n              <span>用户管理</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n\n      <!-- 右侧内容区 -->\n      <div class=\"content\">\n        <!-- 账户信息内容 -->\n        <div v-if=\"activeMenuItem === 'account'\" class=\"account-container\">\n          <div class=\"account-header\">\n            <div class=\"title\">我的账号</div>\n            <div class=\"actions\">\n              <el-button size=\"small\" @click=\"showAccountInfo\">账号安全</el-button>\n              <el-button type=\"primary\" size=\"small\" @click=\"showPasswordForm\">修改密码</el-button>\n            </div>\n          </div>\n\n          <!-- 账户信息内容 -->\n          <div v-if=\"!showPasswordChange\" class=\"account-info\">\n            <div class=\"info-item\">\n              <div class=\"label\">用户账号：</div>\n              <div class=\"value\">***********</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">手机号码：</div>\n              <div class=\"value\">***********</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">创建时间：</div>\n              <div class=\"value\">2023-04-28</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">专业认证分类：</div>\n              <div class=\"value\">互联网+</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">方案：</div>\n              <div class=\"value\">【免费】+【限时】</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">总积分值：</div>\n              <div class=\"value\">2000</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">积分等级：</div>\n              <div class=\"value\">初级VIP</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">剩余可用积分：</div>\n              <div class=\"value\">【250积分 = 600次】</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">预计可用天数：</div>\n              <div class=\"value\">365</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">预计到期日：</div>\n              <div class=\"value\">365</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">剩余次数：</div>\n              <div class=\"value\">10000次</div>\n            </div>\n          </div>\n\n          <!-- 修改密码表单 -->\n          <div v-if=\"showPasswordChange\" class=\"password-form\">\n            <div class=\"form-group\">\n              <div class=\"form-label\">旧密码：</div>\n              <el-input\n                v-model=\"passwordForm.oldPassword\"\n                type=\"password\"\n                placeholder=\"请输入旧密码\"\n                show-password\n              ></el-input>\n            </div>\n\n            <div class=\"form-group\">\n              <div class=\"form-label\">新密码：</div>\n              <el-input\n                v-model=\"passwordForm.newPassword\"\n                type=\"password\"\n                placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\"\n                show-password\n              ></el-input>\n            </div>\n\n            <div class=\"form-group\">\n              <div class=\"form-label\">确认新密码：</div>\n              <el-input\n                v-model=\"passwordForm.confirmPassword\"\n                type=\"password\"\n                placeholder=\"请再次输入新密码\"\n                show-password\n              ></el-input>\n            </div>\n\n            <div class=\"form-actions\">\n              <el-button type=\"primary\" @click=\"changePassword\">确认修改</el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 我的下载内容 -->\n        <div v-if=\"activeMenuItem === 'download'\" class=\"download-container\">\n          <div class=\"download-header\">\n            <div class=\"title\">我的下载</div>\n            <div class=\"actions\">\n              <el-button type=\"primary\" size=\"small\" @click=\"batchDownload\">批量下载</el-button>\n              <el-button type=\"danger\" size=\"small\" @click=\"batchDelete\">批量删除</el-button>\n            </div>\n          </div>\n\n          <div class=\"download-content\">\n            <el-table\n              :data=\"downloadList\"\n              style=\"width: 100%\"\n              @selection-change=\"handleSelectionChange\">\n              <el-table-column\n                type=\"selection\"\n                width=\"55\">\n              </el-table-column>\n              <el-table-column\n                prop=\"name\"\n                label=\"名称\"\n                width=\"300\">\n              </el-table-column>\n              <el-table-column\n                prop=\"dataSize\"\n                label=\"数据量\"\n                width=\"100\">\n              </el-table-column>\n              <el-table-column\n                prop=\"createTime\"\n                label=\"生成时间\"\n                width=\"180\">\n              </el-table-column>\n              <el-table-column\n                prop=\"status\"\n                label=\"下载状态\"\n                width=\"100\">\n                <template slot-scope=\"scope\">\n                  <span class=\"download-status\">{{ scope.row.status }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column\n                label=\"操作\"\n                width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-download\"\n                    @click=\"handleDownload(scope.row)\">\n                  </el-button>\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    @click=\"handleDelete(scope.row)\">\n                  </el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <div class=\"pagination-container\">\n              <span>共 {{ total }} 条记录</span>\n              <el-pagination\n                background\n                layout=\"prev, pager, next, jumper\"\n                :total=\"total\"\n                :current-page.sync=\"currentPage\"\n                :page-size=\"pageSize\"\n                @current-change=\"handleCurrentChange\">\n              </el-pagination>\n              <span>前往第</span>\n              <el-input\n                v-model=\"jumpPage\"\n                size=\"mini\"\n                class=\"jump-page-input\">\n              </el-input>\n              <span>页</span>\n              <el-button\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleJumpPage\">\n                确定\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 我的收藏内容 -->\n        <div v-if=\"activeMenuItem === 'favorite'\" class=\"favorite-container\">\n          <div class=\"favorite-header\">\n            <div class=\"title\">我的收藏</div>\n          </div>\n\n          <div class=\"favorite-toolbar\">\n            <div class=\"toolbar-left\">\n              <el-checkbox v-model=\"selectAllFavorites\">全选</el-checkbox>\n              <el-button size=\"small\" type=\"danger\" :disabled=\"selectedFavorites.length === 0\" @click=\"batchCancelFavorite\">批量取消收藏</el-button>\n              <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\n            </div>\n\n            <div class=\"toolbar-right\">\n              <div class=\"date-filter\">\n                <el-date-picker\n                  v-model=\"favoriteStartDate\"\n                  type=\"date\"\n                  placeholder=\"开始日期\"\n                  size=\"small\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  style=\"width: 130px;\"\n                ></el-date-picker>\n                <span class=\"date-separator\">-</span>\n                <el-date-picker\n                  v-model=\"favoriteEndDate\"\n                  type=\"date\"\n                  placeholder=\"结束日期\"\n                  size=\"small\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  style=\"width: 130px;\"\n                ></el-date-picker>\n              </div>\n\n              <div class=\"search-box\">\n                <el-input\n                  v-model=\"favoriteSearchKeyword\"\n                  placeholder=\"搜索内容\"\n                  size=\"small\"\n                  prefix-icon=\"el-icon-search\"\n                  clearable\n                  style=\"width: 200px;\"\n                ></el-input>\n              </div>\n\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-download\" @click=\"exportFavorites\">全部下载</el-button>\n            </div>\n          </div>\n\n          <div class=\"favorite-content\">\n            <div class=\"favorite-list\">\n              <div v-for=\"item in favoriteList\" :key=\"item.id\" class=\"favorite-item\">\n                <div class=\"item-checkbox\">\n                  <el-checkbox v-model=\"item.selected\" @change=\"handleFavoriteSelect\"></el-checkbox>\n                </div>\n                <div class=\"item-content\">\n                  <div class=\"item-title\">\n                    <span class=\"title-tag\">{{ item.tag }}</span>\n                    <span class=\"title-text\">{{ item.title }}</span>\n                  </div>\n                  <div class=\"item-info\">\n                    <span class=\"info-time\">收藏时间: {{ item.time }}</span>\n                    <span class=\"info-source\">来源: {{ item.source }}</span>\n                  </div>\n                </div>\n                <div class=\"item-actions\">\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    icon=\"el-icon-delete\"\n                    class=\"cancel-favorite-btn\"\n                    @click=\"cancelFavorite(item)\">\n                    取消收藏\n                  </el-button>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"favorite-pagination\">\n              <div class=\"pagination-info\">\n                共 <span>{{ favoritesTotal }}</span> 条记录\n              </div>\n              <div class=\"pagination-controls\">\n                <el-pagination\n                  background\n                  layout=\"prev, pager, next\"\n                  :total=\"favoritesTotal\"\n                  :current-page.sync=\"favoritesCurrentPage\"\n                  :page-size=\"favoritesPageSize\"\n                  @current-change=\"handleFavoritePageChange\">\n                </el-pagination>\n              </div>\n              <div class=\"pagination-jump\">\n                <span>前往</span>\n                <el-input\n                  v-model=\"favoritesJumpPage\"\n                  size=\"mini\"\n                  class=\"jump-page-input\">\n                </el-input>\n                <span>页</span>\n                <el-button\n                  size=\"mini\"\n                  type=\"primary\"\n                  @click=\"handleFavoriteJumpPage\">\n                  确定\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 素材库内容 -->\n        <div v-if=\"activeMenuItem === 'material'\" class=\"material-container\">\n          <div class=\"material-header\">\n            <div class=\"title\">素材库</div>\n          </div>\n\n          <div class=\"material-content\">\n            <div class=\"material-add-box\" @click=\"showAddMaterialDialog\">\n              <div class=\"add-icon\">\n                <i class=\"el-icon-plus\"></i>\n              </div>\n              <div class=\"add-text\">新建素材包</div>\n            </div>\n          </div>\n\n          <!-- 新建素材对话框 -->\n          <el-dialog\n            title=\"新建素材包\"\n            :visible.sync=\"addMaterialDialogVisible\"\n            width=\"400px\"\n            center\n            :show-close=\"false\"\n            custom-class=\"material-dialog\"\n          >\n            <div class=\"material-form\">\n              <div class=\"form-item\">\n                <div class=\"form-label\">素材包名称：</div>\n                <el-input v-model=\"newMaterialName\" placeholder=\"请输入素材包名称\"></el-input>\n              </div>\n            </div>\n            <div slot=\"footer\" class=\"dialog-footer\">\n              <el-button type=\"primary\" @click=\"addMaterial\">确定</el-button>\n              <el-button @click=\"addMaterialDialogVisible = false\">取消</el-button>\n            </div>\n          </el-dialog>\n        </div>\n\n        <!-- 我的联系人内容 -->\n        <div v-if=\"activeMenuItem === 'contact'\" class=\"contact-container\">\n          <div class=\"contact-header\">\n            <div class=\"title\">我的联系人</div>\n            <div class=\"contact-tabs\">\n              <el-radio-group v-model=\"contactActiveTab\" size=\"small\">\n                <el-radio-button label=\"wechat\">微信</el-radio-button>\n                <el-radio-button label=\"sms\">短信</el-radio-button>\n                <el-radio-button label=\"email\">邮箱</el-radio-button>\n                <el-radio-button label=\"qwechat\">企微群</el-radio-button>\n                <el-radio-button label=\"dingtalk\">钉钉群</el-radio-button>\n                <el-radio-button label=\"feishu\">飞书群</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n\n          <div class=\"contact-toolbar\">\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddContactDialog\">添加/导入联系人</el-button>\n          </div>\n\n          <div class=\"contact-content\">\n            <el-table\n              :data=\"contactList\"\n              style=\"width: 100%\"\n              border\n              stripe\n              :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\n            >\n              <el-table-column\n                label=\"头像\"\n                width=\"80\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-avatar :size=\"40\" icon=\"el-icon-user\"></el-avatar>\n                </template>\n              </el-table-column>\n              <el-table-column\n                prop=\"name\"\n                label=\"姓名\"\n                width=\"120\"\n                align=\"center\"\n              />\n              <el-table-column\n                prop=\"createTime\"\n                label=\"创建/更新\"\n                width=\"180\"\n                align=\"center\"\n              />\n              <el-table-column\n                prop=\"location\"\n                label=\"位置/时间\"\n                width=\"180\"\n                align=\"center\"\n              />\n              <el-table-column\n                label=\"操作\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-edit\"\n                    @click=\"handleEditContact(scope.row)\"\n                  >编辑</el-button>\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    class=\"delete-btn\"\n                    @click=\"handleDeleteContact(scope.row)\"\n                  >删除</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 添加联系人对话框 -->\n          <el-dialog title=\"添加联系人\" :visible.sync=\"addContactDialogVisible\" width=\"500px\" center>\n            <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactFormRules\" label-width=\"80px\">\n              <el-form-item label=\"姓名\" prop=\"name\">\n                <el-input v-model=\"contactForm.name\" placeholder=\"请输入姓名\" />\n              </el-form-item>\n              <el-form-item label=\"手机号码\" prop=\"phone\">\n                <el-input v-model=\"contactForm.phone\" placeholder=\"请输入手机号码\" />\n              </el-form-item>\n              <el-form-item label=\"邮箱\" prop=\"email\">\n                <el-input v-model=\"contactForm.email\" placeholder=\"请输入邮箱\" />\n              </el-form-item>\n              <el-form-item label=\"位置\" prop=\"location\">\n                <el-input v-model=\"contactForm.location\" placeholder=\"请输入位置\" />\n              </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n              <el-button @click=\"addContactDialogVisible = false\">取消</el-button>\n              <el-button type=\"primary\" @click=\"submitContactForm\">确定</el-button>\n            </div>\n          </el-dialog>\n        </div>\n\n        <!-- 用户管理内容 -->\n        <div v-if=\"activeMenuItem === 'user-management'\">\n          <user-management />\n        </div>\n\n        <!-- 系统设置内容 -->\n        <div v-if=\"activeMenuItem === 'privacy'\" class=\"settings-container\">\n          <div class=\"settings-header\">\n            <div class=\"title\">系统设置</div>\n          </div>\n\n          <div class=\"settings-content\">\n            <!-- 左侧设置菜单 -->\n            <div class=\"settings-sidebar\">\n              <div class=\"settings-menu\">\n                <div :class=\"['menu-item', currentSettingsTab === 'basic' ? 'active' : '']\" @click=\"switchSettingsTab('basic')\">\n                  <i class=\"el-icon-setting\"></i>\n                  <span>基础设置</span>\n                </div>\n                <div :class=\"['menu-item', currentSettingsTab === 'updates' ? 'active' : '']\" @click=\"switchSettingsTab('updates')\">\n                  <i class=\"el-icon-document\"></i>\n                  <span>更新说明</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- 右侧设置内容 -->\n            <div class=\"settings-main\">\n              <!-- 基础设置内容 -->\n              <div v-if=\"currentSettingsTab === 'basic'\" class=\"settings-section\">\n                <div class=\"section-title\">文章管理选项：</div>\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.auto\">自动</el-checkbox>\n                  <el-checkbox v-model=\"settings.manual\">手动</el-checkbox>\n                  <el-checkbox v-model=\"settings.downloadImages\">下载图片</el-checkbox>\n                  <el-checkbox v-model=\"settings.backup\">备份</el-checkbox>\n                  <el-checkbox v-model=\"settings.realTimeSync\">实时同步</el-checkbox>\n                  <el-checkbox v-model=\"settings.humanReview\">人工审核</el-checkbox>\n                </div>\n\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.autoPublish\">自动发布</el-checkbox>\n                  <el-checkbox v-model=\"settings.keywords\">关键词</el-checkbox>\n                  <el-checkbox v-model=\"settings.highQuality\">高质量</el-checkbox>\n                  <el-checkbox v-model=\"settings.multiPlatform\">多平台发布</el-checkbox>\n                </div>\n\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.autoSave\">自动保存</el-checkbox>\n                  <el-checkbox v-model=\"settings.batchProcess\">批量处理</el-checkbox>\n                  <el-checkbox v-model=\"settings.imageProcess\">图片处理</el-checkbox>\n                </div>\n\n                <div class=\"section-title\">下载设置：</div>\n                <div class=\"download-settings\">\n                  <div class=\"setting-item\">\n                    <span class=\"label\">下载路径：</span>\n                    <span class=\"value\">自动生成文件夹</span>\n                  </div>\n                </div>\n\n                <div class=\"section-title\">高级自动化设置：</div>\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.autoOn\">开启</el-checkbox>\n                  <el-checkbox v-model=\"settings.autoOff\">关闭</el-checkbox>\n                </div>\n\n                <div class=\"save-button\">\n                  <el-button type=\"primary\" @click=\"saveSettings\">保存</el-button>\n                </div>\n              </div>\n\n              <!-- 更新说明内容 -->\n              <div v-if=\"currentSettingsTab === 'updates'\" class=\"updates-section\">\n                <div class=\"updates-header\">\n                  <div class=\"version-title\">{{ selectedVersion.version }} 版本更新说明</div>\n                  <div class=\"version-date\">{{ selectedVersion.date }}</div>\n                </div>\n\n                <div class=\"updates-content\">\n                  <div class=\"version-list\">\n                    <div\n                      v-for=\"version in versionList\"\n                      :key=\"version.version\"\n                      :class=\"['version-item', selectedVersion.version === version.version ? 'active' : '']\"\n                      @click=\"selectVersion(version.version)\"\n                    >\n                      {{ version.version }}\n                    </div>\n                  </div>\n\n                  <div class=\"update-details\">\n                    <div class=\"update-notes\">\n                      <div v-for=\"(note, index) in selectedVersion.notes\" :key=\"index\" class=\"note-item\">\n                        <div class=\"note-number\">{{ index + 1 }}. </div>\n                        <div class=\"note-content\" v-html=\"note\"></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport UserManagement from './user-management.vue';\n\nexport default {\n  name: \"AccountManagement\",\n  components: {\n    UserManagement\n  },\n  watch: {\n    selectAllFavorites(val) {\n      this.watchSelectAllFavorites(val);\n    }\n  },\n  data() {\n    return {\n      activeMenuItem: 'account', // 默认选中我的账号\n      currentSettingsTab: 'basic', // 默认选中基础设置\n      // 用户信息\n      userInfo: {\n        userId: '***********',\n        phoneNumber: '***********',\n        registerDate: '2023-04-28',\n        category: '互联网+',\n        plan: '【免费】+【限时】',\n        totalPoints: '2000',\n        pointsLevel: '初级VIP',\n        availablePoints: '【250积分 = 600次】',\n        estimatedDays: '365',\n        expirationDate: '365',\n        remainingCount: '10000次'\n      },\n      // 系统设置\n      settings: {\n        auto: true,\n        manual: true,\n        downloadImages: true,\n        backup: true,\n        realTimeSync: false,\n        humanReview: true,\n        autoPublish: false,\n        keywords: true,\n        highQuality: false,\n        multiPlatform: true,\n        autoSave: true,\n        batchProcess: true,\n        imageProcess: true,\n        autoOn: true,\n        autoOff: false\n      },\n      // 下载列表\n      downloadList: [\n        {\n          id: 1,\n          name: '方案_20240428163743_1',\n          dataSize: '1323',\n          createTime: '2024-04-28 16:37:57',\n          status: '已完成'\n        },\n        {\n          id: 2,\n          name: '方案_20240428163743_1',\n          dataSize: '2000',\n          createTime: '2024-04-28 16:37:57',\n          status: '已完成'\n        },\n        {\n          id: 3,\n          name: '方案_20240427173742_4',\n          dataSize: '1893',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 4,\n          name: '方案_20240427173742_3',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 5,\n          name: '方案_20240427173742_2',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 6,\n          name: '方案_20240427173742_2',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 7,\n          name: '方案_20240427173742_1',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 8,\n          name: '台账_20240427173129_5',\n          dataSize: '1281',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 9,\n          name: '台账_20240427173129_4',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 10,\n          name: '台账_20240427173129_3',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 11,\n          name: '台账_20240427173129_2',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 12,\n          name: '台账_20240427173129_1',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        }\n      ],\n      // 分页相关\n      total: 12,\n      currentPage: 1,\n      pageSize: 10,\n      jumpPage: '',\n      // 选中的下载项\n      selectedDownloads: [],\n      // 版本列表\n      versionList: [\n        { version: '6.2.9', date: '2024.01.19' },\n        { version: '6.2.8', date: '2023.12.15' },\n        { version: '6.2.7', date: '2023.11.20' },\n        { version: '6.2.6', date: '2023.10.18' },\n        { version: '6.2.5', date: '2023.09.25' },\n        { version: '6.2.4', date: '2023.08.30' },\n        { version: '6.2.3', date: '2023.07.28' },\n        { version: '6.2.2', date: '2023.06.22' },\n        { version: '6.2.1', date: '2023.05.15' },\n        { version: '6.2.0', date: '2023.04.10' }\n      ],\n      // 当前选中的版本\n      selectedVersion: {\n        version: '6.2.9',\n        date: '2024.01.19',\n        notes: [\n          '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\n          '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\n          '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\n          '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\n          '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\n        ]\n      },\n      // 素材库相关\n      addMaterialDialogVisible: false,\n      newMaterialName: '',\n      materialList: [],\n      // 修改密码相关\n      showPasswordChange: false,\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      // 我的收藏相关\n      selectAllFavorites: false,\n      selectedFavorites: [],\n      favoriteStartDate: '',\n      favoriteEndDate: '',\n      favoriteSearchKeyword: '',\n      favoriteList: [\n        {\n          id: 1,\n          selected: false,\n          tag: 'HOT',\n          title: '新产品市场分析报告',\n          time: '2023-04-29 20:37:51',\n          source: '市场调查部门 A 数据库'\n        },\n        {\n          id: 2,\n          selected: false,\n          tag: 'NEW',\n          title: '2024年第一季度行业趋势分析',\n          time: '2024-04-28 15:22:36',\n          source: '行业研究中心'\n        },\n        {\n          id: 3,\n          selected: false,\n          tag: 'HOT',\n          title: '竞品分析与市场定位策略',\n          time: '2024-04-27 09:15:42',\n          source: '战略规划部'\n        },\n        {\n          id: 4,\n          selected: false,\n          tag: 'TOP',\n          title: '用户行为数据分析报告',\n          time: '2024-04-26 14:30:18',\n          source: '数据分析部门'\n        },\n        {\n          id: 5,\n          selected: false,\n          tag: 'NEW',\n          title: '新媒体营销策略白皮书',\n          time: '2024-04-25 11:45:23',\n          source: '营销部门'\n        },\n        {\n          id: 6,\n          selected: false,\n          tag: 'HOT',\n          title: '产品迭代计划与路线图',\n          time: '2024-04-24 16:20:37',\n          source: '产品部门'\n        },\n        {\n          id: 7,\n          selected: false,\n          tag: 'TOP',\n          title: '行业政策解读与影响分析',\n          time: '2024-04-23 10:05:12',\n          source: '政策研究中心'\n        }\n      ],\n      favoritesTotal: 7,\n      favoritesCurrentPage: 1,\n      favoritesPageSize: 10,\n      favoritesJumpPage: '',\n\n      // 我的联系人相关\n      contactActiveTab: 'wechat', // 默认选中微信标签\n      contactList: [\n        {\n          id: 1,\n          name: '张三',\n          phone: '13800138001',\n          email: '<EMAIL>',\n          location: '北京市朝阳区',\n          createTime: '2024-04-28 10:30:45'\n        },\n        {\n          id: 2,\n          name: '李四',\n          phone: '13800138002',\n          email: '<EMAIL>',\n          location: '上海市浦东新区',\n          createTime: '2024-04-27 15:20:36'\n        },\n        {\n          id: 3,\n          name: '王五',\n          phone: '13800138003',\n          email: '<EMAIL>',\n          location: '广州市天河区',\n          createTime: '2024-04-26 09:15:22'\n        }\n      ],\n      addContactDialogVisible: false,\n      contactForm: {\n        id: null,\n        name: '',\n        phone: '',\n        email: '',\n        location: ''\n      },\n      contactFormRules: {\n        name: [\n          { required: true, message: '请输入姓名', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n        ],\n        email: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n        ]\n      }\n    };\n  },\n  methods: {\n    // 处理菜单项选择\n    handleMenuSelect(index) {\n      this.activeMenuItem = index;\n    },\n    // 显示账户信息\n    showAccountInfo() {\n      this.showPasswordChange = false;\n    },\n    // 显示修改密码表单\n    showPasswordForm() {\n      this.showPasswordChange = true;\n      this.passwordForm = {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      };\n    },\n    // 修改密码\n    changePassword() {\n      // 表单验证\n      if (!this.passwordForm.oldPassword) {\n        this.$message.warning('请输入旧密码');\n        return;\n      }\n      if (!this.passwordForm.newPassword) {\n        this.$message.warning('请输入新密码');\n        return;\n      }\n      if (!this.passwordForm.confirmPassword) {\n        this.$message.warning('请确认新密码');\n        return;\n      }\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\n        this.$message.warning('两次输入的新密码不一致');\n        return;\n      }\n\n      // 提交修改密码请求\n      this.$message.success('密码修改成功');\n      this.showPasswordChange = false;\n    },\n    // 切换设置选项卡\n    switchSettingsTab(tab) {\n      this.currentSettingsTab = tab;\n    },\n    // 保存设置\n    saveSettings() {\n      this.$message.success('设置已保存');\n    },\n    // 处理表格选择变化\n    handleSelectionChange(selection) {\n      this.selectedDownloads = selection;\n    },\n    // 批量下载\n    batchDownload() {\n      if (this.selectedDownloads.length === 0) {\n        this.$message.warning('请选择要下载的文件');\n        return;\n      }\n      this.$message.success(`已开始下载${this.selectedDownloads.length}个文件`);\n    },\n    // 批量删除\n    batchDelete() {\n      if (this.selectedDownloads.length === 0) {\n        this.$message.warning('请选择要删除的文件');\n        return;\n      }\n      this.$confirm('确定要删除选中的文件吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$message.success(`已删除${this.selectedDownloads.length}个文件`);\n        // 实际应用中这里需要调用接口删除文件\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n    // 下载单个文件\n    handleDownload(row) {\n      this.$message.success(`开始下载: ${row.name}`);\n    },\n    // 删除单个文件\n    handleDelete(row) {\n      this.$confirm('确定要删除该文件吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$message.success(`已删除: ${row.name}`);\n        // 实际应用中这里需要调用接口删除文件\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n    // 处理页码变化\n    handleCurrentChange(val) {\n      this.currentPage = val;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n    // 处理跳转页面\n    handleJumpPage() {\n      if (!this.jumpPage) {\n        return;\n      }\n      const page = parseInt(this.jumpPage);\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.total / this.pageSize)) {\n        this.$message.warning('请输入有效的页码');\n        return;\n      }\n      this.currentPage = page;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n    // 选择版本\n    selectVersion(version) {\n      const versionData = this.versionList.find(v => v.version === version);\n      if (versionData) {\n        // 根据版本号获取对应的更新说明\n        let notes = [];\n        if (version === '6.2.9') {\n          notes = [\n            '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\n            '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\n            '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\n            '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\n            '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\n          ];\n        } else if (version === '6.2.8') {\n          notes = [\n            '1. 【新增功能】新增<b>数据分析</b>模块，提供更全面的数据统计。',\n            '2. 【功能优化】优化了<b>文件上传</b>功能，支持更多文件格式。',\n            '3. 【问题修复】修复了部分用户<b>无法登录</b>的问题。'\n          ];\n        } else {\n          notes = [\n            '1. 【功能优化】优化系统性能，提升用户体验。',\n            '2. 【问题修复】修复已知问题，提高系统稳定性。'\n          ];\n        }\n\n        this.selectedVersion = {\n          version: versionData.version,\n          date: versionData.date,\n          notes: notes\n        };\n      }\n    },\n    // 显示添加素材对话框\n    showAddMaterialDialog() {\n      this.newMaterialName = '';\n      this.addMaterialDialogVisible = true;\n    },\n    // 添加素材\n    addMaterial() {\n      if (!this.newMaterialName.trim()) {\n        this.$message.warning('请输入素材名称');\n        return;\n      }\n\n      // 添加新素材\n      this.materialList.push({\n        id: Date.now(),\n        name: this.newMaterialName,\n        createTime: new Date().toLocaleString()\n      });\n\n      this.$message.success(`素材\"${this.newMaterialName}\"创建成功`);\n      this.addMaterialDialogVisible = false;\n    },\n\n    // 处理收藏项选择\n    handleFavoriteSelect() {\n      this.selectedFavorites = this.favoriteList.filter(item => item.selected);\n      // 检查是否全选\n      this.selectAllFavorites = this.selectedFavorites.length === this.favoriteList.length;\n    },\n\n    // 全选/取消全选收藏项\n    watchSelectAllFavorites(val) {\n      this.favoriteList.forEach(item => {\n        item.selected = val;\n      });\n      this.selectedFavorites = val ? [...this.favoriteList] : [];\n    },\n\n    // 批量取消收藏\n    batchCancelFavorite() {\n      if (this.selectedFavorites.length === 0) {\n        this.$message.warning('请选择要取消收藏的项');\n        return;\n      }\n\n      this.$confirm('确定要取消选中的收藏吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 实际应用中这里需要调用接口取消收藏\n        const selectedIds = this.selectedFavorites.map(item => item.id);\n        this.favoriteList = this.favoriteList.filter(item => !selectedIds.includes(item.id));\n        this.selectedFavorites = [];\n        this.selectAllFavorites = false;\n        this.$message.success('已取消收藏');\n      }).catch(() => {\n        this.$message.info('已取消操作');\n      });\n    },\n\n    // 取消单个收藏\n    cancelFavorite(item) {\n      this.$confirm('确定要取消收藏\"' + item.title + '\"吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 实际应用中这里需要调用接口取消收藏\n        this.favoriteList = this.favoriteList.filter(i => i.id !== item.id);\n        // 更新选中的收藏项\n        this.selectedFavorites = this.selectedFavorites.filter(i => i.id !== item.id);\n        // 更新总数\n        this.favoritesTotal = this.favoriteList.length;\n        this.$message.success('已取消收藏');\n      }).catch(() => {\n        this.$message.info('已取消操作');\n      });\n    },\n\n    // 显示添加联系人对话框\n    showAddContactDialog() {\n      this.contactForm = {\n        id: null,\n        name: '',\n        phone: '',\n        email: '',\n        location: ''\n      };\n      this.addContactDialogVisible = true;\n    },\n\n    // 提交联系人表单\n    submitContactForm() {\n      this.$refs.contactForm.validate(valid => {\n        if (valid) {\n          if (this.contactForm.id) {\n            // 编辑联系人\n            const index = this.contactList.findIndex(item => item.id === this.contactForm.id);\n            if (index !== -1) {\n              // 更新创建时间\n              this.contactForm.createTime = new Date().toLocaleString('zh-CN', {\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n                hour12: false\n              }).replace(/\\//g, '-');\n              this.contactList.splice(index, 1, this.contactForm);\n              this.$message.success('联系人修改成功');\n            }\n          } else {\n            // 添加联系人\n            const newContact = {\n              ...this.contactForm,\n              id: this.contactList.length > 0 ? Math.max(...this.contactList.map(item => item.id)) + 1 : 1,\n              createTime: new Date().toLocaleString('zh-CN', {\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n                hour12: false\n              }).replace(/\\//g, '-')\n            };\n            this.contactList.push(newContact);\n            this.$message.success('联系人添加成功');\n          }\n          this.addContactDialogVisible = false;\n        }\n      });\n    },\n\n    // 编辑联系人\n    handleEditContact(row) {\n      this.contactForm = JSON.parse(JSON.stringify(row));\n      this.addContactDialogVisible = true;\n    },\n\n    // 删除联系人\n    handleDeleteContact(row) {\n      this.$confirm(`确定要删除联系人\"${row.name}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 实际应用中这里需要调用接口删除联系人\n        this.contactList = this.contactList.filter(item => item.id !== row.id);\n        this.$message.success('联系人删除成功');\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n\n    // 导出收藏\n    exportFavorites() {\n      this.$message.success('开始导出收藏');\n      // 实际应用中这里需要调用接口导出收藏\n    },\n\n    // 处理收藏分页变化\n    handleFavoritePageChange(val) {\n      this.favoritesCurrentPage = val;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n\n    // 处理收藏跳转页面\n    handleFavoriteJumpPage() {\n      if (!this.favoritesJumpPage) {\n        return;\n      }\n\n      const page = parseInt(this.favoritesJumpPage);\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.favoritesTotal / this.favoritesPageSize)) {\n        this.$message.warning('请输入有效的页码');\n        return;\n      }\n\n      this.favoritesCurrentPage = page;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.page-layout {\n  display: flex;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.left-sidebar {\n  width: 200px;\n  background-color: #fff;\n  border-right: 1px solid #e6e6e6;\n}\n\n.user-info {\n  padding: 20px;\n  text-align: center;\n  border-bottom: 1px solid #e6e6e6;\n\n  .avatar {\n    width: 60px;\n    height: 60px;\n    margin: 0 auto 10px;\n    border-radius: 50%;\n    overflow: hidden;\n\n    img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n  }\n\n  .user-id {\n    font-size: 16px;\n    font-weight: bold;\n    margin-bottom: 5px;\n  }\n\n  .register-date {\n    font-size: 12px;\n    color: #999;\n  }\n}\n\n.sidebar-menu {\n  .sidebar-menu-list {\n    border-right: none;\n  }\n\n  .el-menu-item {\n    height: 50px;\n    line-height: 50px;\n\n    i {\n      margin-right: 5px;\n      color: #666;\n    }\n  }\n\n  .el-menu-item.is-active {\n    background-color: #f0f9eb;\n    color: #67c23a;\n\n    i {\n      color: #67c23a;\n    }\n  }\n}\n\n.content {\n  flex: 1;\n  padding: 20px;\n}\n\n.account-container {\n  background-color: #fff;\n}\n\n.account-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 20px;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #eee;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.account-info {\n  .info-item {\n    display: flex;\n    margin-bottom: 20px;\n    line-height: 24px;\n\n    .label {\n      width: 120px;\n      color: #666;\n      text-align: right;\n      padding-right: 10px;\n    }\n\n    .value {\n      flex: 1;\n      color: #333;\n    }\n  }\n}\n\n/* 系统设置样式 */\n.settings-container {\n  background-color: #fff;\n}\n\n.settings-header {\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.settings-content {\n  display: flex;\n}\n\n.settings-sidebar {\n  width: 120px;\n  border-right: 1px solid #eee;\n  padding-right: 10px;\n}\n\n.settings-menu {\n  .menu-item {\n    padding: 10px 0;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    color: #666;\n    font-size: 14px;\n\n    i {\n      margin-right: 8px;\n      color: #1890ff;\n    }\n\n    &.active {\n      color: #1890ff;\n      font-weight: bold;\n    }\n\n    &:hover {\n      color: #1890ff;\n    }\n  }\n}\n\n.settings-main {\n  flex: 1;\n  padding-left: 20px;\n}\n\n.settings-section {\n  .section-title {\n    font-weight: bold;\n    margin: 15px 0 10px;\n    color: #333;\n    font-size: 14px;\n  }\n}\n\n.options-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 15px;\n\n  .el-checkbox {\n    margin-right: 15px;\n    margin-bottom: 10px;\n    min-width: 80px;\n  }\n}\n\n.download-settings {\n  margin-bottom: 15px;\n\n  .setting-item {\n    display: flex;\n    margin-bottom: 10px;\n    font-size: 14px;\n\n    .label {\n      width: 80px;\n      color: #666;\n    }\n\n    .value {\n      flex: 1;\n      color: #333;\n    }\n  }\n}\n\n.save-button {\n  margin-top: 20px;\n\n  .el-button {\n    background-color: #1890ff;\n    border-color: #1890ff;\n    padding: 8px 20px;\n  }\n}\n\n/* 我的下载样式 */\n.download-container {\n  background-color: #fff;\n}\n\n.download-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n\n  .actions {\n    .el-button {\n      margin-left: 10px;\n    }\n  }\n}\n\n.download-content {\n  .el-table {\n    margin-bottom: 20px;\n  }\n\n  .download-status {\n    color: #67c23a;\n  }\n}\n\n.pagination-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 20px;\n\n  span {\n    margin: 0 5px;\n  }\n\n  .jump-page-input {\n    width: 50px;\n    margin: 0 5px;\n  }\n}\n\n/* 修改密码表单样式 */\n.password-form {\n  max-width: 500px;\n  margin: 20px 0;\n\n  .form-group {\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n\n    .form-label {\n      width: 100px;\n      text-align: right;\n      margin-right: 15px;\n      color: #606266;\n    }\n\n    .el-input {\n      width: 300px;\n    }\n  }\n\n  .form-actions {\n    margin-top: 30px;\n    padding-left: 115px;\n\n    .el-button {\n      width: 100px;\n    }\n  }\n}\n\n/* 我的收藏样式 */\n.favorite-container {\n  background-color: #fff;\n}\n\n.favorite-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n  border-bottom: 1px solid #eee;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.favorite-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .toolbar-left {\n    display: flex;\n    align-items: center;\n\n    .el-checkbox {\n      margin-right: 15px;\n    }\n\n    .el-button {\n      margin-right: 10px;\n    }\n  }\n\n  .toolbar-right {\n    display: flex;\n    align-items: center;\n\n    .date-filter {\n      display: flex;\n      align-items: center;\n      margin-right: 15px;\n\n      .date-separator {\n        margin: 0 5px;\n      }\n    }\n\n    .search-box {\n      margin-right: 15px;\n    }\n  }\n}\n\n.favorite-content {\n  margin-top: 20px;\n\n  .favorite-list {\n    border: 1px solid #ebeef5;\n    border-radius: 4px;\n\n    .favorite-item {\n      display: flex;\n      padding: 15px;\n      border-bottom: 1px solid #ebeef5;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .item-checkbox {\n        margin-right: 15px;\n        display: flex;\n        align-items: center;\n      }\n\n      .item-content {\n        flex: 1;\n\n        .item-title {\n          margin-bottom: 8px;\n\n          .title-tag {\n            display: inline-block;\n            padding: 2px 6px;\n            font-size: 12px;\n            color: #fff;\n            background-color: #f56c6c;\n            border-radius: 2px;\n            margin-right: 8px;\n          }\n\n          .title-text {\n            font-size: 16px;\n            font-weight: 500;\n            color: #303133;\n          }\n        }\n\n        .item-info {\n          font-size: 13px;\n          color: #909399;\n\n          .info-time {\n            margin-right: 15px;\n          }\n        }\n      }\n\n      .item-actions {\n        display: flex;\n        align-items: center;\n        margin-left: 15px;\n\n        .cancel-favorite-btn {\n          color: #f56c6c;\n\n          &:hover {\n            color: #ff7c7c;\n          }\n        }\n      }\n    }\n  }\n\n  .favorite-pagination {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 20px;\n    padding: 10px 0;\n\n    .pagination-info {\n      font-size: 14px;\n      color: #606266;\n\n      span {\n        font-weight: bold;\n        color: #303133;\n      }\n    }\n\n    .pagination-controls {\n      flex: 1;\n      text-align: center;\n    }\n\n    .pagination-jump {\n      display: flex;\n      align-items: center;\n\n      span {\n        margin: 0 5px;\n        font-size: 14px;\n        color: #606266;\n      }\n\n      .jump-page-input {\n        width: 50px;\n        margin: 0 5px;\n      }\n\n      .el-button {\n        margin-left: 5px;\n      }\n    }\n  }\n}\n\n/* 我的联系人样式 */\n.contact-container {\n  background-color: #fff;\n  padding: 20px;\n  border-radius: 4px;\n}\n\n.contact-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n\n  .contact-tabs {\n    .el-radio-group {\n      .el-radio-button {\n        margin-right: -1px;\n\n        &:first-child .el-radio-button__inner {\n          border-radius: 4px 0 0 4px;\n        }\n\n        &:last-child .el-radio-button__inner {\n          border-radius: 0 4px 4px 0;\n        }\n\n        .el-radio-button__inner {\n          padding: 8px 15px;\n          font-size: 13px;\n        }\n      }\n    }\n  }\n}\n\n.contact-toolbar {\n  margin-bottom: 20px;\n}\n\n.contact-content {\n  .el-table {\n    margin-bottom: 20px;\n  }\n\n  .delete-btn {\n    color: #f56c6c;\n  }\n}\n\n/* 素材库样式 */\n.material-container {\n  background-color: #fff;\n}\n\n.material-header {\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.material-content {\n  display: flex;\n  flex-wrap: wrap;\n\n  .material-add-box {\n    width: 200px;\n    height: 150px;\n    border: 1px dashed #d9d9d9;\n    border-radius: 4px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    margin-right: 20px;\n    margin-bottom: 20px;\n\n    &:hover {\n      border-color: #1890ff;\n\n      .add-icon, .add-text {\n        color: #1890ff;\n      }\n    }\n\n    .add-icon {\n      font-size: 30px;\n      color: #8c8c8c;\n      margin-bottom: 10px;\n    }\n\n    .add-text {\n      font-size: 14px;\n      color: #8c8c8c;\n    }\n  }\n}\n\n.material-dialog {\n  .el-dialog__header {\n    padding: 15px;\n    text-align: center;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  .material-form {\n    padding: 20px 0;\n\n    .form-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 15px;\n\n      .form-label {\n        width: 80px;\n        text-align: right;\n        margin-right: 10px;\n      }\n    }\n  }\n\n  .dialog-footer {\n    text-align: center;\n\n    .el-button {\n      width: 80px;\n    }\n  }\n}\n\n/* 更新说明样式 */\n.updates-section {\n  .updates-header {\n    margin-bottom: 20px;\n\n    .version-title {\n      font-size: 16px;\n      font-weight: bold;\n      margin-bottom: 5px;\n    }\n\n    .version-date {\n      font-size: 12px;\n      color: #999;\n    }\n  }\n\n  .updates-content {\n    display: flex;\n\n    .version-list {\n      width: 100px;\n      border-right: 1px solid #eee;\n      padding-right: 15px;\n      margin-right: 20px;\n\n      .version-item {\n        padding: 8px 0;\n        cursor: pointer;\n        color: #666;\n        font-size: 14px;\n        text-align: center;\n        border-radius: 4px;\n        margin-bottom: 5px;\n\n        &:hover {\n          background-color: #f5f7fa;\n        }\n\n        &.active {\n          background-color: #e6f7ff;\n          color: #1890ff;\n          font-weight: bold;\n        }\n      }\n    }\n\n    .update-details {\n      flex: 1;\n\n      .update-notes {\n        .note-item {\n          display: flex;\n          margin-bottom: 10px;\n          line-height: 1.6;\n\n          .note-number {\n            margin-right: 5px;\n            color: #666;\n          }\n\n          .note-content {\n            flex: 1;\n\n            b {\n              color: #1890ff;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}