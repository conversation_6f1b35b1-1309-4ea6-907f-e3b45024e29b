from sqlalchemy.ext.asyncio import AsyncSession
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.dao.info_summary_dao import InfoSummaryDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.info_summary_vo import (
    DeleteInfoSummaryModel, 
    InfoSummaryModel, 
    InfoSummaryPageQueryModel,
    InfoSummaryStatisticsModel
)
from utils.common_util import CamelCaseUtil


class InfoSummaryService:
    """
    信息汇总管理模块服务层
    """

    @classmethod
    async def get_info_summary_list_services(
        cls, query_db: AsyncSession, query_object: InfoSummaryPageQueryModel, is_page: bool = True
    ):
        """
        获取信息汇总列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 信息汇总列表信息对象
        """
        info_list_result = await InfoSummaryDao.get_info_summary_list(query_db, query_object, is_page)
        return info_list_result

    @classmethod
    async def check_info_summary_unique_services(cls, query_db: AsyncSession, page_object: InfoSummaryModel):
        """
        校验信息是否存在service

        :param query_db: orm对象
        :param page_object: 信息对象
        :return: 校验结果
        """
        # 这里可以根据业务需求定义唯一性校验规则
        # 例如：同一来源的同一标题在同一时间不能重复
        # 暂时返回唯一，实际项目中可以根据需要实现具体的校验逻辑
        return CommonConstant.UNIQUE

    @classmethod
    async def add_info_summary_services(cls, query_db: AsyncSession, page_object: InfoSummaryModel):
        """
        新增信息service

        :param query_db: orm对象
        :param page_object: 新增信息对象
        :return: 新增信息校验结果
        """
        if not await cls.check_info_summary_unique_services(query_db, page_object):
            raise ServiceException(message=f'新增信息{page_object.title}失败，信息已存在')
        else:
            try:
                await InfoSummaryDao.add_info_summary_dao(query_db, page_object)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='新增成功')
            except Exception as e:
                await query_db.rollback()
                raise e

    @classmethod
    async def edit_info_summary_services(cls, query_db: AsyncSession, page_object: InfoSummaryModel):
        """
        编辑信息service

        :param query_db: orm对象
        :param page_object: 编辑信息对象
        :return: 编辑信息校验结果
        """
        edit_info = page_object.model_dump(exclude_unset=True)
        info_info = await cls.info_summary_detail_services(query_db, page_object.id)
        if info_info.id:
            try:
                await InfoSummaryDao.edit_info_summary_dao(query_db, edit_info)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='信息不存在')

    @classmethod
    async def delete_info_summary_services(cls, query_db: AsyncSession, page_object: DeleteInfoSummaryModel):
        """
        删除信息service

        :param query_db: orm对象
        :param page_object: 删除信息对象
        :return: 删除信息校验结果
        """
        if page_object.info_ids:
            info_id_list = page_object.info_ids.split(',')
            try:
                for info_id in info_id_list:
                    await InfoSummaryDao.delete_info_summary_dao(query_db, InfoSummaryModel(id=info_id))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入信息id为空')

    @classmethod
    async def info_summary_detail_services(cls, query_db: AsyncSession, info_id: int):
        """
        获取信息详细信息service

        :param query_db: orm对象
        :param info_id: 信息id
        :return: 信息id对应的信息
        """
        info = await InfoSummaryDao.get_info_summary_detail_by_id(query_db, info_id=info_id)
        if info:
            result = InfoSummaryModel(**CamelCaseUtil.transform_result(info))
        else:
            result = InfoSummaryModel(**dict())
        return result

    @classmethod
    async def get_platform_statistics_services(cls, query_db: AsyncSession):
        """
        获取平台统计数据service

        :param query_db: orm对象
        :return: 平台统计数据
        """
        statistics_data = await InfoSummaryDao.get_platform_statistics(query_db)
        
        # 转换为统计模型列表
        result = []
        for row in statistics_data:
            # 将Row对象转换为字典
            row_dict = {
                'platform_type': row.platform_type,
                'total_count': row.total_count or 0,
                'today_count': row.today_count or 0,
                'positive_count': row.positive_count or 0,
                'neutral_count': row.neutral_count or 0,
                'negative_count': row.negative_count or 0
            }
            stat = InfoSummaryStatisticsModel(**row_dict)
            result.append(stat)
        
        return result

    @classmethod
    async def update_sentiment_services(cls, query_db: AsyncSession, info_id: int, sentiment: str):
        """
        更新信息情感倾向service

        :param query_db: orm对象
        :param info_id: 信息ID
        :param sentiment: 新的情感倾向
        :return: 更新结果
        """
        try:
            edit_info = {'id': info_id, 'sentiment': sentiment}
            await InfoSummaryDao.edit_info_summary_dao(query_db, edit_info)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='情感倾向更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e
