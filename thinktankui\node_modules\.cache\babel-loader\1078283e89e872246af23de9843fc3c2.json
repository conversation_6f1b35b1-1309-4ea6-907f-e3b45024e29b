{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_RightPanel", "_interopRequireDefault", "require", "_components", "_ResizeHandler", "_vuex", "_variables2", "name", "components", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "RightPanel", "Settings", "Sidebar", "TagsView", "mixins", "ResizeMixin", "computed", "_objectSpread2", "default", "mapState", "theme", "state", "settings", "sideTheme", "sidebar", "app", "device", "needTagsView", "tagsView", "fixedHeader", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "variables", "methods", "handleClickOutside", "$store", "dispatch"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar/>\n        <tags-view v-if=\"needTagsView\"/>\n      </div>\n      <app-main/>\n      <right-panel>\n        <settings/>\n      </right-panel>\n    </div>\n  </div>\n</template>\n\n<script>\nimport RightPanel from '@/components/RightPanel'\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    RightPanel,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      theme: state => state.settings.theme,\n      sideTheme: state => state.settings.sideTheme,\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    },\n    variables() {\n      return variables;\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/assets/styles/mixin.scss\";\n  @import \"~@/assets/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$base-sidebar-width});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px);\n  }\n\n  .sidebarHide .fixed-header {\n    width: 100%;\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"], "mappings": ";;;;;;;;AAkBA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA,oBAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA;EACA;EACAC,MAAA,GAAAC,sBAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA;IACAC,KAAA,WAAAA,MAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAF,KAAA;IAAA;IACAG,SAAA,WAAAA,UAAAF,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAC,SAAA;IAAA;IACAC,OAAA,WAAAA,QAAAH,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAD,OAAA;IAAA;IACAE,MAAA,WAAAA,OAAAL,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAC,MAAA;IAAA;IACAC,YAAA,WAAAA,aAAAN,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAM,QAAA;IAAA;IACAC,WAAA,WAAAA,YAAAR,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAO,WAAA;IAAA;EACA;IACAC,QAAA,WAAAA,SAAA;MACA;QACAC,WAAA,QAAAP,OAAA,CAAAQ,MAAA;QACAC,WAAA,OAAAT,OAAA,CAAAQ,MAAA;QACAE,gBAAA,OAAAV,OAAA,CAAAU,gBAAA;QACAC,MAAA,OAAAT,MAAA;MACA;IACA;IACAU,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;QAAAN,gBAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}