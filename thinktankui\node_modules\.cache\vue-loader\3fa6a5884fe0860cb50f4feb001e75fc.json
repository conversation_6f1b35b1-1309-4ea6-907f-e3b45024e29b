{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\PanelGroup.vue?vue&type=template&id=0333a520&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\PanelGroup.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749105919489}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}