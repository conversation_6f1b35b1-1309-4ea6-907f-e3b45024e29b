2025-06-06 10:30:08.052 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:30:08.053 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:30:08.815 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:30:08.815 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:30:08.820 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:30:09.342 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:30:09.859 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:30:09.859 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:30:46.096 | e5f05c4c31924960bb714f1d3cef87c3 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为e929e531-66dc-4849-942d-8e1ed91cb205的会话获取图片验证码成功
2025-06-06 10:31:06.661 | eccbc9f882d441448f4b9a74f019a79e | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-06 10:31:06.867 | 33787ecfb7644745b94e0f2eb7532c3e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 10:31:07.044 | d321b3a305ae4fe9b1ec5084d74d30e9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 10:32:00.309 | c43274f58b1d481aa695f12e29287d93 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 10:32:00.480 | b494858e69fd4f0c89adced8b8afd538 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 10:34:04.596 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:34:04.597 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:34:06.921 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:34:06.922 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:34:07.809 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:34:07.809 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:34:07.811 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:34:08.275 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:34:08.820 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:34:08.820 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:36:19.866 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:36:19.866 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:36:22.090 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:36:22.091 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:36:22.933 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:36:22.933 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:36:22.936 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:36:23.377 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:36:23.945 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:36:23.945 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:36:33.506 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:36:33.507 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:36:36.011 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:36:36.012 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:36:36.847 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:36:36.847 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:36:36.850 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:36:37.309 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:36:37.850 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:36:37.851 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:36:45.691 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:36:45.692 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:36:47.870 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:36:47.871 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:36:48.721 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:36:48.721 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:36:48.724 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:36:49.190 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:36:49.727 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:36:49.727 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:37:16.587 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:37:16.587 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:37:18.746 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:37:18.746 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:37:19.481 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:37:19.481 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:37:19.483 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:37:19.881 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:37:20.389 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:37:20.390 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:37:24.961 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:37:24.961 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:37:27.242 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:37:27.242 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:37:27.993 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:37:27.994 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:37:28.020 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:37:28.411 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:37:28.995 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:37:28.995 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:37:48.538 | 1ac12b08b8a348b9ad2ae3eda0f2b5cf | ERROR    | exceptions.handle:exception_handler:70 - 'list' object has no attribute 'rows'
Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "C:\Program Files\Python39\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 344
               │     └ 3
               └ <function _main at 0x00000265F306E790>

  File "C:\Program Files\Python39\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 344
           │    └ <function BaseProcess._bootstrap at 0x00000265F2FB2940>
           └ <SpawnProcess name='SpawnProcess-7' parent=27616 started>

  File "C:\Program Files\Python39\lib\multiprocessing\process.py", line 315, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000265F2FA3F70>
    └ <SpawnProcess name='SpawnProcess-7' parent=27616 started>

  File "C:\Program Files\Python39\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000265F3084940>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-7' parent=27616 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-7' parent=27616 started>
    │    └ <function subprocess_started at 0x00000265F5398700>
    └ <SpawnProcess name='SpawnProcess-7' parent=27616 started>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=2200, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000265F3084A60>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=2200, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x00000265F5395940>
           │       │   └ <uvicorn.server.Server object at 0x00000265F3084A60>
           │       └ <function run at 0x00000265F3092940>
           └ <module 'asyncio' from 'C:\\Program Files\\Python39\\lib\\asyncio\\__init__.py'>

  File "C:\Program Files\Python39\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000265A232D2C0>
           │    └ <function BaseEventLoop.run_until_complete at 0x00000265F4C29C10>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000265F4C29B80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000265F4C2B700>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000265F4BCA310>
    └ <Handle <TaskWakeupMethWrapper object at 0x00000265A265A1F0>(<Future finished result=None>)>

  File "C:\Program Files\Python39\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskWakeupMethWrapper object at 0x00000265A265A1F0>(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskWakeupMethWrapper object at 0x00000265A265A1F0>(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskWakeupMethWrapper object at 0x00000265A265A1F0>(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000265A236D190>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000265A2...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x00000265F54F0070>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000265A236D190>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000265A2...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000265A2...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000265A244B490>
          └ <fastapi.applications.FastAPI object at 0x00000265F54F0070>
> File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000265A24A30D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x00000265A244B400>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000265A244B490>

  File "H:\项目\金刚\3\thinktankapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000265A2587550>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x00000265A244B3D0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x00000265A244B400>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000265A2587550>
          │         │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x00000265A25BC730>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x00000265FFE8A700>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x00000265A25BC730>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000265A244B370>
          └ <starlette.middleware.gzip.GZipResponder object at 0x00000265A25BC730>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x00000265A25BC730>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000265A244B340>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000265A244B370>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x00000265A25BC730>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000265A25BC400>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000265A1FEE550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000265A244B340>
          └ <function wrap_app_handling_exceptions at 0x00000265F63975E0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000265A25870D0>
          │   │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000265A1FEE550>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000265A25870D0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000265A1FEE550>>
          └ <fastapi.routing.APIRouter object at 0x00000265A1FEE550>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000265A25870D0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x00000265F63AFD30>
          └ APIRoute(path='/public/keywordData/list', name='get_keyword_data_list_public', methods=['GET'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000265A25870D0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000265A2339310>
          └ APIRoute(path='/public/keywordData/list', name='get_keyword_data_list_public', methods=['GET'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000265A25870D0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000265A25BC130>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000265A2587C10>
          └ <function wrap_app_handling_exceptions at 0x00000265F63975E0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000265A2587280>
          │   │      └ <function RequestResponseCycle.receive at 0x00000265A25875E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000265A2587C10>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000265A25BC130>
                     └ <function get_request_handler.<locals>.app at 0x00000265A2339280>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000265F63BE940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'query_db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000265A258EE50>, 'pageNum': 1, 'pageSize': 10}
                 │         └ <function get_keyword_data_list_public at 0x00000265A19AFCA0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(1), name='pageNum', mode='validation'), ModelField(field_...

  File "H:\项目\金刚\3\thinktankapi\module_admin\controller\keyword_data_controller.py", line 78, in get_keyword_data_list_public
    keyword_data_page_query_result = await KeywordDataService.get_keyword_data_list_services(query_db, keyword_data_page_query, is_page=True)
                                           │                  │                              │         └ KeywordDataPageQueryModel(page_num=1, page_size=10, title=None, keyword=None, type=None, web=None, begin_time=None, end_time=...
                                           │                  │                              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000265A258EE50>
                                           │                  └ <classmethod object at 0x00000265A19ED4C0>
                                           └ <class 'module_admin.service.keyword_data_service.KeywordDataService'>

  File "H:\项目\金刚\3\thinktankapi\module_admin\service\keyword_data_service.py", line 39, in get_keyword_data_list_services
    for row in keyword_data_list_result.rows:
               └ [{'url': 'http://finance.eastmoney.com/a/202501223304743824.html', 'content': '以下是该网页的正文内容：\n\n---\n\n每经AI快讯，有投资者在投资者互动平台提问：请...

AttributeError: 'list' object has no attribute 'rows'
2025-06-06 10:38:15.125 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:38:15.125 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:38:17.384 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:38:17.385 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:38:18.150 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:38:18.150 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:38:18.152 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:38:18.569 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:38:19.085 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:38:19.085 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:38:21.083 | 3e59926990d44c64b347b96ff200a1d7 | ERROR    | exceptions.handle:exception_handler:70 - 'list' object has no attribute 'rows'
Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "C:\Program Files\Python39\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 180
               │     └ 3
               └ <function _main at 0x0000022E1D08E790>

  File "C:\Program Files\Python39\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 180
           │    └ <function BaseProcess._bootstrap at 0x0000022E1CFF2940>
           └ <SpawnProcess name='SpawnProcess-8' parent=27616 started>

  File "C:\Program Files\Python39\lib\multiprocessing\process.py", line 315, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000022E1CFE4F70>
    └ <SpawnProcess name='SpawnProcess-8' parent=27616 started>

  File "C:\Program Files\Python39\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000022E1D0A4940>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-8' parent=27616 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-8' parent=27616 started>
    │    └ <function subprocess_started at 0x0000022E1F3B8700>
    └ <SpawnProcess name='SpawnProcess-8' parent=27616 started>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=196, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000022E1D0A4A60>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=196, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000022E1F3B5940>
           │       │   └ <uvicorn.server.Server object at 0x0000022E1D0A4A60>
           │       └ <function run at 0x0000022E1D0B3940>
           └ <module 'asyncio' from 'C:\\Program Files\\Python39\\lib\\asyncio\\__init__.py'>

  File "C:\Program Files\Python39\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000022E4C34F340>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000022E1EC49C10>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000022E1EC49B80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022E1EC4B700>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022E1EBEA310>
    └ <Handle <TaskWakeupMethWrapper object at 0x0000022E4C6791F0>(<Future finished result=None>)>

  File "C:\Program Files\Python39\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskWakeupMethWrapper object at 0x0000022E4C6791F0>(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskWakeupMethWrapper object at 0x0000022E4C6791F0>(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskWakeupMethWrapper object at 0x0000022E4C6791F0>(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000022E4C38F190>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022E4C...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000022E1D0A4E50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000022E4C38F190>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022E4C...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022E4C...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000022E4C46B490>
          └ <fastapi.applications.FastAPI object at 0x0000022E1D0A4E50>
> File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000022E4C4C40D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000022...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000022E4C46B400>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000022E4C46B490>

  File "H:\项目\金刚\3\thinktankapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000022E4C5A8550>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000022E4C46B3D0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000022E4C46B400>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000022E4C5A8550>
          │         │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000022E4C5DD730>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000022E49EA9700>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000022E4C5DD730>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000022E4C46B370>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000022E4C5DD730>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000022E4C5DD730>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000022E4C46B340>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000022E4C46B370>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000022E4C5DD730>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000022E4C5DD400>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000022E4C00D550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000022E4C46B340>
          └ <function wrap_app_handling_exceptions at 0x0000022E203B75E0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022E4C5A80D0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000022E4C00D550>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022E4C5A80D0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000022E4C00D550>>
          └ <fastapi.routing.APIRouter object at 0x0000022E4C00D550>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022E4C5A80D0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000022E20394D30>
          └ APIRoute(path='/public/keywordData/list', name='get_keyword_data_list_public', methods=['GET'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022E4C5A80D0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000022E4C35B310>
          └ APIRoute(path='/public/keywordData/list', name='get_keyword_data_list_public', methods=['GET'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022E4C5A80D0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000022E4C5DD130>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000022E4C5A8C10>
          └ <function wrap_app_handling_exceptions at 0x0000022E203B75E0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022E4C5A8280>
          │   │      └ <function RequestResponseCycle.receive at 0x0000022E4C5A85E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000022E4C5A8C10>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000022E4C5DD130>
                     └ <function get_request_handler.<locals>.app at 0x0000022E4C35B280>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000022E203DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'query_db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000022E4C5ADE50>, 'pageNum': 1, 'pageSize': 10}
                 │         └ <function get_keyword_data_list_public at 0x0000022E4BA3DC10>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(1), name='pageNum', mode='validation'), ModelField(field_...

  File "H:\项目\金刚\3\thinktankapi\module_admin\controller\keyword_data_controller.py", line 78, in get_keyword_data_list_public
    keyword_data_page_query_result = await KeywordDataService.get_keyword_data_list_services(query_db, keyword_data_page_query, is_page=True)
                                           │                  │                              │         └ KeywordDataPageQueryModel(page_num=1, page_size=10, title=None, keyword=None, type=None, web=None, begin_time=None, end_time=...
                                           │                  │                              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000022E4C5ADE50>
                                           │                  └ <classmethod object at 0x0000022E4BA1EA90>
                                           └ <class 'module_admin.service.keyword_data_service.KeywordDataService'>

  File "H:\项目\金刚\3\thinktankapi\module_admin\service\keyword_data_service.py", line 39, in get_keyword_data_list_services
    for row in keyword_data_list_result.rows:
               └ [{'title': '*ST威<em>帝</em>：公司目前经营一切正常，公司2024年的业绩同比有一定提升', 'url': 'http://finance.eastmoney.com/a/202501223304743824.html', 'c...

AttributeError: 'list' object has no attribute 'rows'
2025-06-06 10:39:06.399 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:39:06.400 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:39:08.647 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:39:08.648 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:39:09.435 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:39:09.435 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:39:09.437 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:39:09.846 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:39:10.357 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:39:10.357 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:39:14.634 | 040f4bf1ddf14f6788ea575748d47162 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:78 - 查询参数: pageNum=1, pageSize=10
2025-06-06 10:39:14.823 | 040f4bf1ddf14f6788ea575748d47162 | ERROR    | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:98 - 获取关键词数据列表失败: 'list' object has no attribute 'rows'
2025-06-06 10:39:37.218 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:39:37.218 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:39:39.489 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:39:39.490 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 10:39:40.411 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 10:39:40.412 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 10:39:40.413 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 10:39:40.910 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 10:39:41.460 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 10:39:41.460 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-06 10:39:43.922 | b3894e7679f4433e952e17b7c16c53ab | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:78 - 查询参数: pageNum=1, pageSize=10
2025-06-06 10:39:44.007 | b3894e7679f4433e952e17b7c16c53ab | ERROR    | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:98 - 获取关键词数据列表失败: 'dict' object has no attribute 'id'
2025-06-06 10:40:05.242 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 10:40:05.242 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 10:40:07.494 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-06 10:40:07.495 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
