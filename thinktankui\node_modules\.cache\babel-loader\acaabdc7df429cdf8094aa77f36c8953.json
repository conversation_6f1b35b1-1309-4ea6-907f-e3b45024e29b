{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\IconSelect\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJIOi9cdTk4NzlcdTc2RUUvXHU5MUQxXHU1MjFBLzMvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbHRlci5qcyIpOwp2YXIgX3JlcXVpcmVJY29ucyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9yZXF1aXJlSWNvbnMiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnSWNvblNlbGVjdCcsCiAgcHJvcHM6IHsKICAgIGFjdGl2ZUljb246IHsKICAgICAgdHlwZTogU3RyaW5nCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbmFtZTogJycsCiAgICAgIGljb25MaXN0OiBfcmVxdWlyZUljb25zLmRlZmF1bHQKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaWx0ZXJJY29uczogZnVuY3Rpb24gZmlsdGVySWNvbnMoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuaWNvbkxpc3QgPSBfcmVxdWlyZUljb25zLmRlZmF1bHQ7CiAgICAgIGlmICh0aGlzLm5hbWUpIHsKICAgICAgICB0aGlzLmljb25MaXN0ID0gdGhpcy5pY29uTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLmluY2x1ZGVzKF90aGlzLm5hbWUpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgc2VsZWN0ZWRJY29uOiBmdW5jdGlvbiBzZWxlY3RlZEljb24obmFtZSkgewogICAgICB0aGlzLiRlbWl0KCdzZWxlY3RlZCcsIG5hbWUpOwogICAgICBkb2N1bWVudC5ib2R5LmNsaWNrKCk7CiAgICB9LAogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLm5hbWUgPSAnJzsKICAgICAgdGhpcy5pY29uTGlzdCA9IF9yZXF1aXJlSWNvbnMuZGVmYXVsdDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_requireIcons", "_interopRequireDefault", "require", "name", "props", "activeIcon", "type", "String", "data", "iconList", "icons", "methods", "filterIcons", "_this", "filter", "item", "includes", "selectedIcon", "$emit", "document", "body", "click", "reset"], "sources": ["src/components/IconSelect/index.vue"], "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div class=\"list-container\">\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\n            <span>{{ item }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  props: {\n    activeIcon: {\n      type: String\n    }\n  },\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-search {\n      position: relative;\n      margin-bottom: 5px;\n    }\n    .icon-list {\n      height: 200px;\n      overflow: auto;\n      .list-container {\n        display: flex;\n        flex-wrap: wrap;\n        .icon-item-wrapper {\n          width: calc(100% / 3);\n          height: 25px;\n          line-height: 25px;\n          cursor: pointer;\n          display: flex;\n          .icon-item {\n            display: flex;\n            max-width: 100%;\n            height: 100%;\n            padding: 0 5px;\n            &:hover {\n              background: #ececec;\n              border-radius: 5px;\n            }\n            .icon {\n              flex-shrink: 0;\n            }\n            span {\n              display: inline-block;\n              vertical-align: -0.15em;\n              fill: currentColor;\n              padding-left: 2px;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n          }\n          .icon-item.active {\n            background: #ececec;\n            border-radius: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AAoBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAL,IAAA;MACAM,QAAA,EAAAC;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAAJ,QAAA,GAAAC,qBAAA;MACA,SAAAP,IAAA;QACA,KAAAM,QAAA,QAAAA,QAAA,CAAAK,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAV,IAAA;QAAA;MACA;IACA;IACAc,YAAA,WAAAA,aAAAd,IAAA;MACA,KAAAe,KAAA,aAAAf,IAAA;MACAgB,QAAA,CAAAC,IAAA,CAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAnB,IAAA;MACA,KAAAM,QAAA,GAAAC,qBAAA;IACA;EACA;AACA", "ignoreList": []}]}