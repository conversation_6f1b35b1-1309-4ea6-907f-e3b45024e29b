<template>
  <div class="app-container">
    <div class="page-container">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <el-button type="warning" class="new-scheme-btn" @click="createNewScheme">
            <i class="el-icon-plus"></i> 新建方案
          </el-button>
          <div class="sidebar-btn" @click="toggleSidebar">
            <i class="el-icon-s-fold"></i>
          </div>
        </div>

        <div class="sidebar-search">
          <el-input
            v-model="sidebarSearchText"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            size="small"
            @input="searchSidebar"
          ></el-input>
        </div>

        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect"
          >
            <template v-for="(item, index) in menuCategories">
              <!-- 使用唯一的key -->
              <el-menu-item
                v-if="item.isItem"
                :key="'item-' + item.name"
                :index="item.name"
                :class="{ 'active-menu-item': activeMenuItem === item.name }"
              >
                <span>{{ item.name }}</span>
              </el-menu-item>

              <!-- 如果是子菜单 -->
              <el-submenu
                v-else
                :key="'submenu-' + item.name"
                :index="item.name"
              >
                <template slot="title">
                  <span>{{ item.name }}({{ item.count }})</span>
                </template>
                <!-- 子菜单项 -->
                <el-menu-item
                  v-for="child in item.children"
                  :key="child.name"
                  :index="child.name"
                >
                  {{ child.name }}
                </el-menu-item>
              </el-submenu>
            </template>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content">
        <div class="report-container">
          <div class="report-header">
            <div class="title">方案报告</div>
            <div class="actions">
              <el-button type="primary" size="small" icon="el-icon-edit" @click="editReport">模板管理</el-button>
              <el-button type="primary" size="small" icon="el-icon-document" @click="exportReport">定时任务管理</el-button>
              <el-button type="primary" size="small" icon="el-icon-plus" @click="createReport">新建报告</el-button>
            </div>
          </div>

          <!-- 报告列表视图 -->
          <div v-if="currentView === 'report'">
            <div class="filter-options">
              <div class="filter-row">
                <div class="filter-item">
                  <span>时间范围：</span>
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    style="width: 240px;"
                  ></el-date-picker>
                </div>
                <div class="filter-item">
                  <el-select v-model="filterType" placeholder="全部" size="small" style="width: 100px;">
                    <el-option label="全部" value="all"></el-option>
                    <el-option label="名称" value="name"></el-option>
                    <el-option label="类型" value="type"></el-option>
                  </el-select>
                  <el-input
                    v-model="searchKeyword"
                    placeholder="请输入内容"
                    size="small"
                    style="width: 200px; margin-left: 5px;"
                  ></el-input>
                </div>
                <div class="tab-container">
                  <div
                    class="tab-item"
                    :class="{ 'active': activeTab === 'normal' }"
                    @click="switchTab('normal')"
                  >
                    普通报告
                  </div>
                  <div
                    class="tab-item"
                    :class="{ 'active': activeTab === 'competitor' }"
                    @click="switchTab('competitor')"
                  >
                    竞对报告
                  </div>
                </div>
              </div>
            </div>

            <div class="report-table">
              <el-table
                :data="reportList"
                style="width: 100%"
                border
                stripe
                :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
              >
                <el-table-column
                  prop="name"
                  label="配置名称"
                  width="180"
                ></el-table-column>
                <el-table-column
                  prop="createTime"
                  label="创建时间"
                  width="180"
                ></el-table-column>
                <el-table-column
                  prop="type"
                  label="配置类型"
                  width="120"
                ></el-table-column>
                <el-table-column
                  prop="status"
                  label="状态"
                  width="100"
                ></el-table-column>
                <el-table-column
                  prop="reportStatus"
                  label="报告状态"
                  width="120"
                ></el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      @click="viewReport(scope.row)"
                    >查看</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="editReportItem(scope.row)"
                    >编辑</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      class="delete-btn"
                      @click="deleteReport(scope.row)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 空数据显示 -->
              <div v-if="reportList.length === 0" class="empty-data">
                <i class="el-icon-data-analysis empty-icon"></i>
                <p>暂无数据</p>
              </div>
            </div>
          </div>

          <!-- 模板管理视图 -->
          <div v-if="currentView === 'template'">
            <div class="back-button">
              <el-button type="text" icon="el-icon-arrow-left" @click="goBack">返回</el-button>
            </div>
            <div class="template-header">
              <div class="template-title">
                <i class="el-icon-document"></i> 报告模板
              </div>
              <div class="template-actions">
                <div class="template-tab-container">
                  <div
                    class="template-tab"
                    :class="{ 'active': templateType === 'normal' }"
                    @click="switchTemplateType('normal')"
                  >
                    普通模板
                  </div>
                  <div
                    class="template-tab"
                    :class="{ 'active': templateType === 'competitor' }"
                    @click="switchTemplateType('competitor')"
                  >
                    竞对模板
                  </div>
                </div>
              </div>
            </div>

            <div class="create-template-btn">
              <el-button type="primary" plain @click="createProductTemplate">
                <i class="el-icon-plus"></i> 创建产品模板
              </el-button>
            </div>

            <div class="template-table">
              <el-table
                :data="templateList"
                style="width: 100%"
                border
                stripe
                :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
              >
                <el-table-column
                  prop="name"
                  label="模板名称"
                  width="180"
                ></el-table-column>
                <el-table-column
                  prop="createTime"
                  label="创建时间"
                  width="180"
                ></el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      @click="viewTemplate(scope.row)"
                    >查看</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="editTemplate(scope.row)"
                    >编辑</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      class="delete-btn"
                      @click="deleteTemplate(scope.row)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <div class="pagination-container">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="total"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ReportList",
  data() {
    return {
      dateRange: [],
      filterType: 'all',
      searchKeyword: '',
      reportList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      activeTab: 'normal', // 当前激活的选项卡：normal-普通报告，competitor-竞对报告
      currentView: 'report', // 当前视图：report-报告列表，template-模板管理
      templateType: 'normal', // 当前模板类型：normal-普通模板，competitor-竞对模板
      // 模板列表数据
      templateList: [
        { name: '品牌-热议话题', createTime: '2019-11-18 14:02:08', operation: '' },
        { name: '品牌-舆论', createTime: '2019-11-18 14:06:52', operation: '' },
        { name: '品牌-竞品', createTime: '2021-04-07 15:15:00', operation: '' }
      ],
      // 侧边栏数据
      sidebarCollapsed: false,
      sidebarSearchText: '',
      activeMenuItem: '方太',
      menuCategories: [
        { name: '总监', count: 1, children: [] },
        { name: '品牌', count: 1, children: [] },
        { name: '方太', count: 0, isItem: true },
        { name: '人物', count: 0, children: [] },
        { name: '机构', count: 0, children: [] },
        { name: '产品', count: 0, children: [] },
        { name: '事件', count: 0, children: [] },
        { name: '话题', count: 0, children: [] }
      ]
    };
  },
  methods: {
    // 侧边栏相关方法
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },
    handleMenuSelect(index) {
      this.activeMenuItem = index;
      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等
      this.fetchReportList();
    },
    createNewScheme() {
      // 新建方案的逻辑
      this.$message({
        message: '新建方案功能待实现',
        type: 'info'
      });
    },
    searchSidebar() {
      // 侧边栏搜索逻辑
      console.log('搜索关键词：', this.sidebarSearchText);
      // 实现搜索逻辑
    },
    // 模板管理
    editReport() {
      this.currentView = 'template';
      this.$message.success("切换到模板管理");
    },
    // 导出报告
    exportReport() {
      this.$message.success("定时任务管理");
    },
    // 创建新报告
    createReport() {
      this.$message.success("新建报告");
    },
    // 查看报告
    viewReport(row) {
      this.$message.success(`查看报告: ${row.name}`);
    },
    // 编辑报告项
    editReportItem(row) {
      this.$message.success(`编辑报告: ${row.name}`);
    },
    // 删除报告
    deleteReport(row) {
      this.$confirm(`确认删除报告 "${row.name}"?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$message.success(`删除报告: ${row.name}`);
      }).catch(() => {
        this.$message.info("已取消删除");
      });
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchReportList();
    },
    // 获取报告列表
    fetchReportList() {
      // 实际应用中这里需要调用接口获取数据
      // 这里模拟空数据
      this.reportList = [];
      this.total = 0;
    },
    // 查询按钮点击事件
    handleQuery() {
      this.$message.success("执行查询操作");
      this.fetchReportList();
    },
    // 重置按钮点击事件
    handleReset() {
      this.dateRange = [];
      this.filterType = 'all';
      this.searchKeyword = '';
      this.$message.success("重置筛选条件");
    },
    // 切换选项卡
    switchTab(tab) {
      this.activeTab = tab;
      if (tab === 'normal') {
        this.handleQuery();
      } else {
        this.handleReset();
      }
    },
    // 创建模板
    createTemplate() {
      this.$message.success("创建新模板");
    },
    // 创建产品模板
    createProductTemplate() {
      this.$message.success("创建产品模板");
    },
    // 查看模板
    viewTemplate(row) {
      this.$message.success(`查看模板: ${row.name}`);
    },
    // 编辑模板
    editTemplate(row) {
      this.$message.success(`编辑模板: ${row.name}`);
    },
    // 删除模板
    deleteTemplate(row) {
      this.$confirm(`确认删除模板 "${row.name}"?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$message.success(`删除模板: ${row.name}`);
      }).catch(() => {
        this.$message.info("已取消删除");
      });
    },
    // 返回按钮点击事件
    goBack() {
      this.currentView = 'report';
      this.$message.success("返回报告列表");
    },
    // 切换模板类型
    switchTemplateType(type) {
      this.templateType = type;
      this.$message.success(`切换到${type === 'normal' ? '普通模板' : '竞对模板'}`);
    }
  },
  created() {
    this.fetchReportList();
  }
};
</script>

<style scoped>
.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-container {
  display: flex;
  height: 100%;
}

/* 左侧导航栏样式 */
.left-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: width 0.3s;
}

/* 折叠状态的侧边栏 */
.left-sidebar.collapsed {
  width: 64px;
}

.left-sidebar.collapsed .sidebar-search,
.left-sidebar.collapsed .el-menu-item span,
.left-sidebar.collapsed .el-submenu__title span {
  display: none;
}

.left-sidebar.collapsed .new-scheme-btn {
  padding: 8px 0;
  font-size: 0;
}

.left-sidebar.collapsed .new-scheme-btn i {
  font-size: 16px;
  margin: 0;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.new-scheme-btn {
  flex: 1;
  font-size: 12px;
  padding: 8px 10px;
}

.sidebar-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  cursor: pointer;
  color: #909399;
}

.sidebar-search {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu-list {
  border-right: none;
}

.active-menu-item {
  background-color: #ecf5ff !important;
  color: #409EFF !important;
}

/* 右侧内容区样式 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
  background-color: #f5f7fa;
}

/* 覆盖Element UI的一些默认样式 */
::v-deep .el-menu-item, ::v-deep .el-submenu__title {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

::v-deep .el-submenu .el-menu-item {
  height: 36px;
  line-height: 36px;
  padding: 0 20px 0 40px;
}

/* 报告中心样式 */
.report-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  height: 100%;
  overflow-y: auto;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.report-header .title {
  font-size: 18px;
  font-weight: bold;
}

.report-header .actions {
  display: flex;
  gap: 10px;
}

.filter-options {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.tab-container {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  width: fit-content;
  margin-left: auto;
}

.tab-item {
  padding: 8px 20px;
  cursor: pointer;
  background-color: #fff;
  color: #606266;
  font-size: 14px;
  text-align: center;
  border-right: 1px solid #dcdfe6;
  min-width: 100px;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background-color: #409EFF;
  color: #fff;
}

.filter-item span {
  margin-right: 10px;
  color: #606266;
  font-size: 14px;
}

.report-table {
  margin-bottom: 20px;
  position: relative;
  min-height: 300px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.report-table ::v-deep .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  text-align: center;
}

.report-table ::v-deep .el-table td {
  text-align: center;
}

.empty-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.empty-data .empty-icon {
  font-size: 60px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-data p {
  color: #909399;
  font-size: 14px;
}

.delete-btn {
  color: #f56c6c;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 模板管理样式 */
.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.template-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.template-actions {
  display: flex;
  gap: 10px;
}

.template-tab-container {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  width: fit-content;
}

.template-tab {
  padding: 8px 20px;
  cursor: pointer;
  background-color: #fff;
  color: #606266;
  font-size: 14px;
  text-align: center;
  border-right: 1px solid #dcdfe6;
  min-width: 100px;
}

.template-tab:last-child {
  border-right: none;
}

.template-tab.active {
  background-color: #409EFF;
  color: #fff;
}

.template-table {
  margin-bottom: 20px;
}

.create-template-btn {
  margin-bottom: 15px;
}

.create-template-btn .el-button {
  border-color: #f56c6c;
  color: #f56c6c;
}

.create-template-btn .el-button:hover {
  background-color: #fef0f0;
}

.back-button {
  text-align: right;
  margin-bottom: 10px;
}

.back-button .el-button {
  color: #409EFF;
  font-size: 14px;
  padding: 0;
}

.back-button .el-button:hover {
  color: #66b1ff;
}
</style>
