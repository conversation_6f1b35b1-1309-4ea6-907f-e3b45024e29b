{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\opinion-overview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\opinion-overview\\index.vue", "mtime": 1748441392737}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnT3Bpbmlvbk92ZXJ2aWV3JywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlTWVudUl0ZW06ICfmlrnlpKonLAogICAgICBzZWFyY2hUZXh0OiAnJywKICAgICAgYWN0aXZlVGFiOiAnb3Bpbmlvbi1tb25pdG9yJywgLy8g6buY6K6k5r+A5rS76IiG5oOF55uR5rWL5qCH562+CiAgICAgIG9yaWdpbmFsVG9wTmF2OiB1bmRlZmluZWQsIC8vIOWtmOWCqOWOn+Wni+eahHRvcE5hdueKtuaAgQogICAgICAvLyDlm77ooajlrp7kvosKICAgICAgbWFwQ2hhcnQ6IG51bGwsCiAgICAgIHBsYXRmb3JtQ2hhcnQ6IG51bGwsCiAgICAgIHNlbnRpbWVudENoYXJ0OiBudWxsLAogICAgICB0cmVuZENoYXJ0OiBudWxsLCAvLyDotovlir/lm77ooajlrp7kvosKICAgICAgLy8g54Ot6Zeo5paH56ug5pWw5o2uCiAgICAgIGhvdEFydGljbGVzOiBbCiAgICAgICAgewogICAgICAgICAgaWNvbjogJ+e6oicsCiAgICAgICAgICB0eXBlOiAnbmVnYXRpdmUnLAogICAgICAgICAgdGl0bGU6ICfmn5Dlk4HniYzkuqflk4HotKjph4/pl67popjlvJXlj5HmtojotLnogIXkuI3mu6EnLAogICAgICAgICAgc291cmNlOiAn5paw5rWq6LSi57uPJywKICAgICAgICAgIGF1dGhvcjogJ+i0oue7j+iusOiAhScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGljb246ICfpu4QnLAogICAgICAgICAgdHlwZTogJ25ldXRyYWwnLAogICAgICAgICAgdGl0bGU6ICfluILlnLrliIbmnpDvvJrlrrbnlLXooYzkuJrlj5HlsZXotovlir8nLAogICAgICAgICAgc291cmNlOiAn5Lit5Zu957uP5rWO572RJywKICAgICAgICAgIGF1dGhvcjogJ+W4guWcuuWIhuaekOW4iCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGljb246ICfnu78nLAogICAgICAgICAgdHlwZTogJ3Bvc2l0aXZlJywKICAgICAgICAgIHRpdGxlOiAn5Yib5paw5oqA5pyv5o6o5Yqo6KGM5Lia5Y+R5bGVJywKICAgICAgICAgIHNvdXJjZTogJ+enkeaKgOaXpeaKpScsCiAgICAgICAgICBhdXRob3I6ICfnp5HmioDorrDogIUnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpY29uOiAn57qiJywKICAgICAgICAgIHR5cGU6ICduZWdhdGl2ZScsCiAgICAgICAgICB0aXRsZTogJ+a2iOi0ueiAheaKleivieWkhOeQhuS4jeW9k+W8leWPkeWFs+azqCcsCiAgICAgICAgICBzb3VyY2U6ICfmtojotLnogIXmiqUnLAogICAgICAgICAgYXV0aG9yOiAn5raI6LS557u05p2DJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWNvbjogJ+e7vycsCiAgICAgICAgICB0eXBlOiAncG9zaXRpdmUnLAogICAgICAgICAgdGl0bGU6ICfkvIHkuJrnpL7kvJrotKPku7vojrflvpforqTlj68nLAogICAgICAgICAgc291cmNlOiAn5Lq65rCR5pel5oqlJywKICAgICAgICAgIGF1dGhvcjogJ+ekvuS8muiusOiAhScKICAgICAgICB9CiAgICAgIF0sCiAgICAgIC8vIOacgOaWsOWFrOWRiuaVsOaNrgogICAgICBhbm5vdW5jZW1lbnRzOiBbCiAgICAgICAgewogICAgICAgICAgbGV2ZWw6ICdoaWdoJywKICAgICAgICAgIHRpdGxlOiAn6IiG5oOF55uR5rWL57O757uf5Y2H57qn6YCa55+lJywKICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTIwIDEwOjMwOjAwJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGV2ZWw6ICdtZWRpdW0nLAogICAgICAgICAgdGl0bGU6ICfkupTkuIDlgYfmnJ/nm5HmtYvlronmjpInLAogICAgICAgICAgdGltZTogJzIwMjMtMDQtMTkgMTY6NDU6MDAnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsZXZlbDogJ2xvdycsCiAgICAgICAgICB0aXRsZTogJ+aVsOaNrue7n+iuoeaKpeWRiuW3sueUn+aIkCcsCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0xOSAxNDoyMDowMCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxldmVsOiAnaGlnaCcsCiAgICAgICAgICB0aXRsZTogJ+mHjeimgeiIhuaDhemihOitpuaPkOmGkicsCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0xOSAwOToxNTowMCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxldmVsOiAnbWVkaXVtJywKICAgICAgICAgIHRpdGxlOiAn57O757uf57u05oqk5a6M5oiQ6YCa55+lJywKICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTE4IDE4OjMwOjAwJwogICAgICAgIH0KICAgICAgXSwKICAgICAgLy8g5Zyw5Zu+5pWw5o2uCiAgICAgIG1hcERhdGE6IFsKICAgICAgICB7bmFtZTogJ+WMl+S6rCcsIHZhbHVlOiAxNTAwMH0sCiAgICAgICAge25hbWU6ICfkuIrmtbcnLCB2YWx1ZTogMTIwMDB9LAogICAgICAgIHtuYW1lOiAn5bm/5LicJywgdmFsdWU6IDE4MDAwfSwKICAgICAgICB7bmFtZTogJ+a1meaxnycsIHZhbHVlOiA4MDAwfSwKICAgICAgICB7bmFtZTogJ+axn+iLjycsIHZhbHVlOiA5MDAwfSwKICAgICAgICB7bmFtZTogJ+WxseS4nCcsIHZhbHVlOiA3MDAwfSwKICAgICAgICB7bmFtZTogJ+Wbm+W3nScsIHZhbHVlOiA2MDAwfSwKICAgICAgICB7bmFtZTogJ+a5luWMlycsIHZhbHVlOiA1MDAwfQogICAgICBdLAogICAgICAvLyDlubPlj7DmlbDmja4KICAgICAgcGxhdGZvcm1EYXRhOiBbCiAgICAgICAge25hbWU6ICflvq7ljZonLCB2YWx1ZTogMzUuMiwgY29sb3I6ICcjZmY2YjZiJ30sCiAgICAgICAge25hbWU6ICflvq7kv6EnLCB2YWx1ZTogMjguNiwgY29sb3I6ICcjNTFjZjY2J30sCiAgICAgICAge25hbWU6ICfmipbpn7MnLCB2YWx1ZTogMTguNCwgY29sb3I6ICcjMzM5YWYwJ30sCiAgICAgICAge25hbWU6ICfku4rml6XlpLTmnaEnLCB2YWx1ZTogMTIuOCwgY29sb3I6ICcjZmZkNDNiJ30sCiAgICAgICAge25hbWU6ICflhbbku5YnLCB2YWx1ZTogNS4wLCBjb2xvcjogJyM4NjhlOTYnfQogICAgICBdLAogICAgICAvLyDmg4XmhJ/mlbDmja4KICAgICAgc2VudGltZW50RGF0YTogWwogICAgICAgIHtuYW1lOiAn5q2j6Z2iJywgdmFsdWU6IDY1LjIsIGNvbG9yOiAnIzUyYzQxYSd9LAogICAgICAgIHtuYW1lOiAn5Lit5oCnJywgdmFsdWU6IDI4LjMsIGNvbG9yOiAnI2ZhYWQxNCd9LAogICAgICAgIHtuYW1lOiAn6LSf6Z2iJywgdmFsdWU6IDYuNSwgY29sb3I6ICcjZmY0ZDRmJ30KICAgICAgXQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOmakOiXj+mhtumDqOWvvOiIquagjwogICAgdGhpcy5vcmlnaW5hbFRvcE5hdiA9IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRvcE5hdgogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgIGtleTogJ3RvcE5hdicsCiAgICAgIHZhbHVlOiBmYWxzZQogICAgfSkKCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIC8vIOagueaNruW9k+WJjea/gOa0u+eahOagh+etvuWIneWni+WMluWvueW6lOeahOWbvuihqAogICAgICBpZiAodGhpcy5hY3RpdmVUYWIgPT09ICdvcGluaW9uLW1vbml0b3InKSB7CiAgICAgICAgdGhpcy5pbml0VHJlbmRDaGFydCgpCiAgICAgIH0gZWxzZSBpZiAodGhpcy5hY3RpdmVUYWIgPT09ICdpbmZvLXN1bW1hcnknKSB7CiAgICAgICAgdGhpcy5pbml0Q2hhcnRzKCkKICAgICAgfQogICAgfSkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4KICAgIGlmICh0aGlzLm9yaWdpbmFsVG9wTmF2ICE9PSB1bmRlZmluZWQpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAga2V5OiAndG9wTmF2JywKICAgICAgICB2YWx1ZTogdGhpcy5vcmlnaW5hbFRvcE5hdgogICAgICB9KQogICAgfQoKICAgIC8vIOmUgOavgeWbvuihqOWunuS+iwogICAgaWYgKHRoaXMubWFwQ2hhcnQpIHsKICAgICAgdGhpcy5tYXBDaGFydC5kaXNwb3NlKCkKICAgIH0KICAgIGlmICh0aGlzLnBsYXRmb3JtQ2hhcnQpIHsKICAgICAgdGhpcy5wbGF0Zm9ybUNoYXJ0LmRpc3Bvc2UoKQogICAgfQogICAgaWYgKHRoaXMuc2VudGltZW50Q2hhcnQpIHsKICAgICAgdGhpcy5zZW50aW1lbnRDaGFydC5kaXNwb3NlKCkKICAgIH0KICAgIGlmICh0aGlzLnRyZW5kQ2hhcnQpIHsKICAgICAgdGhpcy50cmVuZENoYXJ0LmRpc3Bvc2UoKQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlTWVudVNlbGVjdChpbmRleCkgewogICAgICB0aGlzLmFjdGl2ZU1lbnVJdGVtID0gaW5kZXgKICAgICAgY29uc29sZS5sb2coJ01lbnUgc2VsZWN0ZWQ6JywgaW5kZXgpCiAgICAgIC8vIOi/memHjOWPr+S7peagueaNrumAieaLqeeahOaWueahiOWKoOi9veS4jeWQjOeahOaVsOaNrgogICAgfSwKICAgIGhhbmRsZVRhYkNsaWNrKHRhYikgewogICAgICBjb25zb2xlLmxvZygnVGFiIGNsaWNrZWQ6JywgdGFiLm5hbWUpCiAgICAgIC8vIOagueaNruS4jeWQjOagh+etvuWKoOi9veS4jeWQjOWGheWuuQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgaWYgKHRhYi5uYW1lID09PSAnb3Bpbmlvbi1tb25pdG9yJykgewogICAgICAgICAgdGhpcy5pbml0VHJlbmRDaGFydCgpCiAgICAgICAgfSBlbHNlIGlmICh0YWIubmFtZSA9PT0gJ2luZm8tc3VtbWFyeScpIHsKICAgICAgICAgIHRoaXMuaW5pdENoYXJ0cygpCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGluaXRDaGFydHMoKSB7CiAgICAgIC8vIOWIneWni+WMluWbvuihqAogICAgICB0aGlzLmluaXRNYXAoKQogICAgICB0aGlzLmluaXRQbGF0Zm9ybUNoYXJ0KCkKICAgICAgdGhpcy5pbml0U2VudGltZW50Q2hhcnQoKQogICAgfSwKICAgIGluaXRUcmVuZENoYXJ0KCkgewogICAgICAvLyDliJ3lp4vljJboiIbmg4Xotovlir/lm77ooagKICAgICAgY29uc3QgY2hhcnRDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgndHJlbmQtY2hhcnQnKQogICAgICBpZiAoIWNoYXJ0Q29udGFpbmVyKSByZXR1cm4KCiAgICAgIHRoaXMudHJlbmRDaGFydCA9IGVjaGFydHMuaW5pdChjaGFydENvbnRhaW5lcikKCiAgICAgIC8vIOeUn+aIkDMw5aSp55qE5pel5pyf5pWw5o2uCiAgICAgIGNvbnN0IGRhdGVzID0gW10KICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpCiAgICAgIGZvciAobGV0IGkgPSAyOTsgaSA+PSAwOyBpLS0pIHsKICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodG9kYXkpCiAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpIC0gaSkKICAgICAgICBkYXRlcy5wdXNoKGRhdGUuZ2V0TW9udGgoKSArIDEgKyAnLicgKyBkYXRlLmdldERhdGUoKSkKICAgICAgfQoKICAgICAgLy8g5qih5ouf5ZCE5bmz5Y+w55qE5pWw5o2uCiAgICAgIGNvbnN0IHdlaWJvRGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCAxNTAwLCAyNTAwKQogICAgICBjb25zdCB3ZWNoYXREYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDEyMDAsIDIwMDApCiAgICAgIGNvbnN0IGRvdXlpbkRhdGEgPSB0aGlzLmdlbmVyYXRlUmFuZG9tRGF0YSgzMCwgODAwLCAxNTAwKQogICAgICBjb25zdCB0b3V0aWFEYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDYwMCwgMTIwMCkKICAgICAgY29uc3Qgb3RoZXJEYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDMwMCwgODAwKQoKICAgICAgY29uc3Qgb3B0aW9uID0gewogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdjcm9zcycsCiAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzZhNzk4NScKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7CiAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPicKICAgICAgICAgICAgcGFyYW1zLmZvckVhY2gocGFyYW0gPT4gewogICAgICAgICAgICAgIHJlc3VsdCArPSBgPHNwYW4gc3R5bGU9ImNvbG9yOiR7cGFyYW0uY29sb3J9Ij7il488L3NwYW4+ICR7cGFyYW0uc2VyaWVzTmFtZX06ICR7cGFyYW0udmFsdWV9PGJyLz5gCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHJldHVybiByZXN1bHQKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGxlZ2VuZDogewogICAgICAgICAgZGF0YTogWyflvq7ljZonLCAn5b6u5L+hJywgJ+aKlumfsycsICflpLTmnaEnLCAn5YW25LuWJ10sCiAgICAgICAgICB0b3A6IDIwLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgZ3JpZDogewogICAgICAgICAgbGVmdDogJzMlJywKICAgICAgICAgIHJpZ2h0OiAnNCUnLAogICAgICAgICAgYm90dG9tOiAnMyUnLAogICAgICAgICAgdG9wOiAnMTUlJywKICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgeEF4aXM6IHsKICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsCiAgICAgICAgICBkYXRhOiBkYXRlcywKICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICBmb250U2l6ZTogMTAsCiAgICAgICAgICAgIGNvbG9yOiAnIzY2NicKICAgICAgICAgIH0sCiAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyNlMGUwZTAnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAxMCwKICAgICAgICAgICAgY29sb3I6ICcjNjY2JwogICAgICAgICAgfSwKICAgICAgICAgIGF4aXNMaW5lOiB7CiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI2UwZTBlMCcKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHNwbGl0TGluZTogewogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyNmMGYwZjAnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHNlcmllczogWwogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAn5b6u5Y2aJywKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywKICAgICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWwogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgyNCwgMTQ0LCAyNTUsIDAuNiknIH0sCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDI0LCAxNDQsIDI1NSwgMC4xKScgfQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzE4OTBmZicsCiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjMTg5MGZmJwogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhOiB3ZWlib0RhdGEKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICflvq7kv6EnLAogICAgICAgICAgICB0eXBlOiAnbGluZScsCiAgICAgICAgICAgIHN0YWNrOiAnVG90YWwnLAogICAgICAgICAgICBhcmVhU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDgyLCAxOTYsIDI2LCAwLjYpJyB9LAogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSg4MiwgMTk2LCAyNiwgMC4xKScgfQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzUyYzQxYScsCiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjNTJjNDFhJwogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhOiB3ZWNoYXREYXRhCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAn5oqW6Z+zJywKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywKICAgICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWwogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgxMTQsIDQ2LCAyMDksIDAuNiknIH0sCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDExNCwgNDYsIDIwOSwgMC4xKScgfQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzcyMmVkMScsCiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjNzIyZWQxJwogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhOiBkb3V5aW5EYXRhCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAn5aS05p2hJywKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywKICAgICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWwogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgyNTAsIDE0MCwgMjIsIDAuNiknIH0sCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDI1MCwgMTQwLCAyMiwgMC4xKScgfQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI2ZhOGMxNicsCiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjZmE4YzE2JwogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhOiB0b3V0aWFEYXRhCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAn5YW25LuWJywKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywKICAgICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWwogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgxNjAsIDIxNywgMTcsIDAuNiknIH0sCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDE2MCwgMjE3LCAxNywgMC4xKScgfQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI2EwZDkxMScsCiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjYTBkOTExJwogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhOiBvdGhlckRhdGEKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0KCiAgICAgIHRoaXMudHJlbmRDaGFydC5zZXRPcHRpb24ob3B0aW9uKQogICAgfSwKICAgIGluaXRNYXAoKSB7CiAgICAgIC8vIOaaguaXtui3s+i/h+WcsOWbvuWIneWni+WMlu+8jOmBv+WFjee8uuWwkeWcsOWbvuaVsOaNruWvvOiHtOeahOmUmeivrwogICAgICBjb25zb2xlLmxvZygn5Zyw5Zu+5Yid5aeL5YyW5bey6Lez6L+H77yM6ZyA6KaB5byV5YWl5Lit5Zu95Zyw5Zu+5pWw5o2uJykKICAgIH0sCiAgICBpbml0UGxhdGZvcm1DaGFydCgpIHsKICAgICAgLy8g5Yid5aeL5YyW5bmz5Y+w5YiG5p6Q6Z2i56ev5Zu+CiAgICAgIGNvbnN0IGNoYXJ0Q29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3BsYXRmb3JtLWNoYXJ0JykKICAgICAgaWYgKCFjaGFydENvbnRhaW5lcikgcmV0dXJuCgogICAgICB0aGlzLnBsYXRmb3JtQ2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnRDb250YWluZXIpCgogICAgICAvLyDnlJ/miJAzMOWkqeeahOaXpeacn+aVsOaNrgogICAgICBjb25zdCBkYXRlcyA9IFtdCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKQogICAgICBmb3IgKGxldCBpID0gMjk7IGkgPj0gMDsgaS0tKSB7CiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRvZGF5KQogICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpCiAgICAgICAgZGF0ZXMucHVzaChkYXRlLmdldE1vbnRoKCkgKyAxICsgJy4nICsgZGF0ZS5nZXREYXRlKCkpCiAgICAgIH0KCiAgICAgIC8vIOaooeaLn+WQhOW5s+WPsOeahOaVsOaNrgogICAgICBjb25zdCB3ZWlib0RhdGEgPSB0aGlzLmdlbmVyYXRlUmFuZG9tRGF0YSgzMCwgMTUwMCwgMjAwMCkKICAgICAgY29uc3Qgd2VjaGF0RGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCAxMjAwLCAxODAwKQogICAgICBjb25zdCBkb3V5aW5EYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDgwMCwgMTIwMCkKICAgICAgY29uc3QgdG91dGlhRGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCA2MDAsIDEwMDApCiAgICAgIGNvbnN0IG90aGVyRGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCAzMDAsIDYwMCkKCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsKICAgICAgICB0aXRsZTogewogICAgICAgICAgdGV4dDogJ+i/kTMw5aSp5bmz5Y+w6IiG5oOF6LaL5Yq/JywKICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAxNiwKICAgICAgICAgICAgZm9udFdlaWdodDogJ25vcm1hbCcsCiAgICAgICAgICAgIGNvbG9yOiAnIzMzMycKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdjcm9zcycsCiAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzZhNzk4NScKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgewogICAgICAgICAgICBsZXQgcmVzdWx0ID0gcGFyYW1zWzBdLm5hbWUgKyAnPGJyLz4nCiAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKHBhcmFtID0+IHsKICAgICAgICAgICAgICByZXN1bHQgKz0gYDxzcGFuIHN0eWxlPSJjb2xvcjoke3BhcmFtLmNvbG9yfSI+4pePPC9zcGFuPiAke3BhcmFtLnNlcmllc05hbWV9OiAke3BhcmFtLnZhbHVlfTxici8+YAogICAgICAgICAgICB9KQogICAgICAgICAgICByZXR1cm4gcmVzdWx0CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIGRhdGE6IFsn5b6u5Y2aJywgJ+W+ruS/oScsICfmipbpn7MnLCAn5aS05p2hJywgJ+WFtuS7liddLAogICAgICAgICAgdG9wOiAzMCwKICAgICAgICAgIHRleHRTdHlsZTogewogICAgICAgICAgICBmb250U2l6ZTogMTIKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGxlZnQ6ICczJScsCiAgICAgICAgICByaWdodDogJzQlJywKICAgICAgICAgIGJvdHRvbTogJzMlJywKICAgICAgICAgIHRvcDogJzE1JScsCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLAogICAgICAgICAgZGF0YTogZGF0ZXMsCiAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgZm9udFNpemU6IDEwLAogICAgICAgICAgICBjb2xvcjogJyM2NjYnCiAgICAgICAgICB9LAogICAgICAgICAgYXhpc0xpbmU6IHsKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjZTBlMGUwJwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICB5QXhpczogewogICAgICAgICAgdHlwZTogJ3ZhbHVlJywKICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICBmb250U2l6ZTogMTAsCiAgICAgICAgICAgIGNvbG9yOiAnIzY2NicKICAgICAgICAgIH0sCiAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyNlMGUwZTAnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBzcGxpdExpbmU6IHsKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjZjBmMGYwJwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBzZXJpZXM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+W+ruWNmicsCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsCiAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsKICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMjQsIDE0NCwgMjU1LCAwLjYpJyB9LAogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgyNCwgMTQ0LCAyNTUsIDAuMSknIH0KICAgICAgICAgICAgICBdKQogICAgICAgICAgICB9LAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyMxODkwZmYnLAogICAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzE4OTBmZicKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogd2VpYm9EYXRhCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAn5b6u5L+hJywKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywKICAgICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWwogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSg4MiwgMTk2LCAyNiwgMC42KScgfSwKICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoODIsIDE5NiwgMjYsIDAuMSknIH0KICAgICAgICAgICAgICBdKQogICAgICAgICAgICB9LAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyM1MmM0MWEnLAogICAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzUyYzQxYScKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogd2VjaGF0RGF0YQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+aKlumfsycsCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsCiAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsKICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMTE0LCA0NiwgMjA5LCAwLjYpJyB9LAogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgxMTQsIDQ2LCAyMDksIDAuMSknIH0KICAgICAgICAgICAgICBdKQogICAgICAgICAgICB9LAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyM3MjJlZDEnLAogICAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzcyMmVkMScKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogZG91eWluRGF0YQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+WktOadoScsCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsCiAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsKICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMjUwLCAxNDAsIDIyLCAwLjYpJyB9LAogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgyNTAsIDE0MCwgMjIsIDAuMSknIH0KICAgICAgICAgICAgICBdKQogICAgICAgICAgICB9LAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyNmYThjMTYnLAogICAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI2ZhOGMxNicKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogdG91dGlhRGF0YQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+WFtuS7licsCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsCiAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsKICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMTYwLCAyMTcsIDE3LCAwLjYpJyB9LAogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgxNjAsIDIxNywgMTcsIDAuMSknIH0KICAgICAgICAgICAgICBdKQogICAgICAgICAgICB9LAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyNhMGQ5MTEnLAogICAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI2EwZDkxMScKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogb3RoZXJEYXRhCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9CgogICAgICB0aGlzLnBsYXRmb3JtQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbikKICAgIH0sCiAgICBpbml0U2VudGltZW50Q2hhcnQoKSB7CiAgICAgIC8vIOWIneWni+WMluaDheaEn+WxnuaAp+mlvOWbvgogICAgICBjb25zdCBjaGFydENvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzZW50aW1lbnQtY2hhcnQnKQogICAgICBpZiAoIWNoYXJ0Q29udGFpbmVyKSByZXR1cm4KCiAgICAgIHRoaXMuc2VudGltZW50Q2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnRDb250YWluZXIpCgogICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgdGl0bGU6IHsKICAgICAgICAgIHRleHQ6ICfmg4XmhJ/liIbluIMnLAogICAgICAgICAgbGVmdDogJ2NlbnRlcicsCiAgICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgICAgZm9udFNpemU6IDE0LAogICAgICAgICAgICBmb250V2VpZ2h0OiAnbm9ybWFsJywKICAgICAgICAgICAgY29sb3I6ICcjMzMzJwogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLAogICAgICAgICAgZm9ybWF0dGVyOiAne2F9IDxici8+e2J9OiB7Y30gKHtkfSUpJwogICAgICAgIH0sCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBvcmllbnQ6ICd2ZXJ0aWNhbCcsCiAgICAgICAgICBsZWZ0OiAnbGVmdCcsCiAgICAgICAgICB0b3A6ICdtaWRkbGUnLAogICAgICAgICAgZGF0YTogWyfmraPpnaInLCAn5Lit5oCnJywgJ+i0n+mdoiddLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICfmg4XmhJ/liIbluIMnLAogICAgICAgICAgICB0eXBlOiAncGllJywKICAgICAgICAgICAgcmFkaXVzOiBbJzQwJScsICc3MCUnXSwKICAgICAgICAgICAgY2VudGVyOiBbJzYwJScsICc1MCUnXSwKICAgICAgICAgICAgYXZvaWRMYWJlbE92ZXJsYXA6IGZhbHNlLAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIHNob3c6IGZhbHNlLAogICAgICAgICAgICAgIHBvc2l0aW9uOiAnY2VudGVyJwogICAgICAgICAgICB9LAogICAgICAgICAgICBlbXBoYXNpczogewogICAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICAgICAgZm9udFNpemU6ICcxOCcsCiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxhYmVsTGluZTogewogICAgICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGRhdGE6IFsKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBuYW1lOiAn5q2j6Z2iJywKICAgICAgICAgICAgICAgIHZhbHVlOiA2NS4yLAogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzUyYzQxYScKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIG5hbWU6ICfkuK3mgKcnLAogICAgICAgICAgICAgICAgdmFsdWU6IDI4LjMsCiAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICAgICAgY29sb3I6ICcjZmFhZDE0JwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgbmFtZTogJ+i0n+mdoicsCiAgICAgICAgICAgICAgICB2YWx1ZTogNi41LAogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmNGQ0ZicKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0KCiAgICAgIHRoaXMuc2VudGltZW50Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbikKICAgIH0sCiAgICAvLyDnlJ/miJDpmo/mnLrmlbDmja7nmoTovoXliqnmlrnms5UKICAgIGdlbmVyYXRlUmFuZG9tRGF0YShjb3VudCwgbWluLCBtYXgpIHsKICAgICAgY29uc3QgZGF0YSA9IFtdCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgY291bnQ7IGkrKykgewogICAgICAgIGNvbnN0IHZhbHVlID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogKG1heCAtIG1pbiArIDEpKSArIG1pbgogICAgICAgIGRhdGEucHVzaCh2YWx1ZSkKICAgICAgfQogICAgICByZXR1cm4gZGF0YQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "index.vue", "sourceRoot": "src/views/opinion-overview", "sourcesContent": ["<template>\n  <div class=\"opinion-overview\">\n    <!-- 左侧导航栏 -->\n    <div class=\"left-sidebar\">\n      <div class=\"sidebar-header\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\n      </div>\n\n      <div class=\"sidebar-search\">\n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索方案\"\n          size=\"small\"\n          prefix-icon=\"el-icon-search\">\n        </el-input>\n      </div>\n\n      <div class=\"sidebar-menu\">\n        <div class=\"menu-section\">\n          <div class=\"section-title\">已有方案</div>\n          <el-menu\n            :default-active=\"activeMenuItem\"\n            class=\"sidebar-menu-list\"\n            @select=\"handleMenuSelect\">\n            <el-menu-item index=\"基础(1)\">\n              <i class=\"el-icon-s-custom\"></i>\n              <span>基础(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"品牌(1)\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span>品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"方太\" class=\"active-item\">\n              <i class=\"el-icon-star-off\"></i>\n              <span>方太</span>\n            </el-menu-item>\n            <el-menu-item index=\"人物(0)\">\n              <i class=\"el-icon-user\"></i>\n              <span>人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"机构(0)\">\n              <i class=\"el-icon-office-building\"></i>\n              <span>机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"产品(0)\">\n              <i class=\"el-icon-goods\"></i>\n              <span>产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"事件(0)\">\n              <i class=\"el-icon-warning\"></i>\n              <span>事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"话题(0)\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              <span>话题(0)</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧内容区 -->\n    <div class=\"right-content\">\n      <!-- 顶部导航标签栏 -->\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <!-- 根据activeTab显示不同内容 -->\n        <div v-if=\"activeTab === 'opinion-monitor'\">\n          <!-- 舆情监测内容 -->\n\n          <!-- 舆情趋势图表 -->\n          <div class=\"section-card\">\n            <div class=\"chart-container\">\n              <div id=\"trend-chart\" style=\"width: 100%; height: 400px;\"></div>\n            </div>\n          </div>\n\n          <!-- 热门文章和最新公告 -->\n          <div class=\"bottom-section\">\n            <div class=\"left-articles\">\n              <div class=\"article-card\">\n                <div class=\"card-header\">\n                  <h3><i class=\"el-icon-document\" style=\"color: #1890ff; margin-right: 8px;\"></i>热门文章</h3>\n                </div>\n                <div class=\"article-list\">\n                  <div class=\"article-item\" v-for=\"(article, index) in hotArticles\" :key=\"index\">\n                    <div class=\"article-icon\" :class=\"article.type\">{{ article.icon }}</div>\n                    <div class=\"article-content\">\n                      <div class=\"article-title\">{{ article.title }}</div>\n                      <div class=\"article-meta\">\n                        <span class=\"article-source\">{{ article.source }}</span>\n                        <span class=\"article-author\">{{ article.author }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"right-announcements\">\n              <div class=\"announcement-card\">\n                <div class=\"card-header\">\n                  <h3><i class=\"el-icon-bell\" style=\"color: #52c41a; margin-right: 8px;\"></i>最新公告</h3>\n                </div>\n                <div class=\"announcement-list\">\n                  <div class=\"announcement-item\" v-for=\"(announcement, index) in announcements\" :key=\"index\">\n                    <div class=\"announcement-indicator\" :class=\"announcement.level\"></div>\n                    <div class=\"announcement-content\">\n                      <div class=\"announcement-title\">{{ announcement.title }}</div>\n                      <div class=\"announcement-time\">{{ announcement.time }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else-if=\"activeTab === 'info-summary'\">\n          <!-- 信息汇总内容 -->\n          <!-- 近30天舆情发布地区 -->\n          <div class=\"section-card\">\n            <div class=\"card-header\">\n              <h3><i class=\"el-icon-location\" style=\"color: #409EFF; margin-right: 8px;\"></i>近30天舆情发布地区</h3>\n              <div class=\"stats\">\n                <div class=\"stat-item positive\">\n                  <span class=\"label\">正面舆情</span>\n                  <span class=\"value\">111930</span>\n                </div>\n                <div class=\"stat-item neutral\">\n                  <span class=\"label\">中性舆情</span>\n                  <span class=\"value\">1118</span>\n                </div>\n                <div class=\"stat-item negative\">\n                  <span class=\"label\">负面舆情</span>\n                  <span class=\"value\">444</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"map-container\">\n              <div id=\"china-map\" style=\"width: 100%; height: 400px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center; color: #999;\">\n                <div>地图组件加载中...</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 今日舆情总量和平台分析 -->\n          <div class=\"bottom-section\">\n          <div class=\"left-charts\">\n            <!-- 近30天平台分析 -->\n            <div class=\"chart-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-pie-chart\" style=\"color: #52C41A; margin-right: 8px;\"></i>近30天平台分析</h3>\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\n              </div>\n              <div id=\"platform-chart\" style=\"width: 100%; height: 300px;\"></div>\n            </div>\n          </div>\n\n          <div class=\"right-stats\">\n            <!-- 今日舆情总量 -->\n            <div class=\"stats-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-data-line\" style=\"color: #FA8C16; margin-right: 8px;\"></i>今日舆情总量</h3>\n              </div>\n              <div class=\"total-count\">0 0 0,0 0 4,6 8 1</div>\n              <div class=\"platform-stats\">\n                <div class=\"platform-item weibo\">\n                  <div class=\"platform-icon\">微</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">微博</span>\n                    <span class=\"platform-count\">534</span>\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item wechat\">\n                  <div class=\"platform-icon\">微</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">微信</span>\n                    <span class=\"platform-count\">1483</span>\n                    <span class=\"platform-change\">今日新增 15.2%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item weibo-red\">\n                  <div class=\"platform-icon\">微</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">微博</span>\n                    <span class=\"platform-count\">279</span>\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item xiaohongshu\">\n                  <div class=\"platform-icon\">小</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">小红书</span>\n                    <span class=\"platform-count\">129</span>\n                    <span class=\"platform-change\">今日新增 3.2%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item app\">\n                  <div class=\"platform-icon\">A</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">APP</span>\n                    <span class=\"platform-count\">764</span>\n                    <span class=\"platform-change\">今日新增 -1.8%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item toutiao\">\n                  <div class=\"platform-icon\">头</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">头条</span>\n                    <span class=\"platform-count\">1455</span>\n                    <span class=\"platform-change\">今日新增 4.5%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item douyin\">\n                  <div class=\"platform-icon\">抖</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">抖音</span>\n                    <span class=\"platform-count\">23</span>\n                    <span class=\"platform-change\">今日新增 100%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item news\">\n                  <div class=\"platform-icon\">新</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">新闻</span>\n                    <span class=\"platform-count\">2</span>\n                    <span class=\"platform-change\">今日新增 100%</span>\n                  </div>\n                </div>\n                <div class=\"platform-item forum\">\n                  <div class=\"platform-icon\">论</div>\n                  <div class=\"platform-info\">\n                    <span class=\"platform-name\">论坛</span>\n                    <span class=\"platform-count\">12</span>\n                    <span class=\"platform-change\">今日新增 -2.8%</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 近30天情感属性 -->\n            <div class=\"chart-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-sunny\" style=\"color: #722ED1; margin-right: 8px;\"></i>近30天情感属性</h3>\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\n              </div>\n              <div id=\"sentiment-chart\" style=\"width: 100%; height: 200px;\"></div>\n            </div>\n          </div>\n        </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'OpinionOverview',\n  data() {\n    return {\n      activeMenuItem: '方太',\n      searchText: '',\n      activeTab: 'opinion-monitor', // 默认激活舆情监测标签\n      originalTopNav: undefined, // 存储原始的topNav状态\n      // 图表实例\n      mapChart: null,\n      platformChart: null,\n      sentimentChart: null,\n      trendChart: null, // 趋势图表实例\n      // 热门文章数据\n      hotArticles: [\n        {\n          icon: '红',\n          type: 'negative',\n          title: '某品牌产品质量问题引发消费者不满',\n          source: '新浪财经',\n          author: '财经记者'\n        },\n        {\n          icon: '黄',\n          type: 'neutral',\n          title: '市场分析：家电行业发展趋势',\n          source: '中国经济网',\n          author: '市场分析师'\n        },\n        {\n          icon: '绿',\n          type: 'positive',\n          title: '创新技术推动行业发展',\n          source: '科技日报',\n          author: '科技记者'\n        },\n        {\n          icon: '红',\n          type: 'negative',\n          title: '消费者投诉处理不当引发关注',\n          source: '消费者报',\n          author: '消费维权'\n        },\n        {\n          icon: '绿',\n          type: 'positive',\n          title: '企业社会责任获得认可',\n          source: '人民日报',\n          author: '社会记者'\n        }\n      ],\n      // 最新公告数据\n      announcements: [\n        {\n          level: 'high',\n          title: '舆情监测系统升级通知',\n          time: '2023-04-20 10:30:00'\n        },\n        {\n          level: 'medium',\n          title: '五一假期监测安排',\n          time: '2023-04-19 16:45:00'\n        },\n        {\n          level: 'low',\n          title: '数据统计报告已生成',\n          time: '2023-04-19 14:20:00'\n        },\n        {\n          level: 'high',\n          title: '重要舆情预警提醒',\n          time: '2023-04-19 09:15:00'\n        },\n        {\n          level: 'medium',\n          title: '系统维护完成通知',\n          time: '2023-04-18 18:30:00'\n        }\n      ],\n      // 地图数据\n      mapData: [\n        {name: '北京', value: 15000},\n        {name: '上海', value: 12000},\n        {name: '广东', value: 18000},\n        {name: '浙江', value: 8000},\n        {name: '江苏', value: 9000},\n        {name: '山东', value: 7000},\n        {name: '四川', value: 6000},\n        {name: '湖北', value: 5000}\n      ],\n      // 平台数据\n      platformData: [\n        {name: '微博', value: 35.2, color: '#ff6b6b'},\n        {name: '微信', value: 28.6, color: '#51cf66'},\n        {name: '抖音', value: 18.4, color: '#339af0'},\n        {name: '今日头条', value: 12.8, color: '#ffd43b'},\n        {name: '其他', value: 5.0, color: '#868e96'}\n      ],\n      // 情感数据\n      sentimentData: [\n        {name: '正面', value: 65.2, color: '#52c41a'},\n        {name: '中性', value: 28.3, color: '#faad14'},\n        {name: '负面', value: 6.5, color: '#ff4d4f'}\n      ]\n    }\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n\n    this.$nextTick(() => {\n      // 根据当前激活的标签初始化对应的图表\n      if (this.activeTab === 'opinion-monitor') {\n        this.initTrendChart()\n      } else if (this.activeTab === 'info-summary') {\n        this.initCharts()\n      }\n    })\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n\n    // 销毁图表实例\n    if (this.mapChart) {\n      this.mapChart.dispose()\n    }\n    if (this.platformChart) {\n      this.platformChart.dispose()\n    }\n    if (this.sentimentChart) {\n      this.sentimentChart.dispose()\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose()\n    }\n  },\n  methods: {\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('Menu selected:', index)\n      // 这里可以根据选择的方案加载不同的数据\n    },\n    handleTabClick(tab) {\n      console.log('Tab clicked:', tab.name)\n      // 根据不同标签加载不同内容\n      this.$nextTick(() => {\n        if (tab.name === 'opinion-monitor') {\n          this.initTrendChart()\n        } else if (tab.name === 'info-summary') {\n          this.initCharts()\n        }\n      })\n    },\n    initCharts() {\n      // 初始化图表\n      this.initMap()\n      this.initPlatformChart()\n      this.initSentimentChart()\n    },\n    initTrendChart() {\n      // 初始化舆情趋势图表\n      const chartContainer = document.getElementById('trend-chart')\n      if (!chartContainer) return\n\n      this.trendChart = echarts.init(chartContainer)\n\n      // 生成30天的日期数据\n      const dates = []\n      const today = new Date()\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(today)\n        date.setDate(date.getDate() - i)\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\n      }\n\n      // 模拟各平台的数据\n      const weiboData = this.generateRandomData(30, 1500, 2500)\n      const wechatData = this.generateRandomData(30, 1200, 2000)\n      const douyinData = this.generateRandomData(30, 800, 1500)\n      const toutiaData = this.generateRandomData(30, 600, 1200)\n      const otherData = this.generateRandomData(30, 300, 800)\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            label: {\n              backgroundColor: '#6a7985'\n            }\n          },\n          formatter: function(params) {\n            let result = params[0].name + '<br/>'\n            params.forEach(param => {\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\n            })\n            return result\n          }\n        },\n        legend: {\n          data: ['微博', '微信', '抖音', '头条', '其他'],\n          top: 20,\n          textStyle: {\n            fontSize: 12\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          top: '15%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: dates,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#f0f0f0'\n            }\n          }\n        },\n        series: [\n          {\n            name: '微博',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#1890ff',\n              width: 2\n            },\n            itemStyle: {\n              color: '#1890ff'\n            },\n            data: weiboData\n          },\n          {\n            name: '微信',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#52c41a',\n              width: 2\n            },\n            itemStyle: {\n              color: '#52c41a'\n            },\n            data: wechatData\n          },\n          {\n            name: '抖音',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#722ed1',\n              width: 2\n            },\n            itemStyle: {\n              color: '#722ed1'\n            },\n            data: douyinData\n          },\n          {\n            name: '头条',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#fa8c16',\n              width: 2\n            },\n            itemStyle: {\n              color: '#fa8c16'\n            },\n            data: toutiaData\n          },\n          {\n            name: '其他',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#a0d911',\n              width: 2\n            },\n            itemStyle: {\n              color: '#a0d911'\n            },\n            data: otherData\n          }\n        ]\n      }\n\n      this.trendChart.setOption(option)\n    },\n    initMap() {\n      // 暂时跳过地图初始化，避免缺少地图数据导致的错误\n      console.log('地图初始化已跳过，需要引入中国地图数据')\n    },\n    initPlatformChart() {\n      // 初始化平台分析面积图\n      const chartContainer = document.getElementById('platform-chart')\n      if (!chartContainer) return\n\n      this.platformChart = echarts.init(chartContainer)\n\n      // 生成30天的日期数据\n      const dates = []\n      const today = new Date()\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(today)\n        date.setDate(date.getDate() - i)\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\n      }\n\n      // 模拟各平台的数据\n      const weiboData = this.generateRandomData(30, 1500, 2000)\n      const wechatData = this.generateRandomData(30, 1200, 1800)\n      const douyinData = this.generateRandomData(30, 800, 1200)\n      const toutiaData = this.generateRandomData(30, 600, 1000)\n      const otherData = this.generateRandomData(30, 300, 600)\n\n      const option = {\n        title: {\n          text: '近30天平台舆情趋势',\n          left: 'center',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'normal',\n            color: '#333'\n          }\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            label: {\n              backgroundColor: '#6a7985'\n            }\n          },\n          formatter: function (params) {\n            let result = params[0].name + '<br/>'\n            params.forEach(param => {\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\n            })\n            return result\n          }\n        },\n        legend: {\n          data: ['微博', '微信', '抖音', '头条', '其他'],\n          top: 30,\n          textStyle: {\n            fontSize: 12\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          top: '15%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: dates,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e0e0e0'\n            }\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#f0f0f0'\n            }\n          }\n        },\n        series: [\n          {\n            name: '微博',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#1890ff',\n              width: 2\n            },\n            itemStyle: {\n              color: '#1890ff'\n            },\n            data: weiboData\n          },\n          {\n            name: '微信',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#52c41a',\n              width: 2\n            },\n            itemStyle: {\n              color: '#52c41a'\n            },\n            data: wechatData\n          },\n          {\n            name: '抖音',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#722ed1',\n              width: 2\n            },\n            itemStyle: {\n              color: '#722ed1'\n            },\n            data: douyinData\n          },\n          {\n            name: '头条',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#fa8c16',\n              width: 2\n            },\n            itemStyle: {\n              color: '#fa8c16'\n            },\n            data: toutiaData\n          },\n          {\n            name: '其他',\n            type: 'line',\n            stack: 'Total',\n            areaStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\n              ])\n            },\n            lineStyle: {\n              color: '#a0d911',\n              width: 2\n            },\n            itemStyle: {\n              color: '#a0d911'\n            },\n            data: otherData\n          }\n        ]\n      }\n\n      this.platformChart.setOption(option)\n    },\n    initSentimentChart() {\n      // 初始化情感属性饼图\n      const chartContainer = document.getElementById('sentiment-chart')\n      if (!chartContainer) return\n\n      this.sentimentChart = echarts.init(chartContainer)\n\n      const option = {\n        title: {\n          text: '情感分布',\n          left: 'center',\n          textStyle: {\n            fontSize: 14,\n            fontWeight: 'normal',\n            color: '#333'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle',\n          data: ['正面', '中性', '负面'],\n          textStyle: {\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            name: '情感分布',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              position: 'center'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '18',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [\n              {\n                name: '正面',\n                value: 65.2,\n                itemStyle: {\n                  color: '#52c41a'\n                }\n              },\n              {\n                name: '中性',\n                value: 28.3,\n                itemStyle: {\n                  color: '#faad14'\n                }\n              },\n              {\n                name: '负面',\n                value: 6.5,\n                itemStyle: {\n                  color: '#ff4d4f'\n                }\n              }\n            ]\n          }\n        ]\n      }\n\n      this.sentimentChart.setOption(option)\n    },\n    // 生成随机数据的辅助方法\n    generateRandomData(count, min, max) {\n      const data = []\n      for (let i = 0; i < count; i++) {\n        const value = Math.floor(Math.random() * (max - min + 1)) + min\n        data.push(value)\n      }\n      return data\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-overview {\n  display: flex;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 16px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .sidebar-search {\n    padding: 16px;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 0 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 8px;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 40px;\n        line-height: 40px;\n        margin-bottom: 4px;\n        border-radius: 4px;\n\n        &.active-item {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &:hover {\n          background-color: #f5f5f5;\n        }\n      }\n    }\n  }\n}\n\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n\n  .top-tabs {\n    background-color: #fff;\n    border-bottom: 1px solid #e8e8e8;\n\n    .el-tabs {\n      .el-tabs__header {\n        margin: 0;\n\n        .el-tabs__nav-wrap {\n          padding: 0 24px;\n        }\n\n        .el-tabs__item {\n          height: 50px;\n          line-height: 50px;\n          font-size: 14px;\n\n          &.is-active {\n            color: #1890ff;\n            border-bottom-color: #1890ff;\n          }\n        }\n      }\n    }\n  }\n\n  .main-content {\n    flex: 1;\n    padding: 24px;\n    overflow-y: auto;\n  }\n}\n\n.section-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 24px;\n\n    h3 {\n      margin: 0;\n      font-size: 18px;\n      font-weight: 600;\n    }\n\n    .stats {\n      display: flex;\n      gap: 24px;\n\n      .stat-item {\n        text-align: center;\n\n        .label {\n          display: block;\n          font-size: 14px;\n          color: #666;\n          margin-bottom: 4px;\n        }\n\n        .value {\n          display: block;\n          font-size: 24px;\n          font-weight: 600;\n        }\n\n        &.positive .value {\n          color: #52c41a;\n        }\n\n        &.neutral .value {\n          color: #faad14;\n        }\n\n        &.negative .value {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n}\n\n.bottom-section {\n  display: flex;\n  gap: 24px;\n\n  .left-charts {\n    flex: 2;\n  }\n\n  .right-stats {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n  }\n\n  .left-articles {\n    flex: 1;\n  }\n\n  .right-announcements {\n    flex: 1;\n  }\n}\n\n.chart-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n}\n\n.stats-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .total-count {\n    font-size: 32px;\n    font-weight: 600;\n    text-align: center;\n    margin: 24px 0;\n    letter-spacing: 2px;\n  }\n\n  .platform-stats {\n    .platform-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .platform-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 4px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n        font-weight: 600;\n        margin-right: 12px;\n      }\n\n      &.weibo .platform-icon {\n        background-color: #1890ff;\n      }\n\n      &.wechat .platform-icon {\n        background-color: #52c41a;\n      }\n\n      &.weibo-red .platform-icon {\n        background-color: #ff4d4f;\n      }\n\n      &.xiaohongshu .platform-icon {\n        background-color: #eb2f96;\n      }\n\n      &.app .platform-icon {\n        background-color: #13c2c2;\n      }\n\n      &.toutiao .platform-icon {\n        background-color: #fa8c16;\n      }\n\n      &.douyin .platform-icon {\n        background-color: #722ed1;\n      }\n\n      &.news .platform-icon {\n        background-color: #faad14;\n      }\n\n      &.forum .platform-icon {\n        background-color: #a0d911;\n      }\n\n      .platform-info {\n        flex: 1;\n\n        .platform-name {\n          display: block;\n          font-size: 14px;\n          color: #333;\n        }\n\n        .platform-count {\n          display: block;\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .platform-change {\n          display: block;\n          font-size: 12px;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n\n// 热门文章样式\n.article-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n\n  .article-list {\n    .article-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .article-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 4px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n        font-weight: 600;\n        margin-right: 12px;\n        font-size: 14px;\n\n        &.negative {\n          background-color: #ff4d4f;\n        }\n\n        &.neutral {\n          background-color: #faad14;\n        }\n\n        &.positive {\n          background-color: #52c41a;\n        }\n      }\n\n      .article-content {\n        flex: 1;\n\n        .article-title {\n          font-size: 14px;\n          color: #333;\n          margin-bottom: 4px;\n          line-height: 1.4;\n        }\n\n        .article-meta {\n          font-size: 12px;\n          color: #666;\n\n          .article-source {\n            margin-right: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 最新公告样式\n.announcement-card {\n  background-color: #fff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n\n  .announcement-list {\n    .announcement-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .announcement-indicator {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 12px;\n\n        &.high {\n          background-color: #ff4d4f;\n        }\n\n        &.medium {\n          background-color: #faad14;\n        }\n\n        &.low {\n          background-color: #52c41a;\n        }\n      }\n\n      .announcement-content {\n        flex: 1;\n\n        .announcement-title {\n          font-size: 14px;\n          color: #333;\n          margin-bottom: 4px;\n          line-height: 1.4;\n        }\n\n        .announcement-time {\n          font-size: 12px;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}