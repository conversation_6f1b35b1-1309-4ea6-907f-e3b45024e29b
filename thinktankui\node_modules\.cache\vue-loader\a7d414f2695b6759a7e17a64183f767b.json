{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\info-summary\\index.vue?vue&type=style&index=1&id=7e184638&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\info-summary\\index.vue", "mtime": 1749118359488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFwcC1jb250YWluZXIgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBoZWlnaHQ6IDEwMHZoOwogIG92ZXJmbG93OiBoaWRkZW47CiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjJmNTsKfQoKLy8g5pON5L2c5oyJ6ZKu5Yy65Z+f5qC35byPCi5hY3Rpb24tYnV0dG9ucyB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogMTVweDsKICBsZWZ0OiAxNXB4OwogIHotaW5kZXg6IDEwOwogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxMHB4Owp9CgouaW5mby1zdW1tYXJ5LWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBoZWlnaHQ6IDEwMCU7CiAgcGFkZGluZy10b3A6IDYwcHg7IC8vIOS4uuaWsOW7uuaWueahiOaMiemSrueVmeWHuuepuumXtAp9CgovLyDlt6bkvqflr7zoiKrmoI/moLflvI8KLmxlZnQtc2lkZWJhciB7CiAgd2lkdGg6IDE4MHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2U4ZThlODsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgb3ZlcmZsb3cteTogYXV0bzsKfQoKLnNpZGViYXItaGVhZGVyIHsKICBwYWRkaW5nOiAxNXB4OwogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogYm9sZDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9Cgouc2lkZWJhci1tZW51IHsKICBmbGV4OiAxOwp9CgouZWwtbWVudS12ZXJ0aWNhbCB7CiAgYm9yZGVyLXJpZ2h0OiBub25lOwp9CgovLyDlj7PkvqflhoXlrrnljLrln5/moLflvI8KLnJpZ2h0LWNvbnRlbnQgewogIGZsZXg6IDE7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBtYXJnaW46IDAgMTVweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLmNvbnRlbnQtaGVhZGVyIHsKICBwYWRkaW5nOiAxNXB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4OwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5lbnRpdHktdGl0bGUgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5lbnRpdHktbmFtZSB7CiAgbWFyZ2luLXJpZ2h0OiA1cHg7Cn0KCi8vIOagh+etvumhteagt+W8jwoudGFicy1jb250YWluZXIgewogIHBhZGRpbmc6IDEwcHggMTVweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKfQoKLmZpbHRlci10YWJzIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi8vIOetm+mAieWMuuWfn+agt+W8jwouZmlsdGVyLXNlY3Rpb24gewogIHBhZGRpbmc6IDE1cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKfQoKLmZpbHRlci1yb3cgewogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKfQoKLmZpbHRlci1yb3c6bGFzdC1jaGlsZCB7CiAgbWFyZ2luLWJvdHRvbTogMDsKfQoKLmZpbHRlci1sYWJlbCB7CiAgd2lkdGg6IDgwcHg7CiAgY29sb3I6ICM2MDYyNjY7CiAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCi5maWx0ZXItYWN0aW9ucyB7CiAgbWFyZ2luLXRvcDogMTBweDsKICBwYWRkaW5nLWxlZnQ6IDgwcHg7Cn0KCi8vIOaTjeS9nOagj+agt+W8jwouYWN0aW9uLWJhciB7CiAgcGFkZGluZzogMTBweCAxNXB4OwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7Cn0KCi5sZWZ0LWFjdGlvbnMsIC5yaWdodC1hY3Rpb25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5zZWFyY2gtaW5wdXQgewogIHdpZHRoOiAyMDBweDsKICBtYXJnaW4tcmlnaHQ6IDEwcHg7Cn0KCi8vIOS/oeaBr+WIl+ihqOagt+W8jwouaW5mby1saXN0IHsKICBmbGV4OiAxOwogIG92ZXJmbG93LXk6IGF1dG87CiAgcGFkZGluZzogMDsKfQoKLmluZm8taXRlbSB7CiAgcGFkZGluZzogMTVweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Owp9CgouaXRlbS1jaGVja2JveCB7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogIG1hcmdpbi10b3A6IDNweDsKfQoKLmluZm8tY29udGVudCB7CiAgZmxleDogMTsKfQoKLmluZm8taGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBtYXJnaW4tYm90dG9tOiA4cHg7Cn0KCi5pbmZvLXRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzMwMzEzMzsKfQoKLmluZm8tc3VtbWFyeSB7CiAgY29sb3I6ICM2MDYyNjY7CiAgbWFyZ2luLWJvdHRvbTogOHB4OwogIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCi5pbmZvLWZvb3RlciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTNweDsKfQoKLmluZm8tc291cmNlLCAuaW5mby10aW1lLCAuaW5mby1zZW50aW1lbnQsIC5pbmZvLXZpZXdzLCAuaW5mby1jb21tZW50cyB7CiAgbWFyZ2luLXJpZ2h0OiAxNXB4Owp9CgouaW5mby1pbmRleCB7CiAgbWFyZ2luLWxlZnQ6IGF1dG87Cn0KCi5zZW50aW1lbnQtcG9zaXRpdmUgewogIGNvbG9yOiAjNjdjMjNhOwp9Cgouc2VudGltZW50LW5ldXRyYWwgewogIGNvbG9yOiAjOTA5Mzk5Owp9Cgouc2VudGltZW50LW5lZ2F0aXZlIHsKICBjb2xvcjogI2Y1NmM2YzsKfQoKLmluZm8taW1hZ2VzIHsKICBkaXNwbGF5OiBmbGV4OwogIG1hcmdpbi10b3A6IDEwcHg7Cn0KCi5pbmZvLWltYWdlIHsKICB3aWR0aDogODBweDsKICBoZWlnaHQ6IDgwcHg7CiAgb2JqZWN0LWZpdDogY292ZXI7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLy8g6auY5Lqu5qC35byPCjpkZWVwKC5oaWdobGlnaHQpIHsKICBjb2xvcjogIzQwOWVmZjsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLy8g5paw5bu65pa55qGI5a+56K+d5qGG5qC35byPCi5jcmVhdGUtcGxhbi1kaWFsb2cgewogIC5lbC1kaWFsb2dfX2hlYWRlciB7CiAgICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKICB9CgogIC5lbC10YWJzX19oZWFkZXIgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICB9CgogIC5lbC10YWJzX19uYXYgewogICAgd2lkdGg6IDEwMCU7CiAgfQoKICAuZWwtdGFic19faXRlbSB7CiAgICBmbGV4OiAxOwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgaGVpZ2h0OiA0MHB4OwogICAgbGluZS1oZWlnaHQ6IDQwcHg7CiAgfQoKICAuZWwtZm9ybS1pdGVtIHsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgfQp9CgoubW9uaXRvci1vYmplY3Qtc2VsZWN0IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcG9zaXRpb246IHJlbGF0aXZlOwoKICAuZWwtZHJvcGRvd24gewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgcmlnaHQ6IDEwcHg7CiAgICB0b3A6IDA7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGN1cnNvcjogcG9pbnRlcjsKICB9Cn0KCi5sb2NhdGlvbi1zZWxlY3QgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmxvY2F0aW9uLWJ0biB7CiAgbWFyZ2luLWxlZnQ6IDEwcHg7Cn0KCi5pbmR1c3RyeS1zZWxlY3QtYnRuIHsKICB3aWR0aDogMTAwJTsKICB0ZXh0LWFsaWduOiBsZWZ0OwogIG92ZXJmbG93OiBoaWRkZW47CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBwYWRkaW5nOiA4cHggMTVweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogIGNvbG9yOiAjNjA2MjY2OwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKCiAgJjpob3ZlciB7CiAgICBib3JkZXItY29sb3I6ICNjMGM0Y2M7CiAgfQp9CgoudGhlbWUtcm93LCAuaW5kdXN0cnktcm93IHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtd3JhcDogd3JhcDsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoudGhlbWUtaW5wdXQsIC5pbmR1c3RyeS1pbnB1dCB7CiAgd2lkdGg6IDEwMHB4OwogIG1hcmdpbi1sZWZ0OiAxMHB4OwogIHZlcnRpY2FsLWFsaWduOiBib3R0b207Cn0KCi50aGVtZS1idXR0b24sIC5pbmR1c3RyeS1idXR0b24gewogIG1hcmdpbi1sZWZ0OiAxMHB4OwogIGhlaWdodDogMzJweDsKICBsaW5lLWhlaWdodDogMzBweDsKICBwYWRkaW5nLXRvcDogMDsKICBwYWRkaW5nLWJvdHRvbTogMDsKfQoKLmVsLXRhZyB7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogIG1hcmdpbi1ib3R0b206IDVweDsKfQoKLmNoYW5uZWxzLXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBtYXJnaW4tYm90dG9tOiAxMHB4OwoKICAuZWwtY2hlY2tib3ggewogICAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogIH0KCiAgJjpsYXN0LWNoaWxkIHsKICAgIG1hcmdpbi1ib3R0b206IDA7CiAgfQp9CgovLyDliIbpobXlrrnlmajmoLflvI8KLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICBwYWRkaW5nOiAxNXB4OwogIHRleHQtYWxpZ246IHJpZ2h0OwogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOGU4ZTg7Cn0KCi8vIOihjOS4muWIhuexu+W8ueeql+agt+W8jwouaW5kdXN0cnktZGlhbG9nIHsKICAuZWwtZGlhbG9nX19oZWFkZXIgewogICAgcGFkZGluZzogMTVweCAyMHB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7CiAgfQoKICAuaW5kdXN0cnktZGlhbG9nLWNvbnRlbnQgewogICAgZGlzcGxheTogZmxleDsKICAgIGhlaWdodDogNDAwcHg7CiAgfQoKICAuaW5kdXN0cnktdHJlZS1jb250YWluZXIgewogICAgZmxleDogMTsKICAgIHBhZGRpbmc6IDE1cHg7CiAgICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZThlOGU4OwogICAgb3ZlcmZsb3cteTogYXV0bzsKICB9CgogIC5pbmR1c3RyeS1zZWxlY3RlZC1jb250YWluZXIgewogICAgd2lkdGg6IDIwMHB4OwogICAgcGFkZGluZzogMTVweDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIH0KCiAgLmluZHVzdHJ5LXNlbGVjdGVkLXRpdGxlIHsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICB9Cn0KCi8vIOWPkemAgemihOitpuW8ueeql+agt+W8jwouc2VuZC1hbGVydC1kaWFsb2cgewogIC5lbC1kaWFsb2dfX2hlYWRlciB7CiAgICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKICB9CgogIC5zZW5kLWFsZXJ0LWNvbnRlbnQgewogICAgcGFkZGluZzogMjBweDsKICB9CgogIC5yZWNlaXZlci1saXN0IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgZ2FwOiAxMHB4OwogIH0KCiAgLnJlY2VpdmVyLWl0ZW0gewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgfQoKICAucmVjZWl2ZXItdHlwZSB7CiAgICB3aWR0aDogODBweDsKICAgIG1hcmdpbi1yaWdodDogMTBweDsKICB9CgogIC5yZWNlaXZlci1zZWxlY3QgewogICAgd2lkdGg6IDEwMCU7CiAgfQp9CgovLyDmraPpnaLmg4XmhJ/lvLnnqpfmoLflvI8KLnBvc2l0aXZlLXNlbnRpbWVudC1kaWFsb2cgewogIC5lbC1kaWFsb2dfX2hlYWRlciB7CiAgICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsKICB9CiAgLmVsLWRpYWxvZ19fYm9keSB7CiAgICBwYWRkaW5nOiAyMHB4OwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0wCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/info-summary", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <el-button type=\"primary\" icon=\"el-icon-message\" @click=\"showSendAlertDialog\">发送预警</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreatePlanDialog\">新建方案</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-folder-add\" @click=\"showAddToAlertMaterialDialog\">加入至报告素材</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-share\" @click=\"showInfoGraphDialog\">信息图谱</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-document-checked\" @click=\"showOriginalProofreadingDialog\">原稿校对</el-button>\n    </div>\n\n    <div class=\"info-summary-container\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"sidebar-header\">\n          <span class=\"sidebar-title\">方太</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        <div class=\"sidebar-menu\">\n          <el-menu\n            :default-active=\"activeMenu\"\n            class=\"el-menu-vertical\"\n          >\n            <el-menu-item index=\"1\" @click=\"handleMenuClick('总览')\">\n              <i class=\"el-icon-s-home\"></i>\n              <span slot=\"title\">总览</span>\n            </el-menu-item>\n            <el-menu-item index=\"2\" @click=\"handleMenuClick('品牌')\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span slot=\"title\">品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"3\" @click=\"handleMenuClick('人物')\">\n              <i class=\"el-icon-user\"></i>\n              <span slot=\"title\">人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"4\" @click=\"handleMenuClick('机构')\">\n              <i class=\"el-icon-office-building\"></i>\n              <span slot=\"title\">机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"5\" @click=\"handleMenuClick('产品')\">\n              <i class=\"el-icon-shopping-bag-1\"></i>\n              <span slot=\"title\">产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"6\" @click=\"handleMenuClick('事件')\">\n              <i class=\"el-icon-bell\"></i>\n              <span slot=\"title\">事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"7\" @click=\"handleMenuClick('话题')\">\n              <i class=\"el-icon-chat-dot-square\"></i>\n              <span slot=\"title\">话题(0)</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n\n      <!-- 右侧内容区域 -->\n      <div class=\"right-content\">\n        <!-- 标题和操作区域 -->\n        <div class=\"content-header\">\n          <div class=\"entity-title\">\n            <span class=\"entity-name\">方太</span>\n            <i class=\"el-icon-arrow-right\"></i>\n          </div>\n          <div class=\"view-actions\">\n            <el-button-group>\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-menu\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\"></el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 标签页 -->\n        <div class=\"tabs-container\">\n          <div class=\"filter-tabs\">\n            <el-radio-group v-model=\"activeTab\" size=\"small\">\n              <el-radio-button label=\"today\">今天</el-radio-button>\n              <el-radio-button label=\"yesterday\">昨天</el-radio-button>\n              <el-radio-button label=\"before_yesterday\">前天</el-radio-button>\n              <el-radio-button label=\"earlier\">更早</el-radio-button>\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\n            </el-radio-group>\n          </div>\n        </div>\n\n        <!-- 信息类型筛选 -->\n        <div class=\"filter-section\">\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">平台分类:</span>\n            <el-checkbox-group v-model=\"platformTypes\" size=\"small\">\n              <el-checkbox label=\"news\">新闻 (46/217)</el-checkbox>\n              <el-checkbox label=\"weibo\">微博 (5/34)</el-checkbox>\n              <el-checkbox label=\"wechat\">微信 (14/54)</el-checkbox>\n              <el-checkbox label=\"video\">视频 (2/78)</el-checkbox>\n              <el-checkbox label=\"app\">APP (1/29)</el-checkbox>\n              <el-checkbox label=\"forum\">论坛 (0/9)</el-checkbox>\n              <el-checkbox label=\"ecommerce\">电商 (44/446)</el-checkbox>\n              <el-checkbox label=\"qa\">问答 (6/21)</el-checkbox>\n              <el-checkbox label=\"other\">其他 (1/2)</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">情感倾向:</span>\n            <el-checkbox-group v-model=\"sentimentTypes\" size=\"small\">\n              <el-checkbox label=\"positive\">正面 (46/217)</el-checkbox>\n              <el-checkbox label=\"neutral\">中性 (7/8)</el-checkbox>\n              <el-checkbox label=\"negative\">负面 (3/57)</el-checkbox>\n              <el-checkbox label=\"other\">其他 (1/3)</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">信息属性:</span>\n            <el-checkbox-group v-model=\"infoAttributes\" size=\"small\">\n              <el-checkbox label=\"official\">官方发布</el-checkbox>\n              <el-checkbox label=\"media\">媒体报道</el-checkbox>\n              <el-checkbox label=\"user\">用户评价</el-checkbox>\n              <el-checkbox label=\"competitor\">竞品信息</el-checkbox>\n              <el-checkbox label=\"industry\">行业动态</el-checkbox>\n              <el-checkbox label=\"policy\">政策法规</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-actions\">\n            <el-button size=\"small\" type=\"primary\">筛选</el-button>\n            <el-button size=\"small\">重置</el-button>\n          </div>\n        </div>\n\n        <!-- 操作栏 -->\n        <div class=\"action-bar\">\n          <div class=\"left-actions\">\n            <el-button size=\"small\" type=\"primary\">全选</el-button>\n            <el-button size=\"small\">导出</el-button>\n          </div>\n          <div class=\"right-actions\">\n            <el-input\n              placeholder=\"搜索关键词\"\n              prefix-icon=\"el-icon-search\"\n              v-model=\"searchKeyword\"\n              size=\"small\"\n              clearable\n              class=\"search-input\"\n              @keyup.enter=\"handleSearchKeyword\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearchKeyword\"></el-button>\n            </el-input>\n            <el-dropdown size=\"small\" split-button type=\"primary\" @command=\"handleCommand\">\n              排序\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"time_desc\">时间降序</el-dropdown-item>\n                <el-dropdown-item command=\"time_asc\">时间升序</el-dropdown-item>\n                <el-dropdown-item command=\"relevance\">相关性</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n        </div>\n\n        <!-- 信息列表 -->\n        <div class=\"info-list\">\n          <div v-for=\"(item, index) in paginatedList\" :key=\"index\" class=\"info-item\">\n            <el-checkbox v-model=\"item.selected\" class=\"item-checkbox\"></el-checkbox>\n            <div class=\"info-content\">\n              <div class=\"info-header\">\n                <div class=\"info-title\" v-html=\"item.title\"></div>\n                <div class=\"info-actions\">\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\n                  <el-button type=\"text\" icon=\"el-icon-share\"></el-button>\n                  <el-button type=\"text\" icon=\"el-icon-more\"></el-button>\n                </div>\n              </div>\n              <div class=\"info-summary\" v-html=\"item.content\"></div>\n              <div class=\"info-footer\">\n                <span class=\"info-source\">{{ item.source }}</span>\n                <span class=\"info-time\">{{ item.time }}</span>\n                <span class=\"info-sentiment\" :class=\"'sentiment-' + item.sentiment\">\n                  <el-button\n                    :type=\"item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'\"\n                    size=\"mini\"\n                    @click=\"handleSentimentClick(item)\"\n                  >\n                    <i :class=\"getSentimentIcon(item.sentiment)\"></i>\n                    {{ getSentimentText(item.sentiment) }}\n                  </el-button>\n                </span>\n                <span class=\"info-views\">\n                  <i class=\"el-icon-view\"></i> {{ item.views }}\n                </span>\n                <span class=\"info-comments\">\n                  <i class=\"el-icon-chat-line-square\"></i> {{ item.comments }}\n                </span>\n                <span class=\"info-index\">{{ (currentPage - 1) * pageSize + index + 1 }}</span>\n              </div>\n              <div class=\"info-images\" v-if=\"item.images && item.images.length > 0\">\n                <img v-for=\"(img, imgIndex) in item.images\" :key=\"imgIndex\" :src=\"img\" class=\"info-image\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[10, 20, 30, 50]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalItems\"\n            background\n          ></el-pagination>\n        </div>\n      </div>\n    </div>\n\n    <!-- 新建方案对话框 -->\n    <el-dialog\n      title=\"新建方案\"\n      :visible.sync=\"createPlanDialogVisible\"\n      width=\"50%\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      append-to-body\n      custom-class=\"create-plan-dialog\"\n    >\n      <el-tabs v-model=\"planActiveTab\">\n        <el-tab-pane label=\"监测方式\" name=\"standard\">\n          <el-form :model=\"planForm\" label-width=\"70px\" size=\"small\">\n            <el-form-item label=\"方案名称\">\n              <el-input v-model=\"planForm.name\" placeholder=\"请输入方案名称\"></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"作用范围\">\n              <el-select v-model=\"planForm.scope\" placeholder=\"请选择\" style=\"width: 100%\">\n                <el-option label=\"全部\" value=\"all\"></el-option>\n                <el-option label=\"选项1\" value=\"option1\"></el-option>\n                <el-option label=\"选项2\" value=\"option2\"></el-option>\n              </el-select>\n            </el-form-item>\n\n            <el-form-item label=\"监测对象\">\n              <div class=\"monitor-object-select\">\n                <el-input v-model=\"planForm.monitorObject\" placeholder=\"请输入监测对象\"></el-input>\n                <el-dropdown trigger=\"click\" @command=\"handleMonitorObjectCommand\">\n                  <span class=\"el-dropdown-link\">\n                    <i class=\"el-icon-arrow-down el-icon--right\"></i>\n                  </span>\n                  <el-dropdown-menu slot=\"dropdown\">\n                    <el-dropdown-item command=\"option1\">选项1</el-dropdown-item>\n                    <el-dropdown-item command=\"option2\">选项2</el-dropdown-item>\n                    <el-dropdown-item command=\"option3\">选项3</el-dropdown-item>\n                  </el-dropdown-menu>\n                </el-dropdown>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"地域\">\n              <div class=\"location-select\">\n                <el-input v-model=\"planForm.location\" placeholder=\"请选择地域\" readonly></el-input>\n                <el-button type=\"text\" class=\"location-btn\" @click=\"showLocationMap\">\n                  <i class=\"el-icon-location\"></i>\n                </el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"主题\">\n              <div class=\"theme-row\">\n                <el-tag\n                  v-for=\"(tag, index) in planForm.themes\"\n                  :key=\"index\"\n                  closable\n                  @close=\"handleRemoveTheme(tag)\"\n                >\n                  {{ tag }}\n                </el-tag>\n                <el-input\n                  class=\"theme-input\"\n                  v-if=\"themeInputVisible\"\n                  v-model=\"themeInputValue\"\n                  ref=\"themeInput\"\n                  size=\"small\"\n                  @keyup.enter.native=\"handleAddTheme\"\n                  @blur=\"handleAddTheme\"\n                >\n                </el-input>\n                <el-button v-else class=\"theme-button\" size=\"small\" @click=\"showThemeInput\">+ 添加主题</el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"行业分类\">\n              <div class=\"industry-row\">\n                <el-tag\n                  v-if=\"planForm.industry\"\n                  closable\n                  @close=\"planForm.industry = ''\"\n                >\n                  {{ planForm.industry }}\n                </el-tag>\n                <el-button v-if=\"!planForm.industry\" class=\"industry-button\" size=\"small\" @click=\"showIndustrySelect\">+ 添加行业分类</el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"时间段\">\n              <el-date-picker\n                v-model=\"planForm.timeRange\"\n                type=\"daterange\"\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n\n            <el-form-item label=\"渠道\">\n              <el-checkbox-group v-model=\"planForm.channels\">\n                <div class=\"channels-row\">\n                  <el-checkbox label=\"news\">新闻</el-checkbox>\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\n                  <el-checkbox label=\"wechat\">微信</el-checkbox>\n                </div>\n                <div class=\"channels-row\">\n                  <el-checkbox label=\"video\">视频</el-checkbox>\n                  <el-checkbox label=\"app\">APP</el-checkbox>\n                  <el-checkbox label=\"forum\">论坛</el-checkbox>\n                </div>\n              </el-checkbox-group>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"高级方式\" name=\"advanced\">\n          <el-form :model=\"advancedPlanForm\" label-width=\"70px\" size=\"small\">\n            <!-- 高级模式的表单内容 -->\n            <el-form-item label=\"方案名称\">\n              <el-input v-model=\"advancedPlanForm.name\" placeholder=\"请输入方案名称\"></el-input>\n            </el-form-item>\n\n            <!-- 其他高级选项 -->\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"createPlanDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 行业分类弹窗 -->\n    <el-dialog\n      title=\"行业分类\"\n      :visible.sync=\"industryDialogVisible\"\n      width=\"40%\"\n      :close-on-click-modal=\"true\"\n      :close-on-press-escape=\"true\"\n      append-to-body\n      custom-class=\"industry-dialog\"\n    >\n      <div class=\"industry-dialog-content\">\n        <div class=\"industry-tree-container\">\n          <el-tree\n            :data=\"industryTreeData\"\n            :props=\"industryTreeProps\"\n            node-key=\"id\"\n            default-expand-all\n            highlight-current\n            @node-click=\"handleIndustryNodeClick\"\n          />\n        </div>\n        <div class=\"industry-selected-container\">\n          <div class=\"industry-selected-title\">\n            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"industryDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmIndustrySelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 发送预警弹窗 -->\n    <el-dialog\n      title=\"发送预警\"\n      :visible.sync=\"sendAlertDialogVisible\"\n      width=\"40%\"\n      :close-on-click-modal=\"true\"\n      :close-on-press-escape=\"true\"\n      append-to-body\n      custom-class=\"send-alert-dialog\"\n    >\n      <div class=\"send-alert-content\">\n        <el-form :model=\"alertForm\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"预警标题\">\n            <el-input v-model=\"alertForm.title\" placeholder=\"请输入预警标题\"></el-input>\n          </el-form-item>\n\n          <el-form-item label=\"接收人\">\n            <div class=\"receiver-list\">\n              <div class=\"receiver-item\" v-for=\"(receiver, index) in receivers\" :key=\"index\">\n                <div class=\"receiver-type\">{{ receiver.type }}</div>\n                <el-select\n                  v-model=\"alertForm.selectedReceivers[index]\"\n                  :placeholder=\"'请选择' + receiver.type\"\n                  class=\"receiver-select\"\n                >\n                  <el-option\n                    v-for=\"person in receiver.persons\"\n                    :key=\"person\"\n                    :label=\"person\"\n                    :value=\"person\"\n                  ></el-option>\n                </el-select>\n              </div>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelSendAlert\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmSendAlert\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 正面情感信息弹窗 -->\n    <el-dialog\n      title=\"情感属性纠错\"\n      :visible.sync=\"positiveSentimentDialogVisible\"\n      width=\"50%\"\n      append-to-body\n      custom-class=\"positive-sentiment-dialog\"\n    >\n      <el-radio-group v-model=\"selectedSentiment\" size=\"small\">\n        <el-radio-button label=\"positive\">正面</el-radio-button>\n        <el-radio-button label=\"neutral\">中性</el-radio-button>\n        <el-radio-button label=\"negative\">负面</el-radio-button>\n      </el-radio-group>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"positiveSentimentDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handlePositiveDialogConfirm\">确定</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 加入至报告素材对话框 -->\n    <el-dialog\n      title=\"加入至报告素材\"\n      :visible.sync=\"addToAlertMaterialDialogVisible\"\n      width=\"30%\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      append-to-body\n      custom-class=\"add-to-alert-material-dialog\"\n    >\n      <div class=\"add-to-alert-material-content\">\n        <el-form label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"选择素材库\">\n            <el-select v-model=\"selectedMaterialLibrary\" placeholder=\"请选择素材库\" style=\"width: 100%\">\n              <!-- 这里需要根据实际数据填充选项 -->\n              <el-option label=\"素材库1\" value=\"library1\"></el-option>\n              <el-option label=\"素材库2\" value=\"library2\"></el-option>\n            </el-select>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelAddToAlertMaterial\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddToAlertMaterial\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'InfoSummary',\n  computed: {\n    // 根据当前页码和每页显示数量计算当前页的数据\n    paginatedList() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      // 实际应用中，这里应该是从后端获取分页数据\n      // 这里为了演示，我们从本地数据中截取一部分\n      return this.infoList.slice(0, Math.min(this.infoList.length, this.pageSize));\n    }\n  },\n  data() {\n    return {\n      // 页面基础数据\n      originalTopNav: undefined, // 存储原始的topNav状态\n      searchKeyword: '',\n      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示\n      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值\n      editingItem: null, // 当前编辑的情感条目\n      activeMenu: '2', // 默认选中品牌\n      activeTab: 'today', // 默认选中今天\n      platformTypes: ['news', 'weibo', 'wechat', 'video', 'app'], // 默认选中的平台类型\n      sentimentTypes: ['positive', 'neutral'], // 默认选中的情感类型\n      infoAttributes: ['official', 'media'], // 默认选中的信息属性\n\n      // 分页相关数据\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 120, // 假设总共有120条数据\n\n      // 新建方案对话框相关数据\n      createPlanDialogVisible: false,\n      planActiveTab: 'standard',\n      themeInputVisible: false,\n      themeInputValue: '',\n      planForm: {\n        name: '',\n        scope: 'all',\n        monitorObject: '',\n        location: '',\n        themes: [],\n        industry: '',\n        timeRange: '',\n        channels: ['news', 'weibo', 'wechat']\n      },\n      advancedPlanForm: {\n        name: ''\n      },\n\n      // 行业分类弹窗相关数据\n      industryDialogVisible: false,\n      selectedIndustry: null,\n      industryTreeProps: {\n        label: 'label',\n        children: 'children'\n      },\n\n      // 发送预警弹窗相关数据\n      sendAlertDialogVisible: false,\n      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示\n      alertForm: {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      },\n      receivers: [\n        {\n          type: '总监',\n          persons: ['王总监', '李总监', '张总监']\n        },\n        {\n          type: '经理',\n          persons: ['王经理', '李经理', '张经理']\n        },\n        {\n          type: '主管',\n          persons: ['王主管', '李主管', '张主管']\n        },\n        {\n          type: '员工',\n          persons: ['王员工', '李员工', '张员工']\n        },\n        {\n          type: '外部人员',\n          persons: ['外部人员1', '外部人员2', '外部人员3']\n        },\n        {\n          type: '其他',\n          persons: ['其他人员1', '其他人员2', '其他人员3']\n        }\n      ],\n      industryTreeData: [\n        {\n          id: 1,\n          label: '制造',\n          children: []\n        },\n        {\n          id: 2,\n          label: '公共',\n          children: []\n        },\n        {\n          id: 3,\n          label: '教育',\n          children: []\n        },\n        {\n          id: 4,\n          label: '工业设备',\n          children: []\n        },\n        {\n          id: 5,\n          label: '环保设备',\n          children: []\n        },\n        {\n          id: 6,\n          label: '金融',\n          children: []\n        },\n        {\n          id: 7,\n          label: '商业',\n          children: []\n        },\n        {\n          id: 8,\n          label: '民用与商用',\n          children: []\n        },\n        {\n          id: 9,\n          label: '政府部门',\n          children: []\n        }\n      ],\n\n      // 信息列表数据\n      infoList: []\n    };\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n    // 加载关键词数据\n    this.loadKeywordData()\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    // 加载关键词数据\n    async loadKeywordData() {\n      try {\n        const params = {\n          pageNum: this.currentPage,\n          pageSize: this.pageSize\n        }\n\n        const response = await this.$http.get('/admin/keywordData/list', { params })\n\n        if (response.data.code === 200) {\n          // 将API返回的数据转换为前端需要的格式\n          this.infoList = response.data.data.rows || []\n          this.totalItems = response.data.data.total || 0\n        } else {\n          this.$message.error('获取数据失败：' + response.data.msg)\n        }\n      } catch (error) {\n        console.error('加载关键词数据失败:', error)\n        this.$message.error('加载数据失败，请稍后重试')\n      }\n    },\n\n    // 菜单点击事件\n    handleMenuClick(menuName) {\n      console.log('选择菜单:', menuName);\n    },\n\n    // 获取情感图标\n    getSentimentIcon(sentiment) {\n      const icons = {\n        positive: 'el-icon-sunny',\n        neutral: 'el-icon-partly-cloudy',\n        negative: 'el-icon-cloudy'\n      };\n      return icons[sentiment] || 'el-icon-question';\n    },\n\n    // 获取情感文本\n    getSentimentText(sentiment) {\n      const texts = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      };\n      return texts[sentiment] || '未知';\n    },\n\n    // 下拉菜单命令处理\n    handleCommand(command) {\n      console.log('执行命令:', command);\n    },\n\n    // 处理搜索关键词\n    handleSearchKeyword() {\n      if (this.searchKeyword.trim()) {\n        // 跳转到搜索结果页面，并传递搜索关键词\n        this.$router.push({\n          path: '/search-results',\n          query: {\n            q: this.searchKeyword.trim()\n          }\n        });\n      } else {\n        this.$message.warning('请输入搜索关键词');\n      }\n    },\n\n    // 分页相关方法\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1; // 切换每页显示数量时，重置为第一页\n      console.log('每页显示数量:', size);\n      this.loadKeywordData(); // 重新加载数据\n    },\n\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      console.log('当前页码:', page);\n      this.loadKeywordData(); // 重新加载数据\n    },\n\n    // 新建方案相关方法\n    showCreatePlanDialog() {\n      this.createPlanDialogVisible = true;\n    },\n\n    // 主题标签相关方法\n    handleRemoveTheme(tag) {\n      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);\n    },\n\n    showThemeInput() {\n      this.themeInputVisible = true;\n      this.$nextTick(_ => {\n        this.$refs.themeInput.$refs.input.focus();\n      });\n    },\n\n    handleAddTheme() {\n      let inputValue = this.themeInputValue;\n      if (inputValue) {\n        if (!this.planForm.themes.includes(inputValue)) {\n          this.planForm.themes.push(inputValue);\n        }\n      }\n      this.themeInputVisible = false;\n      this.themeInputValue = '';\n    },\n\n    // 监测对象下拉菜单命令处理\n    handleMonitorObjectCommand(command) {\n      this.planForm.monitorObject = command;\n    },\n\n    // 显示地域选择地图\n    showLocationMap() {\n      // 这里可以实现地图选择功能\n      this.$message.info('显示地域选择地图');\n    },\n\n    // 显示行业分类选择\n    showIndustrySelect() {\n      this.industryDialogVisible = true;\n    },\n\n    // 处理行业分类树节点点击\n    handleIndustryNodeClick(data) {\n      this.selectedIndustry = data;\n    },\n\n    // 确认行业分类选择\n    confirmIndustrySelect() {\n      if (this.selectedIndustry) {\n        this.planForm.industry = this.selectedIndustry.label;\n      }\n      this.industryDialogVisible = false;\n    },\n\n    // 保存方案\n    savePlan() {\n      // 根据当前激活的标签页选择不同的表单数据\n      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;\n\n      console.log('保存方案:', formData);\n      // 这里可以添加表单验证和提交到后端的逻辑\n\n      // 关闭对话框\n      this.createPlanDialogVisible = false;\n\n      // 重置表单\n      if (this.planActiveTab === 'standard') {\n        this.planForm = {\n          name: '',\n          scope: 'all',\n          monitorObject: '',\n          location: '',\n          themes: [],\n          industry: '',\n          timeRange: '',\n          channels: ['news', 'weibo', 'wechat']\n        };\n      } else {\n        this.advancedPlanForm = {\n          name: ''\n        };\n      }\n    },\n\n    // 显示发送预警弹窗\n    showSendAlertDialog() {\n      this.sendAlertDialogVisible = true;\n    },\n\n    // 取消发送预警\n    cancelSendAlert() {\n      this.sendAlertDialogVisible = false;\n      // 重置表单\n      this.alertForm = {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      };\n    },\n    handleSentimentClick(item) {\n      this.editingItem = item;\n      this.selectedSentiment = item.sentiment;\n      this.positiveSentimentDialogVisible = true;\n    },\n    handlePositiveDialogConfirm() {\n      if (this.editingItem) {\n        this.editingItem.sentiment = this.selectedSentiment;\n        // 在实际应用中，这里可能需要调用API将更改保存到后端\n        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);\n      }\n      this.positiveSentimentDialogVisible = false;\n    },\n\n    // 确认发送预警\n    confirmSendAlert() {\n      // 在这里处理发送预警的逻辑\n      console.log('发送预警:', this.alertForm);\n      this.sendAlertDialogVisible = false;\n      // 清空表单\n      this.alertForm = {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      };\n    },\n\n    // 加入至报告素材对话框相关方法\n    showAddToAlertMaterialDialog() {\n      this.addToAlertMaterialDialogVisible = true;\n    },\n    cancelAddToAlertMaterial() {\n      this.addToAlertMaterialDialogVisible = false;\n      this.selectedMaterialLibrary = ''; // 清空选中值\n    },\n    confirmAddToAlertMaterial() {\n      // 这里添加确认逻辑，例如提交选中的素材库\n      console.log('Selected Material Library:', this.selectedMaterialLibrary);\n      this.addToAlertMaterialDialogVisible = false;\n      this.selectedMaterialLibrary = ''; // 清空选中值\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\nel-dialog\ntitle=\"信息图谱\"\n:visible.sync=\"infoGraphDialogVisible\"\nwidth=\"50%\"\nappend-to-body\ncustom-class=\"info-graph-dialog\"\n>\n<div class=\"info-graph-content\">\n<!-- 根据提供的图示调整内容布局 -->\n<div class=\"graph-container\">\n<div class=\"graph-node\">东木头人</div>\n<div class=\"graph-node\">永兴队</div>\n<!-- 添加更多节点 -->\n</div>\n</div>\n<div slot=\"footer\" class=\"dialog-footer\">\n<el-button @click=\"infoGraphDialogVisible = false\">关闭</el-button>\n</div>\n</el-dialog>\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n"]}]}