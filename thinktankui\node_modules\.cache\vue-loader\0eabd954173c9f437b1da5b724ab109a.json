{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\info-summary\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\info-summary\\index.vue", "mtime": 1749177625089}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnSW5mb1N1bW1hcnknLAogIGNvbXB1dGVkOiB7CiAgICAvLyDmoLnmja7lvZPliY3pobXnoIHlkozmr4/pobXmmL7npLrmlbDph4/orqHnrpflvZPliY3pobXnmoTmlbDmja4KICAgIHBhZ2luYXRlZExpc3QoKSB7CiAgICAgIGNvbnN0IHN0YXJ0ID0gKHRoaXMuY3VycmVudFBhZ2UgLSAxKSAqIHRoaXMucGFnZVNpemU7CiAgICAgIC8vIOWunumZheW6lOeUqOS4re+8jOi/memHjOW6lOivpeaYr+S7juWQjuerr+iOt+WPluWIhumhteaVsOaNrgogICAgICAvLyDov5nph4zkuLrkuobmvJTnpLrvvIzmiJHku6zku47mnKzlnLDmlbDmja7kuK3miKrlj5bkuIDpg6jliIYKICAgICAgcmV0dXJuIHRoaXMuaW5mb0xpc3Quc2xpY2UoMCwgTWF0aC5taW4odGhpcy5pbmZvTGlzdC5sZW5ndGgsIHRoaXMucGFnZVNpemUpKTsKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpobXpnaLln7rnoYDmlbDmja4KICAgICAgb3JpZ2luYWxUb3BOYXY6IHVuZGVmaW5lZCwgLy8g5a2Y5YKo5Y6f5aeL55qEdG9wTmF254q25oCBCiAgICAgIHNlYXJjaEtleXdvcmQ6ICcnLAogICAgICBwb3NpdGl2ZVNlbnRpbWVudERpYWxvZ1Zpc2libGU6IGZhbHNlLCAvLyDmjqfliLbmraPpnaLmg4XmhJ/lvLnnqpfnmoTmmL7npLoKICAgICAgc2VsZWN0ZWRTZW50aW1lbnQ6ICcnLCAvLyDnlKjkuo7mg4XmhJ/lsZ7mgKfnuqDplJnlvLnnqpfnmoTpgInkuK3lgLwKICAgICAgZWRpdGluZ0l0ZW06IG51bGwsIC8vIOW9k+WJjee8lui+keeahOaDheaEn+adoeebrgogICAgICBhY3RpdmVNZW51OiAnMicsIC8vIOm7mOiupOmAieS4reWTgeeJjAogICAgICBhY3RpdmVUYWI6ICd0b2RheScsIC8vIOm7mOiupOmAieS4reS7iuWkqQogICAgICBwbGF0Zm9ybVR5cGVzOiBbJ25ld3MnLCAnd2VpYm8nLCAnd2VjaGF0JywgJ3ZpZGVvJywgJ2FwcCddLCAvLyDpu5jorqTpgInkuK3nmoTlubPlj7DnsbvlnosKICAgICAgc2VudGltZW50VHlwZXM6IFsncG9zaXRpdmUnLCAnbmV1dHJhbCddLCAvLyDpu5jorqTpgInkuK3nmoTmg4XmhJ/nsbvlnosKICAgICAgaW5mb0F0dHJpYnV0ZXM6IFsnb2ZmaWNpYWwnLCAnbWVkaWEnXSwgLy8g6buY6K6k6YCJ5Lit55qE5L+h5oGv5bGe5oCnCgogICAgICAvLyDliIbpobXnm7jlhbPmlbDmja4KICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgdG90YWxJdGVtczogMTIwLCAvLyDlgYforr7mgLvlhbHmnIkxMjDmnaHmlbDmja4KCiAgICAgIC8vIOaWsOW7uuaWueahiOWvueivneahhuebuOWFs+aVsOaNrgogICAgICBjcmVhdGVQbGFuRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHBsYW5BY3RpdmVUYWI6ICdzdGFuZGFyZCcsCiAgICAgIHRoZW1lSW5wdXRWaXNpYmxlOiBmYWxzZSwKICAgICAgdGhlbWVJbnB1dFZhbHVlOiAnJywKICAgICAgcGxhbkZvcm06IHsKICAgICAgICBuYW1lOiAnJywKICAgICAgICBzY29wZTogJ2FsbCcsCiAgICAgICAgbW9uaXRvck9iamVjdDogJycsCiAgICAgICAgbG9jYXRpb246ICcnLAogICAgICAgIHRoZW1lczogW10sCiAgICAgICAgaW5kdXN0cnk6ICcnLAogICAgICAgIHRpbWVSYW5nZTogJycsCiAgICAgICAgY2hhbm5lbHM6IFsnbmV3cycsICd3ZWlibycsICd3ZWNoYXQnXQogICAgICB9LAogICAgICBhZHZhbmNlZFBsYW5Gb3JtOiB7CiAgICAgICAgbmFtZTogJycKICAgICAgfSwKCiAgICAgIC8vIOihjOS4muWIhuexu+W8ueeql+ebuOWFs+aVsOaNrgogICAgICBpbmR1c3RyeURpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBzZWxlY3RlZEluZHVzdHJ5OiBudWxsLAogICAgICBpbmR1c3RyeVRyZWVQcm9wczogewogICAgICAgIGxhYmVsOiAnbGFiZWwnLAogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nCiAgICAgIH0sCgogICAgICAvLyDlj5HpgIHpooTorablvLnnqpfnm7jlhbPmlbDmja4KICAgICAgc2VuZEFsZXJ0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGFkZFRvQWxlcnRNYXRlcmlhbERpYWxvZ1Zpc2libGU6IGZhbHNlLCAvLyDmlrDlop7vvJrmjqfliLbliqDlhaXmiqXlkYrntKDmnZDlvLnnqpfnmoTmmL7npLoKICAgICAgYWxlcnRGb3JtOiB7CiAgICAgICAgdGl0bGU6ICcnLAogICAgICAgIHNlbGVjdGVkUmVjZWl2ZXJzOiBbJycsICcnLCAnJywgJycsICcnLCAnJ10KICAgICAgfSwKICAgICAgcmVjZWl2ZXJzOiBbCiAgICAgICAgewogICAgICAgICAgdHlwZTogJ+aAu+ebkScsCiAgICAgICAgICBwZXJzb25zOiBbJ+eOi+aAu+ebkScsICfmnY7mgLvnm5EnLCAn5byg5oC755uRJ10KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHR5cGU6ICfnu4/nkIYnLAogICAgICAgICAgcGVyc29uczogWyfnjovnu4/nkIYnLCAn5p2O57uP55CGJywgJ+W8oOe7j+eQhiddCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0eXBlOiAn5Li7566hJywKICAgICAgICAgIHBlcnNvbnM6IFsn546L5Li7566hJywgJ+adjuS4u+euoScsICflvKDkuLvnrqEnXQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdHlwZTogJ+WRmOW3pScsCiAgICAgICAgICBwZXJzb25zOiBbJ+eOi+WRmOW3pScsICfmnY7lkZjlt6UnLCAn5byg5ZGY5belJ10KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHR5cGU6ICflpJbpg6jkurrlkZgnLAogICAgICAgICAgcGVyc29uczogWyflpJbpg6jkurrlkZgxJywgJ+WklumDqOS6uuWRmDInLCAn5aSW6YOo5Lq65ZGYMyddCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0eXBlOiAn5YW25LuWJywKICAgICAgICAgIHBlcnNvbnM6IFsn5YW25LuW5Lq65ZGYMScsICflhbbku5bkurrlkZgyJywgJ+WFtuS7luS6uuWRmDMnXQogICAgICAgIH0KICAgICAgXSwKICAgICAgaW5kdXN0cnlUcmVlRGF0YTogWwogICAgICAgIHsKICAgICAgICAgIGlkOiAxLAogICAgICAgICAgbGFiZWw6ICfliLbpgKAnLAogICAgICAgICAgY2hpbGRyZW46IFtdCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogMiwKICAgICAgICAgIGxhYmVsOiAn5YWs5YWxJywKICAgICAgICAgIGNoaWxkcmVuOiBbXQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDMsCiAgICAgICAgICBsYWJlbDogJ+aVmeiCsicsCiAgICAgICAgICBjaGlsZHJlbjogW10KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiA0LAogICAgICAgICAgbGFiZWw6ICflt6XkuJrorr7lpIcnLAogICAgICAgICAgY2hpbGRyZW46IFtdCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogNSwKICAgICAgICAgIGxhYmVsOiAn546v5L+d6K6+5aSHJywKICAgICAgICAgIGNoaWxkcmVuOiBbXQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDYsCiAgICAgICAgICBsYWJlbDogJ+mHkeiejScsCiAgICAgICAgICBjaGlsZHJlbjogW10KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiA3LAogICAgICAgICAgbGFiZWw6ICfllYbkuJonLAogICAgICAgICAgY2hpbGRyZW46IFtdCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogOCwKICAgICAgICAgIGxhYmVsOiAn5rCR55So5LiO5ZWG55SoJywKICAgICAgICAgIGNoaWxkcmVuOiBbXQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDksCiAgICAgICAgICBsYWJlbDogJ+aUv+W6nOmDqOmXqCcsCiAgICAgICAgICBjaGlsZHJlbjogW10KICAgICAgICB9CiAgICAgIF0sCgogICAgICAvLyDkv6Hmga/liJfooajmlbDmja4KICAgICAgaW5mb0xpc3Q6IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOmakOiXj+mhtumDqOWvvOiIquagjwogICAgdGhpcy5vcmlnaW5hbFRvcE5hdiA9IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRvcE5hdgogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgIGtleTogJ3RvcE5hdicsCiAgICAgIHZhbHVlOiBmYWxzZQogICAgfSkKICAgIC8vIOWKoOi9veWFs+mUruivjeaVsOaNrgogICAgdGhpcy5sb2FkS2V5d29yZERhdGEoKQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIC8vIOaBouWkjemhtumDqOWvvOiIquagj+iuvue9rgogICAgaWYgKHRoaXMub3JpZ2luYWxUb3BOYXYgIT09IHVuZGVmaW5lZCkgewogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICBrZXk6ICd0b3BOYXYnLAogICAgICAgIHZhbHVlOiB0aGlzLm9yaWdpbmFsVG9wTmF2CiAgICAgIH0pCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliqDovb3lhbPplK7or43mlbDmja4KICAgIGFzeW5jIGxvYWRLZXl3b3JkRGF0YSgpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJhbXMgPSB7CiAgICAgICAgICBwYWdlTnVtOiB0aGlzLmN1cnJlbnRQYWdlLAogICAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUKICAgICAgICB9CgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5nZXQoJy9wdWJsaWMva2V5d29yZERhdGEvbGlzdCcsIHsgcGFyYW1zIH0pCgogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgLy8g5bCGQVBJ6L+U5Zue55qE5pWw5o2u6L2s5o2i5Li65YmN56uv6ZyA6KaB55qE5qC85byPCiAgICAgICAgICB0aGlzLmluZm9MaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhLnJvd3MgfHwgW10KICAgICAgICAgIHRoaXMudG90YWxJdGVtcyA9IHJlc3BvbnNlLmRhdGEuZGF0YS50b3RhbCB8fCAwCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pe+8micgKyByZXNwb25zZS5kYXRhLm1zZykKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295YWz6ZSu6K+N5pWw5o2u5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veaVsOaNruWksei0pe+8jOivt+eojeWQjumHjeivlScpCiAgICAgIH0KICAgIH0sCgogICAgLy8g6I+c5Y2V54K55Ye75LqL5Lu2CiAgICBoYW5kbGVNZW51Q2xpY2sobWVudU5hbWUpIHsKICAgICAgY29uc29sZS5sb2coJ+mAieaLqeiPnOWNlTonLCBtZW51TmFtZSk7CiAgICB9LAoKICAgIC8vIOiOt+WPluaDheaEn+WbvuaghwogICAgZ2V0U2VudGltZW50SWNvbihzZW50aW1lbnQpIHsKICAgICAgY29uc3QgaWNvbnMgPSB7CiAgICAgICAgcG9zaXRpdmU6ICdlbC1pY29uLXN1bm55JywKICAgICAgICBuZXV0cmFsOiAnZWwtaWNvbi1wYXJ0bHktY2xvdWR5JywKICAgICAgICBuZWdhdGl2ZTogJ2VsLWljb24tY2xvdWR5JwogICAgICB9OwogICAgICByZXR1cm4gaWNvbnNbc2VudGltZW50XSB8fCAnZWwtaWNvbi1xdWVzdGlvbic7CiAgICB9LAoKICAgIC8vIOiOt+WPluaDheaEn+aWh+acrAogICAgZ2V0U2VudGltZW50VGV4dChzZW50aW1lbnQpIHsKICAgICAgY29uc3QgdGV4dHMgPSB7CiAgICAgICAgcG9zaXRpdmU6ICfmraPpnaInLAogICAgICAgIG5ldXRyYWw6ICfkuK3mgKcnLAogICAgICAgIG5lZ2F0aXZlOiAn6LSf6Z2iJwogICAgICB9OwogICAgICByZXR1cm4gdGV4dHNbc2VudGltZW50XSB8fCAn5pyq55+lJzsKICAgIH0sCgogICAgLy8g5LiL5ouJ6I+c5Y2V5ZG95Luk5aSE55CGCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQpIHsKICAgICAgY29uc29sZS5sb2coJ+aJp+ihjOWRveS7pDonLCBjb21tYW5kKTsKICAgIH0sCgogICAgLy8g5aSE55CG5pCc57Si5YWz6ZSu6K+NCiAgICBoYW5kbGVTZWFyY2hLZXl3b3JkKCkgewogICAgICBpZiAodGhpcy5zZWFyY2hLZXl3b3JkLnRyaW0oKSkgewogICAgICAgIC8vIOi3s+i9rOWIsOaQnOe0oue7k+aenOmhtemdou+8jOW5tuS8oOmAkuaQnOe0ouWFs+mUruivjQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICAgIHBhdGg6ICcvc2VhcmNoLXJlc3VsdHMnLAogICAgICAgICAgcXVlcnk6IHsKICAgICAgICAgICAgcTogdGhpcy5zZWFyY2hLZXl3b3JkLnRyaW0oKQogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl5pCc57Si5YWz6ZSu6K+NJyk7CiAgICAgIH0KICAgIH0sCgogICAgLy8g5YiG6aG155u45YWz5pa55rOVCiAgICBoYW5kbGVTaXplQ2hhbmdlKHNpemUpIHsKICAgICAgdGhpcy5wYWdlU2l6ZSA9IHNpemU7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOyAvLyDliIfmjaLmr4/pobXmmL7npLrmlbDph4/ml7bvvIzph43nva7kuLrnrKzkuIDpobUKICAgICAgY29uc29sZS5sb2coJ+avj+mhteaYvuekuuaVsOmHjzonLCBzaXplKTsKICAgICAgdGhpcy5sb2FkS2V5d29yZERhdGEoKTsgLy8g6YeN5paw5Yqg6L295pWw5o2uCiAgICB9LAoKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZTsKICAgICAgY29uc29sZS5sb2coJ+W9k+WJjemhteeggTonLCBwYWdlKTsKICAgICAgdGhpcy5sb2FkS2V5d29yZERhdGEoKTsgLy8g6YeN5paw5Yqg6L295pWw5o2uCiAgICB9LAoKICAgIC8vIOaWsOW7uuaWueahiOebuOWFs+aWueazlQogICAgc2hvd0NyZWF0ZVBsYW5EaWFsb2coKSB7CiAgICAgIHRoaXMuY3JlYXRlUGxhbkRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKCiAgICAvLyDkuLvpopjmoIfnrb7nm7jlhbPmlrnms5UKICAgIGhhbmRsZVJlbW92ZVRoZW1lKHRhZykgewogICAgICB0aGlzLnBsYW5Gb3JtLnRoZW1lcy5zcGxpY2UodGhpcy5wbGFuRm9ybS50aGVtZXMuaW5kZXhPZih0YWcpLCAxKTsKICAgIH0sCgogICAgc2hvd1RoZW1lSW5wdXQoKSB7CiAgICAgIHRoaXMudGhlbWVJbnB1dFZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsKICAgICAgICB0aGlzLiRyZWZzLnRoZW1lSW5wdXQuJHJlZnMuaW5wdXQuZm9jdXMoKTsKICAgICAgfSk7CiAgICB9LAoKICAgIGhhbmRsZUFkZFRoZW1lKCkgewogICAgICBsZXQgaW5wdXRWYWx1ZSA9IHRoaXMudGhlbWVJbnB1dFZhbHVlOwogICAgICBpZiAoaW5wdXRWYWx1ZSkgewogICAgICAgIGlmICghdGhpcy5wbGFuRm9ybS50aGVtZXMuaW5jbHVkZXMoaW5wdXRWYWx1ZSkpIHsKICAgICAgICAgIHRoaXMucGxhbkZvcm0udGhlbWVzLnB1c2goaW5wdXRWYWx1ZSk7CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMudGhlbWVJbnB1dFZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy50aGVtZUlucHV0VmFsdWUgPSAnJzsKICAgIH0sCgogICAgLy8g55uR5rWL5a+56LGh5LiL5ouJ6I+c5Y2V5ZG95Luk5aSE55CGCiAgICBoYW5kbGVNb25pdG9yT2JqZWN0Q29tbWFuZChjb21tYW5kKSB7CiAgICAgIHRoaXMucGxhbkZvcm0ubW9uaXRvck9iamVjdCA9IGNvbW1hbmQ7CiAgICB9LAoKICAgIC8vIOaYvuekuuWcsOWfn+mAieaLqeWcsOWbvgogICAgc2hvd0xvY2F0aW9uTWFwKCkgewogICAgICAvLyDov5nph4zlj6/ku6Xlrp7njrDlnLDlm77pgInmi6nlip/og70KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmmL7npLrlnLDln5/pgInmi6nlnLDlm74nKTsKICAgIH0sCgogICAgLy8g5pi+56S66KGM5Lia5YiG57G76YCJ5oupCiAgICBzaG93SW5kdXN0cnlTZWxlY3QoKSB7CiAgICAgIHRoaXMuaW5kdXN0cnlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCgogICAgLy8g5aSE55CG6KGM5Lia5YiG57G75qCR6IqC54K554K55Ye7CiAgICBoYW5kbGVJbmR1c3RyeU5vZGVDbGljayhkYXRhKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRJbmR1c3RyeSA9IGRhdGE7CiAgICB9LAoKICAgIC8vIOehruiupOihjOS4muWIhuexu+mAieaLqQogICAgY29uZmlybUluZHVzdHJ5U2VsZWN0KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZEluZHVzdHJ5KSB7CiAgICAgICAgdGhpcy5wbGFuRm9ybS5pbmR1c3RyeSA9IHRoaXMuc2VsZWN0ZWRJbmR1c3RyeS5sYWJlbDsKICAgICAgfQogICAgICB0aGlzLmluZHVzdHJ5RGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKCiAgICAvLyDkv53lrZjmlrnmoYgKICAgIHNhdmVQbGFuKCkgewogICAgICAvLyDmoLnmja7lvZPliY3mv4DmtLvnmoTmoIfnrb7pobXpgInmi6nkuI3lkIznmoTooajljZXmlbDmja4KICAgICAgY29uc3QgZm9ybURhdGEgPSB0aGlzLnBsYW5BY3RpdmVUYWIgPT09ICdzdGFuZGFyZCcgPyB0aGlzLnBsYW5Gb3JtIDogdGhpcy5hZHZhbmNlZFBsYW5Gb3JtOwoKICAgICAgY29uc29sZS5sb2coJ+S/neWtmOaWueahiDonLCBmb3JtRGF0YSk7CiAgICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOihqOWNlemqjOivgeWSjOaPkOS6pOWIsOWQjuerr+eahOmAu+i+kQoKICAgICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICAgIHRoaXMuY3JlYXRlUGxhbkRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKCiAgICAgIC8vIOmHjee9ruihqOWNlQogICAgICBpZiAodGhpcy5wbGFuQWN0aXZlVGFiID09PSAnc3RhbmRhcmQnKSB7CiAgICAgICAgdGhpcy5wbGFuRm9ybSA9IHsKICAgICAgICAgIG5hbWU6ICcnLAogICAgICAgICAgc2NvcGU6ICdhbGwnLAogICAgICAgICAgbW9uaXRvck9iamVjdDogJycsCiAgICAgICAgICBsb2NhdGlvbjogJycsCiAgICAgICAgICB0aGVtZXM6IFtdLAogICAgICAgICAgaW5kdXN0cnk6ICcnLAogICAgICAgICAgdGltZVJhbmdlOiAnJywKICAgICAgICAgIGNoYW5uZWxzOiBbJ25ld3MnLCAnd2VpYm8nLCAnd2VjaGF0J10KICAgICAgICB9OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuYWR2YW5jZWRQbGFuRm9ybSA9IHsKICAgICAgICAgIG5hbWU6ICcnCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKCiAgICAvLyDmmL7npLrlj5HpgIHpooTorablvLnnqpcKICAgIHNob3dTZW5kQWxlcnREaWFsb2coKSB7CiAgICAgIHRoaXMuc2VuZEFsZXJ0RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAoKICAgIC8vIOWPlua2iOWPkemAgemihOitpgogICAgY2FuY2VsU2VuZEFsZXJ0KCkgewogICAgICB0aGlzLnNlbmRBbGVydERpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgLy8g6YeN572u6KGo5Y2VCiAgICAgIHRoaXMuYWxlcnRGb3JtID0gewogICAgICAgIHRpdGxlOiAnJywKICAgICAgICBzZWxlY3RlZFJlY2VpdmVyczogWycnLCAnJywgJycsICcnLCAnJywgJyddCiAgICAgIH07CiAgICB9LAogICAgaGFuZGxlU2VudGltZW50Q2xpY2soaXRlbSkgewogICAgICB0aGlzLmVkaXRpbmdJdGVtID0gaXRlbTsKICAgICAgdGhpcy5zZWxlY3RlZFNlbnRpbWVudCA9IGl0ZW0uc2VudGltZW50OwogICAgICB0aGlzLnBvc2l0aXZlU2VudGltZW50RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlUG9zaXRpdmVEaWFsb2dDb25maXJtKCkgewogICAgICBpZiAodGhpcy5lZGl0aW5nSXRlbSkgewogICAgICAgIHRoaXMuZWRpdGluZ0l0ZW0uc2VudGltZW50ID0gdGhpcy5zZWxlY3RlZFNlbnRpbWVudDsKICAgICAgICAvLyDlnKjlrp7pmYXlupTnlKjkuK3vvIzov5nph4zlj6/og73pnIDopoHosIPnlKhBUEnlsIbmm7TmlLnkv53lrZjliLDlkI7nq68KICAgICAgICAvLyDkvovlpoI6IHRoaXMudXBkYXRlU2VudGltZW50QXBpKHRoaXMuZWRpdGluZ0l0ZW0uaWQsIHRoaXMuc2VsZWN0ZWRTZW50aW1lbnQpOwogICAgICB9CiAgICAgIHRoaXMucG9zaXRpdmVTZW50aW1lbnREaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICB9LAoKICAgIC8vIOehruiupOWPkemAgemihOitpgogICAgY29uZmlybVNlbmRBbGVydCgpIHsKICAgICAgLy8g5Zyo6L+Z6YeM5aSE55CG5Y+R6YCB6aKE6K2m55qE6YC76L6RCiAgICAgIGNvbnNvbGUubG9nKCflj5HpgIHpooToraY6JywgdGhpcy5hbGVydEZvcm0pOwogICAgICB0aGlzLnNlbmRBbGVydERpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgLy8g5riF56m66KGo5Y2VCiAgICAgIHRoaXMuYWxlcnRGb3JtID0gewogICAgICAgIHRpdGxlOiAnJywKICAgICAgICBzZWxlY3RlZFJlY2VpdmVyczogWycnLCAnJywgJycsICcnLCAnJywgJyddCiAgICAgIH07CiAgICB9LAoKICAgIC8vIOWKoOWFpeiHs+aKpeWRiue0oOadkOWvueivneahhuebuOWFs+aWueazlQogICAgc2hvd0FkZFRvQWxlcnRNYXRlcmlhbERpYWxvZygpIHsKICAgICAgdGhpcy5hZGRUb0FsZXJ0TWF0ZXJpYWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBjYW5jZWxBZGRUb0FsZXJ0TWF0ZXJpYWwoKSB7CiAgICAgIHRoaXMuYWRkVG9BbGVydE1hdGVyaWFsRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxMaWJyYXJ5ID0gJyc7IC8vIOa4heepuumAieS4reWAvAogICAgfSwKICAgIGNvbmZpcm1BZGRUb0FsZXJ0TWF0ZXJpYWwoKSB7CiAgICAgIC8vIOi/memHjOa3u+WKoOehruiupOmAu+i+ke+8jOS+i+WmguaPkOS6pOmAieS4reeahOe0oOadkOW6kwogICAgICBjb25zb2xlLmxvZygnU2VsZWN0ZWQgTWF0ZXJpYWwgTGlicmFyeTonLCB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxMaWJyYXJ5KTsKICAgICAgdGhpcy5hZGRUb0FsZXJ0TWF0ZXJpYWxEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbExpYnJhcnkgPSAnJzsgLy8g5riF56m66YCJ5Lit5YC8CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4dA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/info-summary", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <el-button type=\"primary\" icon=\"el-icon-message\" @click=\"showSendAlertDialog\">发送预警</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreatePlanDialog\">新建方案</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-folder-add\" @click=\"showAddToAlertMaterialDialog\">加入至报告素材</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-share\" @click=\"showInfoGraphDialog\">信息图谱</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-document-checked\" @click=\"showOriginalProofreadingDialog\">原稿校对</el-button>\n    </div>\n\n    <div class=\"info-summary-container\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"sidebar-header\">\n          <span class=\"sidebar-title\">方太</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        <div class=\"sidebar-menu\">\n          <el-menu\n            :default-active=\"activeMenu\"\n            class=\"el-menu-vertical\"\n          >\n            <el-menu-item index=\"1\" @click=\"handleMenuClick('总览')\">\n              <i class=\"el-icon-s-home\"></i>\n              <span slot=\"title\">总览</span>\n            </el-menu-item>\n            <el-menu-item index=\"2\" @click=\"handleMenuClick('品牌')\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span slot=\"title\">品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"3\" @click=\"handleMenuClick('人物')\">\n              <i class=\"el-icon-user\"></i>\n              <span slot=\"title\">人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"4\" @click=\"handleMenuClick('机构')\">\n              <i class=\"el-icon-office-building\"></i>\n              <span slot=\"title\">机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"5\" @click=\"handleMenuClick('产品')\">\n              <i class=\"el-icon-shopping-bag-1\"></i>\n              <span slot=\"title\">产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"6\" @click=\"handleMenuClick('事件')\">\n              <i class=\"el-icon-bell\"></i>\n              <span slot=\"title\">事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"7\" @click=\"handleMenuClick('话题')\">\n              <i class=\"el-icon-chat-dot-square\"></i>\n              <span slot=\"title\">话题(0)</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n\n      <!-- 右侧内容区域 -->\n      <div class=\"right-content\">\n        <!-- 标题和操作区域 -->\n        <div class=\"content-header\">\n          <div class=\"entity-title\">\n            <span class=\"entity-name\">方太</span>\n            <i class=\"el-icon-arrow-right\"></i>\n          </div>\n          <div class=\"view-actions\">\n            <el-button-group>\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-menu\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\"></el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 标签页 -->\n        <div class=\"tabs-container\">\n          <div class=\"filter-tabs\">\n            <el-radio-group v-model=\"activeTab\" size=\"small\">\n              <el-radio-button label=\"today\">今天</el-radio-button>\n              <el-radio-button label=\"yesterday\">昨天</el-radio-button>\n              <el-radio-button label=\"before_yesterday\">前天</el-radio-button>\n              <el-radio-button label=\"earlier\">更早</el-radio-button>\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\n            </el-radio-group>\n          </div>\n        </div>\n\n        <!-- 信息类型筛选 -->\n        <div class=\"filter-section\">\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">平台分类:</span>\n            <el-checkbox-group v-model=\"platformTypes\" size=\"small\">\n              <el-checkbox label=\"news\">新闻 (46/217)</el-checkbox>\n              <el-checkbox label=\"weibo\">微博 (5/34)</el-checkbox>\n              <el-checkbox label=\"wechat\">微信 (14/54)</el-checkbox>\n              <el-checkbox label=\"video\">视频 (2/78)</el-checkbox>\n              <el-checkbox label=\"app\">APP (1/29)</el-checkbox>\n              <el-checkbox label=\"forum\">论坛 (0/9)</el-checkbox>\n              <el-checkbox label=\"ecommerce\">电商 (44/446)</el-checkbox>\n              <el-checkbox label=\"qa\">问答 (6/21)</el-checkbox>\n              <el-checkbox label=\"other\">其他 (1/2)</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">情感倾向:</span>\n            <el-checkbox-group v-model=\"sentimentTypes\" size=\"small\">\n              <el-checkbox label=\"positive\">正面 (46/217)</el-checkbox>\n              <el-checkbox label=\"neutral\">中性 (7/8)</el-checkbox>\n              <el-checkbox label=\"negative\">负面 (3/57)</el-checkbox>\n              <el-checkbox label=\"other\">其他 (1/3)</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">信息属性:</span>\n            <el-checkbox-group v-model=\"infoAttributes\" size=\"small\">\n              <el-checkbox label=\"official\">官方发布</el-checkbox>\n              <el-checkbox label=\"media\">媒体报道</el-checkbox>\n              <el-checkbox label=\"user\">用户评价</el-checkbox>\n              <el-checkbox label=\"competitor\">竞品信息</el-checkbox>\n              <el-checkbox label=\"industry\">行业动态</el-checkbox>\n              <el-checkbox label=\"policy\">政策法规</el-checkbox>\n            </el-checkbox-group>\n          </div>\n\n          <div class=\"filter-actions\">\n            <el-button size=\"small\" type=\"primary\">筛选</el-button>\n            <el-button size=\"small\">重置</el-button>\n          </div>\n        </div>\n\n        <!-- 操作栏 -->\n        <div class=\"action-bar\">\n          <div class=\"left-actions\">\n            <el-button size=\"small\" type=\"primary\">全选</el-button>\n            <el-button size=\"small\">导出</el-button>\n          </div>\n          <div class=\"right-actions\">\n            <el-input\n              placeholder=\"搜索关键词\"\n              prefix-icon=\"el-icon-search\"\n              v-model=\"searchKeyword\"\n              size=\"small\"\n              clearable\n              class=\"search-input\"\n              @keyup.enter=\"handleSearchKeyword\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearchKeyword\"></el-button>\n            </el-input>\n            <el-dropdown size=\"small\" split-button type=\"primary\" @command=\"handleCommand\">\n              排序\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item command=\"time_desc\">时间降序</el-dropdown-item>\n                <el-dropdown-item command=\"time_asc\">时间升序</el-dropdown-item>\n                <el-dropdown-item command=\"relevance\">相关性</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </div>\n        </div>\n\n        <!-- 信息列表 -->\n        <div class=\"info-list\">\n          <div v-for=\"(item, index) in paginatedList\" :key=\"index\" class=\"info-item\">\n            <el-checkbox v-model=\"item.selected\" class=\"item-checkbox\"></el-checkbox>\n            <div class=\"info-content\">\n              <div class=\"info-header\">\n                <div class=\"info-title\" v-html=\"item.title\"></div>\n                <div class=\"info-actions\">\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\n                  <el-button type=\"text\" icon=\"el-icon-share\"></el-button>\n                  <el-button type=\"text\" icon=\"el-icon-more\"></el-button>\n                </div>\n              </div>\n              <div class=\"info-summary\" v-html=\"item.content\"></div>\n              <div class=\"info-footer\">\n                <span class=\"info-source\">{{ item.source }}</span>\n                <span class=\"info-time\">{{ item.time }}</span>\n                <span class=\"info-sentiment\" :class=\"'sentiment-' + item.sentiment\">\n                  <el-button\n                    :type=\"item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'\"\n                    size=\"mini\"\n                    @click=\"handleSentimentClick(item)\"\n                  >\n                    <i :class=\"getSentimentIcon(item.sentiment)\"></i>\n                    {{ getSentimentText(item.sentiment) }}\n                  </el-button>\n                </span>\n                <span class=\"info-views\">\n                  <i class=\"el-icon-view\"></i> {{ item.views }}\n                </span>\n                <span class=\"info-comments\">\n                  <i class=\"el-icon-chat-line-square\"></i> {{ item.comments }}\n                </span>\n                <span class=\"info-index\">{{ (currentPage - 1) * pageSize + index + 1 }}</span>\n              </div>\n              <div class=\"info-images\" v-if=\"item.images && item.images.length > 0\">\n                <img v-for=\"(img, imgIndex) in item.images\" :key=\"imgIndex\" :src=\"img\" class=\"info-image\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[10, 20, 30, 50]\"\n            :page-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"totalItems\"\n            background\n          ></el-pagination>\n        </div>\n      </div>\n    </div>\n\n    <!-- 新建方案对话框 -->\n    <el-dialog\n      title=\"新建方案\"\n      :visible.sync=\"createPlanDialogVisible\"\n      width=\"50%\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      append-to-body\n      custom-class=\"create-plan-dialog\"\n    >\n      <el-tabs v-model=\"planActiveTab\">\n        <el-tab-pane label=\"监测方式\" name=\"standard\">\n          <el-form :model=\"planForm\" label-width=\"70px\" size=\"small\">\n            <el-form-item label=\"方案名称\">\n              <el-input v-model=\"planForm.name\" placeholder=\"请输入方案名称\"></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"作用范围\">\n              <el-select v-model=\"planForm.scope\" placeholder=\"请选择\" style=\"width: 100%\">\n                <el-option label=\"全部\" value=\"all\"></el-option>\n                <el-option label=\"选项1\" value=\"option1\"></el-option>\n                <el-option label=\"选项2\" value=\"option2\"></el-option>\n              </el-select>\n            </el-form-item>\n\n            <el-form-item label=\"监测对象\">\n              <div class=\"monitor-object-select\">\n                <el-input v-model=\"planForm.monitorObject\" placeholder=\"请输入监测对象\"></el-input>\n                <el-dropdown trigger=\"click\" @command=\"handleMonitorObjectCommand\">\n                  <span class=\"el-dropdown-link\">\n                    <i class=\"el-icon-arrow-down el-icon--right\"></i>\n                  </span>\n                  <el-dropdown-menu slot=\"dropdown\">\n                    <el-dropdown-item command=\"option1\">选项1</el-dropdown-item>\n                    <el-dropdown-item command=\"option2\">选项2</el-dropdown-item>\n                    <el-dropdown-item command=\"option3\">选项3</el-dropdown-item>\n                  </el-dropdown-menu>\n                </el-dropdown>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"地域\">\n              <div class=\"location-select\">\n                <el-input v-model=\"planForm.location\" placeholder=\"请选择地域\" readonly></el-input>\n                <el-button type=\"text\" class=\"location-btn\" @click=\"showLocationMap\">\n                  <i class=\"el-icon-location\"></i>\n                </el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"主题\">\n              <div class=\"theme-row\">\n                <el-tag\n                  v-for=\"(tag, index) in planForm.themes\"\n                  :key=\"index\"\n                  closable\n                  @close=\"handleRemoveTheme(tag)\"\n                >\n                  {{ tag }}\n                </el-tag>\n                <el-input\n                  class=\"theme-input\"\n                  v-if=\"themeInputVisible\"\n                  v-model=\"themeInputValue\"\n                  ref=\"themeInput\"\n                  size=\"small\"\n                  @keyup.enter.native=\"handleAddTheme\"\n                  @blur=\"handleAddTheme\"\n                >\n                </el-input>\n                <el-button v-else class=\"theme-button\" size=\"small\" @click=\"showThemeInput\">+ 添加主题</el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"行业分类\">\n              <div class=\"industry-row\">\n                <el-tag\n                  v-if=\"planForm.industry\"\n                  closable\n                  @close=\"planForm.industry = ''\"\n                >\n                  {{ planForm.industry }}\n                </el-tag>\n                <el-button v-if=\"!planForm.industry\" class=\"industry-button\" size=\"small\" @click=\"showIndustrySelect\">+ 添加行业分类</el-button>\n              </div>\n            </el-form-item>\n\n            <el-form-item label=\"时间段\">\n              <el-date-picker\n                v-model=\"planForm.timeRange\"\n                type=\"daterange\"\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n\n            <el-form-item label=\"渠道\">\n              <el-checkbox-group v-model=\"planForm.channels\">\n                <div class=\"channels-row\">\n                  <el-checkbox label=\"news\">新闻</el-checkbox>\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\n                  <el-checkbox label=\"wechat\">微信</el-checkbox>\n                </div>\n                <div class=\"channels-row\">\n                  <el-checkbox label=\"video\">视频</el-checkbox>\n                  <el-checkbox label=\"app\">APP</el-checkbox>\n                  <el-checkbox label=\"forum\">论坛</el-checkbox>\n                </div>\n              </el-checkbox-group>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"高级方式\" name=\"advanced\">\n          <el-form :model=\"advancedPlanForm\" label-width=\"70px\" size=\"small\">\n            <!-- 高级模式的表单内容 -->\n            <el-form-item label=\"方案名称\">\n              <el-input v-model=\"advancedPlanForm.name\" placeholder=\"请输入方案名称\"></el-input>\n            </el-form-item>\n\n            <!-- 其他高级选项 -->\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"createPlanDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 行业分类弹窗 -->\n    <el-dialog\n      title=\"行业分类\"\n      :visible.sync=\"industryDialogVisible\"\n      width=\"40%\"\n      :close-on-click-modal=\"true\"\n      :close-on-press-escape=\"true\"\n      append-to-body\n      custom-class=\"industry-dialog\"\n    >\n      <div class=\"industry-dialog-content\">\n        <div class=\"industry-tree-container\">\n          <el-tree\n            :data=\"industryTreeData\"\n            :props=\"industryTreeProps\"\n            node-key=\"id\"\n            default-expand-all\n            highlight-current\n            @node-click=\"handleIndustryNodeClick\"\n          />\n        </div>\n        <div class=\"industry-selected-container\">\n          <div class=\"industry-selected-title\">\n            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"industryDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmIndustrySelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 发送预警弹窗 -->\n    <el-dialog\n      title=\"发送预警\"\n      :visible.sync=\"sendAlertDialogVisible\"\n      width=\"40%\"\n      :close-on-click-modal=\"true\"\n      :close-on-press-escape=\"true\"\n      append-to-body\n      custom-class=\"send-alert-dialog\"\n    >\n      <div class=\"send-alert-content\">\n        <el-form :model=\"alertForm\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"预警标题\">\n            <el-input v-model=\"alertForm.title\" placeholder=\"请输入预警标题\"></el-input>\n          </el-form-item>\n\n          <el-form-item label=\"接收人\">\n            <div class=\"receiver-list\">\n              <div class=\"receiver-item\" v-for=\"(receiver, index) in receivers\" :key=\"index\">\n                <div class=\"receiver-type\">{{ receiver.type }}</div>\n                <el-select\n                  v-model=\"alertForm.selectedReceivers[index]\"\n                  :placeholder=\"'请选择' + receiver.type\"\n                  class=\"receiver-select\"\n                >\n                  <el-option\n                    v-for=\"person in receiver.persons\"\n                    :key=\"person\"\n                    :label=\"person\"\n                    :value=\"person\"\n                  ></el-option>\n                </el-select>\n              </div>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelSendAlert\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmSendAlert\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 正面情感信息弹窗 -->\n    <el-dialog\n      title=\"情感属性纠错\"\n      :visible.sync=\"positiveSentimentDialogVisible\"\n      width=\"50%\"\n      append-to-body\n      custom-class=\"positive-sentiment-dialog\"\n    >\n      <el-radio-group v-model=\"selectedSentiment\" size=\"small\">\n        <el-radio-button label=\"positive\">正面</el-radio-button>\n        <el-radio-button label=\"neutral\">中性</el-radio-button>\n        <el-radio-button label=\"negative\">负面</el-radio-button>\n      </el-radio-group>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"positiveSentimentDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handlePositiveDialogConfirm\">确定</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 加入至报告素材对话框 -->\n    <el-dialog\n      title=\"加入至报告素材\"\n      :visible.sync=\"addToAlertMaterialDialogVisible\"\n      width=\"30%\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      append-to-body\n      custom-class=\"add-to-alert-material-dialog\"\n    >\n      <div class=\"add-to-alert-material-content\">\n        <el-form label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"选择素材库\">\n            <el-select v-model=\"selectedMaterialLibrary\" placeholder=\"请选择素材库\" style=\"width: 100%\">\n              <!-- 这里需要根据实际数据填充选项 -->\n              <el-option label=\"素材库1\" value=\"library1\"></el-option>\n              <el-option label=\"素材库2\" value=\"library2\"></el-option>\n            </el-select>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelAddToAlertMaterial\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddToAlertMaterial\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'InfoSummary',\n  computed: {\n    // 根据当前页码和每页显示数量计算当前页的数据\n    paginatedList() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      // 实际应用中，这里应该是从后端获取分页数据\n      // 这里为了演示，我们从本地数据中截取一部分\n      return this.infoList.slice(0, Math.min(this.infoList.length, this.pageSize));\n    }\n  },\n  data() {\n    return {\n      // 页面基础数据\n      originalTopNav: undefined, // 存储原始的topNav状态\n      searchKeyword: '',\n      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示\n      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值\n      editingItem: null, // 当前编辑的情感条目\n      activeMenu: '2', // 默认选中品牌\n      activeTab: 'today', // 默认选中今天\n      platformTypes: ['news', 'weibo', 'wechat', 'video', 'app'], // 默认选中的平台类型\n      sentimentTypes: ['positive', 'neutral'], // 默认选中的情感类型\n      infoAttributes: ['official', 'media'], // 默认选中的信息属性\n\n      // 分页相关数据\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 120, // 假设总共有120条数据\n\n      // 新建方案对话框相关数据\n      createPlanDialogVisible: false,\n      planActiveTab: 'standard',\n      themeInputVisible: false,\n      themeInputValue: '',\n      planForm: {\n        name: '',\n        scope: 'all',\n        monitorObject: '',\n        location: '',\n        themes: [],\n        industry: '',\n        timeRange: '',\n        channels: ['news', 'weibo', 'wechat']\n      },\n      advancedPlanForm: {\n        name: ''\n      },\n\n      // 行业分类弹窗相关数据\n      industryDialogVisible: false,\n      selectedIndustry: null,\n      industryTreeProps: {\n        label: 'label',\n        children: 'children'\n      },\n\n      // 发送预警弹窗相关数据\n      sendAlertDialogVisible: false,\n      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示\n      alertForm: {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      },\n      receivers: [\n        {\n          type: '总监',\n          persons: ['王总监', '李总监', '张总监']\n        },\n        {\n          type: '经理',\n          persons: ['王经理', '李经理', '张经理']\n        },\n        {\n          type: '主管',\n          persons: ['王主管', '李主管', '张主管']\n        },\n        {\n          type: '员工',\n          persons: ['王员工', '李员工', '张员工']\n        },\n        {\n          type: '外部人员',\n          persons: ['外部人员1', '外部人员2', '外部人员3']\n        },\n        {\n          type: '其他',\n          persons: ['其他人员1', '其他人员2', '其他人员3']\n        }\n      ],\n      industryTreeData: [\n        {\n          id: 1,\n          label: '制造',\n          children: []\n        },\n        {\n          id: 2,\n          label: '公共',\n          children: []\n        },\n        {\n          id: 3,\n          label: '教育',\n          children: []\n        },\n        {\n          id: 4,\n          label: '工业设备',\n          children: []\n        },\n        {\n          id: 5,\n          label: '环保设备',\n          children: []\n        },\n        {\n          id: 6,\n          label: '金融',\n          children: []\n        },\n        {\n          id: 7,\n          label: '商业',\n          children: []\n        },\n        {\n          id: 8,\n          label: '民用与商用',\n          children: []\n        },\n        {\n          id: 9,\n          label: '政府部门',\n          children: []\n        }\n      ],\n\n      // 信息列表数据\n      infoList: []\n    };\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n    // 加载关键词数据\n    this.loadKeywordData()\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    // 加载关键词数据\n    async loadKeywordData() {\n      try {\n        const params = {\n          pageNum: this.currentPage,\n          pageSize: this.pageSize\n        }\n\n        const response = await this.$http.get('/public/keywordData/list', { params })\n\n        if (response.data.code === 200) {\n          // 将API返回的数据转换为前端需要的格式\n          this.infoList = response.data.data.rows || []\n          this.totalItems = response.data.data.total || 0\n        } else {\n          this.$message.error('获取数据失败：' + response.data.msg)\n        }\n      } catch (error) {\n        console.error('加载关键词数据失败:', error)\n        this.$message.error('加载数据失败，请稍后重试')\n      }\n    },\n\n    // 菜单点击事件\n    handleMenuClick(menuName) {\n      console.log('选择菜单:', menuName);\n    },\n\n    // 获取情感图标\n    getSentimentIcon(sentiment) {\n      const icons = {\n        positive: 'el-icon-sunny',\n        neutral: 'el-icon-partly-cloudy',\n        negative: 'el-icon-cloudy'\n      };\n      return icons[sentiment] || 'el-icon-question';\n    },\n\n    // 获取情感文本\n    getSentimentText(sentiment) {\n      const texts = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      };\n      return texts[sentiment] || '未知';\n    },\n\n    // 下拉菜单命令处理\n    handleCommand(command) {\n      console.log('执行命令:', command);\n    },\n\n    // 处理搜索关键词\n    handleSearchKeyword() {\n      if (this.searchKeyword.trim()) {\n        // 跳转到搜索结果页面，并传递搜索关键词\n        this.$router.push({\n          path: '/search-results',\n          query: {\n            q: this.searchKeyword.trim()\n          }\n        });\n      } else {\n        this.$message.warning('请输入搜索关键词');\n      }\n    },\n\n    // 分页相关方法\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1; // 切换每页显示数量时，重置为第一页\n      console.log('每页显示数量:', size);\n      this.loadKeywordData(); // 重新加载数据\n    },\n\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      console.log('当前页码:', page);\n      this.loadKeywordData(); // 重新加载数据\n    },\n\n    // 新建方案相关方法\n    showCreatePlanDialog() {\n      this.createPlanDialogVisible = true;\n    },\n\n    // 主题标签相关方法\n    handleRemoveTheme(tag) {\n      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);\n    },\n\n    showThemeInput() {\n      this.themeInputVisible = true;\n      this.$nextTick(_ => {\n        this.$refs.themeInput.$refs.input.focus();\n      });\n    },\n\n    handleAddTheme() {\n      let inputValue = this.themeInputValue;\n      if (inputValue) {\n        if (!this.planForm.themes.includes(inputValue)) {\n          this.planForm.themes.push(inputValue);\n        }\n      }\n      this.themeInputVisible = false;\n      this.themeInputValue = '';\n    },\n\n    // 监测对象下拉菜单命令处理\n    handleMonitorObjectCommand(command) {\n      this.planForm.monitorObject = command;\n    },\n\n    // 显示地域选择地图\n    showLocationMap() {\n      // 这里可以实现地图选择功能\n      this.$message.info('显示地域选择地图');\n    },\n\n    // 显示行业分类选择\n    showIndustrySelect() {\n      this.industryDialogVisible = true;\n    },\n\n    // 处理行业分类树节点点击\n    handleIndustryNodeClick(data) {\n      this.selectedIndustry = data;\n    },\n\n    // 确认行业分类选择\n    confirmIndustrySelect() {\n      if (this.selectedIndustry) {\n        this.planForm.industry = this.selectedIndustry.label;\n      }\n      this.industryDialogVisible = false;\n    },\n\n    // 保存方案\n    savePlan() {\n      // 根据当前激活的标签页选择不同的表单数据\n      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;\n\n      console.log('保存方案:', formData);\n      // 这里可以添加表单验证和提交到后端的逻辑\n\n      // 关闭对话框\n      this.createPlanDialogVisible = false;\n\n      // 重置表单\n      if (this.planActiveTab === 'standard') {\n        this.planForm = {\n          name: '',\n          scope: 'all',\n          monitorObject: '',\n          location: '',\n          themes: [],\n          industry: '',\n          timeRange: '',\n          channels: ['news', 'weibo', 'wechat']\n        };\n      } else {\n        this.advancedPlanForm = {\n          name: ''\n        };\n      }\n    },\n\n    // 显示发送预警弹窗\n    showSendAlertDialog() {\n      this.sendAlertDialogVisible = true;\n    },\n\n    // 取消发送预警\n    cancelSendAlert() {\n      this.sendAlertDialogVisible = false;\n      // 重置表单\n      this.alertForm = {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      };\n    },\n    handleSentimentClick(item) {\n      this.editingItem = item;\n      this.selectedSentiment = item.sentiment;\n      this.positiveSentimentDialogVisible = true;\n    },\n    handlePositiveDialogConfirm() {\n      if (this.editingItem) {\n        this.editingItem.sentiment = this.selectedSentiment;\n        // 在实际应用中，这里可能需要调用API将更改保存到后端\n        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);\n      }\n      this.positiveSentimentDialogVisible = false;\n    },\n\n    // 确认发送预警\n    confirmSendAlert() {\n      // 在这里处理发送预警的逻辑\n      console.log('发送预警:', this.alertForm);\n      this.sendAlertDialogVisible = false;\n      // 清空表单\n      this.alertForm = {\n        title: '',\n        selectedReceivers: ['', '', '', '', '', '']\n      };\n    },\n\n    // 加入至报告素材对话框相关方法\n    showAddToAlertMaterialDialog() {\n      this.addToAlertMaterialDialogVisible = true;\n    },\n    cancelAddToAlertMaterial() {\n      this.addToAlertMaterialDialogVisible = false;\n      this.selectedMaterialLibrary = ''; // 清空选中值\n    },\n    confirmAddToAlertMaterial() {\n      // 这里添加确认逻辑，例如提交选中的素材库\n      console.log('Selected Material Library:', this.selectedMaterialLibrary);\n      this.addToAlertMaterialDialogVisible = false;\n      this.selectedMaterialLibrary = ''; // 清空选中值\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\nel-dialog\ntitle=\"信息图谱\"\n:visible.sync=\"infoGraphDialogVisible\"\nwidth=\"50%\"\nappend-to-body\ncustom-class=\"info-graph-dialog\"\n>\n<div class=\"info-graph-content\">\n<!-- 根据提供的图示调整内容布局 -->\n<div class=\"graph-container\">\n<div class=\"graph-node\">东木头人</div>\n<div class=\"graph-node\">永兴队</div>\n<!-- 添加更多节点 -->\n</div>\n</div>\n<div slot=\"footer\" class=\"dialog-footer\">\n<el-button @click=\"infoGraphDialogVisible = false\">关闭</el-button>\n</div>\n</el-dialog>\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .el-tabs__header {\n    margin-bottom: 20px;\n  }\n\n  .el-tabs__nav {\n    width: 100%;\n  }\n\n  .el-tabs__item {\n    flex: 1;\n    text-align: center;\n    height: 40px;\n    line-height: 40px;\n  }\n\n  .el-form-item {\n    margin-bottom: 15px;\n  }\n}\n\n.monitor-object-select {\n  display: flex;\n  align-items: center;\n  position: relative;\n\n  .el-dropdown {\n    position: absolute;\n    right: 10px;\n    top: 0;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n  }\n}\n\n.location-select {\n  display: flex;\n  align-items: center;\n}\n\n.location-btn {\n  margin-left: 10px;\n}\n\n.industry-select-btn {\n  width: 100%;\n  text-align: left;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 8px 15px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #c0c4cc;\n  }\n}\n\n.theme-row, .industry-row {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.theme-input, .industry-input {\n  width: 100px;\n  margin-left: 10px;\n  vertical-align: bottom;\n}\n\n.theme-button, .industry-button {\n  margin-left: 10px;\n  height: 32px;\n  line-height: 30px;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.el-tag {\n  margin-right: 10px;\n  margin-bottom: 5px;\n}\n\n.channels-row {\n  display: flex;\n  margin-bottom: 10px;\n\n  .el-checkbox {\n    margin-right: 20px;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n// 分页容器样式\n.pagination-container {\n  padding: 15px;\n  text-align: right;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n}\n\n// 行业分类弹窗样式\n.industry-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .industry-dialog-content {\n    display: flex;\n    height: 400px;\n  }\n\n  .industry-tree-container {\n    flex: 1;\n    padding: 15px;\n    border-right: 1px solid #e8e8e8;\n    overflow-y: auto;\n  }\n\n  .industry-selected-container {\n    width: 200px;\n    padding: 15px;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .industry-selected-title {\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n}\n\n// 发送预警弹窗样式\n.send-alert-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .send-alert-content {\n    padding: 20px;\n  }\n\n  .receiver-list {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .receiver-item {\n    display: flex;\n    align-items: center;\n  }\n\n  .receiver-type {\n    width: 80px;\n    margin-right: 10px;\n  }\n\n  .receiver-select {\n    width: 100%;\n  }\n}\n\n// 正面情感弹窗样式\n.positive-sentiment-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n</style>\n\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f0f2f5;\n}\n\n// 操作按钮区域样式\n.action-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  z-index: 10;\n  display: flex;\n  gap: 10px;\n}\n\n.info-summary-container {\n  display: flex;\n  height: 100%;\n  padding-top: 60px; // 为新建方案按钮留出空间\n}\n\n// 左侧导航栏样式\n.left-sidebar {\n  width: 180px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sidebar-menu {\n  flex: 1;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n\n// 右侧内容区域样式\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  overflow: hidden;\n  margin: 0 15px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n  padding: 15px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.entity-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.entity-name {\n  margin-right: 5px;\n}\n\n// 标签页样式\n.tabs-container {\n  padding: 10px 15px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-tabs {\n  display: flex;\n  align-items: center;\n}\n\n// 筛选区域样式\n.filter-section {\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.filter-row {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.filter-row:last-child {\n  margin-bottom: 0;\n}\n\n.filter-label {\n  width: 80px;\n  color: #606266;\n  line-height: 28px;\n}\n\n.filter-actions {\n  margin-top: 10px;\n  padding-left: 80px;\n}\n\n// 操作栏样式\n.action-bar {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.left-actions, .right-actions {\n  display: flex;\n  align-items: center;\n}\n\n.search-input {\n  width: 200px;\n  margin-right: 10px;\n}\n\n// 信息列表样式\n.info-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.info-item {\n  padding: 15px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.item-checkbox {\n  margin-right: 10px;\n  margin-top: 3px;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.info-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.info-summary {\n  color: #606266;\n  margin-bottom: 8px;\n  line-height: 1.5;\n}\n\n.info-footer {\n  display: flex;\n  align-items: center;\n  color: #909399;\n  font-size: 13px;\n}\n\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\n  margin-right: 15px;\n}\n\n.info-index {\n  margin-left: auto;\n}\n\n.sentiment-positive {\n  color: #67c23a;\n}\n\n.sentiment-neutral {\n  color: #909399;\n}\n\n.sentiment-negative {\n  color: #f56c6c;\n}\n\n.info-images {\n  display: flex;\n  margin-top: 10px;\n}\n\n.info-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n// 高亮样式\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n\n// 新建方案对话框样式\n.create-plan-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n"]}]}