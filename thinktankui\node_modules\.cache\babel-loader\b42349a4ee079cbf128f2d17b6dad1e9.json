{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core.js", "mtime": 1749105929545}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill", "_interopRequireWildcard", "require", "_block", "_break", "_interopRequireDefault", "_container", "_cursor", "_embed", "_inline", "_scroll", "_text", "_clipboard", "_history", "_keyboard", "_uploader", "_quill<PERSON><PERSON><PERSON>", "_input", "_uiNode", "_module", "<PERSON><PERSON><PERSON>", "register", "Block", "BlockEmbed", "Break", "Container", "<PERSON><PERSON><PERSON>", "Embed", "Inline", "<PERSON><PERSON>", "TextBlot", "Clipboard", "History", "Keyboard", "Uploader", "Input", "UINode", "_default", "exports", "default"], "sources": ["../src/core.ts"], "sourcesContent": ["import Quill, { Parchment, Range } from './core/quill.js';\nimport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n} from './core/quill.js';\n\nimport Block, { BlockEmbed } from './blots/block.js';\nimport Break from './blots/break.js';\nimport Container from './blots/container.js';\nimport Cursor from './blots/cursor.js';\nimport Embed from './blots/embed.js';\nimport Inline from './blots/inline.js';\nimport Scroll from './blots/scroll.js';\nimport TextBlot from './blots/text.js';\n\nimport Clipboard from './modules/clipboard.js';\nimport History from './modules/history.js';\nimport Keyboard from './modules/keyboard.js';\nimport Uploader from './modules/uploader.js';\nimport Delta, { Op, OpIterator, AttributeMap } from 'quill-delta';\nimport Input from './modules/input.js';\nimport UINode from './modules/uiNode.js';\n\nexport { default as Module } from './core/module.js';\nexport { Delta, Op, OpIterator, AttributeMap, Parchment, Range };\nexport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n};\n\nQuill.register({\n  'blots/block': Block,\n  'blots/block/embed': BlockEmbed,\n  'blots/break': Break,\n  'blots/container': Container,\n  'blots/cursor': Cursor,\n  'blots/embed': Embed,\n  'blots/inline': Inline,\n  'blots/scroll': Scroll,\n  'blots/text': TextBlot,\n\n  'modules/clipboard': Clipboard,\n  'modules/history': History,\n  'modules/keyboard': Keyboard,\n  'modules/uploader': Uploader,\n  'modules/input': Input,\n  'modules/uiNode': UINode,\n});\n\nexport default Quill;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AASA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,UAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,OAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,MAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,OAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,OAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,KAAA,GAAAN,sBAAA,CAAAH,OAAA;AAEA,IAAAU,UAAA,GAAAP,sBAAA,CAAAH,OAAA;AACA,IAAAW,QAAA,GAAAR,sBAAA,CAAAH,OAAA;AACA,IAAAY,SAAA,GAAAT,sBAAA,CAAAH,OAAA;AACA,IAAAa,SAAA,GAAAV,sBAAA,CAAAH,OAAA;AACA,IAAAc,WAAA,GAAAf,uBAAA,CAAAC,OAAA;AACA,IAAAe,MAAA,GAAAZ,sBAAA,CAAAH,OAAA;AACA,IAAAgB,OAAA,GAAAb,sBAAA,CAAAH,OAAA;AAEA,IAAAiB,OAAA,GAAAd,sBAAA,CAAAH,OAAA;AAUAkB,cAAK,CAACC,QAAQ,CAAC;EACb,aAAa,EAAEC,cAAK;EACpB,mBAAmB,EAAEC,iBAAU;EAC/B,aAAa,EAAEC,cAAK;EACpB,iBAAiB,EAAEC,kBAAS;EAC5B,cAAc,EAAEC,eAAM;EACtB,aAAa,EAAEC,cAAK;EACpB,cAAc,EAAEC,eAAM;EACtB,cAAc,EAAEC,eAAM;EACtB,YAAY,EAAEC,aAAQ;EAEtB,mBAAmB,EAAEC,kBAAS;EAC9B,iBAAiB,EAAEC,gBAAO;EAC1B,kBAAkB,EAAEC,iBAAQ;EAC5B,kBAAkB,EAAEC,iBAAQ;EAC5B,eAAe,EAAEC,cAAK;EACtB,gBAAgB,EAAEC;AACpB,CAAC,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEanB,cAAK", "ignoreList": []}]}