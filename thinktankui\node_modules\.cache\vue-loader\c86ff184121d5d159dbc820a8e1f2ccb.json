{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\RuoYi\\Doc\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\RuoYi\\Doc\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUnVvWWlEb2MnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwOi8vZG9jLnJ1b3lpLnZpcC9ydW95aS12dWUnCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnb3RvKCkgewogICAgICB3aW5kb3cub3Blbih0aGlzLnVybCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RuoYi/Doc", "sourcesContent": ["<template>\n  <div>\n    <svg-icon icon-class=\"question\" @click=\"goto\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RuoYiDoc',\n  data() {\n    return {\n      url: 'http://doc.ruoyi.vip/ruoyi-vue'\n    }\n  },\n  methods: {\n    goto() {\n      window.open(this.url)\n    }\n  }\n}\n</script>"]}]}