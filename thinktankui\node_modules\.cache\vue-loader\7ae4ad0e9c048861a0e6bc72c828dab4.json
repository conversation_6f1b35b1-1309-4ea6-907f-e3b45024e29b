{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\my-issues\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\my-issues\\index.vue", "mtime": 1748097830346}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTXlJc3N1ZXMnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVNZW51SXRlbTogJ+a<PERSON>ue<PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/my-issues", "sourcesContent": ["<template>\n  <div class=\"my-issues\">\n    <!-- 左侧导航栏 -->\n    <div class=\"left-sidebar\">\n      <div class=\"sidebar-header\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\n      </div>\n\n      <div class=\"sidebar-search\">\n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索方案\"\n          size=\"small\"\n          prefix-icon=\"el-icon-search\">\n        </el-input>\n      </div>\n\n      <div class=\"sidebar-menu\">\n        <div class=\"menu-section\">\n          <div class=\"section-title\">已有方案</div>\n          <el-menu\n            :default-active=\"activeMenuItem\"\n            class=\"sidebar-menu-list\"\n            @select=\"handleMenuSelect\">\n            <el-menu-item index=\"首页\">\n              <i class=\"el-icon-s-home\"></i>\n              <span>首页</span>\n            </el-menu-item>\n            <el-menu-item index=\"总监(1)\">\n              <i class=\"el-icon-s-custom\"></i>\n              <span>总监(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"品牌(1)\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span>品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"方太\" class=\"active-item\">\n              <i class=\"el-icon-star-off\"></i>\n              <span>方太</span>\n            </el-menu-item>\n            <el-menu-item index=\"人物(0)\">\n              <i class=\"el-icon-user\"></i>\n              <span>人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"机构(0)\">\n              <i class=\"el-icon-office-building\"></i>\n              <span>机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"产品(0)\">\n              <i class=\"el-icon-goods\"></i>\n              <span>产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"事件(0)\">\n              <i class=\"el-icon-warning\"></i>\n              <span>事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"话题(0)\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              <span>话题(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"舆情总览\">\n              <i class=\"el-icon-monitor\"></i>\n              <span>舆情总览</span>\n            </el-menu-item>\n            <el-menu-item index=\"发送预警\">\n              <i class=\"el-icon-message\"></i>\n              <span>发送预警</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧内容区 -->\n    <div class=\"right-content\">\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <div class=\"issues-header\">\n          <h2>我的问题</h2>\n        </div>\n\n        <div class=\"issues-list\">\n          <div\n            v-for=\"(issue, index) in issuesList\"\n            :key=\"index\"\n            class=\"issue-item\"\n            :class=\"issue.type\">\n            <div class=\"issue-indicator\"></div>\n            <div class=\"issue-content\">\n              <div class=\"issue-title\">{{ issue.title }}</div>\n              <div class=\"issue-description\" v-if=\"issue.description\">{{ issue.description }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'MyIssues',\n  data() {\n    return {\n      activeMenuItem: '方太',\n      searchText: '',\n      originalTopNav: undefined, // 存储原始的topNav状态\n      issuesList: [\n        {\n          type: 'negative',\n          title: '为什么会被误解为不好？',\n          description: '网民对某些产品或服务产生负面印象，可能是由于信息传播不当或误解造成的。'\n        },\n        {\n          type: 'neutral',\n          title: '在网络上的口碑怎么样？',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '网络舆情的影响因素有哪些？',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '什么是网络舆情？',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '什么是网络舆情？',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '网络舆情的影响因素有哪些？',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '为什么网络舆情监测很重要？',\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\n        },\n        {\n          type: 'positive',\n          title: '为什么网络舆情监测很重要？',\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\n        },\n        {\n          type: 'negative',\n          title: '中国网络舆情监测的发展历程是什么？',\n          description: '从早期的人工监测到现在的智能化监测系统，经历了技术革新的过程。'\n        },\n        {\n          type: 'positive',\n          title: '网络舆情监测的技术手段有哪些？',\n          description: '包括数据采集、情感分析、关键词监测、趋势预测等多种技术手段。'\n        },\n        {\n          type: 'negative',\n          title: '为什么会被误解为\"全球干旱\"，而不是下雨或者其他气象变化？',\n          description: '媒体报道的角度和用词选择可能会影响公众对气象事件的理解。'\n        }\n      ]\n    }\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('Menu selected:', index)\n\n      // 根据选择的菜单项进行路由跳转\n      if (index === '首页') {\n        this.$router.push('/index')\n      } else if (index === '舆情总览') {\n        this.$router.push('/opinion-overview')\n      } else if (index === '发送预警') {\n        // 这里可以触发发送预警的功能\n        console.log('发送预警功能')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.my-issues {\n  display: flex;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 16px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .sidebar-search {\n    padding: 16px;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 0 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 8px;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 40px;\n        line-height: 40px;\n        margin-bottom: 4px;\n        border-radius: 4px;\n\n        &.active-item {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &:hover {\n          background-color: #f5f5f5;\n        }\n      }\n    }\n  }\n}\n\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n\n  .main-content {\n    flex: 1;\n    padding: 20px 24px;\n    overflow-y: auto;\n  }\n}\n\n.issues-header {\n  margin-bottom: 16px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #e8e8e8;\n\n  h2 {\n    margin: 0;\n    font-size: 18px;\n    font-weight: 500;\n    color: #333;\n  }\n}\n\n.issues-list {\n  .issue-item {\n    display: flex;\n    align-items: flex-start;\n    padding: 12px 0;\n    border-bottom: 1px solid #f0f0f0;\n    cursor: pointer;\n    transition: background-color 0.2s;\n\n    &:hover {\n      background-color: #f9f9f9;\n    }\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    .issue-indicator {\n      width: 6px;\n      height: 6px;\n      border-radius: 50%;\n      margin-right: 8px;\n      margin-top: 8px;\n      flex-shrink: 0;\n    }\n\n    &.positive .issue-indicator {\n      background-color: #52c41a;\n    }\n\n    &.negative .issue-indicator {\n      background-color: #ff4d4f;\n    }\n\n    &.neutral .issue-indicator {\n      background-color: #faad14;\n    }\n\n    .issue-content {\n      flex: 1;\n\n      .issue-title {\n        font-size: 14px;\n        color: #333;\n        line-height: 1.6;\n        margin-bottom: 4px;\n        font-weight: normal;\n        word-wrap: break-word;\n        word-break: break-all;\n      }\n\n      .issue-description {\n        font-size: 12px;\n        color: #999;\n        line-height: 1.5;\n        margin-top: 4px;\n      }\n    }\n  }\n}\n</style>\n"]}]}