{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue?vue&type=style&index=0&id=6ed73c63&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue", "mtime": 1749115113523}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyPA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/news", "sourcesContent": ["<template>\n  <div class=\"news-detail-container\">\n    <div v-loading=\"loading\" class=\"news-detail-content\">\n      <!-- 返回按钮 -->\n      <div class=\"back-section\">\n        <el-button \n          type=\"text\" \n          icon=\"el-icon-arrow-left\" \n          @click=\"goBack\"\n          class=\"back-btn\"\n        >\n          返回新闻列表\n        </el-button>\n      </div>\n\n      <!-- 新闻详情 -->\n      <div v-if=\"newsDetail\" class=\"news-article\">\n        <!-- 新闻头部 -->\n        <div class=\"news-header\">\n          <h1 class=\"news-title\">{{ newsDetail.title }}</h1>\n          <div class=\"news-meta\">\n            <div class=\"meta-left\">\n              <span class=\"news-source\">{{ newsDetail.sourceName }}</span>\n              <span class=\"news-time\">{{ formatTime(newsDetail.publishTime) }}</span>\n              <span class=\"sentiment-tag\" :class=\"'sentiment-' + newsDetail.sentiment\">\n                {{ getSentimentText(newsDetail.sentiment) }}\n              </span>\n            </div>\n            <div class=\"meta-right\">\n              <span class=\"news-stats\">\n                <i class=\"el-icon-view\"></i> {{ newsDetail.viewsCount }}\n              </span>\n              <span class=\"news-stats\">\n                <i class=\"el-icon-chat-line-square\"></i> {{ newsDetail.commentsCount }}\n              </span>\n              <span class=\"news-stats\">\n                <i class=\"el-icon-share\"></i> {{ newsDetail.sharesCount }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 新闻摘要 -->\n        <div v-if=\"newsDetail.summary\" class=\"news-summary\">\n          <h3>摘要</h3>\n          <p>{{ newsDetail.summary }}</p>\n        </div>\n\n        <!-- 新闻内容 -->\n        <div class=\"news-content\">\n          <div class=\"content-text\" v-html=\"formatContent(newsDetail.content)\"></div>\n          \n          <!-- 新闻图片 -->\n          <div v-if=\"newsDetail.images && newsDetail.images.length > 0\" class=\"news-images\">\n            <div class=\"images-grid\">\n              <img \n                v-for=\"(img, index) in newsDetail.images\" \n                :key=\"index\" \n                :src=\"img\" \n                class=\"news-image\"\n                @click=\"previewImage(img)\"\n                @error=\"handleImageError\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 新闻标签 -->\n        <div v-if=\"newsDetail.keywords && newsDetail.keywords.length > 0\" class=\"news-tags\">\n          <h4>相关标签</h4>\n          <el-tag \n            v-for=\"keyword in newsDetail.keywords\" \n            :key=\"keyword\" \n            size=\"small\"\n            class=\"keyword-tag\"\n          >\n            {{ keyword }}\n          </el-tag>\n        </div>\n\n        <!-- 新闻信息 -->\n        <div class=\"news-info\">\n          <div class=\"info-item\">\n            <span class=\"info-label\">来源链接：</span>\n            <a v-if=\"newsDetail.sourceUrl\" :href=\"newsDetail.sourceUrl\" target=\"_blank\" class=\"source-link\">\n              查看原文\n            </a>\n            <span v-else>暂无</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">信息属性：</span>\n            <span>{{ getInfoAttributeText(newsDetail.infoAttribute) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">关联实体：</span>\n            <span>{{ newsDetail.entityName || '暂无' }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 导航区域 -->\n      <div v-if=\"newsDetail\" class=\"news-navigation\">\n        <div class=\"nav-item prev-news\" v-if=\"newsDetail.prevNews\">\n          <span class=\"nav-label\">上一篇：</span>\n          <a @click=\"goToNews(newsDetail.prevNews.id)\" class=\"nav-link\">\n            {{ newsDetail.prevNews.title }}\n          </a>\n        </div>\n        <div class=\"nav-item next-news\" v-if=\"newsDetail.nextNews\">\n          <span class=\"nav-label\">下一篇：</span>\n          <a @click=\"goToNews(newsDetail.nextNews.id)\" class=\"nav-link\">\n            {{ newsDetail.nextNews.title }}\n          </a>\n        </div>\n      </div>\n\n      <!-- 相关新闻 -->\n      <div v-if=\"newsDetail && newsDetail.relatedNews && newsDetail.relatedNews.length > 0\" class=\"related-news\">\n        <h3>相关新闻</h3>\n        <div class=\"related-list\">\n          <div \n            v-for=\"related in newsDetail.relatedNews\" \n            :key=\"related.id\"\n            class=\"related-item\"\n            @click=\"goToNews(related.id)\"\n          >\n            <div class=\"related-content\">\n              <h4 class=\"related-title\">{{ related.title }}</h4>\n              <div class=\"related-meta\">\n                <span>{{ related.sourceName }}</span>\n                <span>{{ formatTime(related.publishTime) }}</span>\n              </div>\n            </div>\n            <div v-if=\"related.images && related.images.length > 0\" class=\"related-image\">\n              <img :src=\"related.images[0]\" @error=\"handleImageError\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 图片预览 -->\n    <el-dialog\n      title=\"图片预览\"\n      :visible.sync=\"imagePreviewVisible\"\n      width=\"80%\"\n      center\n    >\n      <div class=\"image-preview\">\n        <img :src=\"previewImageUrl\" style=\"width: 100%; height: auto;\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getNewsDetail } from '@/api/news'\n\nexport default {\n  name: 'NewsDetail',\n  data() {\n    return {\n      loading: true,\n      newsDetail: null,\n      imagePreviewVisible: false,\n      previewImageUrl: ''\n    }\n  },\n  created() {\n    this.getNewsDetail()\n  },\n  watch: {\n    '$route'() {\n      this.getNewsDetail()\n    }\n  },\n  methods: {\n    /** 获取新闻详情 */\n    getNewsDetail() {\n      this.loading = true\n      const newsId = this.$route.params.id\n      \n      getNewsDetail(newsId).then(response => {\n        this.newsDetail = response.data\n        this.loading = false\n        \n        // 更新页面标题\n        if (this.newsDetail.title) {\n          document.title = this.newsDetail.title\n        }\n      }).catch(() => {\n        this.loading = false\n        this.$message.error('获取新闻详情失败')\n      })\n    },\n    /** 返回上一页 */\n    goBack() {\n      this.$router.go(-1)\n    },\n    /** 跳转到指定新闻 */\n    goToNews(newsId) {\n      this.$router.push({ path: `/news/detail/${newsId}` })\n    },\n    /** 格式化时间 */\n    formatTime(time) {\n      if (!time) return ''\n      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')\n    },\n    /** 格式化内容 */\n    formatContent(content) {\n      if (!content) return ''\n      // 简单的换行处理\n      return content.replace(/\\n/g, '<br>')\n    },\n    /** 获取情感文本 */\n    getSentimentText(sentiment) {\n      const sentimentMap = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      }\n      return sentimentMap[sentiment] || '未知'\n    },\n    /** 获取信息属性文本 */\n    getInfoAttributeText(attribute) {\n      const attributeMap = {\n        official: '官方发布',\n        media: '媒体报道',\n        user: '用户评价',\n        competitor: '竞品信息',\n        industry: '行业动态',\n        policy: '政策法规'\n      }\n      return attributeMap[attribute] || '未知'\n    },\n    /** 预览图片 */\n    previewImage(imageUrl) {\n      this.previewImageUrl = imageUrl\n      this.imagePreviewVisible = true\n    },\n    /** 图片加载错误处理 */\n    handleImageError(event) {\n      event.target.style.display = 'none'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.news-detail-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.back-section {\n  margin-bottom: 20px;\n  \n  .back-btn {\n    font-size: 14px;\n    color: #409eff;\n  }\n}\n\n.news-article {\n  background: #fff;\n  padding: 30px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.news-header {\n  border-bottom: 1px solid #ebeef5;\n  padding-bottom: 20px;\n  margin-bottom: 20px;\n  \n  .news-title {\n    font-size: 28px;\n    font-weight: bold;\n    color: #303133;\n    line-height: 1.4;\n    margin: 0 0 15px 0;\n  }\n  \n  .news-meta {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    \n    .meta-left, .meta-right {\n      display: flex;\n      align-items: center;\n      \n      span {\n        margin-right: 15px;\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    .sentiment-tag {\n      padding: 4px 12px;\n      border-radius: 16px;\n      font-size: 12px;\n      \n      &.sentiment-positive {\n        background: #f0f9ff;\n        color: #67c23a;\n      }\n      \n      &.sentiment-neutral {\n        background: #f4f4f5;\n        color: #909399;\n      }\n      \n      &.sentiment-negative {\n        background: #fef0f0;\n        color: #f56c6c;\n      }\n    }\n    \n    .news-stats {\n      i {\n        margin-right: 4px;\n      }\n    }\n  }\n}\n\n.news-summary {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n  \n  h3 {\n    margin: 0 0 10px 0;\n    color: #303133;\n    font-size: 16px;\n  }\n  \n  p {\n    margin: 0;\n    color: #606266;\n    line-height: 1.6;\n  }\n}\n\n.news-content {\n  .content-text {\n    font-size: 16px;\n    line-height: 1.8;\n    color: #303133;\n    margin-bottom: 20px;\n  }\n  \n  .news-images {\n    margin: 20px 0;\n    \n    .images-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n      \n      .news-image {\n        width: 100%;\n        height: 200px;\n        object-fit: cover;\n        border-radius: 6px;\n        cursor: pointer;\n        transition: transform 0.3s;\n        \n        &:hover {\n          transform: scale(1.05);\n        }\n      }\n    }\n  }\n}\n\n.news-tags {\n  margin: 20px 0;\n  \n  h4 {\n    margin: 0 0 10px 0;\n    color: #303133;\n    font-size: 14px;\n  }\n  \n  .keyword-tag {\n    margin-right: 8px;\n    margin-bottom: 8px;\n  }\n}\n\n.news-info {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 6px;\n  margin-top: 20px;\n  \n  .info-item {\n    margin-bottom: 8px;\n    font-size: 14px;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .info-label {\n      color: #909399;\n      margin-right: 8px;\n    }\n    \n    .source-link {\n      color: #409eff;\n      text-decoration: none;\n      \n      &:hover {\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n.news-navigation {\n  background: #fff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n  \n  .nav-item {\n    margin-bottom: 10px;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .nav-label {\n      color: #909399;\n      font-size: 14px;\n    }\n    \n    .nav-link {\n      color: #409eff;\n      cursor: pointer;\n      text-decoration: none;\n      \n      &:hover {\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n.related-news {\n  background: #fff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n  \n  h3 {\n    margin: 0 0 15px 0;\n    color: #303133;\n    font-size: 18px;\n  }\n  \n  .related-list {\n    .related-item {\n      display: flex;\n      padding: 15px 0;\n      border-bottom: 1px solid #ebeef5;\n      cursor: pointer;\n      transition: background-color 0.3s;\n      \n      &:hover {\n        background-color: #f5f7fa;\n      }\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .related-content {\n        flex: 1;\n        \n        .related-title {\n          margin: 0 0 8px 0;\n          color: #303133;\n          font-size: 16px;\n          font-weight: 500;\n        }\n        \n        .related-meta {\n          font-size: 13px;\n          color: #909399;\n          \n          span {\n            margin-right: 15px;\n          }\n        }\n      }\n      \n      .related-image {\n        width: 80px;\n        height: 60px;\n        margin-left: 15px;\n        \n        img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          border-radius: 4px;\n        }\n      }\n    }\n  }\n}\n\n.image-preview {\n  text-align: center;\n}\n</style>\n"]}]}