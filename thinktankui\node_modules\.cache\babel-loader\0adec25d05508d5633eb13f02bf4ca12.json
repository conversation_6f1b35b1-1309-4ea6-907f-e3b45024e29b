{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\BarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\BarChart.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_resize", "_interopRequireDefault", "animationDuration", "_default", "exports", "default", "mixins", "resize", "props", "className", "type", "String", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "axisPointer", "grid", "top", "left", "right", "bottom", "containLabel", "xAxis", "axisTick", "alignWithLabel", "yAxis", "show", "series", "name", "stack", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/dashboard/BarChart.vue"], "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nconst animationDuration = 6000\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\n          }\n        },\n        grid: {\n          top: 10,\n          left: '2%',\n          right: '2%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: [{\n          type: 'category',\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n          axisTick: {\n            alignWithLabel: true\n          }\n        }],\n        yAxis: [{\n          type: 'value',\n          axisTick: {\n            show: false\n          }\n        }],\n        series: [{\n          name: 'pageA',\n          type: 'bar',\n          stack: 'vistors',\n          barWidth: '60%',\n          data: [79, 52, 200, 334, 390, 330, 220],\n          animationDuration\n        }, {\n          name: 'pageB',\n          type: 'bar',\n          stack: 'vistors',\n          barWidth: '60%',\n          data: [80, 52, 200, 334, 390, 330, 220],\n          animationDuration\n        }, {\n          name: 'pageC',\n          type: 'bar',\n          stack: 'vistors',\n          barWidth: '60%',\n          data: [30, 52, 200, 334, 390, 330, 220],\n          animationDuration\n        }]\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;AADAA,OAAA;;AAGA,IAAAG,iBAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAO,KAAA;MACAF,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAQ,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAlB,OAAA,CAAA0B,IAAA,MAAAC,GAAA;MAEA,KAAAT,KAAA,CAAAU,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YAAA;YACAlB,IAAA;UACA;QACA;QACAmB,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAzB,IAAA;UACAI,IAAA;UACAsB,QAAA;YACAC,cAAA;UACA;QACA;QACAC,KAAA;UACA5B,IAAA;UACA0B,QAAA;YACAG,IAAA;UACA;QACA;QACAC,MAAA;UACAC,IAAA;UACA/B,IAAA;UACAgC,KAAA;UACAC,QAAA;UACA7B,IAAA;UACAZ,iBAAA,EAAAA;QACA;UACAuC,IAAA;UACA/B,IAAA;UACAgC,KAAA;UACAC,QAAA;UACA7B,IAAA;UACAZ,iBAAA,EAAAA;QACA;UACAuC,IAAA;UACA/B,IAAA;UACAgC,KAAA;UACAC,QAAA;UACA7B,IAAA;UACAZ,iBAAA,EAAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}