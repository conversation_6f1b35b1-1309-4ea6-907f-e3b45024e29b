{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\my-issues\\index.vue?vue&type=style&index=0&id=02dd1d94&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\my-issues\\index.vue", "mtime": 1748097830346}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749*********}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0NA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/my-issues", "sourcesContent": ["<template>\n  <div class=\"my-issues\">\n    <!-- 左侧导航栏 -->\n    <div class=\"left-sidebar\">\n      <div class=\"sidebar-header\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\n      </div>\n\n      <div class=\"sidebar-search\">\n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索方案\"\n          size=\"small\"\n          prefix-icon=\"el-icon-search\">\n        </el-input>\n      </div>\n\n      <div class=\"sidebar-menu\">\n        <div class=\"menu-section\">\n          <div class=\"section-title\">已有方案</div>\n          <el-menu\n            :default-active=\"activeMenuItem\"\n            class=\"sidebar-menu-list\"\n            @select=\"handleMenuSelect\">\n            <el-menu-item index=\"首页\">\n              <i class=\"el-icon-s-home\"></i>\n              <span>首页</span>\n            </el-menu-item>\n            <el-menu-item index=\"总监(1)\">\n              <i class=\"el-icon-s-custom\"></i>\n              <span>总监(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"品牌(1)\">\n              <i class=\"el-icon-s-goods\"></i>\n              <span>品牌(1)</span>\n            </el-menu-item>\n            <el-menu-item index=\"方太\" class=\"active-item\">\n              <i class=\"el-icon-star-off\"></i>\n              <span>方太</span>\n            </el-menu-item>\n            <el-menu-item index=\"人物(0)\">\n              <i class=\"el-icon-user\"></i>\n              <span>人物(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"机构(0)\">\n              <i class=\"el-icon-office-building\"></i>\n              <span>机构(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"产品(0)\">\n              <i class=\"el-icon-goods\"></i>\n              <span>产品(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"事件(0)\">\n              <i class=\"el-icon-warning\"></i>\n              <span>事件(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"话题(0)\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              <span>话题(0)</span>\n            </el-menu-item>\n            <el-menu-item index=\"舆情总览\">\n              <i class=\"el-icon-monitor\"></i>\n              <span>舆情总览</span>\n            </el-menu-item>\n            <el-menu-item index=\"发送预警\">\n              <i class=\"el-icon-message\"></i>\n              <span>发送预警</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧内容区 -->\n    <div class=\"right-content\">\n      <!-- 主要内容区域 -->\n      <div class=\"main-content\">\n        <div class=\"issues-header\">\n          <h2>我的问题</h2>\n        </div>\n\n        <div class=\"issues-list\">\n          <div\n            v-for=\"(issue, index) in issuesList\"\n            :key=\"index\"\n            class=\"issue-item\"\n            :class=\"issue.type\">\n            <div class=\"issue-indicator\"></div>\n            <div class=\"issue-content\">\n              <div class=\"issue-title\">{{ issue.title }}</div>\n              <div class=\"issue-description\" v-if=\"issue.description\">{{ issue.description }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'MyIssues',\n  data() {\n    return {\n      activeMenuItem: '方太',\n      searchText: '',\n      originalTopNav: undefined, // 存储原始的topNav状态\n      issuesList: [\n        {\n          type: 'negative',\n          title: '为什么会被误解为不好？',\n          description: '网民对某些产品或服务产生负面印象，可能是由于信息传播不当或误解造成的。'\n        },\n        {\n          type: 'neutral',\n          title: '在网络上的口碑怎么样？',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '网络舆情的影响因素有哪些？',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '什么是网络舆情？',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '什么是网络舆情？',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\n          description: ''\n        },\n        {\n          type: 'positive',\n          title: '网络舆情的影响因素有哪些？',\n          description: ''\n        },\n        {\n          type: 'negative',\n          title: '为什么网络舆情监测很重要？',\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\n        },\n        {\n          type: 'positive',\n          title: '为什么网络舆情监测很重要？',\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\n        },\n        {\n          type: 'negative',\n          title: '中国网络舆情监测的发展历程是什么？',\n          description: '从早期的人工监测到现在的智能化监测系统，经历了技术革新的过程。'\n        },\n        {\n          type: 'positive',\n          title: '网络舆情监测的技术手段有哪些？',\n          description: '包括数据采集、情感分析、关键词监测、趋势预测等多种技术手段。'\n        },\n        {\n          type: 'negative',\n          title: '为什么会被误解为\"全球干旱\"，而不是下雨或者其他气象变化？',\n          description: '媒体报道的角度和用词选择可能会影响公众对气象事件的理解。'\n        }\n      ]\n    }\n  },\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n  methods: {\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('Menu selected:', index)\n\n      // 根据选择的菜单项进行路由跳转\n      if (index === '首页') {\n        this.$router.push('/index')\n      } else if (index === '舆情总览') {\n        this.$router.push('/opinion-overview')\n      } else if (index === '发送预警') {\n        // 这里可以触发发送预警的功能\n        console.log('发送预警功能')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.my-issues {\n  display: flex;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 16px;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .sidebar-search {\n    padding: 16px;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 0 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 8px;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 40px;\n        line-height: 40px;\n        margin-bottom: 4px;\n        border-radius: 4px;\n\n        &.active-item {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &:hover {\n          background-color: #f5f5f5;\n        }\n      }\n    }\n  }\n}\n\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n\n  .main-content {\n    flex: 1;\n    padding: 20px 24px;\n    overflow-y: auto;\n  }\n}\n\n.issues-header {\n  margin-bottom: 16px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #e8e8e8;\n\n  h2 {\n    margin: 0;\n    font-size: 18px;\n    font-weight: 500;\n    color: #333;\n  }\n}\n\n.issues-list {\n  .issue-item {\n    display: flex;\n    align-items: flex-start;\n    padding: 12px 0;\n    border-bottom: 1px solid #f0f0f0;\n    cursor: pointer;\n    transition: background-color 0.2s;\n\n    &:hover {\n      background-color: #f9f9f9;\n    }\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    .issue-indicator {\n      width: 6px;\n      height: 6px;\n      border-radius: 50%;\n      margin-right: 8px;\n      margin-top: 8px;\n      flex-shrink: 0;\n    }\n\n    &.positive .issue-indicator {\n      background-color: #52c41a;\n    }\n\n    &.negative .issue-indicator {\n      background-color: #ff4d4f;\n    }\n\n    &.neutral .issue-indicator {\n      background-color: #faad14;\n    }\n\n    .issue-content {\n      flex: 1;\n\n      .issue-title {\n        font-size: 14px;\n        color: #333;\n        line-height: 1.6;\n        margin-bottom: 4px;\n        font-weight: normal;\n        word-wrap: break-word;\n        word-break: break-all;\n      }\n\n      .issue-description {\n        font-size: 12px;\n        color: #999;\n        line-height: 1.5;\n        margin-top: 4px;\n      }\n    }\n  }\n}\n</style>\n"]}]}