{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\index.vue", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userManagement", "_interopRequireDefault", "require", "name", "components", "UserManagement", "watch", "selectAllFavorites", "val", "watchSelectAllFavorites", "data", "activeMenuItem", "currentSettingsTab", "userInfo", "userId", "phoneNumber", "registerDate", "category", "plan", "totalPoints", "pointsLevel", "availablePoints", "estimatedDays", "expirationDate", "remainingCount", "settings", "auto", "manual", "downloadImages", "backup", "realTimeSync", "humanReview", "autoPublish", "keywords", "highQuality", "multiPlatform", "autoSave", "batchProcess", "imageProcess", "autoOn", "autoOff", "downloadList", "id", "dataSize", "createTime", "status", "total", "currentPage", "pageSize", "jumpPage", "selectedDownloads", "versionList", "version", "date", "selectedVersion", "notes", "addMaterialDialogVisible", "newMaterialName", "materialList", "showPasswordChange", "passwordForm", "oldPassword", "newPassword", "confirmPassword", "selectedFavorites", "favoriteStartDate", "favoriteEndDate", "favoriteSearchKeyword", "favoriteList", "selected", "tag", "title", "time", "source", "favoritesTotal", "favoritesCurrentPage", "favoritesPageSize", "favoritesJumpPage", "contactActiveTab", "contactList", "phone", "email", "location", "addContactDialogVisible", "contactForm", "contactFormRules", "required", "message", "trigger", "pattern", "type", "methods", "handleMenuSelect", "index", "showAccountInfo", "showPasswordForm", "changePassword", "$message", "warning", "success", "switchSettingsTab", "tab", "saveSettings", "handleSelectionChange", "selection", "batchDownload", "length", "concat", "batchDelete", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "info", "handleDownload", "row", "handleDelete", "_this2", "handleCurrentChange", "handleJumpPage", "page", "parseInt", "isNaN", "Math", "ceil", "selectVersion", "versionData", "find", "v", "showAddMaterialDialog", "addMaterial", "trim", "push", "Date", "now", "toLocaleString", "handleFavoriteSelect", "filter", "item", "for<PERSON>ach", "_toConsumableArray2", "default", "batchCancelFavorite", "_this3", "selectedIds", "map", "includes", "cancelFavorite", "_this4", "i", "showAddContactDialog", "submitContactForm", "_this5", "$refs", "validate", "valid", "findIndex", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "splice", "newContact", "_objectSpread2", "max", "apply", "handleEditContact", "JSON", "parse", "stringify", "handleDeleteContact", "_this6", "exportFavorites", "handleFavoritePageChange", "handleFavoriteJumpPage"], "sources": ["src/views/account/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"page-layout\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"user-info\">\n          <div class=\"avatar\">\n            <img src=\"@/assets/images/profile.jpg\" alt=\"用户头像\">\n          </div>\n          <div class=\"user-id\">***********</div>\n          <div class=\"register-date\">2023-04-28注册</div>\n        </div>\n\n        <div class=\"sidebar-menu\">\n          <el-menu\n            :default-active=\"activeMenuItem\"\n            class=\"sidebar-menu-list\"\n            @select=\"handleMenuSelect\"\n          >\n            <el-menu-item index=\"account\">\n              <i class=\"el-icon-user\"></i>\n              <span>我的账号</span>\n            </el-menu-item>\n            <el-menu-item index=\"favorite\">\n              <i class=\"el-icon-star-on\"></i>\n              <span>我的收藏</span>\n            </el-menu-item>\n            <el-menu-item index=\"download\">\n              <i class=\"el-icon-download\"></i>\n              <span>我的下载</span>\n            </el-menu-item>\n            <el-menu-item index=\"contact\">\n              <i class=\"el-icon-notebook-1\"></i>\n              <span>我的联系人</span>\n            </el-menu-item>\n            <el-menu-item index=\"material\">\n              <i class=\"el-icon-chat-line-round\"></i>\n              <span>素材库</span>\n            </el-menu-item>\n            <el-menu-item index=\"privacy\">\n              <i class=\"el-icon-setting\"></i>\n              <span>系统设置</span>\n            </el-menu-item>\n            <el-menu-item index=\"user-management\">\n              <i class=\"el-icon-user\"></i>\n              <span>用户管理</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n      </div>\n\n      <!-- 右侧内容区 -->\n      <div class=\"content\">\n        <!-- 账户信息内容 -->\n        <div v-if=\"activeMenuItem === 'account'\" class=\"account-container\">\n          <div class=\"account-header\">\n            <div class=\"title\">我的账号</div>\n            <div class=\"actions\">\n              <el-button size=\"small\" @click=\"showAccountInfo\">账号安全</el-button>\n              <el-button type=\"primary\" size=\"small\" @click=\"showPasswordForm\">修改密码</el-button>\n            </div>\n          </div>\n\n          <!-- 账户信息内容 -->\n          <div v-if=\"!showPasswordChange\" class=\"account-info\">\n            <div class=\"info-item\">\n              <div class=\"label\">用户账号：</div>\n              <div class=\"value\">***********</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">手机号码：</div>\n              <div class=\"value\">***********</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">创建时间：</div>\n              <div class=\"value\">2023-04-28</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">专业认证分类：</div>\n              <div class=\"value\">互联网+</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">方案：</div>\n              <div class=\"value\">【免费】+【限时】</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">总积分值：</div>\n              <div class=\"value\">2000</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">积分等级：</div>\n              <div class=\"value\">初级VIP</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">剩余可用积分：</div>\n              <div class=\"value\">【250积分 = 600次】</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">预计可用天数：</div>\n              <div class=\"value\">365</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">预计到期日：</div>\n              <div class=\"value\">365</div>\n            </div>\n\n            <div class=\"info-item\">\n              <div class=\"label\">剩余次数：</div>\n              <div class=\"value\">10000次</div>\n            </div>\n          </div>\n\n          <!-- 修改密码表单 -->\n          <div v-if=\"showPasswordChange\" class=\"password-form\">\n            <div class=\"form-group\">\n              <div class=\"form-label\">旧密码：</div>\n              <el-input\n                v-model=\"passwordForm.oldPassword\"\n                type=\"password\"\n                placeholder=\"请输入旧密码\"\n                show-password\n              ></el-input>\n            </div>\n\n            <div class=\"form-group\">\n              <div class=\"form-label\">新密码：</div>\n              <el-input\n                v-model=\"passwordForm.newPassword\"\n                type=\"password\"\n                placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\"\n                show-password\n              ></el-input>\n            </div>\n\n            <div class=\"form-group\">\n              <div class=\"form-label\">确认新密码：</div>\n              <el-input\n                v-model=\"passwordForm.confirmPassword\"\n                type=\"password\"\n                placeholder=\"请再次输入新密码\"\n                show-password\n              ></el-input>\n            </div>\n\n            <div class=\"form-actions\">\n              <el-button type=\"primary\" @click=\"changePassword\">确认修改</el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 我的下载内容 -->\n        <div v-if=\"activeMenuItem === 'download'\" class=\"download-container\">\n          <div class=\"download-header\">\n            <div class=\"title\">我的下载</div>\n            <div class=\"actions\">\n              <el-button type=\"primary\" size=\"small\" @click=\"batchDownload\">批量下载</el-button>\n              <el-button type=\"danger\" size=\"small\" @click=\"batchDelete\">批量删除</el-button>\n            </div>\n          </div>\n\n          <div class=\"download-content\">\n            <el-table\n              :data=\"downloadList\"\n              style=\"width: 100%\"\n              @selection-change=\"handleSelectionChange\">\n              <el-table-column\n                type=\"selection\"\n                width=\"55\">\n              </el-table-column>\n              <el-table-column\n                prop=\"name\"\n                label=\"名称\"\n                width=\"300\">\n              </el-table-column>\n              <el-table-column\n                prop=\"dataSize\"\n                label=\"数据量\"\n                width=\"100\">\n              </el-table-column>\n              <el-table-column\n                prop=\"createTime\"\n                label=\"生成时间\"\n                width=\"180\">\n              </el-table-column>\n              <el-table-column\n                prop=\"status\"\n                label=\"下载状态\"\n                width=\"100\">\n                <template slot-scope=\"scope\">\n                  <span class=\"download-status\">{{ scope.row.status }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column\n                label=\"操作\"\n                width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-download\"\n                    @click=\"handleDownload(scope.row)\">\n                  </el-button>\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    @click=\"handleDelete(scope.row)\">\n                  </el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <div class=\"pagination-container\">\n              <span>共 {{ total }} 条记录</span>\n              <el-pagination\n                background\n                layout=\"prev, pager, next, jumper\"\n                :total=\"total\"\n                :current-page.sync=\"currentPage\"\n                :page-size=\"pageSize\"\n                @current-change=\"handleCurrentChange\">\n              </el-pagination>\n              <span>前往第</span>\n              <el-input\n                v-model=\"jumpPage\"\n                size=\"mini\"\n                class=\"jump-page-input\">\n              </el-input>\n              <span>页</span>\n              <el-button\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleJumpPage\">\n                确定\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 我的收藏内容 -->\n        <div v-if=\"activeMenuItem === 'favorite'\" class=\"favorite-container\">\n          <div class=\"favorite-header\">\n            <div class=\"title\">我的收藏</div>\n          </div>\n\n          <div class=\"favorite-toolbar\">\n            <div class=\"toolbar-left\">\n              <el-checkbox v-model=\"selectAllFavorites\">全选</el-checkbox>\n              <el-button size=\"small\" type=\"danger\" :disabled=\"selectedFavorites.length === 0\" @click=\"batchCancelFavorite\">批量取消收藏</el-button>\n              <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\n            </div>\n\n            <div class=\"toolbar-right\">\n              <div class=\"date-filter\">\n                <el-date-picker\n                  v-model=\"favoriteStartDate\"\n                  type=\"date\"\n                  placeholder=\"开始日期\"\n                  size=\"small\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  style=\"width: 130px;\"\n                ></el-date-picker>\n                <span class=\"date-separator\">-</span>\n                <el-date-picker\n                  v-model=\"favoriteEndDate\"\n                  type=\"date\"\n                  placeholder=\"结束日期\"\n                  size=\"small\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  style=\"width: 130px;\"\n                ></el-date-picker>\n              </div>\n\n              <div class=\"search-box\">\n                <el-input\n                  v-model=\"favoriteSearchKeyword\"\n                  placeholder=\"搜索内容\"\n                  size=\"small\"\n                  prefix-icon=\"el-icon-search\"\n                  clearable\n                  style=\"width: 200px;\"\n                ></el-input>\n              </div>\n\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-download\" @click=\"exportFavorites\">全部下载</el-button>\n            </div>\n          </div>\n\n          <div class=\"favorite-content\">\n            <div class=\"favorite-list\">\n              <div v-for=\"item in favoriteList\" :key=\"item.id\" class=\"favorite-item\">\n                <div class=\"item-checkbox\">\n                  <el-checkbox v-model=\"item.selected\" @change=\"handleFavoriteSelect\"></el-checkbox>\n                </div>\n                <div class=\"item-content\">\n                  <div class=\"item-title\">\n                    <span class=\"title-tag\">{{ item.tag }}</span>\n                    <span class=\"title-text\">{{ item.title }}</span>\n                  </div>\n                  <div class=\"item-info\">\n                    <span class=\"info-time\">收藏时间: {{ item.time }}</span>\n                    <span class=\"info-source\">来源: {{ item.source }}</span>\n                  </div>\n                </div>\n                <div class=\"item-actions\">\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    icon=\"el-icon-delete\"\n                    class=\"cancel-favorite-btn\"\n                    @click=\"cancelFavorite(item)\">\n                    取消收藏\n                  </el-button>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"favorite-pagination\">\n              <div class=\"pagination-info\">\n                共 <span>{{ favoritesTotal }}</span> 条记录\n              </div>\n              <div class=\"pagination-controls\">\n                <el-pagination\n                  background\n                  layout=\"prev, pager, next\"\n                  :total=\"favoritesTotal\"\n                  :current-page.sync=\"favoritesCurrentPage\"\n                  :page-size=\"favoritesPageSize\"\n                  @current-change=\"handleFavoritePageChange\">\n                </el-pagination>\n              </div>\n              <div class=\"pagination-jump\">\n                <span>前往</span>\n                <el-input\n                  v-model=\"favoritesJumpPage\"\n                  size=\"mini\"\n                  class=\"jump-page-input\">\n                </el-input>\n                <span>页</span>\n                <el-button\n                  size=\"mini\"\n                  type=\"primary\"\n                  @click=\"handleFavoriteJumpPage\">\n                  确定\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 素材库内容 -->\n        <div v-if=\"activeMenuItem === 'material'\" class=\"material-container\">\n          <div class=\"material-header\">\n            <div class=\"title\">素材库</div>\n          </div>\n\n          <div class=\"material-content\">\n            <div class=\"material-add-box\" @click=\"showAddMaterialDialog\">\n              <div class=\"add-icon\">\n                <i class=\"el-icon-plus\"></i>\n              </div>\n              <div class=\"add-text\">新建素材包</div>\n            </div>\n          </div>\n\n          <!-- 新建素材对话框 -->\n          <el-dialog\n            title=\"新建素材包\"\n            :visible.sync=\"addMaterialDialogVisible\"\n            width=\"400px\"\n            center\n            :show-close=\"false\"\n            custom-class=\"material-dialog\"\n          >\n            <div class=\"material-form\">\n              <div class=\"form-item\">\n                <div class=\"form-label\">素材包名称：</div>\n                <el-input v-model=\"newMaterialName\" placeholder=\"请输入素材包名称\"></el-input>\n              </div>\n            </div>\n            <div slot=\"footer\" class=\"dialog-footer\">\n              <el-button type=\"primary\" @click=\"addMaterial\">确定</el-button>\n              <el-button @click=\"addMaterialDialogVisible = false\">取消</el-button>\n            </div>\n          </el-dialog>\n        </div>\n\n        <!-- 我的联系人内容 -->\n        <div v-if=\"activeMenuItem === 'contact'\" class=\"contact-container\">\n          <div class=\"contact-header\">\n            <div class=\"title\">我的联系人</div>\n            <div class=\"contact-tabs\">\n              <el-radio-group v-model=\"contactActiveTab\" size=\"small\">\n                <el-radio-button label=\"wechat\">微信</el-radio-button>\n                <el-radio-button label=\"sms\">短信</el-radio-button>\n                <el-radio-button label=\"email\">邮箱</el-radio-button>\n                <el-radio-button label=\"qwechat\">企微群</el-radio-button>\n                <el-radio-button label=\"dingtalk\">钉钉群</el-radio-button>\n                <el-radio-button label=\"feishu\">飞书群</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n\n          <div class=\"contact-toolbar\">\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddContactDialog\">添加/导入联系人</el-button>\n          </div>\n\n          <div class=\"contact-content\">\n            <el-table\n              :data=\"contactList\"\n              style=\"width: 100%\"\n              border\n              stripe\n              :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\n            >\n              <el-table-column\n                label=\"头像\"\n                width=\"80\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-avatar :size=\"40\" icon=\"el-icon-user\"></el-avatar>\n                </template>\n              </el-table-column>\n              <el-table-column\n                prop=\"name\"\n                label=\"姓名\"\n                width=\"120\"\n                align=\"center\"\n              />\n              <el-table-column\n                prop=\"createTime\"\n                label=\"创建/更新\"\n                width=\"180\"\n                align=\"center\"\n              />\n              <el-table-column\n                prop=\"location\"\n                label=\"位置/时间\"\n                width=\"180\"\n                align=\"center\"\n              />\n              <el-table-column\n                label=\"操作\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-edit\"\n                    @click=\"handleEditContact(scope.row)\"\n                  >编辑</el-button>\n                  <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    class=\"delete-btn\"\n                    @click=\"handleDeleteContact(scope.row)\"\n                  >删除</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 添加联系人对话框 -->\n          <el-dialog title=\"添加联系人\" :visible.sync=\"addContactDialogVisible\" width=\"500px\" center>\n            <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactFormRules\" label-width=\"80px\">\n              <el-form-item label=\"姓名\" prop=\"name\">\n                <el-input v-model=\"contactForm.name\" placeholder=\"请输入姓名\" />\n              </el-form-item>\n              <el-form-item label=\"手机号码\" prop=\"phone\">\n                <el-input v-model=\"contactForm.phone\" placeholder=\"请输入手机号码\" />\n              </el-form-item>\n              <el-form-item label=\"邮箱\" prop=\"email\">\n                <el-input v-model=\"contactForm.email\" placeholder=\"请输入邮箱\" />\n              </el-form-item>\n              <el-form-item label=\"位置\" prop=\"location\">\n                <el-input v-model=\"contactForm.location\" placeholder=\"请输入位置\" />\n              </el-form-item>\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n              <el-button @click=\"addContactDialogVisible = false\">取消</el-button>\n              <el-button type=\"primary\" @click=\"submitContactForm\">确定</el-button>\n            </div>\n          </el-dialog>\n        </div>\n\n        <!-- 用户管理内容 -->\n        <div v-if=\"activeMenuItem === 'user-management'\">\n          <user-management />\n        </div>\n\n        <!-- 系统设置内容 -->\n        <div v-if=\"activeMenuItem === 'privacy'\" class=\"settings-container\">\n          <div class=\"settings-header\">\n            <div class=\"title\">系统设置</div>\n          </div>\n\n          <div class=\"settings-content\">\n            <!-- 左侧设置菜单 -->\n            <div class=\"settings-sidebar\">\n              <div class=\"settings-menu\">\n                <div :class=\"['menu-item', currentSettingsTab === 'basic' ? 'active' : '']\" @click=\"switchSettingsTab('basic')\">\n                  <i class=\"el-icon-setting\"></i>\n                  <span>基础设置</span>\n                </div>\n                <div :class=\"['menu-item', currentSettingsTab === 'updates' ? 'active' : '']\" @click=\"switchSettingsTab('updates')\">\n                  <i class=\"el-icon-document\"></i>\n                  <span>更新说明</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- 右侧设置内容 -->\n            <div class=\"settings-main\">\n              <!-- 基础设置内容 -->\n              <div v-if=\"currentSettingsTab === 'basic'\" class=\"settings-section\">\n                <div class=\"section-title\">文章管理选项：</div>\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.auto\">自动</el-checkbox>\n                  <el-checkbox v-model=\"settings.manual\">手动</el-checkbox>\n                  <el-checkbox v-model=\"settings.downloadImages\">下载图片</el-checkbox>\n                  <el-checkbox v-model=\"settings.backup\">备份</el-checkbox>\n                  <el-checkbox v-model=\"settings.realTimeSync\">实时同步</el-checkbox>\n                  <el-checkbox v-model=\"settings.humanReview\">人工审核</el-checkbox>\n                </div>\n\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.autoPublish\">自动发布</el-checkbox>\n                  <el-checkbox v-model=\"settings.keywords\">关键词</el-checkbox>\n                  <el-checkbox v-model=\"settings.highQuality\">高质量</el-checkbox>\n                  <el-checkbox v-model=\"settings.multiPlatform\">多平台发布</el-checkbox>\n                </div>\n\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.autoSave\">自动保存</el-checkbox>\n                  <el-checkbox v-model=\"settings.batchProcess\">批量处理</el-checkbox>\n                  <el-checkbox v-model=\"settings.imageProcess\">图片处理</el-checkbox>\n                </div>\n\n                <div class=\"section-title\">下载设置：</div>\n                <div class=\"download-settings\">\n                  <div class=\"setting-item\">\n                    <span class=\"label\">下载路径：</span>\n                    <span class=\"value\">自动生成文件夹</span>\n                  </div>\n                </div>\n\n                <div class=\"section-title\">高级自动化设置：</div>\n                <div class=\"options-group\">\n                  <el-checkbox v-model=\"settings.autoOn\">开启</el-checkbox>\n                  <el-checkbox v-model=\"settings.autoOff\">关闭</el-checkbox>\n                </div>\n\n                <div class=\"save-button\">\n                  <el-button type=\"primary\" @click=\"saveSettings\">保存</el-button>\n                </div>\n              </div>\n\n              <!-- 更新说明内容 -->\n              <div v-if=\"currentSettingsTab === 'updates'\" class=\"updates-section\">\n                <div class=\"updates-header\">\n                  <div class=\"version-title\">{{ selectedVersion.version }} 版本更新说明</div>\n                  <div class=\"version-date\">{{ selectedVersion.date }}</div>\n                </div>\n\n                <div class=\"updates-content\">\n                  <div class=\"version-list\">\n                    <div\n                      v-for=\"version in versionList\"\n                      :key=\"version.version\"\n                      :class=\"['version-item', selectedVersion.version === version.version ? 'active' : '']\"\n                      @click=\"selectVersion(version.version)\"\n                    >\n                      {{ version.version }}\n                    </div>\n                  </div>\n\n                  <div class=\"update-details\">\n                    <div class=\"update-notes\">\n                      <div v-for=\"(note, index) in selectedVersion.notes\" :key=\"index\" class=\"note-item\">\n                        <div class=\"note-number\">{{ index + 1 }}. </div>\n                        <div class=\"note-content\" v-html=\"note\"></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport UserManagement from './user-management.vue';\n\nexport default {\n  name: \"AccountManagement\",\n  components: {\n    UserManagement\n  },\n  watch: {\n    selectAllFavorites(val) {\n      this.watchSelectAllFavorites(val);\n    }\n  },\n  data() {\n    return {\n      activeMenuItem: 'account', // 默认选中我的账号\n      currentSettingsTab: 'basic', // 默认选中基础设置\n      // 用户信息\n      userInfo: {\n        userId: '***********',\n        phoneNumber: '***********',\n        registerDate: '2023-04-28',\n        category: '互联网+',\n        plan: '【免费】+【限时】',\n        totalPoints: '2000',\n        pointsLevel: '初级VIP',\n        availablePoints: '【250积分 = 600次】',\n        estimatedDays: '365',\n        expirationDate: '365',\n        remainingCount: '10000次'\n      },\n      // 系统设置\n      settings: {\n        auto: true,\n        manual: true,\n        downloadImages: true,\n        backup: true,\n        realTimeSync: false,\n        humanReview: true,\n        autoPublish: false,\n        keywords: true,\n        highQuality: false,\n        multiPlatform: true,\n        autoSave: true,\n        batchProcess: true,\n        imageProcess: true,\n        autoOn: true,\n        autoOff: false\n      },\n      // 下载列表\n      downloadList: [\n        {\n          id: 1,\n          name: '方案_20240428163743_1',\n          dataSize: '1323',\n          createTime: '2024-04-28 16:37:57',\n          status: '已完成'\n        },\n        {\n          id: 2,\n          name: '方案_20240428163743_1',\n          dataSize: '2000',\n          createTime: '2024-04-28 16:37:57',\n          status: '已完成'\n        },\n        {\n          id: 3,\n          name: '方案_20240427173742_4',\n          dataSize: '1893',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 4,\n          name: '方案_20240427173742_3',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 5,\n          name: '方案_20240427173742_2',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 6,\n          name: '方案_20240427173742_2',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 7,\n          name: '方案_20240427173742_1',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:37:42',\n          status: '已完成'\n        },\n        {\n          id: 8,\n          name: '台账_20240427173129_5',\n          dataSize: '1281',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 9,\n          name: '台账_20240427173129_4',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 10,\n          name: '台账_20240427173129_3',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 11,\n          name: '台账_20240427173129_2',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        },\n        {\n          id: 12,\n          name: '台账_20240427173129_1',\n          dataSize: '2000',\n          createTime: '2024-04-27 17:31:29',\n          status: '已完成'\n        }\n      ],\n      // 分页相关\n      total: 12,\n      currentPage: 1,\n      pageSize: 10,\n      jumpPage: '',\n      // 选中的下载项\n      selectedDownloads: [],\n      // 版本列表\n      versionList: [\n        { version: '6.2.9', date: '2024.01.19' },\n        { version: '6.2.8', date: '2023.12.15' },\n        { version: '6.2.7', date: '2023.11.20' },\n        { version: '6.2.6', date: '2023.10.18' },\n        { version: '6.2.5', date: '2023.09.25' },\n        { version: '6.2.4', date: '2023.08.30' },\n        { version: '6.2.3', date: '2023.07.28' },\n        { version: '6.2.2', date: '2023.06.22' },\n        { version: '6.2.1', date: '2023.05.15' },\n        { version: '6.2.0', date: '2023.04.10' }\n      ],\n      // 当前选中的版本\n      selectedVersion: {\n        version: '6.2.9',\n        date: '2024.01.19',\n        notes: [\n          '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\n          '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\n          '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\n          '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\n          '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\n        ]\n      },\n      // 素材库相关\n      addMaterialDialogVisible: false,\n      newMaterialName: '',\n      materialList: [],\n      // 修改密码相关\n      showPasswordChange: false,\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      // 我的收藏相关\n      selectAllFavorites: false,\n      selectedFavorites: [],\n      favoriteStartDate: '',\n      favoriteEndDate: '',\n      favoriteSearchKeyword: '',\n      favoriteList: [\n        {\n          id: 1,\n          selected: false,\n          tag: 'HOT',\n          title: '新产品市场分析报告',\n          time: '2023-04-29 20:37:51',\n          source: '市场调查部门 A 数据库'\n        },\n        {\n          id: 2,\n          selected: false,\n          tag: 'NEW',\n          title: '2024年第一季度行业趋势分析',\n          time: '2024-04-28 15:22:36',\n          source: '行业研究中心'\n        },\n        {\n          id: 3,\n          selected: false,\n          tag: 'HOT',\n          title: '竞品分析与市场定位策略',\n          time: '2024-04-27 09:15:42',\n          source: '战略规划部'\n        },\n        {\n          id: 4,\n          selected: false,\n          tag: 'TOP',\n          title: '用户行为数据分析报告',\n          time: '2024-04-26 14:30:18',\n          source: '数据分析部门'\n        },\n        {\n          id: 5,\n          selected: false,\n          tag: 'NEW',\n          title: '新媒体营销策略白皮书',\n          time: '2024-04-25 11:45:23',\n          source: '营销部门'\n        },\n        {\n          id: 6,\n          selected: false,\n          tag: 'HOT',\n          title: '产品迭代计划与路线图',\n          time: '2024-04-24 16:20:37',\n          source: '产品部门'\n        },\n        {\n          id: 7,\n          selected: false,\n          tag: 'TOP',\n          title: '行业政策解读与影响分析',\n          time: '2024-04-23 10:05:12',\n          source: '政策研究中心'\n        }\n      ],\n      favoritesTotal: 7,\n      favoritesCurrentPage: 1,\n      favoritesPageSize: 10,\n      favoritesJumpPage: '',\n\n      // 我的联系人相关\n      contactActiveTab: 'wechat', // 默认选中微信标签\n      contactList: [\n        {\n          id: 1,\n          name: '张三',\n          phone: '13800138001',\n          email: '<EMAIL>',\n          location: '北京市朝阳区',\n          createTime: '2024-04-28 10:30:45'\n        },\n        {\n          id: 2,\n          name: '李四',\n          phone: '13800138002',\n          email: '<EMAIL>',\n          location: '上海市浦东新区',\n          createTime: '2024-04-27 15:20:36'\n        },\n        {\n          id: 3,\n          name: '王五',\n          phone: '13800138003',\n          email: '<EMAIL>',\n          location: '广州市天河区',\n          createTime: '2024-04-26 09:15:22'\n        }\n      ],\n      addContactDialogVisible: false,\n      contactForm: {\n        id: null,\n        name: '',\n        phone: '',\n        email: '',\n        location: ''\n      },\n      contactFormRules: {\n        name: [\n          { required: true, message: '请输入姓名', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n        ],\n        email: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n        ]\n      }\n    };\n  },\n  methods: {\n    // 处理菜单项选择\n    handleMenuSelect(index) {\n      this.activeMenuItem = index;\n    },\n    // 显示账户信息\n    showAccountInfo() {\n      this.showPasswordChange = false;\n    },\n    // 显示修改密码表单\n    showPasswordForm() {\n      this.showPasswordChange = true;\n      this.passwordForm = {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      };\n    },\n    // 修改密码\n    changePassword() {\n      // 表单验证\n      if (!this.passwordForm.oldPassword) {\n        this.$message.warning('请输入旧密码');\n        return;\n      }\n      if (!this.passwordForm.newPassword) {\n        this.$message.warning('请输入新密码');\n        return;\n      }\n      if (!this.passwordForm.confirmPassword) {\n        this.$message.warning('请确认新密码');\n        return;\n      }\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\n        this.$message.warning('两次输入的新密码不一致');\n        return;\n      }\n\n      // 提交修改密码请求\n      this.$message.success('密码修改成功');\n      this.showPasswordChange = false;\n    },\n    // 切换设置选项卡\n    switchSettingsTab(tab) {\n      this.currentSettingsTab = tab;\n    },\n    // 保存设置\n    saveSettings() {\n      this.$message.success('设置已保存');\n    },\n    // 处理表格选择变化\n    handleSelectionChange(selection) {\n      this.selectedDownloads = selection;\n    },\n    // 批量下载\n    batchDownload() {\n      if (this.selectedDownloads.length === 0) {\n        this.$message.warning('请选择要下载的文件');\n        return;\n      }\n      this.$message.success(`已开始下载${this.selectedDownloads.length}个文件`);\n    },\n    // 批量删除\n    batchDelete() {\n      if (this.selectedDownloads.length === 0) {\n        this.$message.warning('请选择要删除的文件');\n        return;\n      }\n      this.$confirm('确定要删除选中的文件吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$message.success(`已删除${this.selectedDownloads.length}个文件`);\n        // 实际应用中这里需要调用接口删除文件\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n    // 下载单个文件\n    handleDownload(row) {\n      this.$message.success(`开始下载: ${row.name}`);\n    },\n    // 删除单个文件\n    handleDelete(row) {\n      this.$confirm('确定要删除该文件吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$message.success(`已删除: ${row.name}`);\n        // 实际应用中这里需要调用接口删除文件\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n    // 处理页码变化\n    handleCurrentChange(val) {\n      this.currentPage = val;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n    // 处理跳转页面\n    handleJumpPage() {\n      if (!this.jumpPage) {\n        return;\n      }\n      const page = parseInt(this.jumpPage);\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.total / this.pageSize)) {\n        this.$message.warning('请输入有效的页码');\n        return;\n      }\n      this.currentPage = page;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n    // 选择版本\n    selectVersion(version) {\n      const versionData = this.versionList.find(v => v.version === version);\n      if (versionData) {\n        // 根据版本号获取对应的更新说明\n        let notes = [];\n        if (version === '6.2.9') {\n          notes = [\n            '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\n            '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\n            '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\n            '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\n            '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\n          ];\n        } else if (version === '6.2.8') {\n          notes = [\n            '1. 【新增功能】新增<b>数据分析</b>模块，提供更全面的数据统计。',\n            '2. 【功能优化】优化了<b>文件上传</b>功能，支持更多文件格式。',\n            '3. 【问题修复】修复了部分用户<b>无法登录</b>的问题。'\n          ];\n        } else {\n          notes = [\n            '1. 【功能优化】优化系统性能，提升用户体验。',\n            '2. 【问题修复】修复已知问题，提高系统稳定性。'\n          ];\n        }\n\n        this.selectedVersion = {\n          version: versionData.version,\n          date: versionData.date,\n          notes: notes\n        };\n      }\n    },\n    // 显示添加素材对话框\n    showAddMaterialDialog() {\n      this.newMaterialName = '';\n      this.addMaterialDialogVisible = true;\n    },\n    // 添加素材\n    addMaterial() {\n      if (!this.newMaterialName.trim()) {\n        this.$message.warning('请输入素材名称');\n        return;\n      }\n\n      // 添加新素材\n      this.materialList.push({\n        id: Date.now(),\n        name: this.newMaterialName,\n        createTime: new Date().toLocaleString()\n      });\n\n      this.$message.success(`素材\"${this.newMaterialName}\"创建成功`);\n      this.addMaterialDialogVisible = false;\n    },\n\n    // 处理收藏项选择\n    handleFavoriteSelect() {\n      this.selectedFavorites = this.favoriteList.filter(item => item.selected);\n      // 检查是否全选\n      this.selectAllFavorites = this.selectedFavorites.length === this.favoriteList.length;\n    },\n\n    // 全选/取消全选收藏项\n    watchSelectAllFavorites(val) {\n      this.favoriteList.forEach(item => {\n        item.selected = val;\n      });\n      this.selectedFavorites = val ? [...this.favoriteList] : [];\n    },\n\n    // 批量取消收藏\n    batchCancelFavorite() {\n      if (this.selectedFavorites.length === 0) {\n        this.$message.warning('请选择要取消收藏的项');\n        return;\n      }\n\n      this.$confirm('确定要取消选中的收藏吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 实际应用中这里需要调用接口取消收藏\n        const selectedIds = this.selectedFavorites.map(item => item.id);\n        this.favoriteList = this.favoriteList.filter(item => !selectedIds.includes(item.id));\n        this.selectedFavorites = [];\n        this.selectAllFavorites = false;\n        this.$message.success('已取消收藏');\n      }).catch(() => {\n        this.$message.info('已取消操作');\n      });\n    },\n\n    // 取消单个收藏\n    cancelFavorite(item) {\n      this.$confirm('确定要取消收藏\"' + item.title + '\"吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 实际应用中这里需要调用接口取消收藏\n        this.favoriteList = this.favoriteList.filter(i => i.id !== item.id);\n        // 更新选中的收藏项\n        this.selectedFavorites = this.selectedFavorites.filter(i => i.id !== item.id);\n        // 更新总数\n        this.favoritesTotal = this.favoriteList.length;\n        this.$message.success('已取消收藏');\n      }).catch(() => {\n        this.$message.info('已取消操作');\n      });\n    },\n\n    // 显示添加联系人对话框\n    showAddContactDialog() {\n      this.contactForm = {\n        id: null,\n        name: '',\n        phone: '',\n        email: '',\n        location: ''\n      };\n      this.addContactDialogVisible = true;\n    },\n\n    // 提交联系人表单\n    submitContactForm() {\n      this.$refs.contactForm.validate(valid => {\n        if (valid) {\n          if (this.contactForm.id) {\n            // 编辑联系人\n            const index = this.contactList.findIndex(item => item.id === this.contactForm.id);\n            if (index !== -1) {\n              // 更新创建时间\n              this.contactForm.createTime = new Date().toLocaleString('zh-CN', {\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n                hour12: false\n              }).replace(/\\//g, '-');\n              this.contactList.splice(index, 1, this.contactForm);\n              this.$message.success('联系人修改成功');\n            }\n          } else {\n            // 添加联系人\n            const newContact = {\n              ...this.contactForm,\n              id: this.contactList.length > 0 ? Math.max(...this.contactList.map(item => item.id)) + 1 : 1,\n              createTime: new Date().toLocaleString('zh-CN', {\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n                hour12: false\n              }).replace(/\\//g, '-')\n            };\n            this.contactList.push(newContact);\n            this.$message.success('联系人添加成功');\n          }\n          this.addContactDialogVisible = false;\n        }\n      });\n    },\n\n    // 编辑联系人\n    handleEditContact(row) {\n      this.contactForm = JSON.parse(JSON.stringify(row));\n      this.addContactDialogVisible = true;\n    },\n\n    // 删除联系人\n    handleDeleteContact(row) {\n      this.$confirm(`确定要删除联系人\"${row.name}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 实际应用中这里需要调用接口删除联系人\n        this.contactList = this.contactList.filter(item => item.id !== row.id);\n        this.$message.success('联系人删除成功');\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n\n    // 导出收藏\n    exportFavorites() {\n      this.$message.success('开始导出收藏');\n      // 实际应用中这里需要调用接口导出收藏\n    },\n\n    // 处理收藏分页变化\n    handleFavoritePageChange(val) {\n      this.favoritesCurrentPage = val;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n\n    // 处理收藏跳转页面\n    handleFavoriteJumpPage() {\n      if (!this.favoritesJumpPage) {\n        return;\n      }\n\n      const page = parseInt(this.favoritesJumpPage);\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.favoritesTotal / this.favoritesPageSize)) {\n        this.$message.warning('请输入有效的页码');\n        return;\n      }\n\n      this.favoritesCurrentPage = page;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.page-layout {\n  display: flex;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.left-sidebar {\n  width: 200px;\n  background-color: #fff;\n  border-right: 1px solid #e6e6e6;\n}\n\n.user-info {\n  padding: 20px;\n  text-align: center;\n  border-bottom: 1px solid #e6e6e6;\n\n  .avatar {\n    width: 60px;\n    height: 60px;\n    margin: 0 auto 10px;\n    border-radius: 50%;\n    overflow: hidden;\n\n    img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n  }\n\n  .user-id {\n    font-size: 16px;\n    font-weight: bold;\n    margin-bottom: 5px;\n  }\n\n  .register-date {\n    font-size: 12px;\n    color: #999;\n  }\n}\n\n.sidebar-menu {\n  .sidebar-menu-list {\n    border-right: none;\n  }\n\n  .el-menu-item {\n    height: 50px;\n    line-height: 50px;\n\n    i {\n      margin-right: 5px;\n      color: #666;\n    }\n  }\n\n  .el-menu-item.is-active {\n    background-color: #f0f9eb;\n    color: #67c23a;\n\n    i {\n      color: #67c23a;\n    }\n  }\n}\n\n.content {\n  flex: 1;\n  padding: 20px;\n}\n\n.account-container {\n  background-color: #fff;\n}\n\n.account-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 20px;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #eee;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.account-info {\n  .info-item {\n    display: flex;\n    margin-bottom: 20px;\n    line-height: 24px;\n\n    .label {\n      width: 120px;\n      color: #666;\n      text-align: right;\n      padding-right: 10px;\n    }\n\n    .value {\n      flex: 1;\n      color: #333;\n    }\n  }\n}\n\n/* 系统设置样式 */\n.settings-container {\n  background-color: #fff;\n}\n\n.settings-header {\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.settings-content {\n  display: flex;\n}\n\n.settings-sidebar {\n  width: 120px;\n  border-right: 1px solid #eee;\n  padding-right: 10px;\n}\n\n.settings-menu {\n  .menu-item {\n    padding: 10px 0;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    color: #666;\n    font-size: 14px;\n\n    i {\n      margin-right: 8px;\n      color: #1890ff;\n    }\n\n    &.active {\n      color: #1890ff;\n      font-weight: bold;\n    }\n\n    &:hover {\n      color: #1890ff;\n    }\n  }\n}\n\n.settings-main {\n  flex: 1;\n  padding-left: 20px;\n}\n\n.settings-section {\n  .section-title {\n    font-weight: bold;\n    margin: 15px 0 10px;\n    color: #333;\n    font-size: 14px;\n  }\n}\n\n.options-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 15px;\n\n  .el-checkbox {\n    margin-right: 15px;\n    margin-bottom: 10px;\n    min-width: 80px;\n  }\n}\n\n.download-settings {\n  margin-bottom: 15px;\n\n  .setting-item {\n    display: flex;\n    margin-bottom: 10px;\n    font-size: 14px;\n\n    .label {\n      width: 80px;\n      color: #666;\n    }\n\n    .value {\n      flex: 1;\n      color: #333;\n    }\n  }\n}\n\n.save-button {\n  margin-top: 20px;\n\n  .el-button {\n    background-color: #1890ff;\n    border-color: #1890ff;\n    padding: 8px 20px;\n  }\n}\n\n/* 我的下载样式 */\n.download-container {\n  background-color: #fff;\n}\n\n.download-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n\n  .actions {\n    .el-button {\n      margin-left: 10px;\n    }\n  }\n}\n\n.download-content {\n  .el-table {\n    margin-bottom: 20px;\n  }\n\n  .download-status {\n    color: #67c23a;\n  }\n}\n\n.pagination-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 20px;\n\n  span {\n    margin: 0 5px;\n  }\n\n  .jump-page-input {\n    width: 50px;\n    margin: 0 5px;\n  }\n}\n\n/* 修改密码表单样式 */\n.password-form {\n  max-width: 500px;\n  margin: 20px 0;\n\n  .form-group {\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n\n    .form-label {\n      width: 100px;\n      text-align: right;\n      margin-right: 15px;\n      color: #606266;\n    }\n\n    .el-input {\n      width: 300px;\n    }\n  }\n\n  .form-actions {\n    margin-top: 30px;\n    padding-left: 115px;\n\n    .el-button {\n      width: 100px;\n    }\n  }\n}\n\n/* 我的收藏样式 */\n.favorite-container {\n  background-color: #fff;\n}\n\n.favorite-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n  border-bottom: 1px solid #eee;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.favorite-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .toolbar-left {\n    display: flex;\n    align-items: center;\n\n    .el-checkbox {\n      margin-right: 15px;\n    }\n\n    .el-button {\n      margin-right: 10px;\n    }\n  }\n\n  .toolbar-right {\n    display: flex;\n    align-items: center;\n\n    .date-filter {\n      display: flex;\n      align-items: center;\n      margin-right: 15px;\n\n      .date-separator {\n        margin: 0 5px;\n      }\n    }\n\n    .search-box {\n      margin-right: 15px;\n    }\n  }\n}\n\n.favorite-content {\n  margin-top: 20px;\n\n  .favorite-list {\n    border: 1px solid #ebeef5;\n    border-radius: 4px;\n\n    .favorite-item {\n      display: flex;\n      padding: 15px;\n      border-bottom: 1px solid #ebeef5;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .item-checkbox {\n        margin-right: 15px;\n        display: flex;\n        align-items: center;\n      }\n\n      .item-content {\n        flex: 1;\n\n        .item-title {\n          margin-bottom: 8px;\n\n          .title-tag {\n            display: inline-block;\n            padding: 2px 6px;\n            font-size: 12px;\n            color: #fff;\n            background-color: #f56c6c;\n            border-radius: 2px;\n            margin-right: 8px;\n          }\n\n          .title-text {\n            font-size: 16px;\n            font-weight: 500;\n            color: #303133;\n          }\n        }\n\n        .item-info {\n          font-size: 13px;\n          color: #909399;\n\n          .info-time {\n            margin-right: 15px;\n          }\n        }\n      }\n\n      .item-actions {\n        display: flex;\n        align-items: center;\n        margin-left: 15px;\n\n        .cancel-favorite-btn {\n          color: #f56c6c;\n\n          &:hover {\n            color: #ff7c7c;\n          }\n        }\n      }\n    }\n  }\n\n  .favorite-pagination {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 20px;\n    padding: 10px 0;\n\n    .pagination-info {\n      font-size: 14px;\n      color: #606266;\n\n      span {\n        font-weight: bold;\n        color: #303133;\n      }\n    }\n\n    .pagination-controls {\n      flex: 1;\n      text-align: center;\n    }\n\n    .pagination-jump {\n      display: flex;\n      align-items: center;\n\n      span {\n        margin: 0 5px;\n        font-size: 14px;\n        color: #606266;\n      }\n\n      .jump-page-input {\n        width: 50px;\n        margin: 0 5px;\n      }\n\n      .el-button {\n        margin-left: 5px;\n      }\n    }\n  }\n}\n\n/* 我的联系人样式 */\n.contact-container {\n  background-color: #fff;\n  padding: 20px;\n  border-radius: 4px;\n}\n\n.contact-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n\n  .contact-tabs {\n    .el-radio-group {\n      .el-radio-button {\n        margin-right: -1px;\n\n        &:first-child .el-radio-button__inner {\n          border-radius: 4px 0 0 4px;\n        }\n\n        &:last-child .el-radio-button__inner {\n          border-radius: 0 4px 4px 0;\n        }\n\n        .el-radio-button__inner {\n          padding: 8px 15px;\n          font-size: 13px;\n        }\n      }\n    }\n  }\n}\n\n.contact-toolbar {\n  margin-bottom: 20px;\n}\n\n.contact-content {\n  .el-table {\n    margin-bottom: 20px;\n  }\n\n  .delete-btn {\n    color: #f56c6c;\n  }\n}\n\n/* 素材库样式 */\n.material-container {\n  background-color: #fff;\n}\n\n.material-header {\n  padding-bottom: 15px;\n  margin-bottom: 15px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.material-content {\n  display: flex;\n  flex-wrap: wrap;\n\n  .material-add-box {\n    width: 200px;\n    height: 150px;\n    border: 1px dashed #d9d9d9;\n    border-radius: 4px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    margin-right: 20px;\n    margin-bottom: 20px;\n\n    &:hover {\n      border-color: #1890ff;\n\n      .add-icon, .add-text {\n        color: #1890ff;\n      }\n    }\n\n    .add-icon {\n      font-size: 30px;\n      color: #8c8c8c;\n      margin-bottom: 10px;\n    }\n\n    .add-text {\n      font-size: 14px;\n      color: #8c8c8c;\n    }\n  }\n}\n\n.material-dialog {\n  .el-dialog__header {\n    padding: 15px;\n    text-align: center;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  .material-form {\n    padding: 20px 0;\n\n    .form-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 15px;\n\n      .form-label {\n        width: 80px;\n        text-align: right;\n        margin-right: 10px;\n      }\n    }\n  }\n\n  .dialog-footer {\n    text-align: center;\n\n    .el-button {\n      width: 80px;\n    }\n  }\n}\n\n/* 更新说明样式 */\n.updates-section {\n  .updates-header {\n    margin-bottom: 20px;\n\n    .version-title {\n      font-size: 16px;\n      font-weight: bold;\n      margin-bottom: 5px;\n    }\n\n    .version-date {\n      font-size: 12px;\n      color: #999;\n    }\n  }\n\n  .updates-content {\n    display: flex;\n\n    .version-list {\n      width: 100px;\n      border-right: 1px solid #eee;\n      padding-right: 15px;\n      margin-right: 20px;\n\n      .version-item {\n        padding: 8px 0;\n        cursor: pointer;\n        color: #666;\n        font-size: 14px;\n        text-align: center;\n        border-radius: 4px;\n        margin-bottom: 5px;\n\n        &:hover {\n          background-color: #f5f7fa;\n        }\n\n        &.active {\n          background-color: #e6f7ff;\n          color: #1890ff;\n          font-weight: bold;\n        }\n      }\n    }\n\n    .update-details {\n      flex: 1;\n\n      .update-notes {\n        .note-item {\n          display: flex;\n          margin-bottom: 10px;\n          line-height: 1.6;\n\n          .note-number {\n            margin-right: 5px;\n            color: #666;\n          }\n\n          .note-content {\n            flex: 1;\n\n            b {\n              color: #1890ff;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAimBA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;IACAC,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAAC,uBAAA,CAAAD,GAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACA;MACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,YAAA;QACAC,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;QACAC,aAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA;MACAC,QAAA;QACAC,IAAA;QACAC,MAAA;QACAC,cAAA;QACAC,MAAA;QACAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,QAAA;QACAC,YAAA;QACAC,YAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA;MACAC,YAAA,GACA;QACAC,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,EACA;MACA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,QAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA,GACA;QAAAC,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,EACA;MACA;MACAC,eAAA;QACAF,OAAA;QACAC,IAAA;QACAE,KAAA,GACA,2CACA,2CACA,sCACA,2CACA;MAEA;MACA;MACAC,wBAAA;MACAC,eAAA;MACAC,YAAA;MACA;MACAC,kBAAA;MACAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACA;MACAxD,kBAAA;MACAyD,iBAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,qBAAA;MACAC,YAAA,GACA;QACA1B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,EACA;MACAC,cAAA;MACAC,oBAAA;MACAC,iBAAA;MACAC,iBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,WAAA,GACA;QACArC,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;QACAtC,UAAA;MACA,GACA;QACAF,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;QACAtC,UAAA;MACA,GACA;QACAF,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;QACAtC,UAAA;MACA,EACA;MACAuC,uBAAA;MACAC,WAAA;QACA1C,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACAG,gBAAA;QACAlF,IAAA,GACA;UAAAmF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,KAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,KAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,IAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAlF,cAAA,GAAAkF,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAnC,kBAAA;IACA;IACA;IACAoC,gBAAA,WAAAA,iBAAA;MACA,KAAApC,kBAAA;MACA,KAAAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;MACA;IACA;IACA;IACAiC,cAAA,WAAAA,eAAA;MACA;MACA,UAAApC,YAAA,CAAAC,WAAA;QACA,KAAAoC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,UAAAtC,YAAA,CAAAE,WAAA;QACA,KAAAmC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,UAAAtC,YAAA,CAAAG,eAAA;QACA,KAAAkC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,SAAAtC,YAAA,CAAAE,WAAA,UAAAF,YAAA,CAAAG,eAAA;QACA,KAAAkC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAD,QAAA,CAAAE,OAAA;MACA,KAAAxC,kBAAA;IACA;IACA;IACAyC,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAzF,kBAAA,GAAAyF,GAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAL,QAAA,CAAAE,OAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtD,iBAAA,GAAAsD,SAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,SAAAvD,iBAAA,CAAAwD,MAAA;QACA,KAAAT,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAD,QAAA,CAAAE,OAAA,kCAAAQ,MAAA,MAAAzD,iBAAA,CAAAwD,MAAA;IACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,SAAA3D,iBAAA,CAAAwD,MAAA;QACA,KAAAT,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAY,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACAJ,KAAA,CAAAZ,QAAA,CAAAE,OAAA,sBAAAQ,MAAA,CAAAE,KAAA,CAAA3D,iBAAA,CAAAwD,MAAA;QACA;MACA,GAAAQ,KAAA;QACAL,KAAA,CAAAZ,QAAA,CAAAkB,IAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAApB,QAAA,CAAAE,OAAA,8BAAAQ,MAAA,CAAAU,GAAA,CAAAlH,IAAA;IACA;IACA;IACAmH,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,KAAAT,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACAM,MAAA,CAAAtB,QAAA,CAAAE,OAAA,wBAAAQ,MAAA,CAAAU,GAAA,CAAAlH,IAAA;QACA;MACA,GAAA+G,KAAA;QACAK,MAAA,CAAAtB,QAAA,CAAAkB,IAAA;MACA;IACA;IACA;IACAK,mBAAA,WAAAA,oBAAAhH,GAAA;MACA,KAAAuC,WAAA,GAAAvC,GAAA;MACA;IACA;IACA;IACAiH,cAAA,WAAAA,eAAA;MACA,UAAAxE,QAAA;QACA;MACA;MACA,IAAAyE,IAAA,GAAAC,QAAA,MAAA1E,QAAA;MACA,IAAA2E,KAAA,CAAAF,IAAA,KAAAA,IAAA,QAAAA,IAAA,GAAAG,IAAA,CAAAC,IAAA,MAAAhF,KAAA,QAAAE,QAAA;QACA,KAAAiD,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAnD,WAAA,GAAA2E,IAAA;MACA;IACA;IACA;IACAK,aAAA,WAAAA,cAAA3E,OAAA;MACA,IAAA4E,WAAA,QAAA7E,WAAA,CAAA8E,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA9E,OAAA,KAAAA,OAAA;MAAA;MACA,IAAA4E,WAAA;QACA;QACA,IAAAzE,KAAA;QACA,IAAAH,OAAA;UACAG,KAAA,IACA,2CACA,2CACA,sCACA,2CACA,uCACA;QACA,WAAAH,OAAA;UACAG,KAAA,IACA,wCACA,uCACA,kCACA;QACA;UACAA,KAAA,IACA,2BACA,2BACA;QACA;QAEA,KAAAD,eAAA;UACAF,OAAA,EAAA4E,WAAA,CAAA5E,OAAA;UACAC,IAAA,EAAA2E,WAAA,CAAA3E,IAAA;UACAE,KAAA,EAAAA;QACA;MACA;IACA;IACA;IACA4E,qBAAA,WAAAA,sBAAA;MACA,KAAA1E,eAAA;MACA,KAAAD,wBAAA;IACA;IACA;IACA4E,WAAA,WAAAA,YAAA;MACA,UAAA3E,eAAA,CAAA4E,IAAA;QACA,KAAApC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAxC,YAAA,CAAA4E,IAAA;QACA5F,EAAA,EAAA6F,IAAA,CAAAC,GAAA;QACArI,IAAA,OAAAsD,eAAA;QACAb,UAAA,MAAA2F,IAAA,GAAAE,cAAA;MACA;MAEA,KAAAxC,QAAA,CAAAE,OAAA,kBAAAQ,MAAA,MAAAlD,eAAA;MACA,KAAAD,wBAAA;IACA;IAEA;IACAkF,oBAAA,WAAAA,qBAAA;MACA,KAAA1E,iBAAA,QAAAI,YAAA,CAAAuE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvE,QAAA;MAAA;MACA;MACA,KAAA9D,kBAAA,QAAAyD,iBAAA,CAAA0C,MAAA,UAAAtC,YAAA,CAAAsC,MAAA;IACA;IAEA;IACAjG,uBAAA,WAAAA,wBAAAD,GAAA;MACA,KAAA4D,YAAA,CAAAyE,OAAA,WAAAD,IAAA;QACAA,IAAA,CAAAvE,QAAA,GAAA7D,GAAA;MACA;MACA,KAAAwD,iBAAA,GAAAxD,GAAA,OAAAsI,mBAAA,CAAAC,OAAA,OAAA3E,YAAA;IACA;IAEA;IACA4E,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjF,iBAAA,CAAA0C,MAAA;QACA,KAAAT,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAY,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACA;QACA,IAAAiC,WAAA,GAAAD,MAAA,CAAAjF,iBAAA,CAAAmF,GAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAlG,EAAA;QAAA;QACAuG,MAAA,CAAA7E,YAAA,GAAA6E,MAAA,CAAA7E,YAAA,CAAAuE,MAAA,WAAAC,IAAA;UAAA,QAAAM,WAAA,CAAAE,QAAA,CAAAR,IAAA,CAAAlG,EAAA;QAAA;QACAuG,MAAA,CAAAjF,iBAAA;QACAiF,MAAA,CAAA1I,kBAAA;QACA0I,MAAA,CAAAhD,QAAA,CAAAE,OAAA;MACA,GAAAe,KAAA;QACA+B,MAAA,CAAAhD,QAAA,CAAAkB,IAAA;MACA;IACA;IAEA;IACAkC,cAAA,WAAAA,eAAAT,IAAA;MAAA,IAAAU,MAAA;MACA,KAAAxC,QAAA,cAAA8B,IAAA,CAAArE,KAAA;QACAwC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACA;QACAqC,MAAA,CAAAlF,YAAA,GAAAkF,MAAA,CAAAlF,YAAA,CAAAuE,MAAA,WAAAY,CAAA;UAAA,OAAAA,CAAA,CAAA7G,EAAA,KAAAkG,IAAA,CAAAlG,EAAA;QAAA;QACA;QACA4G,MAAA,CAAAtF,iBAAA,GAAAsF,MAAA,CAAAtF,iBAAA,CAAA2E,MAAA,WAAAY,CAAA;UAAA,OAAAA,CAAA,CAAA7G,EAAA,KAAAkG,IAAA,CAAAlG,EAAA;QAAA;QACA;QACA4G,MAAA,CAAA5E,cAAA,GAAA4E,MAAA,CAAAlF,YAAA,CAAAsC,MAAA;QACA4C,MAAA,CAAArD,QAAA,CAAAE,OAAA;MACA,GAAAe,KAAA;QACAoC,MAAA,CAAArD,QAAA,CAAAkB,IAAA;MACA;IACA;IAEA;IACAqC,oBAAA,WAAAA,qBAAA;MACA,KAAApE,WAAA;QACA1C,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACA,KAAAC,uBAAA;IACA;IAEA;IACAsE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAvE,WAAA,CAAAwE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtE,WAAA,CAAA1C,EAAA;YACA;YACA,IAAAmD,KAAA,GAAA6D,MAAA,CAAA3E,WAAA,CAAA+E,SAAA,WAAAlB,IAAA;cAAA,OAAAA,IAAA,CAAAlG,EAAA,KAAAgH,MAAA,CAAAtE,WAAA,CAAA1C,EAAA;YAAA;YACA,IAAAmD,KAAA;cACA;cACA6D,MAAA,CAAAtE,WAAA,CAAAxC,UAAA,OAAA2F,IAAA,GAAAE,cAAA;gBACAsB,IAAA;gBACAC,KAAA;gBACAC,GAAA;gBACAC,IAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;cACA,GAAAC,OAAA;cACAZ,MAAA,CAAA3E,WAAA,CAAAwF,MAAA,CAAA1E,KAAA,KAAA6D,MAAA,CAAAtE,WAAA;cACAsE,MAAA,CAAAzD,QAAA,CAAAE,OAAA;YACA;UACA;YACA;YACA,IAAAqE,UAAA,OAAAC,cAAA,CAAA1B,OAAA,MAAA0B,cAAA,CAAA1B,OAAA,MACAW,MAAA,CAAAtE,WAAA;cACA1C,EAAA,EAAAgH,MAAA,CAAA3E,WAAA,CAAA2B,MAAA,OAAAmB,IAAA,CAAA6C,GAAA,CAAAC,KAAA,CAAA9C,IAAA,MAAAiB,mBAAA,CAAAC,OAAA,EAAAW,MAAA,CAAA3E,WAAA,CAAAoE,GAAA,WAAAP,IAAA;gBAAA,OAAAA,IAAA,CAAAlG,EAAA;cAAA;cACAE,UAAA,MAAA2F,IAAA,GAAAE,cAAA;gBACAsB,IAAA;gBACAC,KAAA;gBACAC,GAAA;gBACAC,IAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;cACA,GAAAC,OAAA;YAAA,EACA;YACAZ,MAAA,CAAA3E,WAAA,CAAAuD,IAAA,CAAAkC,UAAA;YACAd,MAAA,CAAAzD,QAAA,CAAAE,OAAA;UACA;UACAuD,MAAA,CAAAvE,uBAAA;QACA;MACA;IACA;IAEA;IACAyF,iBAAA,WAAAA,kBAAAvD,GAAA;MACA,KAAAjC,WAAA,GAAAyF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA1D,GAAA;MACA,KAAAlC,uBAAA;IACA;IAEA;IACA6F,mBAAA,WAAAA,oBAAA3D,GAAA;MAAA,IAAA4D,MAAA;MACA,KAAAnE,QAAA,sDAAAH,MAAA,CAAAU,GAAA,CAAAlH,IAAA;QACA4G,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACA;QACAgE,MAAA,CAAAlG,WAAA,GAAAkG,MAAA,CAAAlG,WAAA,CAAA4D,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAlG,EAAA,KAAA2E,GAAA,CAAA3E,EAAA;QAAA;QACAuI,MAAA,CAAAhF,QAAA,CAAAE,OAAA;MACA,GAAAe,KAAA;QACA+D,MAAA,CAAAhF,QAAA,CAAAkB,IAAA;MACA;IACA;IAEA;IACA+D,eAAA,WAAAA,gBAAA;MACA,KAAAjF,QAAA,CAAAE,OAAA;MACA;IACA;IAEA;IACAgF,wBAAA,WAAAA,yBAAA3K,GAAA;MACA,KAAAmE,oBAAA,GAAAnE,GAAA;MACA;IACA;IAEA;IACA4K,sBAAA,WAAAA,uBAAA;MACA,UAAAvG,iBAAA;QACA;MACA;MAEA,IAAA6C,IAAA,GAAAC,QAAA,MAAA9C,iBAAA;MACA,IAAA+C,KAAA,CAAAF,IAAA,KAAAA,IAAA,QAAAA,IAAA,GAAAG,IAAA,CAAAC,IAAA,MAAApD,cAAA,QAAAE,iBAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAvB,oBAAA,GAAA+C,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}