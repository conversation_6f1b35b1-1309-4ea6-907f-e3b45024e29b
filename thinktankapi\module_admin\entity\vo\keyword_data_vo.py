from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import Optional, List
from module_admin.annotation.pydantic_annotation import as_query


class KeywordDataModel(BaseModel):
    """
    关键词数据表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    title: Optional[str] = Field(default=None, description='标题')
    content: Optional[str] = Field(default=None, description='内容')
    url: Optional[str] = Field(default=None, description='链接')
    keyword: Optional[str] = Field(default=None, description='关键词')
    type: Optional[str] = Field(default=None, description='类型')
    createtime: Optional[datetime] = Field(default=None, description='创建时间')
    web: Optional[str] = Field(default=None, description='网页')


@as_query
class KeywordDataPageQueryModel(BaseModel):
    """
    关键词数据分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页显示数量')
    title: Optional[str] = Field(default=None, description='标题')
    keyword: Optional[str] = Field(default=None, description='关键词')
    type: Optional[str] = Field(default=None, description='类型')
    web: Optional[str] = Field(default=None, description='网页')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


class KeywordDataResponseModel(BaseModel):
    """
    关键词数据响应模型（用于前端显示）
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    title: Optional[str] = Field(default=None, description='标题')
    content: Optional[str] = Field(default=None, description='内容')
    source_url: Optional[str] = Field(default=None, description='原始链接')
    keywords: Optional[str] = Field(default=None, description='关键词')
    source: Optional[str] = Field(default=None, description='来源标签')
    time: Optional[str] = Field(default=None, description='发布时间')
    platform_name: Optional[str] = Field(default=None, description='平台名称')
    selected: bool = Field(default=False, description='是否选中')
    sentiment: str = Field(default='neutral', description='情感倾向')
    views: int = Field(default=0, description='浏览量')
    comments: int = Field(default=0, description='评论数')
    images: List[str] = Field(default_factory=list, description='图片列表')


class DeleteKeywordDataModel(BaseModel):
    """
    删除关键词数据模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    keyword_data_ids: str = Field(description='需要删除的关键词数据ID')


class KeywordDataStatisticsModel(BaseModel):
    """
    关键词数据统计模型
    """

    model_config = ConfigDict(from_attributes=True)

    type: str = Field(description='类型')
    total_count: int = Field(description='总数量')
    today_count: int = Field(description='今日数量')
