{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\selectUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\selectUser.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["selectUser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "selectUser.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\n  <!-- 授权用户 -->\n  <el-dialog title=\"选择用户\" :visible.sync=\"visible\" width=\"800px\" top=\"5vh\" append-to-body>\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n        <el-input\n          v-model=\"queryParams.phonenumber\"\n          placeholder=\"请输入手机号码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n    <el-row>\n      <el-table @row-click=\"clickRow\" ref=\"table\" :data=\"userList\" @selection-change=\"handleSelectionChange\" height=\"260px\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n          <template slot-scope=\"scope\">\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <span>{{ parseTime(scope.row.createTime) }}</span>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-row>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button type=\"primary\" @click=\"handleSelectUser\">确 定</el-button>\n      <el-button @click=\"visible = false\">取 消</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { unallocatedUserList, authUserSelectAll } from \"@/api/system/role\";\nexport default {\n  dicts: ['sys_normal_disable'],\n  props: {\n    // 角色编号\n    roleId: {\n      type: [Number, String]\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      visible: false,\n      // 选中数组值\n      userIds: [],\n      // 总条数\n      total: 0,\n      // 未授权用户数据\n      userList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleId: undefined,\n        userName: undefined,\n        phonenumber: undefined\n      }\n    };\n  },\n  methods: {\n    // 显示弹框\n    show() {\n      this.queryParams.roleId = this.roleId;\n      this.getList();\n      this.visible = true;\n    },\n    clickRow(row) {\n      this.$refs.table.toggleRowSelection(row);\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userIds = selection.map(item => item.userId);\n    },\n    // 查询表数据\n    getList() {\n      unallocatedUserList(this.queryParams).then(res => {\n        this.userList = res.rows;\n        this.total = res.total;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 选择授权用户操作 */\n    handleSelectUser() {\n      const roleId = this.queryParams.roleId;\n      const userIds = this.userIds.join(\",\");\n      if (userIds == \"\") {\n        this.$modal.msgError(\"请选择要分配的用户\");\n        return;\n      }\n      authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {\n        this.$modal.msgSuccess(res.msg);\n        this.visible = false;\n        this.$emit(\"ok\");\n      });\n    }\n  }\n};\n</script>\n"]}]}