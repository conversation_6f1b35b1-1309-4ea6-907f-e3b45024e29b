{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\dept\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\dept\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REZXB0LCBnZXREZXB0LCBkZWxEZXB0LCBhZGREZXB0LCB1cGRhdGVEZXB0LCBsaXN0RGVwdEV4Y2x1ZGVDaGlsZCB9IGZyb20gIkAvYXBpL3N5c3RlbS9kZXB0IjsKaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiOwppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRGVwdCIsCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJ10sCiAgY29tcG9uZW50czogeyBUcmVlc2VsZWN0IH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g6KGo5qC85qCR5pWw5o2uCiAgICAgIGRlcHRMaXN0OiBbXSwKICAgICAgLy8g6YOo6Zeo5qCR6YCJ6aG5CiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKblsZXlvIDvvIzpu5jorqTlhajpg6jlsZXlvIAKICAgICAgaXNFeHBhbmRBbGw6IHRydWUsCiAgICAgIC8vIOmHjeaWsOa4suafk+ihqOagvOeKtuaAgQogICAgICByZWZyZXNoVGFibGU6IHRydWUsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBwYXJlbnRJZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4iue6p+mDqOmXqOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkZXB0TmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumDqOmXqOWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBvcmRlck51bTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaYvuekuuaOkuW6j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBlbWFpbDogWwogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAiZW1haWwiLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgICAgdHJpZ2dlcjogWyJibHVyIiwgImNoYW5nZSJdCiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBwaG9uZTogWwogICAgICAgICAgewogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLAogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6YOo6Zeo5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0RGVwdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcHRMaXN0ID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0SWQiKTsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOi9rOaNoumDqOmXqOaVsOaNrue7k+aehCAqLwogICAgbm9ybWFsaXplcihub2RlKSB7CiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgewogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOwogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgaWQ6IG5vZGUuZGVwdElkLAogICAgICAgIGxhYmVsOiBub2RlLmRlcHROYW1lLAogICAgICAgIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVuCiAgICAgIH07CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudElkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdE5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBvcmRlck51bTogdW5kZWZpbmVkLAogICAgICAgIGxlYWRlcjogdW5kZWZpbmVkLAogICAgICAgIHBob25lOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6ICIwIgogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgaWYgKHJvdyAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSByb3cuZGVwdElkOwogICAgICB9CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6YOo6ZeoIjsKICAgICAgbGlzdERlcHQoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0SWQiKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWxleW8gC/mipjlj6Dmk43kvZwgKi8KICAgIHRvZ2dsZUV4cGFuZEFsbCgpIHsKICAgICAgdGhpcy5yZWZyZXNoVGFibGUgPSBmYWxzZTsKICAgICAgdGhpcy5pc0V4cGFuZEFsbCA9ICF0aGlzLmlzRXhwYW5kQWxsOwogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy5yZWZyZXNoVGFibGUgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgZ2V0RGVwdChyb3cuZGVwdElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnpg6jpl6giOwogICAgICAgIGxpc3REZXB0RXhjbHVkZUNoaWxkKHJvdy5kZXB0SWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiZGVwdElkIik7CiAgICAgICAgICBpZiAodGhpcy5kZXB0T3B0aW9ucy5sZW5ndGggPT0gMCkgewogICAgICAgICAgICBjb25zdCBub1Jlc3VsdHNPcHRpb25zID0geyBkZXB0SWQ6IHRoaXMuZm9ybS5wYXJlbnRJZCwgZGVwdE5hbWU6IHRoaXMuZm9ybS5wYXJlbnROYW1lLCBjaGlsZHJlbjogW10gfTsKICAgICAgICAgICAgdGhpcy5kZXB0T3B0aW9ucy5wdXNoKG5vUmVzdWx0c09wdGlvbnMpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbigpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uZGVwdElkICE9IHVuZGVmaW5lZCkgewogICAgICAgICAgICB1cGRhdGVEZXB0KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkRGVwdCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZCN56ew5Li6IicgKyByb3cuZGVwdE5hbWUgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbERlcHQocm93LmRlcHRJZCk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/dept", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"部门名称\" prop=\"deptName\">\n        <el-input\n          v-model=\"queryParams.deptName\"\n          placeholder=\"请输入部门名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"部门状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:dept:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-sort\"\n          size=\"mini\"\n          @click=\"toggleExpandAll\"\n        >展开/折叠</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-if=\"refreshTable\"\n      v-loading=\"loading\"\n      :data=\"deptList\"\n      row-key=\"deptId\"\n      :default-expand-all=\"isExpandAll\"\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column prop=\"deptName\" label=\"部门名称\" width=\"260\"></el-table-column>\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"200\"></el-table-column>\n      <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:dept:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd(scope.row)\"\n            v-hasPermi=\"['system:dept:add']\"\n          >新增</el-button>\n          <el-button\n            v-if=\"scope.row.parentId != 0\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:dept:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 添加或修改部门对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\" v-if=\"form.parentId !== 0\">\n            <el-form-item label=\"上级部门\" prop=\"parentId\">\n              <treeselect v-model=\"form.parentId\" :options=\"deptOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级部门\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"部门名称\" prop=\"deptName\">\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入部门名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"负责人\" prop=\"leader\">\n              <el-input v-model=\"form.leader\" placeholder=\"请输入负责人\" maxlength=\"20\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input v-model=\"form.phone\" placeholder=\"请输入联系电话\" maxlength=\"11\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"邮箱\" prop=\"email\">\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"部门状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from \"@/api/system/dept\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\n\nexport default {\n  name: \"Dept\",\n  dicts: ['sys_normal_disable'],\n  components: { Treeselect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 表格树数据\n      deptList: [],\n      // 部门树选项\n      deptOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部展开\n      isExpandAll: true,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        deptName: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        parentId: [\n          { required: true, message: \"上级部门不能为空\", trigger: \"blur\" }\n        ],\n        deptName: [\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\n        ],\n        email: [\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phone: [\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询部门列表 */\n    getList() {\n      this.loading = true;\n      listDept(this.queryParams).then(response => {\n        this.deptList = this.handleTree(response.data, \"deptId\");\n        this.loading = false;\n      });\n    },\n    /** 转换部门数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.deptId,\n        label: node.deptName,\n        children: node.children\n      };\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        deptId: undefined,\n        parentId: undefined,\n        deptName: undefined,\n        orderNum: undefined,\n        leader: undefined,\n        phone: undefined,\n        email: undefined,\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset();\n      if (row != undefined) {\n        this.form.parentId = row.deptId;\n      }\n      this.open = true;\n      this.title = \"添加部门\";\n      listDept().then(response => {\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\n      });\n    },\n    /** 展开/折叠操作 */\n    toggleExpandAll() {\n      this.refreshTable = false;\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        this.refreshTable = true;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      getDept(row.deptId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改部门\";\n        listDeptExcludeChild(row.deptId).then(response => {\n          this.deptOptions = this.handleTree(response.data, \"deptId\");\n          if (this.deptOptions.length == 0) {\n            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] };\n            this.deptOptions.push(noResultsOptions);\n          }\n        });\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.deptId != undefined) {\n            updateDept(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addDept(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      this.$modal.confirm('是否确认删除名称为\"' + row.deptName + '\"的数据项？').then(function() {\n        return delDept(row.deptId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>\n"]}]}