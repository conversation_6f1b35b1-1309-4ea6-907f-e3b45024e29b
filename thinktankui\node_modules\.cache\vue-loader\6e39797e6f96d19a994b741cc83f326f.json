{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\index.vue", "mtime": 1748096409758}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBWaXNlciBmcm9tICd2aXNlci12dWUnOwppbXBvcnQgewogIEF2YXRhciwKICBCdXR0b24sCiAgQ2FyZCwKICBDb2wsCiAgTGlzdCwKICBSb3csCiAgU3RhdGlzdGljLAogIFRhZywKICBEaXZpZGVyLAogIFN3aXRjaCwKICBCYWRnZSwKfSBmcm9tICJhbnQtZGVzaWduLXZ1ZSI7CmltcG9ydCAnYW50LWRlc2lnbi12dWUvZGlzdC9hbnRkLmNzcyc7CmltcG9ydCBWdWUgZnJvbSAidnVlIjsKClZ1ZS51c2UoVmlzZXIpOwpWdWUuY29tcG9uZW50KEF2YXRhci5uYW1lLCBBdmF0YXIpOwpWdWUuY29tcG9uZW50KEJ1dHRvbi5uYW1lLCBCdXR0b24pOwpWdWUuY29tcG9uZW50KENhcmQubmFtZSwgQ2FyZCk7ClZ1ZS5jb21wb25lbnQoQ2FyZC5HcmlkLm5hbWUsIENhcmQuR3JpZCk7ClZ1ZS5jb21wb25lbnQoQ2FyZC5NZXRhLm5hbWUsIENhcmQuTWV0YSk7ClZ1ZS5jb21wb25lbnQoQ29sLm5hbWUsIENvbCk7ClZ1ZS5jb21wb25lbnQoTGlzdC5uYW1lLCBMaXN0KTsKVnVlLmNvbXBvbmVudChMaXN0Lkl0ZW0ubmFtZSwgTGlzdC5JdGVtKTsKVnVlLmNvbXBvbmVudChMaXN0Lkl0ZW0uTWV0YS5uYW1lLCBMaXN0Lkl0ZW0uTWV0YSk7ClZ1ZS5jb21wb25lbnQoUm93Lm5hbWUsIFJvdyk7ClZ1ZS5jb21wb25lbnQoU3RhdGlzdGljLm5hbWUsIFN0YXRpc3RpYyk7ClZ1ZS5jb21wb25lbnQoVGFnLm5hbWUsIFRhZyk7ClZ1ZS5jb21wb25lbnQoRGl2aWRlci5uYW1lLCBEaXZpZGVyKTsKVnVlLmNvbXBvbmVudChTd2l0Y2gubmFtZSwgU3dpdGNoKTsKVnVlLmNvbXBvbmVudChCYWRnZS5uYW1lLCBCYWRnZSk7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRhc2hCb2FyZCIsCiAgbWV0aG9kczogewogICAgZ29Ub1NlYXJjaCgpIHsKICAgICAgLy8g6Lez6L2s5Yiw5pCc57Si57uT5p6c6aG16Z2i77yM5bm25Lyg6YCS5rWL6K+V5pCc57Si5YWz6ZSu6K+NCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAnL3NlYXJjaC1yZXN1bHRzJywKICAgICAgICBxdWVyeTogewogICAgICAgICAgcTogJ+aWueWkqiDljqjnlLUnCiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBvcmlnaW5hbFRvcE5hdjogdW5kZWZpbmVkLCAvLyDlrZjlgqjljp/lp4vnmoR0b3BOYXbnirbmgIEKICAgICAgY3VycmVudFVzZXI6IHsKICAgICAgICBhdmF0YXI6CiAgICAgICAgICAiaHR0cHM6Ly9ndy5hbGlwYXlvYmplY3RzLmNvbS96b3Mvcm1zcG9ydGFsL0JpYXpmYW54bWFtTlJveHhWeGthLnBuZyIsCiAgICAgICAgbmFtZTogIuaWueWkqiIsCiAgICAgICAgdXNlcmlkOiAiMDAwMDAwMDEiLAogICAgICAgIGVtYWlsOiAiZmFuZ3RhQGV4YW1wbGUuY29tIiwKICAgICAgICBzaWduYXR1cmU6ICLoiIbmg4Xnm5HmjqfvvIzlrp7ml7bmiormjqciLAogICAgICAgIHRpdGxlOiAi6IiG5oOF5YiG5p6Q5biIIiwKICAgICAgICBncm91cDogIuiIhuaDheebkeaOp+S4reW/gyIsCiAgICAgIH0sCiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICBob3RUb3BpY3NDb3VudDogMjQsCiAgICAgIG5lZ2F0aXZlQ291bnQ6IDgsCiAgICAgIHRvdGFsTW9uaXRvckNvdW50OiAxNDQ2LAoKICAgICAgLy8gMjTlsI/ml7bkvKDmkq3otovlir/mlbDmja4KICAgICAgdHJlbmREYXRhOiBbCiAgICAgICAgeyB0aW1lOiAiMDA6MDAiLCB2YWx1ZTogMzAwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjAxOjAwIiwgdmFsdWU6IDI1MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIwMjowMCIsIHZhbHVlOiAyMDAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMDM6MDAiLCB2YWx1ZTogMTgwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjA0OjAwIiwgdmFsdWU6IDE1MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIwNTowMCIsIHZhbHVlOiAxNzAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMDY6MDAiLCB2YWx1ZTogMjIwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjA3OjAwIiwgdmFsdWU6IDM1MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIwODowMCIsIHZhbHVlOiA1MDAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMDk6MDAiLCB2YWx1ZTogNjIwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjEwOjAwIiwgdmFsdWU6IDU1MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIxMTowMCIsIHZhbHVlOiA0ODAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMTI6MDAiLCB2YWx1ZTogNDAwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjEzOjAwIiwgdmFsdWU6IDQ1MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIxNDowMCIsIHZhbHVlOiA1MDAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMTU6MDAiLCB2YWx1ZTogNDcwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjE2OjAwIiwgdmFsdWU6IDQ2MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIxNzowMCIsIHZhbHVlOiA1MjAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMTg6MDAiLCB2YWx1ZTogNTgwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjE5OjAwIiwgdmFsdWU6IDU1MCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIyMDowMCIsIHZhbHVlOiA1MDAsIHR5cGU6ICLmgLvph48iIH0sCiAgICAgICAgeyB0aW1lOiAiMjE6MDAiLCB2YWx1ZTogNDUwLCB0eXBlOiAi5oC76YePIiB9LAogICAgICAgIHsgdGltZTogIjIyOjAwIiwgdmFsdWU6IDQwMCwgdHlwZTogIuaAu+mHjyIgfSwKICAgICAgICB7IHRpbWU6ICIyMzowMCIsIHZhbHVlOiAzNTAsIHR5cGU6ICLmgLvph48iIH0sCgogICAgICAgIHsgdGltZTogIjAwOjAwIiwgdmFsdWU6IDEwMCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwMTowMCIsIHZhbHVlOiA4MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwMjowMCIsIHZhbHVlOiA2MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwMzowMCIsIHZhbHVlOiA1MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwNDowMCIsIHZhbHVlOiA0MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwNTowMCIsIHZhbHVlOiA1MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwNjowMCIsIHZhbHVlOiA3MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIwNzowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLlvq7ljZoiIH0sCiAgICAgICAgeyB0aW1lOiAiMDg6MDAiLCB2YWx1ZTogMTUwLCB0eXBlOiAi5b6u5Y2aIiB9LAogICAgICAgIHsgdGltZTogIjA5OjAwIiwgdmFsdWU6IDE4MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIxMDowMCIsIHZhbHVlOiAxNjAsIHR5cGU6ICLlvq7ljZoiIH0sCiAgICAgICAgeyB0aW1lOiAiMTE6MDAiLCB2YWx1ZTogMTQwLCB0eXBlOiAi5b6u5Y2aIiB9LAogICAgICAgIHsgdGltZTogIjEyOjAwIiwgdmFsdWU6IDEyMCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIxMzowMCIsIHZhbHVlOiAxMzAsIHR5cGU6ICLlvq7ljZoiIH0sCiAgICAgICAgeyB0aW1lOiAiMTQ6MDAiLCB2YWx1ZTogMTUwLCB0eXBlOiAi5b6u5Y2aIiB9LAogICAgICAgIHsgdGltZTogIjE1OjAwIiwgdmFsdWU6IDE0MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIxNjowMCIsIHZhbHVlOiAxMzAsIHR5cGU6ICLlvq7ljZoiIH0sCiAgICAgICAgeyB0aW1lOiAiMTc6MDAiLCB2YWx1ZTogMTUwLCB0eXBlOiAi5b6u5Y2aIiB9LAogICAgICAgIHsgdGltZTogIjE4OjAwIiwgdmFsdWU6IDE3MCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIxOTowMCIsIHZhbHVlOiAxNjAsIHR5cGU6ICLlvq7ljZoiIH0sCiAgICAgICAgeyB0aW1lOiAiMjA6MDAiLCB2YWx1ZTogMTUwLCB0eXBlOiAi5b6u5Y2aIiB9LAogICAgICAgIHsgdGltZTogIjIxOjAwIiwgdmFsdWU6IDEzMCwgdHlwZTogIuW+ruWNmiIgfSwKICAgICAgICB7IHRpbWU6ICIyMjowMCIsIHZhbHVlOiAxMjAsIHR5cGU6ICLlvq7ljZoiIH0sCiAgICAgICAgeyB0aW1lOiAiMjM6MDAiLCB2YWx1ZTogMTAwLCB0eXBlOiAi5b6u5Y2aIiB9LAoKICAgICAgICB7IHRpbWU6ICIwMDowMCIsIHZhbHVlOiA4MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwMTowMCIsIHZhbHVlOiA3MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwMjowMCIsIHZhbHVlOiA2MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwMzowMCIsIHZhbHVlOiA1MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwNDowMCIsIHZhbHVlOiA0MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwNTowMCIsIHZhbHVlOiA1MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwNjowMCIsIHZhbHVlOiA2MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwNzowMCIsIHZhbHVlOiA4MCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIwODowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMDk6MDAiLCB2YWx1ZTogMTIwLCB0eXBlOiAi6KeG6aKRIiB9LAogICAgICAgIHsgdGltZTogIjEwOjAwIiwgdmFsdWU6IDExMCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIxMTowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMTI6MDAiLCB2YWx1ZTogOTAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMTM6MDAiLCB2YWx1ZTogMTAwLCB0eXBlOiAi6KeG6aKRIiB9LAogICAgICAgIHsgdGltZTogIjE0OjAwIiwgdmFsdWU6IDExMCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIxNTowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMTY6MDAiLCB2YWx1ZTogOTAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMTc6MDAiLCB2YWx1ZTogMTAwLCB0eXBlOiAi6KeG6aKRIiB9LAogICAgICAgIHsgdGltZTogIjE4OjAwIiwgdmFsdWU6IDExMCwgdHlwZTogIuinhumikSIgfSwKICAgICAgICB7IHRpbWU6ICIxOTowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMjA6MDAiLCB2YWx1ZTogOTAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMjE6MDAiLCB2YWx1ZTogODAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMjI6MDAiLCB2YWx1ZTogNzAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgICAgeyB0aW1lOiAiMjM6MDAiLCB2YWx1ZTogNjAsIHR5cGU6ICLop4bpopEiIH0sCiAgICAgIF0sCiAgICAgIHRyZW5kU2NhbGU6IFsKICAgICAgICB7CiAgICAgICAgICBkYXRhS2V5OiAndmFsdWUnLAogICAgICAgICAgbWluOiAwLAogICAgICAgIH0sCiAgICAgIF0sCgogICAgICAvLyDlubPlj7DmnaXmupDljaDmr5TmlbDmja4KICAgICAgcGxhdGZvcm1EYXRhOiBbCiAgICAgICAgeyB0eXBlOiAn5b6u5Y2aJywgcGVyY2VudDogMzEuMiB9LAogICAgICAgIHsgdHlwZTogJ+inhumikScsIHBlcmNlbnQ6IDE3LjkgfSwKICAgICAgICB7IHR5cGU6ICflpLTmnaHlj7cnLCBwZXJjZW50OiAxNS4zIH0sCiAgICAgICAgeyB0eXBlOiAnQVBQJywgcGVyY2VudDogMTIuNyB9LAogICAgICAgIHsgdHlwZTogJ+W+ruS/oScsIHBlcmNlbnQ6IDkuOCB9LAogICAgICAgIHsgdHlwZTogJ+WFtuS7licsIHBlcmNlbnQ6IDEzLjEgfSwKICAgICAgXSwKICAgICAgcGxhdGZvcm1TY2FsZTogWwogICAgICAgIHsKICAgICAgICAgIGRhdGFLZXk6ICdwZXJjZW50JywKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIGZvcm1hdHRlcjogJy4wJScsCiAgICAgICAgfSwKICAgICAgXSwKICAgICAgbGFiZWxDb25maWc6IHsKICAgICAgICBvZmZzZXQ6IC0yMCwKICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgIGZpbGw6ICcjMDAwJywKICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJywKICAgICAgICB9LAogICAgICAgIGZvcm1hdHRlcjogKHRleHQsIGl0ZW0pID0+IHsKICAgICAgICAgIHJldHVybiBgJHtpdGVtLnBvaW50LnR5cGV9OiAke2l0ZW0ucG9pbnQucGVyY2VudH0lYDsKICAgICAgICB9LAogICAgICB9LAoKICAgICAgLy8g5oOF5oSf5YiG5biD5Y2g5q+U5pWw5o2uCiAgICAgIHNlbnRpbWVudERhdGE6IFsKICAgICAgICB7IHRpbWU6ICcwMDowMCcsIHZhbHVlOiAzMDAsIHR5cGU6ICfkuK3mgKcnIH0sCiAgICAgICAgeyB0aW1lOiAnMDI6MDAnLCB2YWx1ZTogMjgwLCB0eXBlOiAn5Lit5oCnJyB9LAogICAgICAgIHsgdGltZTogJzA0OjAwJywgdmFsdWU6IDI1MCwgdHlwZTogJ+S4reaApycgfSwKICAgICAgICB7IHRpbWU6ICcwNjowMCcsIHZhbHVlOiAyNjAsIHR5cGU6ICfkuK3mgKcnIH0sCiAgICAgICAgeyB0aW1lOiAnMDg6MDAnLCB2YWx1ZTogMjgwLCB0eXBlOiAn5Lit5oCnJyB9LAogICAgICAgIHsgdGltZTogJzEwOjAwJywgdmFsdWU6IDMwMCwgdHlwZTogJ+S4reaApycgfSwKICAgICAgICB7IHRpbWU6ICcxMjowMCcsIHZhbHVlOiAyODAsIHR5cGU6ICfkuK3mgKcnIH0sCiAgICAgICAgeyB0aW1lOiAnMTQ6MDAnLCB2YWx1ZTogMjUwLCB0eXBlOiAn5Lit5oCnJyB9LAogICAgICAgIHsgdGltZTogJzE2OjAwJywgdmFsdWU6IDI2MCwgdHlwZTogJ+S4reaApycgfSwKICAgICAgICB7IHRpbWU6ICcxODowMCcsIHZhbHVlOiAyODAsIHR5cGU6ICfkuK3mgKcnIH0sCiAgICAgICAgeyB0aW1lOiAnMjA6MDAnLCB2YWx1ZTogMzAwLCB0eXBlOiAn5Lit5oCnJyB9LAogICAgICAgIHsgdGltZTogJzIyOjAwJywgdmFsdWU6IDI4MCwgdHlwZTogJ+S4reaApycgfSwKCiAgICAgICAgeyB0aW1lOiAnMDA6MDAnLCB2YWx1ZTogMTAwLCB0eXBlOiAn5q2j6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzAyOjAwJywgdmFsdWU6IDEyMCwgdHlwZTogJ+ato+mdoicgfSwKICAgICAgICB7IHRpbWU6ICcwNDowMCcsIHZhbHVlOiAxNDAsIHR5cGU6ICfmraPpnaInIH0sCiAgICAgICAgeyB0aW1lOiAnMDY6MDAnLCB2YWx1ZTogMTMwLCB0eXBlOiAn5q2j6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzA4OjAwJywgdmFsdWU6IDEyMCwgdHlwZTogJ+ato+mdoicgfSwKICAgICAgICB7IHRpbWU6ICcxMDowMCcsIHZhbHVlOiAxMDAsIHR5cGU6ICfmraPpnaInIH0sCiAgICAgICAgeyB0aW1lOiAnMTI6MDAnLCB2YWx1ZTogMTIwLCB0eXBlOiAn5q2j6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzE0OjAwJywgdmFsdWU6IDE0MCwgdHlwZTogJ+ato+mdoicgfSwKICAgICAgICB7IHRpbWU6ICcxNjowMCcsIHZhbHVlOiAxMzAsIHR5cGU6ICfmraPpnaInIH0sCiAgICAgICAgeyB0aW1lOiAnMTg6MDAnLCB2YWx1ZTogMTIwLCB0eXBlOiAn5q2j6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzIwOjAwJywgdmFsdWU6IDEwMCwgdHlwZTogJ+ato+mdoicgfSwKICAgICAgICB7IHRpbWU6ICcyMjowMCcsIHZhbHVlOiAxMjAsIHR5cGU6ICfmraPpnaInIH0sCgogICAgICAgIHsgdGltZTogJzAwOjAwJywgdmFsdWU6IDUwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzAyOjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzA0OjAwJywgdmFsdWU6IDcwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzA2OjAwJywgdmFsdWU6IDY1LCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzA4OjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzEwOjAwJywgdmFsdWU6IDUwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzEyOjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzE0OjAwJywgdmFsdWU6IDcwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzE2OjAwJywgdmFsdWU6IDY1LCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzE4OjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzIwOjAwJywgdmFsdWU6IDUwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICAgIHsgdGltZTogJzIyOjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LAogICAgICBdLAogICAgICBzZW50aW1lbnRTY2FsZTogWwogICAgICAgIHsKICAgICAgICAgIGRhdGFLZXk6ICd2YWx1ZScsCiAgICAgICAgICBtaW46IDAsCiAgICAgICAgfSwKICAgICAgXSwKCiAgICAgIC8vIOeDremXqOaWh+eroOWIl+ihqAogICAgICBob3RBcnRpY2xlczogWwogICAgICAgIHsKICAgICAgICAgIGlkOiAxLAogICAgICAgICAgdGl0bGU6ICfmlrnlpKrng63msLTlmagoRm90aWxlKeWumOaWueaXl+iIsOW6lyDmlrnlpKrng63msLTlmagoRm90aWxlKS0xMTEg5pa55aSq54Ot5rC05ZmoKEZvdGlsZSkgNDAwLjY4MDguNjU1Li4uJywKICAgICAgICAgIHRhZzogJ+eDremXqCcsCiAgICAgICAgICB0YWdDb2xvcjogJyNmNTAnLAogICAgICAgICAgc291cmNlOiAn55S15ZWG5bmz5Y+wJywKICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAyMDoyNDowMCcsCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9hcnRpY2xlLzEnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDIsCiAgICAgICAgICB0aXRsZTogJ+i/keWcsOmTujMw5bm05Zyw5pqW5Y+j56KR77yM6Z2g6ICB5a2X5LqM5YWo5paw6K6+6K6h5L2P5Lq644CCI+aIkOmDveS6jOaJi+aIvyAj5Zyw5pqW55S15rqQJywKICAgICAgICAgIHRhZzogJ+eDremXqCcsCiAgICAgICAgICB0YWdDb2xvcjogJyNmNTAnLAogICAgICAgICAgc291cmNlOiAn6KeG6aKR5bmz5Y+wJywKICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNToxNToxNicsCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9hcnRpY2xlLzInLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDMsCiAgICAgICAgICB0aXRsZTogJ+eOsOWcqOi/meS4quekvuS8muimgemdoOiEkeiii+WQg+mlre+8jOWPr+iDveaYr+S4gOi+iOWtkOeahOS6i++8jOW8gOeOqeeskSDmiJHkuIDkuKrlrp7kvZPkurog5pu05bqU6K+l5pyJ5ZOB6LSo44CCJywKICAgICAgICAgIHRhZzogJ+eDremXqCcsCiAgICAgICAgICB0YWdDb2xvcjogJyNmNTAnLAogICAgICAgICAgc291cmNlOiAn6KeG6aKR5bmz5Y+wJywKICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNDoyOTowOScsCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9hcnRpY2xlLzMnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDQsCiAgICAgICAgICB0aXRsZTogJ++8iOaXoOagh+mimO+8iVMyIFYgYiAjIyBiICMjIyDpmZDlrpoy5Lq65pa55aSq5YWo55CD6aaW5Yib5LiA5Luj54eD5rCU54G25YWo55CD6aaW5Y+RIDAzIEFMT1FBViAwIDEzIOWbvkEtNTk4NiA1OS4uLicsCiAgICAgICAgICB0YWc6ICfng63pl6gnLAogICAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywKICAgICAgICAgIHNvdXJjZTogJ+inhumikeW5s+WPsCcsCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTQ6MTk6MjMnLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS80JywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiA1LAogICAgICAgICAgdGl0bGU6ICfkvaDkuo7ov4fov5nkuovlkJc/IOadpeeci+WQiOS6hiPmlrnlpKrlrpjnvZEgI+WNl+eypCAj5paw5qGl5Y+j5aSn5Y6o55qE5a2X5YaZ5b6XICPlkIzln47lp5DlprnnmoTmnIvlj4vnnIvov4fmnaUgI+eUteW9sScsCiAgICAgICAgICB0YWc6ICfng63pl6gnLAogICAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywKICAgICAgICAgIHNvdXJjZTogJ+inhumikeW5s+WPsCcsCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTI6NDg6MDQnLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS81JywKICAgICAgICB9LAogICAgICBdLAoKICAgICAgLy8g5oql5ZGK5qih5p2/5YiX6KGoCiAgICAgIHJlcG9ydFRlbXBsYXRlczogWwogICAgICAgIHsKICAgICAgICAgIGlkOiAxLAogICAgICAgICAgdGl0bGU6ICfoiIbmg4Ut5ZGo5oql6KGoJywKICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDE5LTExLTE2IDE4OjAyOjAwJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAyLAogICAgICAgICAgdGl0bGU6ICfoiIbmg4Ut5pyI5oql6KGoJywKICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDE5LTExLTE4IDE4OjA2OjUyJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAzLAogICAgICAgICAgdGl0bGU6ICfoiIbmg4Ut5a2j5bqm6KGoJywKICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDIxLTA5LTEyIDEwOjE1OjAwJywKICAgICAgICB9LAogICAgICBdLAoKICAgICAgLy8g5pyA5paw6LSf6Z2i5L+h5oGvCiAgICAgIG5lZ2F0aXZlTmV3czogWwogICAgICAgIHsKICAgICAgICAgIGlkOiAxLAogICAgICAgICAgdGl0bGU6ICfmlrnlpKrng63msLTlmajlh7rnjrDotKjph4/pl67popjvvIzlpJrlkI3nlKjmiLfmipXor4nml6DkurrlpITnkIYnLAogICAgICAgICAgcHVibGlzaFRpbWU6ICcyMDI1LTA0LTI5IDE5OjAzOjAwJywKICAgICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL25lZ2F0aXZlLzEnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDIsCiAgICAgICAgICB0aXRsZTogJ+aWueWkquWOqOeUteWuieijheeUteivneaXoOS6uuaOpeWQrO+8jOWuouaIt+aKleivieacjeWKoeaAgeW6puW3ricsCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTg6MjI6NDMnLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vbmVnYXRpdmUvMicsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogMywKICAgICAgICAgIHRpdGxlOiAn6Iiq56m65aWz5Y+L5YG35ouN5pel6K6w6KKr56S877yM572R5Y+L5ZCO77yM5oiR5Zyo5bqK5LiK55yL5Yiw5LqG55S16KeG5LiA5bmV55qE5aW5JywKICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNzo0ODo0NScsCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9uZWdhdGl2ZS8zJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiA0LAogICAgICAgICAgdGl0bGU6ICfmn5Dlk4HniYzlpKflnovmir3msrnng5/mnLrlmarpn7Ppl67popjlvJXlj5HnlKjmiLfkuI3mu6EnLAogICAgICAgICAgcHVibGlzaFRpbWU6ICcyMDI1LTA0LTI5IDE1OjE1OjE2JywKICAgICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL25lZ2F0aXZlLzQnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDUsCiAgICAgICAgICB0aXRsZTogJ+WutueUteWUruWQjuacjeWKoeiwg+afpe+8muWkmuWTgeeJjOacjeWKoei0qOmHj+WPguW3ruS4jem9kCcsCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTI6MjY6MDQnLAogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vbmVnYXRpdmUvNScsCiAgICAgICAgfSwKICAgICAgXSwKCiAgICAgIGxvYWRpbmc6IHRydWUKICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIC8vIOmakOiXj+mhtumDqOWvvOiIquagjwogICAgdGhpcy5vcmlnaW5hbFRvcE5hdiA9IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRvcE5hdgogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgIGtleTogJ3RvcE5hdicsCiAgICAgIHZhbHVlOiBmYWxzZQogICAgfSkKCiAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICB9LCAxMDAwKTsKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4KICAgIGlmICh0aGlzLm9yaWdpbmFsVG9wTmF2ICE9PSB1bmRlZmluZWQpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAga2V5OiAndG9wTmF2JywKICAgICAgICB2YWx1ZTogdGhpcy5vcmlnaW5hbFRvcE5hdgogICAgICB9KQogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div>\n    <div class=\"page-header-content\">\n      <div class=\"avatar\">\n        <a-avatar size=\"large\" :src=\"currentUser.avatar\" />\n      </div>\n      <div class=\"content\">\n        <div class=\"content-title\">\n          {{ currentUser.name }}<span class=\"welcome-text\">，欢迎使用舆情监控系统</span>\n        </div>\n        <div>{{ currentUser.title }} | {{ currentUser.group }}</div>\n      </div>\n      <div class=\"extra-content\">\n        <div class=\"stat-item\">\n          <a-statistic title=\"今日热点\" :value=\"hotTopicsCount\" />\n        </div>\n        <div class=\"stat-item\">\n          <a-statistic title=\"负面信息\" :value=\"negativeCount\" />\n        </div>\n        <div class=\"stat-item\">\n          <a-statistic title=\"总监控量\" :value=\"totalMonitorCount\" />\n        </div>\n      </div>\n    </div>\n\n    <div>\n      <a-row :gutter=\"24\">\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n          <a-card\n            :loading=\"loading\"\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n            title=\"24小时传播趋势\"\n          >\n            <div style=\"height: 400px; position: relative;\">\n              <v-chart :forceFit=\"true\" height=\"400\" :data=\"trendData\" :scale=\"trendScale\">\n                <v-tooltip crosshairs></v-tooltip>\n                <v-axis dataKey=\"time\"></v-axis>\n                <v-axis dataKey=\"value\"></v-axis>\n                <v-legend></v-legend>\n                <v-line position=\"time*value\" color=\"type\" :size=\"2\"></v-line>\n                <v-point position=\"time*value\" color=\"type\" :size=\"4\" shape=\"circle\"></v-point>\n              </v-chart>\n            </div>\n          </a-card>\n\n          <a-row :gutter=\"24\">\n            <a-col :span=\"12\">\n              <a-card\n                :loading=\"loading\"\n                style=\"margin-bottom: 24px\"\n                :bordered=\"false\"\n                title=\"平台来源占比\"\n              >\n                <div style=\"height: 300px;\">\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"platformData\" :scale=\"platformScale\">\n                    <v-tooltip></v-tooltip>\n                    <v-legend dataKey=\"type\"></v-legend>\n                    <v-coord type=\"theta\" radius=\"0.75\" innerRadius=\"0.6\"></v-coord>\n                    <v-pie position=\"percent\" color=\"type\" :label=\"labelConfig\"></v-pie>\n                  </v-chart>\n                </div>\n              </a-card>\n            </a-col>\n            <a-col :span=\"12\">\n              <a-card\n                :loading=\"loading\"\n                style=\"margin-bottom: 24px\"\n                :bordered=\"false\"\n                title=\"情感分布占比\"\n              >\n                <div style=\"height: 300px;\">\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"sentimentData\" :scale=\"sentimentScale\">\n                    <v-tooltip></v-tooltip>\n                    <v-axis dataKey=\"time\"></v-axis>\n                    <v-axis dataKey=\"value\"></v-axis>\n                    <v-legend dataKey=\"type\"></v-legend>\n                    <v-area position=\"time*value\" color=\"type\" :opacity=\"0.6\"></v-area>\n                  </v-chart>\n                </div>\n              </a-card>\n            </a-col>\n          </a-row>\n\n          <a-card :loading=\"loading\" :bordered=\"false\">\n            <template slot=\"title\">\n              <i class=\"el-icon-hot\" style=\"color: #F5222D; margin-right: 8px;\"></i>\n              热门文章\n            </template>\n            <a-list>\n              <a-list-item :key=\"index\" v-for=\"(item, index) in hotArticles\">\n                <a-list-item-meta>\n                  <template slot=\"avatar\">\n                    <a-tag :color=\"item.tagColor\">{{ item.tag }}</a-tag>\n                  </template>\n                  <div slot=\"title\">\n                    <a :href=\"item.link\">{{ item.title }}</a>\n                  </div>\n                  <div slot=\"description\">\n                    <span>{{ item.source }}</span>\n                    <span style=\"float: right;\">{{ item.publishTime }}</span>\n                  </div>\n                </a-list-item-meta>\n              </a-list-item>\n            </a-list>\n          </a-card>\n        </a-col>\n        <a-col\n          style=\"padding: 0 12px\"\n          :xl=\"8\"\n          :lg=\"24\"\n          :md=\"24\"\n          :sm=\"24\"\n          :xs=\"24\"\n        >\n          <a-card\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n          >\n            <template slot=\"title\">\n              <i class=\"el-icon-warning-outline\" style=\"color: #FA8C16; margin-right: 8px;\"></i>\n              预警方案设置\n            </template>\n            <div class=\"setting-buttons\">\n              <a-button type=\"primary\" icon=\"user\" style=\"margin-right: 8px;\">接收人设置</a-button>\n              <a-button type=\"primary\" icon=\"setting\" style=\"margin-right: 8px;\">预警设置</a-button>\n              <a-button type=\"primary\" icon=\"bell\" style=\"margin-right: 8px;\">关键词设置</a-button>\n              <a-button type=\"primary\" icon=\"search\" @click=\"goToSearch\">搜索测试</a-button>\n            </div>\n            <a-divider />\n            <div class=\"switch-buttons\">\n              <div style=\"margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;\">\n                <span>预警开关</span>\n                <a-switch checked></a-switch>\n              </div>\n              <div style=\"display: flex; justify-content: space-between;\">\n                <a-button>自动预警</a-button>\n                <a-button type=\"primary\">人工预警</a-button>\n              </div>\n            </div>\n          </a-card>\n          <a-card\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n          >\n            <template slot=\"title\">\n              <i class=\"el-icon-document\" style=\"color: #52C41A; margin-right: 8px;\"></i>\n              报告模板\n            </template>\n            <a-list>\n              <a-list-item v-for=\"(item, index) in reportTemplates\" :key=\"index\">\n                <a-list-item-meta>\n                  <div slot=\"title\">{{ item.title }}</div>\n                  <div slot=\"description\">{{ item.createTime }}</div>\n                </a-list-item-meta>\n                <div>\n                  <a-button type=\"link\" icon=\"edit\"></a-button>\n                  <a-button type=\"link\" icon=\"copy\"></a-button>\n                  <a-button type=\"link\" icon=\"delete\"></a-button>\n                </div>\n              </a-list-item>\n            </a-list>\n          </a-card>\n          <a-card :loading=\"loading\" :bordered=\"false\">\n            <template slot=\"title\">\n              <i class=\"el-icon-warning\" style=\"color: #FF4D4F; margin-right: 8px;\"></i>\n              最新负面\n            </template>\n            <a-list>\n              <a-list-item v-for=\"(item, index) in negativeNews\" :key=\"index\">\n                <a-list-item-meta>\n                  <template slot=\"avatar\">\n                    <a-badge status=\"error\" />\n                  </template>\n                  <div slot=\"title\">\n                    <a :href=\"item.link\">{{ item.title }}</a>\n                  </div>\n                  <div slot=\"description\">{{ item.publishTime }}</div>\n                </a-list-item-meta>\n              </a-list-item>\n            </a-list>\n          </a-card>\n        </a-col>\n      </a-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport Viser from 'viser-vue';\nimport {\n  Avatar,\n  Button,\n  Card,\n  Col,\n  List,\n  Row,\n  Statistic,\n  Tag,\n  Divider,\n  Switch,\n  Badge,\n} from \"ant-design-vue\";\nimport 'ant-design-vue/dist/antd.css';\nimport Vue from \"vue\";\n\nVue.use(Viser);\nVue.component(Avatar.name, Avatar);\nVue.component(Button.name, Button);\nVue.component(Card.name, Card);\nVue.component(Card.Grid.name, Card.Grid);\nVue.component(Card.Meta.name, Card.Meta);\nVue.component(Col.name, Col);\nVue.component(List.name, List);\nVue.component(List.Item.name, List.Item);\nVue.component(List.Item.Meta.name, List.Item.Meta);\nVue.component(Row.name, Row);\nVue.component(Statistic.name, Statistic);\nVue.component(Tag.name, Tag);\nVue.component(Divider.name, Divider);\nVue.component(Switch.name, Switch);\nVue.component(Badge.name, Badge);\n\nexport default {\n  name: \"DashBoard\",\n  methods: {\n    goToSearch() {\n      // 跳转到搜索结果页面，并传递测试搜索关键词\n      this.$router.push({\n        path: '/search-results',\n        query: {\n          q: '方太 厨电'\n        }\n      });\n    }\n  },\n  data() {\n    return {\n      originalTopNav: undefined, // 存储原始的topNav状态\n      currentUser: {\n        avatar:\n          \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\n        name: \"方太\",\n        userid: \"00000001\",\n        email: \"<EMAIL>\",\n        signature: \"舆情监控，实时把控\",\n        title: \"舆情分析师\",\n        group: \"舆情监控中心\",\n      },\n      // 统计数据\n      hotTopicsCount: 24,\n      negativeCount: 8,\n      totalMonitorCount: 1446,\n\n      // 24小时传播趋势数据\n      trendData: [\n        { time: \"00:00\", value: 300, type: \"总量\" },\n        { time: \"01:00\", value: 250, type: \"总量\" },\n        { time: \"02:00\", value: 200, type: \"总量\" },\n        { time: \"03:00\", value: 180, type: \"总量\" },\n        { time: \"04:00\", value: 150, type: \"总量\" },\n        { time: \"05:00\", value: 170, type: \"总量\" },\n        { time: \"06:00\", value: 220, type: \"总量\" },\n        { time: \"07:00\", value: 350, type: \"总量\" },\n        { time: \"08:00\", value: 500, type: \"总量\" },\n        { time: \"09:00\", value: 620, type: \"总量\" },\n        { time: \"10:00\", value: 550, type: \"总量\" },\n        { time: \"11:00\", value: 480, type: \"总量\" },\n        { time: \"12:00\", value: 400, type: \"总量\" },\n        { time: \"13:00\", value: 450, type: \"总量\" },\n        { time: \"14:00\", value: 500, type: \"总量\" },\n        { time: \"15:00\", value: 470, type: \"总量\" },\n        { time: \"16:00\", value: 460, type: \"总量\" },\n        { time: \"17:00\", value: 520, type: \"总量\" },\n        { time: \"18:00\", value: 580, type: \"总量\" },\n        { time: \"19:00\", value: 550, type: \"总量\" },\n        { time: \"20:00\", value: 500, type: \"总量\" },\n        { time: \"21:00\", value: 450, type: \"总量\" },\n        { time: \"22:00\", value: 400, type: \"总量\" },\n        { time: \"23:00\", value: 350, type: \"总量\" },\n\n        { time: \"00:00\", value: 100, type: \"微博\" },\n        { time: \"01:00\", value: 80, type: \"微博\" },\n        { time: \"02:00\", value: 60, type: \"微博\" },\n        { time: \"03:00\", value: 50, type: \"微博\" },\n        { time: \"04:00\", value: 40, type: \"微博\" },\n        { time: \"05:00\", value: 50, type: \"微博\" },\n        { time: \"06:00\", value: 70, type: \"微博\" },\n        { time: \"07:00\", value: 100, type: \"微博\" },\n        { time: \"08:00\", value: 150, type: \"微博\" },\n        { time: \"09:00\", value: 180, type: \"微博\" },\n        { time: \"10:00\", value: 160, type: \"微博\" },\n        { time: \"11:00\", value: 140, type: \"微博\" },\n        { time: \"12:00\", value: 120, type: \"微博\" },\n        { time: \"13:00\", value: 130, type: \"微博\" },\n        { time: \"14:00\", value: 150, type: \"微博\" },\n        { time: \"15:00\", value: 140, type: \"微博\" },\n        { time: \"16:00\", value: 130, type: \"微博\" },\n        { time: \"17:00\", value: 150, type: \"微博\" },\n        { time: \"18:00\", value: 170, type: \"微博\" },\n        { time: \"19:00\", value: 160, type: \"微博\" },\n        { time: \"20:00\", value: 150, type: \"微博\" },\n        { time: \"21:00\", value: 130, type: \"微博\" },\n        { time: \"22:00\", value: 120, type: \"微博\" },\n        { time: \"23:00\", value: 100, type: \"微博\" },\n\n        { time: \"00:00\", value: 80, type: \"视频\" },\n        { time: \"01:00\", value: 70, type: \"视频\" },\n        { time: \"02:00\", value: 60, type: \"视频\" },\n        { time: \"03:00\", value: 50, type: \"视频\" },\n        { time: \"04:00\", value: 40, type: \"视频\" },\n        { time: \"05:00\", value: 50, type: \"视频\" },\n        { time: \"06:00\", value: 60, type: \"视频\" },\n        { time: \"07:00\", value: 80, type: \"视频\" },\n        { time: \"08:00\", value: 100, type: \"视频\" },\n        { time: \"09:00\", value: 120, type: \"视频\" },\n        { time: \"10:00\", value: 110, type: \"视频\" },\n        { time: \"11:00\", value: 100, type: \"视频\" },\n        { time: \"12:00\", value: 90, type: \"视频\" },\n        { time: \"13:00\", value: 100, type: \"视频\" },\n        { time: \"14:00\", value: 110, type: \"视频\" },\n        { time: \"15:00\", value: 100, type: \"视频\" },\n        { time: \"16:00\", value: 90, type: \"视频\" },\n        { time: \"17:00\", value: 100, type: \"视频\" },\n        { time: \"18:00\", value: 110, type: \"视频\" },\n        { time: \"19:00\", value: 100, type: \"视频\" },\n        { time: \"20:00\", value: 90, type: \"视频\" },\n        { time: \"21:00\", value: 80, type: \"视频\" },\n        { time: \"22:00\", value: 70, type: \"视频\" },\n        { time: \"23:00\", value: 60, type: \"视频\" },\n      ],\n      trendScale: [\n        {\n          dataKey: 'value',\n          min: 0,\n        },\n      ],\n\n      // 平台来源占比数据\n      platformData: [\n        { type: '微博', percent: 31.2 },\n        { type: '视频', percent: 17.9 },\n        { type: '头条号', percent: 15.3 },\n        { type: 'APP', percent: 12.7 },\n        { type: '微信', percent: 9.8 },\n        { type: '其他', percent: 13.1 },\n      ],\n      platformScale: [\n        {\n          dataKey: 'percent',\n          min: 0,\n          formatter: '.0%',\n        },\n      ],\n      labelConfig: {\n        offset: -20,\n        textStyle: {\n          fill: '#000',\n          fontSize: 12,\n          fontWeight: 'bold',\n        },\n        formatter: (text, item) => {\n          return `${item.point.type}: ${item.point.percent}%`;\n        },\n      },\n\n      // 情感分布占比数据\n      sentimentData: [\n        { time: '00:00', value: 300, type: '中性' },\n        { time: '02:00', value: 280, type: '中性' },\n        { time: '04:00', value: 250, type: '中性' },\n        { time: '06:00', value: 260, type: '中性' },\n        { time: '08:00', value: 280, type: '中性' },\n        { time: '10:00', value: 300, type: '中性' },\n        { time: '12:00', value: 280, type: '中性' },\n        { time: '14:00', value: 250, type: '中性' },\n        { time: '16:00', value: 260, type: '中性' },\n        { time: '18:00', value: 280, type: '中性' },\n        { time: '20:00', value: 300, type: '中性' },\n        { time: '22:00', value: 280, type: '中性' },\n\n        { time: '00:00', value: 100, type: '正面' },\n        { time: '02:00', value: 120, type: '正面' },\n        { time: '04:00', value: 140, type: '正面' },\n        { time: '06:00', value: 130, type: '正面' },\n        { time: '08:00', value: 120, type: '正面' },\n        { time: '10:00', value: 100, type: '正面' },\n        { time: '12:00', value: 120, type: '正面' },\n        { time: '14:00', value: 140, type: '正面' },\n        { time: '16:00', value: 130, type: '正面' },\n        { time: '18:00', value: 120, type: '正面' },\n        { time: '20:00', value: 100, type: '正面' },\n        { time: '22:00', value: 120, type: '正面' },\n\n        { time: '00:00', value: 50, type: '负面' },\n        { time: '02:00', value: 60, type: '负面' },\n        { time: '04:00', value: 70, type: '负面' },\n        { time: '06:00', value: 65, type: '负面' },\n        { time: '08:00', value: 60, type: '负面' },\n        { time: '10:00', value: 50, type: '负面' },\n        { time: '12:00', value: 60, type: '负面' },\n        { time: '14:00', value: 70, type: '负面' },\n        { time: '16:00', value: 65, type: '负面' },\n        { time: '18:00', value: 60, type: '负面' },\n        { time: '20:00', value: 50, type: '负面' },\n        { time: '22:00', value: 60, type: '负面' },\n      ],\n      sentimentScale: [\n        {\n          dataKey: 'value',\n          min: 0,\n        },\n      ],\n\n      // 热门文章列表\n      hotArticles: [\n        {\n          id: 1,\n          title: '方太热水器(Fotile)官方旗舰店 方太热水器(Fotile)-111 方太热水器(Fotile) 400.6808.655...',\n          tag: '热门',\n          tagColor: '#f50',\n          source: '电商平台',\n          publishTime: '2025-04-29 20:24:00',\n          link: 'https://example.com/article/1',\n        },\n        {\n          id: 2,\n          title: '近地铺30年地暖口碑，靠老字二全新设计住人。#成都二手房 #地暖电源',\n          tag: '热门',\n          tagColor: '#f50',\n          source: '视频平台',\n          publishTime: '2025-04-29 15:15:16',\n          link: 'https://example.com/article/2',\n        },\n        {\n          id: 3,\n          title: '现在这个社会要靠脑袋吃饭，可能是一辈子的事，开玩笑 我一个实体人 更应该有品质。',\n          tag: '热门',\n          tagColor: '#f50',\n          source: '视频平台',\n          publishTime: '2025-04-29 14:29:09',\n          link: 'https://example.com/article/3',\n        },\n        {\n          id: 4,\n          title: '（无标题）S2 V b ## b ### 限定2人方太全球首创一代燃气灶全球首发 03 ALOQAV 0 13 图A-5986 59...',\n          tag: '热门',\n          tagColor: '#f50',\n          source: '视频平台',\n          publishTime: '2025-04-29 14:19:23',\n          link: 'https://example.com/article/4',\n        },\n        {\n          id: 5,\n          title: '你于过这事吗? 来看合了#方太官网 #南粤 #新桥口大厨的字写得 #同城姐妹的朋友看过来 #电影',\n          tag: '热门',\n          tagColor: '#f50',\n          source: '视频平台',\n          publishTime: '2025-04-29 12:48:04',\n          link: 'https://example.com/article/5',\n        },\n      ],\n\n      // 报告模板列表\n      reportTemplates: [\n        {\n          id: 1,\n          title: '舆情-周报表',\n          createTime: '2019-11-16 18:02:00',\n        },\n        {\n          id: 2,\n          title: '舆情-月报表',\n          createTime: '2019-11-18 18:06:52',\n        },\n        {\n          id: 3,\n          title: '舆情-季度表',\n          createTime: '2021-09-12 10:15:00',\n        },\n      ],\n\n      // 最新负面信息\n      negativeNews: [\n        {\n          id: 1,\n          title: '方太热水器出现质量问题，多名用户投诉无人处理',\n          publishTime: '2025-04-29 19:03:00',\n          link: 'https://example.com/negative/1',\n        },\n        {\n          id: 2,\n          title: '方太厨电安装电话无人接听，客户投诉服务态度差',\n          publishTime: '2025-04-29 18:22:43',\n          link: 'https://example.com/negative/2',\n        },\n        {\n          id: 3,\n          title: '航空女友偷拍日记被礼，网友后，我在床上看到了电视一幕的她',\n          publishTime: '2025-04-29 17:48:45',\n          link: 'https://example.com/negative/3',\n        },\n        {\n          id: 4,\n          title: '某品牌大型抽油烟机噪音问题引发用户不满',\n          publishTime: '2025-04-29 15:15:16',\n          link: 'https://example.com/negative/4',\n        },\n        {\n          id: 5,\n          title: '家电售后服务调查：多品牌服务质量参差不齐',\n          publishTime: '2025-04-29 12:26:04',\n          link: 'https://example.com/negative/5',\n        },\n      ],\n\n      loading: true\n    };\n  },\n\n  mounted() {\n    // 隐藏顶部导航栏\n    this.originalTopNav = this.$store.state.settings.topNav\n    this.$store.dispatch('settings/changeSetting', {\n      key: 'topNav',\n      value: false\n    })\n\n    setTimeout(() => {\n      this.loading = false;\n    }, 1000);\n  },\n  beforeDestroy() {\n    // 恢复顶部导航栏设置\n    if (this.originalTopNav !== undefined) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'topNav',\n        value: this.originalTopNav\n      })\n    }\n  },\n};\n</script>\n\n  <style lang=\"less\" scoped>\n@import \"./Workplace.less\";\n\n\n\n.page-header-content {\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 24px;\n}\n\n.setting-buttons {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 16px;\n}\n\n.switch-buttons {\n  margin-top: 16px;\n}\n\n.ant-list-item {\n  transition: all 0.3s;\n\n  &:hover {\n    background-color: rgba(24, 144, 255, 0.05);\n  }\n}\n\n.ant-card {\n  border-radius: 4px;\n  overflow: hidden;\n  transition: all 0.3s;\n\n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n}\n\n.ant-tag {\n  margin-right: 0;\n}\n\n.ant-list-item-meta-title {\n  margin-bottom: 4px;\n\n  a {\n    color: rgba(0, 0, 0, 0.85);\n\n    &:hover {\n      color: #1890ff;\n    }\n  }\n}\n\n.ant-list-item-meta-description {\n  color: rgba(0, 0, 0, 0.45);\n}\n\n.mobile {\n  .more-info {\n    border: 0;\n    padding-top: 16px;\n    margin: 16px 0 16px;\n  }\n\n  .headerContent .title .welcome-text {\n    display: none;\n  }\n}\n</style>\n"]}]}