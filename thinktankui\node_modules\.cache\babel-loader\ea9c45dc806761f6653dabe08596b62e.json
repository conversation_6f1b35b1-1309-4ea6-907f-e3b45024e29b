{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@babel\\runtime\\helpers\\arrayWithHoles.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@babel\\runtime\\helpers\\arrayWithHoles.js", "mtime": 1749105927095}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gX2FycmF5V2l0aEhvbGVzKHIpIHsKICBpZiAoQXJyYXkuaXNBcnJheShyKSkgcmV0dXJuIHI7Cn0KbW9kdWxlLmV4cG9ydHMgPSBfYXJyYXlXaXRoSG9sZXMsIG1vZHVsZS5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlLCBtb2R1bGUuZXhwb3J0c1siZGVmYXVsdCJdID0gbW9kdWxlLmV4cG9ydHM7"}, {"version": 3, "names": ["_arrayWithHoles", "r", "Array", "isArray", "module", "exports", "__esModule"], "sources": ["H:/项目/金刚/3/thinktankui/node_modules/@babel/runtime/helpers/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nmodule.exports = _arrayWithHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAE;EAC1B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AACAG,MAAM,CAACC,OAAO,GAAGL,eAAe,EAAEI,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}]}