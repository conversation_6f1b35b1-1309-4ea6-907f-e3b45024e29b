<template>
  <div class="my-issues">
    <!-- 左侧导航栏 -->
    <div class="left-sidebar">
      <div class="sidebar-header">
        <el-button type="primary" icon="el-icon-plus" size="small">新建方案</el-button>
      </div>

      <div class="sidebar-search">
        <el-input
          v-model="searchText"
          placeholder="搜索方案"
          size="small"
          prefix-icon="el-icon-search">
        </el-input>
      </div>

      <div class="sidebar-menu">
        <div class="menu-section">
          <div class="section-title">已有方案</div>
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect">
            <el-menu-item index="首页">
              <i class="el-icon-s-home"></i>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="总监(1)">
              <i class="el-icon-s-custom"></i>
              <span>总监(1)</span>
            </el-menu-item>
            <el-menu-item index="品牌(1)">
              <i class="el-icon-s-goods"></i>
              <span>品牌(1)</span>
            </el-menu-item>
            <el-menu-item index="方太" class="active-item">
              <i class="el-icon-star-off"></i>
              <span>方太</span>
            </el-menu-item>
            <el-menu-item index="人物(0)">
              <i class="el-icon-user"></i>
              <span>人物(0)</span>
            </el-menu-item>
            <el-menu-item index="机构(0)">
              <i class="el-icon-office-building"></i>
              <span>机构(0)</span>
            </el-menu-item>
            <el-menu-item index="产品(0)">
              <i class="el-icon-goods"></i>
              <span>产品(0)</span>
            </el-menu-item>
            <el-menu-item index="事件(0)">
              <i class="el-icon-warning"></i>
              <span>事件(0)</span>
            </el-menu-item>
            <el-menu-item index="话题(0)">
              <i class="el-icon-chat-dot-round"></i>
              <span>话题(0)</span>
            </el-menu-item>
            <el-menu-item index="舆情总览">
              <i class="el-icon-monitor"></i>
              <span>舆情总览</span>
            </el-menu-item>
            <el-menu-item index="发送预警">
              <i class="el-icon-message"></i>
              <span>发送预警</span>
            </el-menu-item>
          </el-menu>
        </div>
      </div>
    </div>

    <!-- 右侧内容区 -->
    <div class="right-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="issues-header">
          <h2>我的问题</h2>
        </div>

        <div class="issues-list">
          <div
            v-for="(issue, index) in issuesList"
            :key="index"
            class="issue-item"
            :class="issue.type">
            <div class="issue-indicator"></div>
            <div class="issue-content">
              <div class="issue-title">{{ issue.title }}</div>
              <div class="issue-description" v-if="issue.description">{{ issue.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MyIssues',
  data() {
    return {
      activeMenuItem: '方太',
      searchText: '',
      originalTopNav: undefined, // 存储原始的topNav状态
      issuesList: [
        {
          type: 'negative',
          title: '为什么会被误解为不好？',
          description: '网民对某些产品或服务产生负面印象，可能是由于信息传播不当或误解造成的。'
        },
        {
          type: 'neutral',
          title: '在网络上的口碑怎么样？',
          description: ''
        },
        {
          type: 'positive',
          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',
          description: ''
        },
        {
          type: 'negative',
          title: '网络舆情的影响因素有哪些？',
          description: ''
        },
        {
          type: 'positive',
          title: '什么是网络舆情？',
          description: ''
        },
        {
          type: 'negative',
          title: '什么是网络舆情？',
          description: ''
        },
        {
          type: 'positive',
          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',
          description: ''
        },
        {
          type: 'negative',
          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',
          description: ''
        },
        {
          type: 'positive',
          title: '网络舆情的影响因素有哪些？',
          description: ''
        },
        {
          type: 'negative',
          title: '为什么网络舆情监测很重要？',
          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'
        },
        {
          type: 'positive',
          title: '为什么网络舆情监测很重要？',
          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'
        },
        {
          type: 'negative',
          title: '中国网络舆情监测的发展历程是什么？',
          description: '从早期的人工监测到现在的智能化监测系统，经历了技术革新的过程。'
        },
        {
          type: 'positive',
          title: '网络舆情监测的技术手段有哪些？',
          description: '包括数据采集、情感分析、关键词监测、趋势预测等多种技术手段。'
        },
        {
          type: 'negative',
          title: '为什么会被误解为"全球干旱"，而不是下雨或者其他气象变化？',
          description: '媒体报道的角度和用词选择可能会影响公众对气象事件的理解。'
        }
      ]
    }
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
  methods: {
    handleMenuSelect(index) {
      this.activeMenuItem = index
      console.log('Menu selected:', index)

      // 根据选择的菜单项进行路由跳转
      if (index === '首页') {
        this.$router.push('/index')
      } else if (index === '舆情总览') {
        this.$router.push('/opinion-overview')
      } else if (index === '发送预警') {
        // 这里可以触发发送预警的功能
        console.log('发送预警功能')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.my-issues {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.left-sidebar {
  width: 280px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .sidebar-search {
    padding: 16px;
  }

  .sidebar-menu {
    flex: 1;
    padding: 0 16px;

    .section-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .sidebar-menu-list {
      border: none;

      .el-menu-item {
        height: 40px;
        line-height: 40px;
        margin-bottom: 4px;
        border-radius: 4px;

        &.active-item {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .main-content {
    flex: 1;
    padding: 20px 24px;
    overflow-y: auto;
  }
}

.issues-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.issues-list {
  .issue-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9f9f9;
    }

    &:last-child {
      border-bottom: none;
    }

    .issue-indicator {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 8px;
      margin-top: 8px;
      flex-shrink: 0;
    }

    &.positive .issue-indicator {
      background-color: #52c41a;
    }

    &.negative .issue-indicator {
      background-color: #ff4d4f;
    }

    &.neutral .issue-indicator {
      background-color: #faad14;
    }

    .issue-content {
      flex: 1;

      .issue-title {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
        margin-bottom: 4px;
        font-weight: normal;
        word-wrap: break-word;
        word-break: break-all;
      }

      .issue-description {
        font-size: 12px;
        color: #999;
        line-height: 1.5;
        margin-top: 4px;
      }
    }
  }
}
</style>
