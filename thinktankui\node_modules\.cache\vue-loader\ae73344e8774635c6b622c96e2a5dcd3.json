{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\RuoYi\\Git\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\RuoYi\\Git\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUnVvWWlHaXQnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwczovL2dpdGVlLmNvbS9pbnNpc3RlbmNlMjAyMi9SdW9ZaS1WdWUtRmFzdEFQSScKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGdvdG8oKSB7CiAgICAgIHdpbmRvdy5vcGVuKHRoaXMudXJsKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RuoYi/Git", "sourcesContent": ["<template>\n  <div>\n    <svg-icon icon-class=\"github\" @click=\"goto\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RuoYiGit',\n  data() {\n    return {\n      url: 'https://gitee.com/insistence2022/RuoYi-Vue-FastAPI'\n    }\n  },\n  methods: {\n    goto() {\n      window.open(this.url)\n    }\n  }\n}\n</script>"]}]}