{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue?vue&type=template&id=6ed73c63&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\detail.vue", "mtime": 1749115113523}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749105919489}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}