{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\index.vue?vue&type=style&index=0&id=764a0da0&lang=scss&scoped=true", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\news\\index.vue", "mtime": 1749114975231}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749105920692}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749105919488}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749105920024}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749105929158}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqRA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/news", "sourcesContent": ["<template>\n  <div class=\"news-container\">\n    <!-- 搜索区域 -->\n    <div class=\"search-section\">\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n        <el-form-item label=\"新闻标题\" prop=\"title\">\n          <el-input\n            v-model=\"queryParams.title\"\n            placeholder=\"请输入新闻标题\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"关键词\" prop=\"keyword\">\n          <el-input\n            v-model=\"queryParams.keyword\"\n            placeholder=\"请输入关键词\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"情感倾向\" prop=\"sentiment\">\n          <el-select v-model=\"queryParams.sentiment\" placeholder=\"请选择情感倾向\" clearable>\n            <el-option label=\"正面\" value=\"positive\" />\n            <el-option label=\"中性\" value=\"neutral\" />\n            <el-option label=\"负面\" value=\"negative\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"信息属性\" prop=\"infoAttribute\">\n          <el-select v-model=\"queryParams.infoAttribute\" placeholder=\"请选择信息属性\" clearable>\n            <el-option label=\"官方发布\" value=\"official\" />\n            <el-option label=\"媒体报道\" value=\"media\" />\n            <el-option label=\"用户评价\" value=\"user\" />\n            <el-option label=\"竞品信息\" value=\"competitor\" />\n            <el-option label=\"行业动态\" value=\"industry\" />\n            <el-option label=\"政策法规\" value=\"policy\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"实体名称\" prop=\"entityName\">\n          <el-input\n            v-model=\"queryParams.entityName\"\n            placeholder=\"请输入实体名称\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"发布时间\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n          ></el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <!-- 操作区域 -->\n    <div class=\"toolbar-section\">\n      <div class=\"toolbar-left\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-refresh\"\n          size=\"mini\"\n          @click=\"handleRefresh\"\n        >刷新</el-button>\n      </div>\n      <div class=\"toolbar-right\">\n        <el-tooltip class=\"item\" effect=\"dark\" content=\"显示/隐藏搜索\" placement=\"top\">\n          <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"showSearch=!showSearch\" />\n        </el-tooltip>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"statistics-section\" v-if=\"statistics\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-number\">{{ statistics.totalCount }}</div>\n            <div class=\"stat-label\">总新闻数</div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-number\">{{ statistics.todayCount }}</div>\n            <div class=\"stat-label\">今日新增</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"stat-card positive\">\n            <div class=\"stat-number\">{{ statistics.positiveCount }}</div>\n            <div class=\"stat-label\">正面</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"stat-card neutral\">\n            <div class=\"stat-number\">{{ statistics.neutralCount }}</div>\n            <div class=\"stat-label\">中性</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"stat-card negative\">\n            <div class=\"stat-number\">{{ statistics.negativeCount }}</div>\n            <div class=\"stat-label\">负面</div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 新闻列表 -->\n    <div class=\"news-list-section\">\n      <div class=\"news-list\">\n        <div \n          v-for=\"news in newsList\" \n          :key=\"news.id\" \n          class=\"news-item\"\n          @click=\"handleNewsClick(news.id)\"\n        >\n          <div class=\"news-content\">\n            <div class=\"news-header\">\n              <h3 class=\"news-title\" v-html=\"news.title\"></h3>\n              <div class=\"news-meta\">\n                <span class=\"news-source\">{{ news.sourceName }}</span>\n                <span class=\"news-time\">{{ formatTime(news.publishTime) }}</span>\n                <span class=\"sentiment-tag\" :class=\"'sentiment-' + news.sentiment\">\n                  {{ getSentimentText(news.sentiment) }}\n                </span>\n              </div>\n            </div>\n            <div class=\"news-summary\" v-if=\"news.summary\" v-html=\"news.summary\"></div>\n            <div class=\"news-footer\">\n              <div class=\"news-stats\">\n                <span><i class=\"el-icon-view\"></i> {{ news.viewsCount }}</span>\n                <span><i class=\"el-icon-chat-line-square\"></i> {{ news.commentsCount }}</span>\n              </div>\n              <div class=\"news-images\" v-if=\"news.images && news.images.length > 0\">\n                <img \n                  v-for=\"(img, index) in news.images.slice(0, 3)\" \n                  :key=\"index\" \n                  :src=\"img\" \n                  class=\"news-image\"\n                  @error=\"handleImageError\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <pagination\n        v-show=\"total > 0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getNewsList, getNewsStatistics } from '@/api/news'\n\nexport default {\n  name: 'News',\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 新闻列表\n      newsList: [],\n      // 统计数据\n      statistics: null,\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null,\n        keyword: null,\n        sentiment: null,\n        infoAttribute: null,\n        entityName: null,\n        beginTime: null,\n        endTime: null,\n        orderBy: 'publishTime',\n        orderDirection: 'desc'\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getStatistics()\n  },\n  methods: {\n    /** 查询新闻列表 */\n    getList() {\n      this.loading = true\n      // 处理时间范围\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.beginTime = this.dateRange[0]\n        this.queryParams.endTime = this.dateRange[1]\n      } else {\n        this.queryParams.beginTime = null\n        this.queryParams.endTime = null\n      }\n\n      getNewsList(this.queryParams).then(response => {\n        this.newsList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 获取统计数据 */\n    getStatistics() {\n      getNewsStatistics().then(response => {\n        this.statistics = response.data\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = []\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    /** 刷新按钮操作 */\n    handleRefresh() {\n      this.getList()\n      this.getStatistics()\n    },\n    /** 新闻点击事件 */\n    handleNewsClick(newsId) {\n      this.$router.push({ path: `/news/detail/${newsId}` })\n    },\n    /** 格式化时间 */\n    formatTime(time) {\n      if (!time) return ''\n      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}')\n    },\n    /** 获取情感文本 */\n    getSentimentText(sentiment) {\n      const sentimentMap = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      }\n      return sentimentMap[sentiment] || '未知'\n    },\n    /** 图片加载错误处理 */\n    handleImageError(event) {\n      event.target.style.display = 'none'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.news-container {\n  padding: 20px;\n}\n\n.search-section {\n  background: #fff;\n  padding: 20px;\n  margin-bottom: 20px;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.1);\n}\n\n.toolbar-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.statistics-section {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: #fff;\n    padding: 20px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0,0,0,0.1);\n    text-align: center;\n    \n    .stat-number {\n      font-size: 24px;\n      font-weight: bold;\n      color: #409eff;\n    }\n    \n    .stat-label {\n      font-size: 14px;\n      color: #666;\n      margin-top: 5px;\n    }\n    \n    &.positive .stat-number {\n      color: #67c23a;\n    }\n    \n    &.neutral .stat-number {\n      color: #909399;\n    }\n    \n    &.negative .stat-number {\n      color: #f56c6c;\n    }\n  }\n}\n\n.news-list-section {\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.1);\n}\n\n.news-list {\n  .news-item {\n    padding: 20px;\n    border-bottom: 1px solid #ebeef5;\n    cursor: pointer;\n    transition: background-color 0.3s;\n    \n    &:hover {\n      background-color: #f5f7fa;\n    }\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n  \n  .news-header {\n    margin-bottom: 10px;\n    \n    .news-title {\n      font-size: 18px;\n      font-weight: 500;\n      color: #303133;\n      margin: 0 0 8px 0;\n      line-height: 1.4;\n    }\n    \n    .news-meta {\n      display: flex;\n      align-items: center;\n      font-size: 13px;\n      color: #909399;\n      \n      span {\n        margin-right: 15px;\n      }\n      \n      .sentiment-tag {\n        padding: 2px 8px;\n        border-radius: 12px;\n        font-size: 12px;\n        \n        &.sentiment-positive {\n          background: #f0f9ff;\n          color: #67c23a;\n        }\n        \n        &.sentiment-neutral {\n          background: #f4f4f5;\n          color: #909399;\n        }\n        \n        &.sentiment-negative {\n          background: #fef0f0;\n          color: #f56c6c;\n        }\n      }\n    }\n  }\n  \n  .news-summary {\n    color: #606266;\n    line-height: 1.6;\n    margin-bottom: 10px;\n  }\n  \n  .news-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    \n    .news-stats {\n      display: flex;\n      align-items: center;\n      font-size: 13px;\n      color: #909399;\n      \n      span {\n        margin-right: 15px;\n        \n        i {\n          margin-right: 4px;\n        }\n      }\n    }\n    \n    .news-images {\n      display: flex;\n      \n      .news-image {\n        width: 60px;\n        height: 60px;\n        object-fit: cover;\n        border-radius: 4px;\n        margin-left: 8px;\n      }\n    }\n  }\n}\n\n:deep(.highlight) {\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"]}]}