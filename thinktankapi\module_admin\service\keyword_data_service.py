from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.dao.keyword_data_dao import KeywordDataDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.keyword_data_vo import (
    DeleteKeywordDataModel,
    KeywordDataModel,
    KeywordDataPageQueryModel,
    KeywordDataResponseModel,
    KeywordDataStatisticsModel
)
from utils.common_util import CamelCaseUtil


class KeywordDataService:
    """
    关键词数据模块服务层
    """

    @classmethod
    async def get_keyword_data_list_services(
        cls, query_db: AsyncSession, query_object: KeywordDataPageQueryModel, is_page: bool = False
    ):
        """
        获取关键词数据列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 关键词数据列表信息对象
        """
        keyword_data_list_result = await KeywordDataDao.get_keyword_data_list(query_db, query_object, is_page)

        if is_page:
            # 分页结果处理
            result = []
            for row in keyword_data_list_result.rows:
                # 检查row是否是字典（经过CamelCaseUtil转换）还是对象
                if isinstance(row, dict):
                    keyword_data_dict = {
                        'id': row.get('id'),
                        'title': row.get('title'),
                        'content': row.get('content'),
                        'source_url': row.get('url'),
                        'keywords': row.get('keyword'),
                        'source': row.get('type'),
                        'time': row.get('createtime').strftime('%Y-%m-%d %H:%M:%S') if row.get('createtime') else '',
                        'platform_name': row.get('web'),
                        'selected': False,
                        'sentiment': 'neutral',  # 默认中性
                        'views': 0,  # 默认值
                        'comments': 0,  # 默认值
                        'images': []  # 默认空列表
                    }
                else:
                    keyword_data_dict = {
                        'id': row.id,
                        'title': row.title,
                        'content': row.content,
                        'source_url': row.url,
                        'keywords': row.keyword,
                        'source': row.type,
                        'time': row.createtime.strftime('%Y-%m-%d %H:%M:%S') if row.createtime else '',
                        'platform_name': row.web,
                        'selected': False,
                        'sentiment': 'neutral',  # 默认中性
                        'views': 0,  # 默认值
                        'comments': 0,  # 默认值
                        'images': []  # 默认空列表
                    }
                result.append(KeywordDataResponseModel(**keyword_data_dict))

            keyword_data_list_result.rows = result
            return keyword_data_list_result
        else:
            # 非分页结果处理
            result = []
            for row in keyword_data_list_result:
                keyword_data_dict = {
                    'id': row.id,
                    'title': row.title,
                    'content': row.content,
                    'source_url': row.url,
                    'keywords': row.keyword,
                    'source': row.type,
                    'time': row.createtime.strftime('%Y-%m-%d %H:%M:%S') if row.createtime else '',
                    'platform_name': row.web,
                    'selected': False,
                    'sentiment': 'neutral',
                    'views': 0,
                    'comments': 0,
                    'images': []
                }
                result.append(KeywordDataResponseModel(**keyword_data_dict))
            return result

    @classmethod
    async def add_keyword_data_services(cls, query_db: AsyncSession, add_keyword_data: KeywordDataModel):
        """
        新增关键词数据信息service

        :param query_db: orm对象
        :param add_keyword_data: 新增关键词数据对象
        :return: 新增关键词数据校验结果
        """
        add_keyword_data_result = await KeywordDataDao.add_keyword_data_dao(query_db, add_keyword_data)
        if add_keyword_data_result:
            return CrudResponseModel(is_success=True, message='新增成功')
        else:
            return CrudResponseModel(is_success=False, message='新增失败')

    @classmethod
    async def edit_keyword_data_services(cls, query_db: AsyncSession, edit_keyword_data: KeywordDataModel):
        """
        编辑关键词数据信息service

        :param query_db: orm对象
        :param edit_keyword_data: 编辑关键词数据对象
        :return: 编辑关键词数据校验结果
        """
        edit_keyword_data.update_time = datetime.now()
        await KeywordDataDao.edit_keyword_data_dao(query_db, edit_keyword_data.model_dump())

        return CrudResponseModel(is_success=True, message='更新成功')

    @classmethod
    async def delete_keyword_data_services(cls, query_db: AsyncSession, delete_keyword_data: DeleteKeywordDataModel):
        """
        删除关键词数据信息service

        :param query_db: orm对象
        :param delete_keyword_data: 删除关键词数据对象
        :return: 删除关键词数据校验结果
        """
        if delete_keyword_data.keyword_data_ids:
            keyword_data_id_list = delete_keyword_data.keyword_data_ids.split(',')
            for keyword_data_id in keyword_data_id_list:
                keyword_data_id_dict = dict(id=keyword_data_id)
                await KeywordDataDao.delete_keyword_data_dao(query_db, KeywordDataModel(**keyword_data_id_dict))
            return CrudResponseModel(is_success=True, message='删除成功')
        else:
            return CrudResponseModel(is_success=False, message='传入关键词数据id为空')

    @classmethod
    async def keyword_data_detail_services(cls, query_db: AsyncSession, keyword_data_id: int):
        """
        获取关键词数据详细信息service

        :param query_db: orm对象
        :param keyword_data_id: 关键词数据id
        :return: 关键词数据id对应的信息
        """
        keyword_data = await KeywordDataDao.get_keyword_data_detail_by_id(query_db, keyword_data_id)
        if keyword_data:
            result = KeywordDataModel(**CamelCaseUtil.transform_result(keyword_data))
        else:
            result = KeywordDataModel(**dict())

        return result

    @classmethod
    async def get_type_statistics_services(cls, query_db: AsyncSession):
        """
        获取类型统计数据service

        :param query_db: orm对象
        :return: 类型统计数据
        """
        statistics_data = await KeywordDataDao.get_type_statistics(query_db)
        
        # 转换为统计模型列表
        result = []
        for row in statistics_data:
            # 将Row对象转换为字典
            row_dict = {
                'type': row.type or '未分类',
                'total_count': row.total_count or 0,
                'today_count': row.today_count or 0
            }
            stat = KeywordDataStatisticsModel(**row_dict)
            result.append(stat)
        
        return result
