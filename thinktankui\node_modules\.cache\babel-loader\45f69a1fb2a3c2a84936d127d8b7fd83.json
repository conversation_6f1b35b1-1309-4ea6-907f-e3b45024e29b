{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\year.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\year.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmdWxsWWVhcjogMCwKICAgICAgcmFkaW9WYWx1ZTogMSwKICAgICAgY3ljbGUwMTogMCwKICAgICAgY3ljbGUwMjogMCwKICAgICAgYXZlcmFnZTAxOiAwLAogICAgICBhdmVyYWdlMDI6IDEsCiAgICAgIGNoZWNrYm94TGlzdDogW10sCiAgICAgIGNoZWNrTnVtOiB0aGlzLiRvcHRpb25zLnByb3BzRGF0YS5jaGVjawogICAgfTsKICB9LAogIG5hbWU6ICdjcm9udGFiLXllYXInLAogIHByb3BzOiBbJ2NoZWNrJywgJ21vbnRoJywgJ2Nyb24nXSwKICBtZXRob2RzOiB7CiAgICAvLyDljZXpgInmjInpkq7lgLzlj5jljJbml7YKICAgIHJhZGlvQ2hhbmdlOiBmdW5jdGlvbiByYWRpb0NoYW5nZSgpIHsKICAgICAgc3dpdGNoICh0aGlzLnJhZGlvVmFsdWUpIHsKICAgICAgICBjYXNlIDE6CiAgICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGUnLCAneWVhcicsICcnKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgMjoKICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICd5ZWFyJywgJyonKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgMzoKICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICd5ZWFyJywgdGhpcy5jeWNsZVRvdGFsKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgNDoKICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICd5ZWFyJywgdGhpcy5hdmVyYWdlVG90YWwpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSA1OgogICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlJywgJ3llYXInLCB0aGlzLmNoZWNrYm94U3RyaW5nKTsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy8g5ZGo5pyf5Lik5Liq5YC85Y+Y5YyW5pe2CiAgICBjeWNsZUNoYW5nZTogZnVuY3Rpb24gY3ljbGVDaGFuZ2UoKSB7CiAgICAgIGlmICh0aGlzLnJhZGlvVmFsdWUgPT0gJzMnKSB7CiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlJywgJ3llYXInLCB0aGlzLmN5Y2xlVG90YWwpOwogICAgICB9CiAgICB9LAogICAgLy8g5bmz5Z2H5Lik5Liq5YC85Y+Y5YyW5pe2CiAgICBhdmVyYWdlQ2hhbmdlOiBmdW5jdGlvbiBhdmVyYWdlQ2hhbmdlKCkgewogICAgICBpZiAodGhpcy5yYWRpb1ZhbHVlID09ICc0JykgewogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICd5ZWFyJywgdGhpcy5hdmVyYWdlVG90YWwpOwogICAgICB9CiAgICB9LAogICAgLy8gY2hlY2tib3jlgLzlj5jljJbml7YKICAgIGNoZWNrYm94Q2hhbmdlOiBmdW5jdGlvbiBjaGVja2JveENoYW5nZSgpIHsKICAgICAgaWYgKHRoaXMucmFkaW9WYWx1ZSA9PSAnNScpIHsKICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGUnLCAneWVhcicsIHRoaXMuY2hlY2tib3hTdHJpbmcpOwogICAgICB9CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgJ3JhZGlvVmFsdWUnOiAncmFkaW9DaGFuZ2UnLAogICAgJ2N5Y2xlVG90YWwnOiAnY3ljbGVDaGFuZ2UnLAogICAgJ2F2ZXJhZ2VUb3RhbCc6ICdhdmVyYWdlQ2hhbmdlJywKICAgICdjaGVja2JveFN0cmluZyc6ICdjaGVja2JveENoYW5nZScKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDorqHnrpfkuKTkuKrlkajmnJ/lgLwKICAgIGN5Y2xlVG90YWw6IGZ1bmN0aW9uIGN5Y2xlVG90YWwoKSB7CiAgICAgIHZhciBjeWNsZTAxID0gdGhpcy5jaGVja051bSh0aGlzLmN5Y2xlMDEsIHRoaXMuZnVsbFllYXIsIDIwOTgpOwogICAgICB2YXIgY3ljbGUwMiA9IHRoaXMuY2hlY2tOdW0odGhpcy5jeWNsZTAyLCBjeWNsZTAxID8gY3ljbGUwMSArIDEgOiB0aGlzLmZ1bGxZZWFyICsgMSwgMjA5OSk7CiAgICAgIHJldHVybiBjeWNsZTAxICsgJy0nICsgY3ljbGUwMjsKICAgIH0sCiAgICAvLyDorqHnrpflubPlnYfnlKjliLDnmoTlgLwKICAgIGF2ZXJhZ2VUb3RhbDogZnVuY3Rpb24gYXZlcmFnZVRvdGFsKCkgewogICAgICB2YXIgYXZlcmFnZTAxID0gdGhpcy5jaGVja051bSh0aGlzLmF2ZXJhZ2UwMSwgdGhpcy5mdWxsWWVhciwgMjA5OCk7CiAgICAgIHZhciBhdmVyYWdlMDIgPSB0aGlzLmNoZWNrTnVtKHRoaXMuYXZlcmFnZTAyLCAxLCAyMDk5IC0gYXZlcmFnZTAxIHx8IHRoaXMuZnVsbFllYXIpOwogICAgICByZXR1cm4gYXZlcmFnZTAxICsgJy8nICsgYXZlcmFnZTAyOwogICAgfSwKICAgIC8vIOiuoeeul+WLvumAieeahGNoZWNrYm945YC85ZCI6ZuGCiAgICBjaGVja2JveFN0cmluZzogZnVuY3Rpb24gY2hlY2tib3hTdHJpbmcoKSB7CiAgICAgIHZhciBzdHIgPSB0aGlzLmNoZWNrYm94TGlzdC5qb2luKCk7CiAgICAgIHJldHVybiBzdHI7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8g5LuF6I635Y+W5b2T5YmN5bm05Lu9CiAgICB0aGlzLmZ1bGxZZWFyID0gTnVtYmVyKG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSk7CiAgICB0aGlzLmN5Y2xlMDEgPSB0aGlzLmZ1bGxZZWFyOwogICAgdGhpcy5hdmVyYWdlMDEgPSB0aGlzLmZ1bGxZZWFyOwogIH0KfTs="}, {"version": 3, "names": ["data", "fullYear", "radioValue", "cycle01", "cycle02", "average01", "average02", "checkboxList", "checkNum", "$options", "propsData", "check", "name", "props", "methods", "radioChange", "$emit", "cycleTotal", "averageTotal", "checkboxString", "cycleChange", "averageChange", "checkboxChange", "watch", "computed", "str", "join", "mounted", "Number", "Date", "getFullYear"], "sources": ["src/components/Crontab/year.vue"], "sourcesContent": ["<template>\n\t<el-form size=\"small\">\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"1\" v-model='radioValue'>\n\t\t\t\t不填，允许的通配符[, - * /]\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"2\" v-model='radioValue'>\n\t\t\t\t每年\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"3\" v-model='radioValue'>\n\t\t\t\t周期从\n\t\t\t\t<el-input-number v-model='cycle01' :min='fullYear' :max=\"2098\" /> -\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : fullYear + 1\" :max=\"2099\" />\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"4\" v-model='radioValue'>\n\t\t\t\t从\n\t\t\t\t<el-input-number v-model='average01' :min='fullYear' :max=\"2098\"/> 年开始，每\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"2099 - average01 || fullYear\" /> 年执行一次\n\t\t\t</el-radio>\n\n\t\t</el-form-item>\n\n\t\t<el-form-item>\n\t\t\t<el-radio :label=\"5\" v-model='radioValue'>\n\t\t\t\t指定\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple>\n\t\t\t\t\t<el-option v-for=\"item in 9\" :key=\"item\" :value=\"item - 1 + fullYear\" :label=\"item -1 + fullYear\" />\n\t\t\t\t</el-select>\n\t\t\t</el-radio>\n\t\t</el-form-item>\n\t</el-form>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tfullYear: 0,\n\t\t\tradioValue: 1,\n\t\t\tcycle01: 0,\n\t\t\tcycle02: 0,\n\t\t\taverage01: 0,\n\t\t\taverage02: 1,\n\t\t\tcheckboxList: [],\n\t\t\tcheckNum: this.$options.propsData.check\n\t\t}\n\t},\n\tname: 'crontab-year',\n\tprops: ['check', 'month', 'cron'],\n\tmethods: {\n\t\t// 单选按钮值变化时\n\t\tradioChange() {\n\t\t\tswitch (this.radioValue) {\n\t\t\t\tcase 1:\n\t\t\t\t\tthis.$emit('update', 'year', '');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tthis.$emit('update', 'year', '*');\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 5:\n\t\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 周期两个值变化时\n\t\tcycleChange() {\n\t\t\tif (this.radioValue == '3') {\n\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\n\t\t\t}\n\t\t},\n\t\t// 平均两个值变化时\n\t\taverageChange() {\n\t\t\tif (this.radioValue == '4') {\n\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\n\t\t\t}\n\t\t},\n\t\t// checkbox值变化时\n\t\tcheckboxChange() {\n\t\t\tif (this.radioValue == '5') {\n\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\n\t\t\t}\n\t\t}\n\t},\n\twatch: {\n\t\t'radioValue': 'radioChange',\n\t\t'cycleTotal': 'cycleChange',\n\t\t'averageTotal': 'averageChange',\n\t\t'checkboxString': 'checkboxChange'\n\t},\n\tcomputed: {\n\t\t// 计算两个周期值\n\t\tcycleTotal: function () {\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, this.fullYear, 2098)\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : this.fullYear + 1, 2099)\n\t\t\treturn cycle01 + '-' + cycle02;\n\t\t},\n\t\t// 计算平均用到的值\n\t\taverageTotal: function () {\n\t\t\tconst average01 = this.checkNum(this.average01, this.fullYear, 2098)\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 2099 - average01 || this.fullYear)\n\t\t\treturn average01 + '/' + average02;\n\t\t},\n\t\t// 计算勾选的checkbox值合集\n\t\tcheckboxString: function () {\n\t\t\tlet str = this.checkboxList.join();\n\t\t\treturn str;\n\t\t}\n\t},\n\tmounted: function () {\n\t\t// 仅获取当前年份\n\t\tthis.fullYear = Number(new Date().getFullYear());\n\t\tthis.cycle01 = this.fullYear\n\t\tthis.average01 = this.fullYear\n\t}\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA2CA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,SAAA;MACAC,YAAA;MACAC,QAAA,OAAAC,QAAA,CAAAC,SAAA,CAAAC;IACA;EACA;EACAC,IAAA;EACAC,KAAA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,aAAAb,UAAA;QACA;UACA,KAAAc,KAAA;UACA;QACA;UACA,KAAAA,KAAA;UACA;QACA;UACA,KAAAA,KAAA,wBAAAC,UAAA;UACA;QACA;UACA,KAAAD,KAAA,wBAAAE,YAAA;UACA;QACA;UACA,KAAAF,KAAA,wBAAAG,cAAA;UACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,SAAAlB,UAAA;QACA,KAAAc,KAAA,wBAAAC,UAAA;MACA;IACA;IACA;IACAI,aAAA,WAAAA,cAAA;MACA,SAAAnB,UAAA;QACA,KAAAc,KAAA,wBAAAE,YAAA;MACA;IACA;IACA;IACAI,cAAA,WAAAA,eAAA;MACA,SAAApB,UAAA;QACA,KAAAc,KAAA,wBAAAG,cAAA;MACA;IACA;EACA;EACAI,KAAA;IACA;IACA;IACA;IACA;EACA;EACAC,QAAA;IACA;IACAP,UAAA,WAAAA,WAAA;MACA,IAAAd,OAAA,QAAAK,QAAA,MAAAL,OAAA,OAAAF,QAAA;MACA,IAAAG,OAAA,QAAAI,QAAA,MAAAJ,OAAA,EAAAD,OAAA,GAAAA,OAAA,YAAAF,QAAA;MACA,OAAAE,OAAA,SAAAC,OAAA;IACA;IACA;IACAc,YAAA,WAAAA,aAAA;MACA,IAAAb,SAAA,QAAAG,QAAA,MAAAH,SAAA,OAAAJ,QAAA;MACA,IAAAK,SAAA,QAAAE,QAAA,MAAAF,SAAA,YAAAD,SAAA,SAAAJ,QAAA;MACA,OAAAI,SAAA,SAAAC,SAAA;IACA;IACA;IACAa,cAAA,WAAAA,eAAA;MACA,IAAAM,GAAA,QAAAlB,YAAA,CAAAmB,IAAA;MACA,OAAAD,GAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA;IACA,KAAA1B,QAAA,GAAA2B,MAAA,KAAAC,IAAA,GAAAC,WAAA;IACA,KAAA3B,OAAA,QAAAF,QAAA;IACA,KAAAI,SAAA,QAAAJ,QAAA;EACA;AACA", "ignoreList": []}]}