{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\user-management.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\account\\user-management.vue", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVXNlck1hbmFnZW1lbnQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDnlKjmiLfliJfooagKICAgICAgdXNlckxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB1c2VySWQ6IDEsCiAgICAgICAgICB1c2VyTmFtZTogImFkbWluIiwKICAgICAgICAgIHBob25lTnVtYmVyOiAiMTM4MDAxMzgwMDAiLAogICAgICAgICAgZW1haWw6ICJhZG1pbkBleGFtcGxlLmNvbSIsCiAgICAgICAgICBwYXNzd29yZDogIioqKioqKiIsCiAgICAgICAgICBtYXhWaXNpdHM6IDEwMDAsCiAgICAgICAgICByb2xlOiAi566h55CG5ZGYIiwKICAgICAgICAgIGRlcGFydG1lbnQ6ICLmioDmnK/pg6giLAogICAgICAgICAgc3RhdHVzOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB1c2VySWQ6IDIsCiAgICAgICAgICB1c2VyTmFtZTogInVzZXIxIiwKICAgICAgICAgIHBob25lTnVtYmVyOiAiMTM4MDAxMzgwMDEiLAogICAgICAgICAgZW1haWw6ICJ1c2VyMUBleGFtcGxlLmNvbSIsCiAgICAgICAgICBwYXNzd29yZDogIioqKioqKiIsCiAgICAgICAgICBtYXhWaXNpdHM6IDUwMCwKICAgICAgICAgIHJvbGU6ICLmma7pgJrnlKjmiLciLAogICAgICAgICAgZGVwYXJ0bWVudDogIuW4guWcuumDqCIsCiAgICAgICAgICBzdGF0dXM6IDEKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHVzZXJJZDogMywKICAgICAgICAgIHVzZXJOYW1lOiAidXNlcjIiLAogICAgICAgICAgcGhvbmVOdW1iZXI6ICIxMzgwMDEzODAwMiIsCiAgICAgICAgICBlbWFpbDogInVzZXIyQGV4YW1wbGUuY29tIiwKICAgICAgICAgIHBhc3N3b3JkOiAiKioqKioqIiwKICAgICAgICAgIG1heFZpc2l0czogMzAwLAogICAgICAgICAgcm9sZTogIuaZrumAmueUqOaItyIsCiAgICAgICAgICBkZXBhcnRtZW50OiAi6ZSA5ZSu6YOoIiwKICAgICAgICAgIHN0YXR1czogMAogICAgICAgIH0KICAgICAgXSwKICAgICAgLy8g5YiG6aG155u45YWzCiAgICAgIHRvdGFsOiAzLAogICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgcGFnZVNpemU6IDEwLAogICAgICAvLyDlr7nor53moYbnm7jlhbMKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGRpYWxvZ1RpdGxlOiAi5re75Yqg55So5oi3IiwKICAgICAgdXNlckZvcm06IHsKICAgICAgICB1c2VySWQ6IG51bGwsCiAgICAgICAgdXNlck5hbWU6ICIiLAogICAgICAgIHBob25lTnVtYmVyOiAiIiwKICAgICAgICBlbWFpbDogIiIsCiAgICAgICAgcGFzc3dvcmQ6ICIiLAogICAgICAgIG1heFZpc2l0czogMCwKICAgICAgICByb2xlOiAiIiwKICAgICAgICBkZXBhcnRtZW50OiAiIiwKICAgICAgICBzdGF0dXM6IDEKICAgICAgfSwKICAgICAgdXNlckZvcm1SdWxlczogewogICAgICAgIHVzZXJOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl55So5oi35ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMywgbWF4OiAyMCwgbWVzc2FnZTogIumVv+W6puWcqCAzIOWIsCAyMCDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcGhvbmVOdW1iZXI6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXmiYvmnLrlj7fnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgcGF0dGVybjogL14xWzMtOV1cZHs5fSQvLCBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGVtYWlsOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl6YKu566xIiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHR5cGU6ICJlbWFpbCIsIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTpgq7nrrHlnLDlnYAiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcGFzc3dvcmQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXlr4bnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiA4LCBtYXg6IDE2LCBtZXNzYWdlOiAi6ZW/5bqm5ZyoIDgg5YiwIDE2IOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgewogICAgICAgICAgICBwYXR0ZXJuOiAvXig/PS4qW2Etel0pKD89LipbQS1aXSkoPz0uKlxkKSg/PS4qW15cZGEtekEtWl0pLns4LDE2fSQvLAogICAgICAgICAgICBtZXNzYWdlOiAi5a+G56CB5b+F6aG75ZCM5pe25YyF5ZCr5pWw5a2X44CB5aSn5bCP5YaZ5a2X5q+N5ZKM56ym5Y+3IiwKICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5aSE55CG6aG156CB5Y+Y5YyWCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gdmFsOwogICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6Pojrflj5blr7nlupTpobXnmoTmlbDmja4KICAgIH0sCiAgICAvLyDmt7vliqDnlKjmiLcKICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICLmt7vliqDnlKjmiLciOwogICAgICB0aGlzLnVzZXJGb3JtID0gewogICAgICAgIHVzZXJJZDogbnVsbCwKICAgICAgICB1c2VyTmFtZTogIiIsCiAgICAgICAgcGhvbmVOdW1iZXI6ICIiLAogICAgICAgIGVtYWlsOiAiIiwKICAgICAgICBwYXNzd29yZDogIiIsCiAgICAgICAgbWF4VmlzaXRzOiAwLAogICAgICAgIHJvbGU6ICIiLAogICAgICAgIGRlcGFydG1lbnQ6ICIiLAogICAgICAgIHN0YXR1czogMQogICAgICB9OwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOe8lui+keeUqOaItwogICAgaGFuZGxlRWRpdChyb3cpIHsKICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICLnvJbovpHnlKjmiLciOwogICAgICB0aGlzLnVzZXJGb3JtID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKTsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDliKDpmaTnlKjmiLcKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kY29uZmlybShg56Gu5a6a6KaB5Yig6Zmk55So5oi3IiR7cm93LnVzZXJOYW1lfSLlkJfvvJ9gLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgLy8g5a6e6ZmF5bqU55So5Lit6L+Z6YeM6ZyA6KaB6LCD55So5o6l5Y+j5Yig6Zmk55So5oi3CiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHRoaXMudXNlckxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS51c2VySWQgIT09IHJvdy51c2VySWQpOwogICAgICAgIHRoaXMudG90YWwgPSB0aGlzLnVzZXJMaXN0Lmxlbmd0aDsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLlt7Llj5bmtojliKDpmaQiKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5L+u5pS555So5oi354q25oCBCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIC8vIOWunumZheW6lOeUqOS4rei/memHjOmcgOimgeiwg+eUqOaOpeWPo+S/ruaUueeUqOaIt+eKtuaAgQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOeUqOaItyIke3Jvdy51c2VyTmFtZX0i54q25oCB5beyJHtyb3cuc3RhdHVzID09PSAxID8gJ+WQr+eUqCcgOiAn56aB55SoJ31gKTsKICAgIH0sCiAgICAvLyDmj5DkuqTooajljZUKICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXNlckZvcm0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMudXNlckZvcm0udXNlcklkKSB7CiAgICAgICAgICAgIC8vIOe8lui+keeUqOaItwogICAgICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMudXNlckxpc3QuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS51c2VySWQgPT09IHRoaXMudXNlckZvcm0udXNlcklkKTsKICAgICAgICAgICAgaWYgKGluZGV4ICE9PSAtMSkgewogICAgICAgICAgICAgIHRoaXMudXNlckxpc3Quc3BsaWNlKGluZGV4LCAxLCB0aGlzLnVzZXJGb3JtKTsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDmt7vliqDnlKjmiLcKICAgICAgICAgICAgdGhpcy51c2VyRm9ybS51c2VySWQgPSB0aGlzLnVzZXJMaXN0Lmxlbmd0aCArIDE7CiAgICAgICAgICAgIHRoaXMudXNlckxpc3QucHVzaCh0aGlzLnVzZXJGb3JtKTsKICAgICAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMudXNlckxpc3QubGVuZ3RoOwogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIua3u+WKoOaIkOWKnyIpOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["user-management.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "user-management.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\n  <div class=\"user-management-container\">\n    <div class=\"user-management-header\">\n      <div class=\"title\">用户管理</div>\n      <div class=\"actions\">\n        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"handleAdd\">添加用户</el-button>\n      </div>\n    </div>\n\n    <div class=\"user-management-content\">\n      <el-table\n        :data=\"userList\"\n        style=\"width: 100%\"\n        border\n        stripe\n        :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\n      >\n        <el-table-column\n          prop=\"userId\"\n          label=\"用户ID\"\n          width=\"100\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"userName\"\n          label=\"用户名称\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"phoneNumber\"\n          label=\"手机号\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"email\"\n          label=\"邮箱\"\n          width=\"180\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"maxVisits\"\n          label=\"访问权限\"\n          width=\"100\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"status\"\n          label=\"状态\"\n          width=\"100\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-switch\n              v-model=\"scope.row.status\"\n              active-color=\"#13ce66\"\n              inactive-color=\"#ff4949\"\n              :active-value=\"1\"\n              :inactive-value=\"0\"\n              @change=\"handleStatusChange(scope.row)\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"操作\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-edit\"\n              @click=\"handleEdit(scope.row)\"\n            >编辑</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-delete\"\n              class=\"delete-btn\"\n              @click=\"handleDelete(scope.row)\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"prev, pager, next, jumper\"\n          :total=\"total\"\n          :current-page.sync=\"currentPage\"\n          :page-size=\"pageSize\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加用户对话框 -->\n    <el-dialog title=\"添加用户\" :visible.sync=\"dialogVisible\" width=\"500px\" center class=\"user-dialog\">\n      <el-form ref=\"userForm\" :model=\"userForm\" :rules=\"userFormRules\" label-width=\"100px\">\n        <el-form-item label=\"用户名称\" prop=\"userName\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.userName\" placeholder=\"请输入用户名/手机号/邮箱等登录名\" />\n        </el-form-item>\n        <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.phoneNumber\" placeholder=\"输入手机号码\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.email\" placeholder=\"输入邮箱\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <div class=\"required-mark\">*</div>\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\" show-password />\n        </el-form-item>\n        <el-form-item label=\"最大访问量\" prop=\"maxVisits\">\n          <el-input v-model=\"userForm.maxVisits\" placeholder=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-switch\n            v-model=\"userForm.status\"\n            active-color=\"#13ce66\"\n            inactive-color=\"#ff4949\"\n            :active-value=\"1\"\n            :inactive-value=\"0\"\n          />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"confirm-btn\">确定</el-button>\n        <el-button @click=\"dialogVisible = false\" class=\"cancel-btn\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"UserManagement\",\n  data() {\n    return {\n      // 用户列表\n      userList: [\n        {\n          userId: 1,\n          userName: \"admin\",\n          phoneNumber: \"13800138000\",\n          email: \"<EMAIL>\",\n          password: \"******\",\n          maxVisits: 1000,\n          role: \"管理员\",\n          department: \"技术部\",\n          status: 1\n        },\n        {\n          userId: 2,\n          userName: \"user1\",\n          phoneNumber: \"13800138001\",\n          email: \"<EMAIL>\",\n          password: \"******\",\n          maxVisits: 500,\n          role: \"普通用户\",\n          department: \"市场部\",\n          status: 1\n        },\n        {\n          userId: 3,\n          userName: \"user2\",\n          phoneNumber: \"13800138002\",\n          email: \"<EMAIL>\",\n          password: \"******\",\n          maxVisits: 300,\n          role: \"普通用户\",\n          department: \"销售部\",\n          status: 0\n        }\n      ],\n      // 分页相关\n      total: 3,\n      currentPage: 1,\n      pageSize: 10,\n      // 对话框相关\n      dialogVisible: false,\n      dialogTitle: \"添加用户\",\n      userForm: {\n        userId: null,\n        userName: \"\",\n        phoneNumber: \"\",\n        email: \"\",\n        password: \"\",\n        maxVisits: 0,\n        role: \"\",\n        department: \"\",\n        status: 1\n      },\n      userFormRules: {\n        userName: [\n          { required: true, message: \"请输入用户名称\", trigger: \"blur\" },\n          { min: 3, max: 20, message: \"长度在 3 到 20 个字符\", trigger: \"blur\" }\n        ],\n        phoneNumber: [\n          { required: true, message: \"请输入手机号码\", trigger: \"blur\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n          { type: \"email\", message: \"请输入正确的邮箱地址\", trigger: \"blur\" }\n        ],\n        password: [\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\n          { min: 8, max: 16, message: \"长度在 8 到 16 个字符\", trigger: \"blur\" },\n          {\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$/,\n            message: \"密码必须同时包含数字、大小写字母和符号\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  methods: {\n    // 处理页码变化\n    handleCurrentChange(val) {\n      this.currentPage = val;\n      // 实际应用中这里需要调用接口获取对应页的数据\n    },\n    // 添加用户\n    handleAdd() {\n      this.dialogTitle = \"添加用户\";\n      this.userForm = {\n        userId: null,\n        userName: \"\",\n        phoneNumber: \"\",\n        email: \"\",\n        password: \"\",\n        maxVisits: 0,\n        role: \"\",\n        department: \"\",\n        status: 1\n      };\n      this.dialogVisible = true;\n    },\n    // 编辑用户\n    handleEdit(row) {\n      this.dialogTitle = \"编辑用户\";\n      this.userForm = JSON.parse(JSON.stringify(row));\n      this.dialogVisible = true;\n    },\n    // 删除用户\n    handleDelete(row) {\n      this.$confirm(`确定要删除用户\"${row.userName}\"吗？`, \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        // 实际应用中这里需要调用接口删除用户\n        this.userList = this.userList.filter(item => item.userId !== row.userId);\n        this.total = this.userList.length;\n        this.$message.success(\"删除成功\");\n      }).catch(() => {\n        this.$message.info(\"已取消删除\");\n      });\n    },\n    // 修改用户状态\n    handleStatusChange(row) {\n      // 实际应用中这里需要调用接口修改用户状态\n      this.$message.success(`用户\"${row.userName}\"状态已${row.status === 1 ? '启用' : '禁用'}`);\n    },\n    // 提交表单\n    submitForm() {\n      this.$refs.userForm.validate(valid => {\n        if (valid) {\n          if (this.userForm.userId) {\n            // 编辑用户\n            const index = this.userList.findIndex(item => item.userId === this.userForm.userId);\n            if (index !== -1) {\n              this.userList.splice(index, 1, this.userForm);\n              this.$message.success(\"修改成功\");\n            }\n          } else {\n            // 添加用户\n            this.userForm.userId = this.userList.length + 1;\n            this.userList.push(this.userForm);\n            this.total = this.userList.length;\n            this.$message.success(\"添加成功\");\n          }\n          this.dialogVisible = false;\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-management-container {\n  background-color: #fff;\n  padding: 20px;\n  border-radius: 4px;\n}\n\n.user-management-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .title {\n    font-size: 18px;\n    font-weight: bold;\n  }\n}\n\n.user-management-content {\n  .el-table {\n    margin-bottom: 20px;\n  }\n\n  .delete-btn {\n    color: #f56c6c;\n  }\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n.dialog-footer {\n  text-align: center;\n\n  .confirm-btn {\n    background-color: #409EFF;\n    border-color: #409EFF;\n    margin-right: 10px;\n  }\n\n  .cancel-btn {\n    color: #606266;\n    background-color: #fff;\n    border-color: #dcdfe6;\n  }\n}\n\n.user-dialog {\n  .el-dialog__header {\n    padding: 15px 20px;\n    border-bottom: 1px solid #e4e7ed;\n\n    .el-dialog__title {\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .el-dialog__headerbtn {\n      top: 15px;\n    }\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n\n  .el-form-item {\n    position: relative;\n    margin-bottom: 20px;\n\n    .required-mark {\n      position: absolute;\n      left: -10px;\n      top: 10px;\n      color: #f56c6c;\n      font-size: 14px;\n    }\n\n    .el-form-item__label {\n      color: #606266;\n      font-weight: normal;\n    }\n\n    .el-input__inner {\n      border-radius: 3px;\n    }\n  }\n}\n</style>\n"]}]}