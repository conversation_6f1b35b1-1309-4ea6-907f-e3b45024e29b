{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "levelList", "watch", "$route", "route", "path", "startsWith", "getBreadcrumb", "created", "methods", "matched", "router", "pathNum", "findPath<PERSON>um", "reg", "pathList", "match", "map", "item", "index", "slice", "getMatched", "$store", "getters", "defaultRoutes", "filter", "meta", "title", "isDashboard", "concat", "breadcrumb", "str", "char", "arguments", "length", "undefined", "indexOf", "num", "routeList", "find", "name", "toLowerCase", "push", "children", "shift", "trim", "handleLink", "redirect", "$router"], "sources": ["src/components/Breadcrumb/index.vue"], "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return\n      }\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = []\n      const router = this.$route\n      const pathNum = this.findPathNum(router.path)\n      // multi-level menu\n      if (pathNum > 2) {\n        const reg = /\\/\\w+/gi\n        const pathList = router.path.match(reg).map((item, index) => {\n          if (index !== 0) item = item.slice(1)\n          return item\n        })\n        this.getMatched(pathList, this.$store.getters.defaultRoutes, matched)\n      } else {\n        matched = router.matched.filter(item => item.meta && item.meta.title)\n      }\n      // 判断是否为首页\n      if (!this.isDashboard(matched[0])) {\n        matched = [{ path: \"/index\", meta: { title: \"首页\" } }].concat(matched)\n      }\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    findPathNum(str, char = \"/\") {\n      let index = str.indexOf(char)\n      let num = 0\n      while (index !== -1) {\n        num++\n        index = str.indexOf(char, index + 1)\n      }\n      return num\n    },\n    getMatched(pathList, routeList, matched) {\n      let data = routeList.find(item => item.path == pathList[0] || (item.name += '').toLowerCase() == pathList[0])\n      if (data) {\n        matched.push(data)\n        if (data.children && pathList.length) {\n          pathList.shift()\n          this.getMatched(pathList, data.children, matched)\n        }\n      }\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim() === 'Index'\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(path)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAYA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAAC,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,IAAA,CAAAC,UAAA;QACA;MACA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,aAAA;EACA;EACAE,OAAA;IACAF,aAAA,WAAAA,cAAA;MACA;MACA,IAAAG,OAAA;MACA,IAAAC,MAAA,QAAAR,MAAA;MACA,IAAAS,OAAA,QAAAC,WAAA,CAAAF,MAAA,CAAAN,IAAA;MACA;MACA,IAAAO,OAAA;QACA,IAAAE,GAAA;QACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAN,IAAA,CAAAW,KAAA,CAAAF,GAAA,EAAAG,GAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAA,KAAA,QAAAD,IAAA,GAAAA,IAAA,CAAAE,KAAA;UACA,OAAAF,IAAA;QACA;QACA,KAAAG,UAAA,CAAAN,QAAA,OAAAO,MAAA,CAAAC,OAAA,CAAAC,aAAA,EAAAd,OAAA;MACA;QACAA,OAAA,GAAAC,MAAA,CAAAD,OAAA,CAAAe,MAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAQ,IAAA,IAAAR,IAAA,CAAAQ,IAAA,CAAAC,KAAA;QAAA;MACA;MACA;MACA,UAAAC,WAAA,CAAAlB,OAAA;QACAA,OAAA;UAAAL,IAAA;UAAAqB,IAAA;YAAAC,KAAA;UAAA;QAAA,GAAAE,MAAA,CAAAnB,OAAA;MACA;MACA,KAAAT,SAAA,GAAAS,OAAA,CAAAe,MAAA,WAAAP,IAAA;QAAA,OAAAA,IAAA,CAAAQ,IAAA,IAAAR,IAAA,CAAAQ,IAAA,CAAAC,KAAA,IAAAT,IAAA,CAAAQ,IAAA,CAAAI,UAAA;MAAA;IACA;IACAjB,WAAA,WAAAA,YAAAkB,GAAA;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAd,KAAA,GAAAY,GAAA,CAAAK,OAAA,CAAAJ,IAAA;MACA,IAAAK,GAAA;MACA,OAAAlB,KAAA;QACAkB,GAAA;QACAlB,KAAA,GAAAY,GAAA,CAAAK,OAAA,CAAAJ,IAAA,EAAAb,KAAA;MACA;MACA,OAAAkB,GAAA;IACA;IACAhB,UAAA,WAAAA,WAAAN,QAAA,EAAAuB,SAAA,EAAA5B,OAAA;MACA,IAAAV,IAAA,GAAAsC,SAAA,CAAAC,IAAA,WAAArB,IAAA;QAAA,OAAAA,IAAA,CAAAb,IAAA,IAAAU,QAAA,QAAAG,IAAA,CAAAsB,IAAA,QAAAC,WAAA,MAAA1B,QAAA;MAAA;MACA,IAAAf,IAAA;QACAU,OAAA,CAAAgC,IAAA,CAAA1C,IAAA;QACA,IAAAA,IAAA,CAAA2C,QAAA,IAAA5B,QAAA,CAAAmB,MAAA;UACAnB,QAAA,CAAA6B,KAAA;UACA,KAAAvB,UAAA,CAAAN,QAAA,EAAAf,IAAA,CAAA2C,QAAA,EAAAjC,OAAA;QACA;MACA;IACA;IACAkB,WAAA,WAAAA,YAAAxB,KAAA;MACA,IAAAoC,IAAA,GAAApC,KAAA,IAAAA,KAAA,CAAAoC,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAA,IAAA,CAAAK,IAAA;IACA;IACAC,UAAA,WAAAA,WAAA5B,IAAA;MACA,IAAA6B,QAAA,GAAA7B,IAAA,CAAA6B,QAAA;QAAA1C,IAAA,GAAAa,IAAA,CAAAb,IAAA;MACA,IAAA0C,QAAA;QACA,KAAAC,OAAA,CAAAN,IAAA,CAAAK,QAAA;QACA;MACA;MACA,KAAAC,OAAA,CAAAN,IAAA,CAAArC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}