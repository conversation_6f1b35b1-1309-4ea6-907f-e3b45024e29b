{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\result.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\components\\Crontab\\result.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["result.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAa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file": "result.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\n\t<div class=\"popup-result\">\n\t\t<p class=\"title\">最近5次运行时间</p>\n\t\t<ul class=\"popup-result-scroll\">\n\t\t\t<template v-if='isShow'>\n\t\t\t\t<li v-for='item in resultList' :key=\"item\">{{item}}</li>\n\t\t\t</template>\n\t\t\t<li v-else>计算结果中...</li>\n\t\t</ul>\n\t</div>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tdayRule: '',\n\t\t\tdayRuleSup: '',\n\t\t\tdateArr: [],\n\t\t\tresultList: [],\n\t\t\tisShow: false\n\t\t}\n\t},\n\tname: 'crontab-result',\n\tmethods: {\n\t\t// 表达式值变化时，开始去计算结果\n\t\texpressionChange() {\n\n\t\t\t// 计算开始-隐藏结果\n\t\t\tthis.isShow = false;\n\t\t\t// 获取规则数组[0秒、1分、2时、3日、4月、5星期、6年]\n\t\t\tlet ruleArr = this.$options.propsData.ex.split(' ');\n\t\t\t// 用于记录进入循环的次数\n\t\t\tlet nums = 0;\n\t\t\t// 用于暂时存符号时间规则结果的数组\n\t\t\tlet resultArr = [];\n\t\t\t// 获取当前时间精确至[年、月、日、时、分、秒]\n\t\t\tlet nTime = new Date();\n\t\t\tlet nYear = nTime.getFullYear();\n\t\t\tlet nMonth = nTime.getMonth() + 1;\n\t\t\tlet nDay = nTime.getDate();\n\t\t\tlet nHour = nTime.getHours();\n\t\t\tlet nMin = nTime.getMinutes();\n\t\t\tlet nSecond = nTime.getSeconds();\n\t\t\t// 根据规则获取到近100年可能年数组、月数组等等\n\t\t\tthis.getSecondArr(ruleArr[0]);\n\t\t\tthis.getMinArr(ruleArr[1]);\n\t\t\tthis.getHourArr(ruleArr[2]);\n\t\t\tthis.getDayArr(ruleArr[3]);\n\t\t\tthis.getMonthArr(ruleArr[4]);\n\t\t\tthis.getWeekArr(ruleArr[5]);\n\t\t\tthis.getYearArr(ruleArr[6], nYear);\n\t\t\t// 将获取到的数组赋值-方便使用\n\t\t\tlet sDate = this.dateArr[0];\n\t\t\tlet mDate = this.dateArr[1];\n\t\t\tlet hDate = this.dateArr[2];\n\t\t\tlet DDate = this.dateArr[3];\n\t\t\tlet MDate = this.dateArr[4];\n\t\t\tlet YDate = this.dateArr[5];\n\t\t\t// 获取当前时间在数组中的索引\n\t\t\tlet sIdx = this.getIndex(sDate, nSecond);\n\t\t\tlet mIdx = this.getIndex(mDate, nMin);\n\t\t\tlet hIdx = this.getIndex(hDate, nHour);\n\t\t\tlet DIdx = this.getIndex(DDate, nDay);\n\t\t\tlet MIdx = this.getIndex(MDate, nMonth);\n\t\t\tlet YIdx = this.getIndex(YDate, nYear);\n\t\t\t// 重置月日时分秒的函数(后面用的比较多)\n\t\t\tconst resetSecond = function () {\n\t\t\t\tsIdx = 0;\n\t\t\t\tnSecond = sDate[sIdx]\n\t\t\t}\n\t\t\tconst resetMin = function () {\n\t\t\t\tmIdx = 0;\n\t\t\t\tnMin = mDate[mIdx]\n\t\t\t\tresetSecond();\n\t\t\t}\n\t\t\tconst resetHour = function () {\n\t\t\t\thIdx = 0;\n\t\t\t\tnHour = hDate[hIdx]\n\t\t\t\tresetMin();\n\t\t\t}\n\t\t\tconst resetDay = function () {\n\t\t\t\tDIdx = 0;\n\t\t\t\tnDay = DDate[DIdx]\n\t\t\t\tresetHour();\n\t\t\t}\n\t\t\tconst resetMonth = function () {\n\t\t\t\tMIdx = 0;\n\t\t\t\tnMonth = MDate[MIdx]\n\t\t\t\tresetDay();\n\t\t\t}\n\t\t\t// 如果当前年份不为数组中当前值\n\t\t\tif (nYear !== YDate[YIdx]) {\n\t\t\t\tresetMonth();\n\t\t\t}\n\t\t\t// 如果当前月份不为数组中当前值\n\t\t\tif (nMonth !== MDate[MIdx]) {\n\t\t\t\tresetDay();\n\t\t\t}\n\t\t\t// 如果当前“日”不为数组中当前值\n\t\t\tif (nDay !== DDate[DIdx]) {\n\t\t\t\tresetHour();\n\t\t\t}\n\t\t\t// 如果当前“时”不为数组中当前值\n\t\t\tif (nHour !== hDate[hIdx]) {\n\t\t\t\tresetMin();\n\t\t\t}\n\t\t\t// 如果当前“分”不为数组中当前值\n\t\t\tif (nMin !== mDate[mIdx]) {\n\t\t\t\tresetSecond();\n\t\t\t}\n\n\t\t\t// 循环年份数组\n\t\t\tgoYear: for (let Yi = YIdx; Yi < YDate.length; Yi++) {\n\t\t\t\tlet YY = YDate[Yi];\n\t\t\t\t// 如果到达最大值时\n\t\t\t\tif (nMonth > MDate[MDate.length - 1]) {\n\t\t\t\t\tresetMonth();\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// 循环月份数组\n\t\t\t\tgoMonth: for (let Mi = MIdx; Mi < MDate.length; Mi++) {\n\t\t\t\t\t// 赋值、方便后面运算\n\t\t\t\t\tlet MM = MDate[Mi];\n\t\t\t\t\tMM = MM < 10 ? '0' + MM : MM;\n\t\t\t\t\t// 如果到达最大值时\n\t\t\t\t\tif (nDay > DDate[DDate.length - 1]) {\n\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\tif (Mi == MDate.length - 1) {\n\t\t\t\t\t\t\tresetMonth();\n\t\t\t\t\t\t\tcontinue goYear;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\t// 循环日期数组\n\t\t\t\t\tgoDay: for (let Di = DIdx; Di < DDate.length; Di++) {\n\t\t\t\t\t\t// 赋值、方便后面运算\n\t\t\t\t\t\tlet DD = DDate[Di];\n\t\t\t\t\t\tlet thisDD = DD < 10 ? '0' + DD : DD;\n\n\t\t\t\t\t\t// 如果到达最大值时\n\t\t\t\t\t\tif (nHour > hDate[hDate.length - 1]) {\n\t\t\t\t\t\t\tresetHour();\n\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\n\t\t\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\n\t\t\t\t\t\t\t\t\tresetMonth();\n\t\t\t\t\t\t\t\t\tcontinue goYear;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcontinue goMonth;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 判断日期的合法性，不合法的话也是跳出当前循环\n\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true && this.dayRule !== 'workDay' && this.dayRule !== 'lastWeek' && this.dayRule !== 'lastDay') {\n\t\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\t\tcontinue goMonth;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 如果日期规则中有值时\n\t\t\t\t\t\tif (this.dayRule == 'lastDay') {\n\t\t\t\t\t\t\t// 如果不是合法日期则需要将前将日期调到合法日期即月末最后一天\n\n\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\twhile (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\t\tDD--;\n\n\t\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.dayRule == 'workDay') {\n\t\t\t\t\t\t\t// 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\n\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\twhile (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\t\tDD--;\n\t\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 获取达到条件的日期是星期X\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\n\t\t\t\t\t\t\t// 当星期日时\n\t\t\t\t\t\t\tif (thisWeek == 1) {\n\t\t\t\t\t\t\t\t// 先找下一个日，并判断是否为月底\n\t\t\t\t\t\t\t\tDD++;\n\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\n\t\t\t\t\t\t\t\t// 判断下一日已经不是合法日期\n\t\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\t\tDD -= 3;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (thisWeek == 7) {\n\t\t\t\t\t\t\t\t// 当星期6时只需判断不是1号就可进行操作\n\t\t\t\t\t\t\t\tif (this.dayRuleSup !== 1) {\n\t\t\t\t\t\t\t\t\tDD--;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tDD += 2;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.dayRule == 'weekDay') {\n\t\t\t\t\t\t\t// 如果指定了是星期几\n\t\t\t\t\t\t\t// 获取当前日期是属于星期几\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\n\t\t\t\t\t\t\t// 校验当前星期是否在星期池（dayRuleSup）中\n\t\t\t\t\t\t\tif (this.dayRuleSup.indexOf(thisWeek) < 0) {\n\t\t\t\t\t\t\t\t// 如果到达最大值时\n\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\n\t\t\t\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\tresetMonth();\n\t\t\t\t\t\t\t\t\t\tcontinue goYear;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tcontinue goMonth;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.dayRule == 'assWeek') {\n\t\t\t\t\t\t\t// 如果指定了是第几周的星期几\n\t\t\t\t\t\t\t// 获取每月1号是属于星期几\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\n\t\t\t\t\t\t\tif (this.dayRuleSup[1] >= thisWeek) {\n\t\t\t\t\t\t\t\tDD = (this.dayRuleSup[0] - 1) * 7 + this.dayRuleSup[1] - thisWeek + 1;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tDD = this.dayRuleSup[0] * 7 + this.dayRuleSup[1] - thisWeek + 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.dayRule == 'lastWeek') {\n\t\t\t\t\t\t\t// 如果指定了每月最后一个星期几\n\t\t\t\t\t\t\t// 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\n\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\twhile (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\n\t\t\t\t\t\t\t\t\tDD--;\n\t\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 获取月末最后一天是星期几\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\n\t\t\t\t\t\t\t// 找到要求中最近的那个星期几\n\t\t\t\t\t\t\tif (this.dayRuleSup < thisWeek) {\n\t\t\t\t\t\t\t\tDD -= thisWeek - this.dayRuleSup;\n\t\t\t\t\t\t\t} else if (this.dayRuleSup > thisWeek) {\n\t\t\t\t\t\t\t\tDD -= 7 - (this.dayRuleSup - thisWeek)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 判断时间值是否小于10置换成“05”这种格式\n\t\t\t\t\t\tDD = DD < 10 ? '0' + DD : DD;\n\n\t\t\t\t\t\t// 循环“时”数组\n\t\t\t\t\t\tgoHour: for (let hi = hIdx; hi < hDate.length; hi++) {\n\t\t\t\t\t\t\tlet hh = hDate[hi] < 10 ? '0' + hDate[hi] : hDate[hi]\n\n\t\t\t\t\t\t\t// 如果到达最大值时\n\t\t\t\t\t\t\tif (nMin > mDate[mDate.length - 1]) {\n\t\t\t\t\t\t\t\tresetMin();\n\t\t\t\t\t\t\t\tif (hi == hDate.length - 1) {\n\t\t\t\t\t\t\t\t\tresetHour();\n\t\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\tresetMonth();\n\t\t\t\t\t\t\t\t\t\t\tcontinue goYear;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tcontinue goMonth;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tcontinue goDay;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 循环\"分\"数组\n\t\t\t\t\t\t\tgoMin: for (let mi = mIdx; mi < mDate.length; mi++) {\n\t\t\t\t\t\t\t\tlet mm = mDate[mi] < 10 ? '0' + mDate[mi] : mDate[mi];\n\n\t\t\t\t\t\t\t\t// 如果到达最大值时\n\t\t\t\t\t\t\t\tif (nSecond > sDate[sDate.length - 1]) {\n\t\t\t\t\t\t\t\t\tresetSecond();\n\t\t\t\t\t\t\t\t\tif (mi == mDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\tresetMin();\n\t\t\t\t\t\t\t\t\t\tif (hi == hDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\tresetHour();\n\t\t\t\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tresetMonth();\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue goYear;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\tcontinue goMonth;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tcontinue goDay;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tcontinue goHour;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// 循环\"秒\"数组\n\t\t\t\t\t\t\t\tgoSecond: for (let si = sIdx; si <= sDate.length - 1; si++) {\n\t\t\t\t\t\t\t\t\tlet ss = sDate[si] < 10 ? '0' + sDate[si] : sDate[si];\n\t\t\t\t\t\t\t\t\t// 添加当前时间（时间合法性在日期循环时已经判断）\n\t\t\t\t\t\t\t\t\tif (MM !== '00' && DD !== '00') {\n\t\t\t\t\t\t\t\t\t\tresultArr.push(YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss)\n\t\t\t\t\t\t\t\t\t\tnums++;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t// 如果条数满了就退出循环\n\t\t\t\t\t\t\t\t\tif (nums == 5) break goYear;\n\t\t\t\t\t\t\t\t\t// 如果到达最大值时\n\t\t\t\t\t\t\t\t\tif (si == sDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\tresetSecond();\n\t\t\t\t\t\t\t\t\t\tif (mi == mDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\tresetMin();\n\t\t\t\t\t\t\t\t\t\t\tif (hi == hDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\t\tresetHour();\n\t\t\t\t\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tresetDay();\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tresetMonth();\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue goYear;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue goMonth;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\tcontinue goDay;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tcontinue goHour;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tcontinue goMin;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} //goSecond\n\t\t\t\t\t\t\t} //goMin\n\t\t\t\t\t\t}//goHour\n\t\t\t\t\t}//goDay\n\t\t\t\t}//goMonth\n\t\t\t}\n\t\t\t// 判断100年内的结果条数\n\t\t\tif (resultArr.length == 0) {\n\t\t\t\tthis.resultList = ['没有达到条件的结果！'];\n\t\t\t} else {\n\t\t\t\tthis.resultList = resultArr;\n\t\t\t\tif (resultArr.length !== 5) {\n\t\t\t\t\tthis.resultList.push('最近100年内只有上面' + resultArr.length + '条结果！')\n\t\t\t\t}\n\t\t\t}\n\t\t\t// 计算完成-显示结果\n\t\t\tthis.isShow = true;\n\n\n\t\t},\n\t\t// 用于计算某位数字在数组中的索引\n\t\tgetIndex(arr, value) {\n\t\t\tif (value <= arr[0] || value > arr[arr.length - 1]) {\n\t\t\t\treturn 0;\n\t\t\t} else {\n\t\t\t\tfor (let i = 0; i < arr.length - 1; i++) {\n\t\t\t\t\tif (value > arr[i] && value <= arr[i + 1]) {\n\t\t\t\t\t\treturn i + 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 获取\"年\"数组\n\t\tgetYearArr(rule, year) {\n\t\t\tthis.dateArr[5] = this.getOrderArr(year, year + 100);\n\t\t\tif (rule !== undefined) {\n\t\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\t\tthis.dateArr[5] = this.getCycleArr(rule, year + 100, false)\n\t\t\t\t} else if (rule.indexOf('/') >= 0) {\n\t\t\t\t\tthis.dateArr[5] = this.getAverageArr(rule, year + 100)\n\t\t\t\t} else if (rule !== '*') {\n\t\t\t\t\tthis.dateArr[5] = this.getAssignArr(rule)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 获取\"月\"数组\n\t\tgetMonthArr(rule) {\n\t\t\tthis.dateArr[4] = this.getOrderArr(1, 12);\n\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\tthis.dateArr[4] = this.getCycleArr(rule, 12, false)\n\t\t\t} else if (rule.indexOf('/') >= 0) {\n\t\t\t\tthis.dateArr[4] = this.getAverageArr(rule, 12)\n\t\t\t} else if (rule !== '*') {\n\t\t\t\tthis.dateArr[4] = this.getAssignArr(rule)\n\t\t\t}\n\t\t},\n\t\t// 获取\"日\"数组-主要为日期规则\n\t\tgetWeekArr(rule) {\n\t\t\t// 只有当日期规则的两个值均为“”时则表达日期是有选项的\n\t\t\tif (this.dayRule == '' && this.dayRuleSup == '') {\n\t\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\t\tthis.dayRule = 'weekDay';\n\t\t\t\t\tthis.dayRuleSup = this.getCycleArr(rule, 7, false)\n\t\t\t\t} else if (rule.indexOf('#') >= 0) {\n\t\t\t\t\tthis.dayRule = 'assWeek';\n\t\t\t\t\tlet matchRule = rule.match(/[0-9]{1}/g);\n\t\t\t\t\tthis.dayRuleSup = [Number(matchRule[1]), Number(matchRule[0])];\n\t\t\t\t\tthis.dateArr[3] = [1];\n\t\t\t\t\tif (this.dayRuleSup[1] == 7) {\n\t\t\t\t\t\tthis.dayRuleSup[1] = 0;\n\t\t\t\t\t}\n\t\t\t\t} else if (rule.indexOf('L') >= 0) {\n\t\t\t\t\tthis.dayRule = 'lastWeek';\n\t\t\t\t\tthis.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\n\t\t\t\t\tthis.dateArr[3] = [31];\n\t\t\t\t\tif (this.dayRuleSup == 7) {\n\t\t\t\t\t\tthis.dayRuleSup = 0;\n\t\t\t\t\t}\n\t\t\t\t} else if (rule !== '*' && rule !== '?') {\n\t\t\t\t\tthis.dayRule = 'weekDay';\n\t\t\t\t\tthis.dayRuleSup = this.getAssignArr(rule)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 获取\"日\"数组-少量为日期规则\n\t\tgetDayArr(rule) {\n\t\t\tthis.dateArr[3] = this.getOrderArr(1, 31);\n\t\t\tthis.dayRule = '';\n\t\t\tthis.dayRuleSup = '';\n\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\tthis.dateArr[3] = this.getCycleArr(rule, 31, false)\n\t\t\t\tthis.dayRuleSup = 'null';\n\t\t\t} else if (rule.indexOf('/') >= 0) {\n\t\t\t\tthis.dateArr[3] = this.getAverageArr(rule, 31)\n\t\t\t\tthis.dayRuleSup = 'null';\n\t\t\t} else if (rule.indexOf('W') >= 0) {\n\t\t\t\tthis.dayRule = 'workDay';\n\t\t\t\tthis.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\n\t\t\t\tthis.dateArr[3] = [this.dayRuleSup];\n\t\t\t} else if (rule.indexOf('L') >= 0) {\n\t\t\t\tthis.dayRule = 'lastDay';\n\t\t\t\tthis.dayRuleSup = 'null';\n\t\t\t\tthis.dateArr[3] = [31];\n\t\t\t} else if (rule !== '*' && rule !== '?') {\n\t\t\t\tthis.dateArr[3] = this.getAssignArr(rule)\n\t\t\t\tthis.dayRuleSup = 'null';\n\t\t\t} else if (rule == '*') {\n\t\t\t\tthis.dayRuleSup = 'null';\n\t\t\t}\n\t\t},\n\t\t// 获取\"时\"数组\n\t\tgetHourArr(rule) {\n\t\t\tthis.dateArr[2] = this.getOrderArr(0, 23);\n\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\tthis.dateArr[2] = this.getCycleArr(rule, 24, true)\n\t\t\t} else if (rule.indexOf('/') >= 0) {\n\t\t\t\tthis.dateArr[2] = this.getAverageArr(rule, 23)\n\t\t\t} else if (rule !== '*') {\n\t\t\t\tthis.dateArr[2] = this.getAssignArr(rule)\n\t\t\t}\n\t\t},\n\t\t// 获取\"分\"数组\n\t\tgetMinArr(rule) {\n\t\t\tthis.dateArr[1] = this.getOrderArr(0, 59);\n\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\tthis.dateArr[1] = this.getCycleArr(rule, 60, true)\n\t\t\t} else if (rule.indexOf('/') >= 0) {\n\t\t\t\tthis.dateArr[1] = this.getAverageArr(rule, 59)\n\t\t\t} else if (rule !== '*') {\n\t\t\t\tthis.dateArr[1] = this.getAssignArr(rule)\n\t\t\t}\n\t\t},\n\t\t// 获取\"秒\"数组\n\t\tgetSecondArr(rule) {\n\t\t\tthis.dateArr[0] = this.getOrderArr(0, 59);\n\t\t\tif (rule.indexOf('-') >= 0) {\n\t\t\t\tthis.dateArr[0] = this.getCycleArr(rule, 60, true)\n\t\t\t} else if (rule.indexOf('/') >= 0) {\n\t\t\t\tthis.dateArr[0] = this.getAverageArr(rule, 59)\n\t\t\t} else if (rule !== '*') {\n\t\t\t\tthis.dateArr[0] = this.getAssignArr(rule)\n\t\t\t}\n\t\t},\n\t\t// 根据传进来的min-max返回一个顺序的数组\n\t\tgetOrderArr(min, max) {\n\t\t\tlet arr = [];\n\t\t\tfor (let i = min; i <= max; i++) {\n\t\t\t\tarr.push(i);\n\t\t\t}\n\t\t\treturn arr;\n\t\t},\n\t\t// 根据规则中指定的零散值返回一个数组\n\t\tgetAssignArr(rule) {\n\t\t\tlet arr = [];\n\t\t\tlet assiginArr = rule.split(',');\n\t\t\tfor (let i = 0; i < assiginArr.length; i++) {\n\t\t\t\tarr[i] = Number(assiginArr[i])\n\t\t\t}\n\t\t\tarr.sort(this.compare)\n\t\t\treturn arr;\n\t\t},\n\t\t// 根据一定算术规则计算返回一个数组\n\t\tgetAverageArr(rule, limit) {\n\t\t\tlet arr = [];\n\t\t\tlet agArr = rule.split('/');\n\t\t\tlet min = Number(agArr[0]);\n\t\t\tlet step = Number(agArr[1]);\n\t\t\twhile (min <= limit) {\n\t\t\t\tarr.push(min);\n\t\t\t\tmin += step;\n\t\t\t}\n\t\t\treturn arr;\n\t\t},\n\t\t// 根据规则返回一个具有周期性的数组\n\t\tgetCycleArr(rule, limit, status) {\n\t\t\t// status--表示是否从0开始（则从1开始）\n\t\t\tlet arr = [];\n\t\t\tlet cycleArr = rule.split('-');\n\t\t\tlet min = Number(cycleArr[0]);\n\t\t\tlet max = Number(cycleArr[1]);\n\t\t\tif (min > max) {\n\t\t\t\tmax += limit;\n\t\t\t}\n\t\t\tfor (let i = min; i <= max; i++) {\n\t\t\t\tlet add = 0;\n\t\t\t\tif (status == false && i % limit == 0) {\n\t\t\t\t\tadd = limit;\n\t\t\t\t}\n\t\t\t\tarr.push(Math.round(i % limit + add))\n\t\t\t}\n\t\t\tarr.sort(this.compare)\n\t\t\treturn arr;\n\t\t},\n\t\t// 比较数字大小（用于Array.sort）\n\t\tcompare(value1, value2) {\n\t\t\tif (value2 - value1 > 0) {\n\t\t\t\treturn -1;\n\t\t\t} else {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t},\n\t\t// 格式化日期格式如：2017-9-19 18:04:33\n\t\tformatDate(value, type) {\n\t\t\t// 计算日期相关值\n\t\t\tlet time = typeof value == 'number' ? new Date(value) : value;\n\t\t\tlet Y = time.getFullYear();\n\t\t\tlet M = time.getMonth() + 1;\n\t\t\tlet D = time.getDate();\n\t\t\tlet h = time.getHours();\n\t\t\tlet m = time.getMinutes();\n\t\t\tlet s = time.getSeconds();\n\t\t\tlet week = time.getDay();\n\t\t\t// 如果传递了type的话\n\t\t\tif (type == undefined) {\n\t\t\t\treturn Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);\n\t\t\t} else if (type == 'week') {\n\t\t\t\t// 在quartz中 1为星期日\n\t\t\t\treturn week + 1;\n\t\t\t}\n\t\t},\n\t\t// 检查日期是否存在\n\t\tcheckDate(value) {\n\t\t\tlet time = new Date(value);\n\t\t\tlet format = this.formatDate(time)\n\t\t\treturn value === format;\n\t\t}\n\t},\n\twatch: {\n\t\t'ex': 'expressionChange'\n\t},\n\tprops: ['ex'],\n\tmounted: function () {\n\t\t// 初始化 获取一次结果\n\t\tthis.expressionChange();\n\t}\n}\n\n</script>\n"]}]}