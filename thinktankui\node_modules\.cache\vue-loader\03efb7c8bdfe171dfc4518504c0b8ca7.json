{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\authUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\role\\authUser.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["authUser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "authUser.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n     <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n        <el-input\n          v-model=\"queryParams.phonenumber\"\n          placeholder=\"请输入手机号码\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"openSelectUser\"\n          v-hasPermi=\"['system:role:add']\"\n        >添加用户</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-circle-close\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"cancelAuthUserAll\"\n          v-hasPermi=\"['system:role:remove']\"\n        >批量取消授权</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-close\"\n          size=\"mini\"\n          @click=\"handleClose\"\n        >关闭</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-circle-close\"\n            @click=\"cancelAuthUser(scope.row)\"\n            v-hasPermi=\"['system:role:remove']\"\n          >取消授权</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n    <select-user ref=\"select\" :roleId=\"queryParams.roleId\" @ok=\"handleQuery\" />\n  </div>\n</template>\n\n<script>\nimport { allocatedUserList, authUserCancel, authUserCancelAll } from \"@/api/system/role\";\nimport selectUser from \"./selectUser\";\n\nexport default {\n  name: \"AuthUser\",\n  dicts: ['sys_normal_disable'],\n  components: { selectUser },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中用户组\n      userIds: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleId: undefined,\n        userName: undefined,\n        phonenumber: undefined\n      }\n    };\n  },\n  created() {\n    const roleId = this.$route.params && this.$route.params.roleId;\n    if (roleId) {\n      this.queryParams.roleId = roleId;\n      this.getList();\n    }\n  },\n  methods: {\n    /** 查询授权用户列表 */\n    getList() {\n      this.loading = true;\n      allocatedUserList(this.queryParams).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 返回按钮\n    handleClose() {\n      const obj = { path: \"/system/role\" };\n      this.$tab.closeOpenPage(obj);\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userIds = selection.map(item => item.userId)\n      this.multiple = !selection.length\n    },\n    /** 打开授权用户表弹窗 */\n    openSelectUser() {\n      this.$refs.select.show();\n    },\n    /** 取消授权按钮操作 */\n    cancelAuthUser(row) {\n      const roleId = this.queryParams.roleId;\n      this.$modal.confirm('确认要取消该用户\"' + row.userName + '\"角色吗？').then(function() {\n        return authUserCancel({ userId: row.userId, roleId: roleId });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"取消授权成功\");\n      }).catch(() => {});\n    },\n    /** 批量取消授权按钮操作 */\n    cancelAuthUserAll(row) {\n      const roleId = this.queryParams.roleId;\n      const userIds = this.userIds.join(\",\");\n      this.$modal.confirm('是否取消选中用户授权数据项？').then(function() {\n        return authUserCancelAll({ roleId: roleId, userIds: userIds });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"取消授权成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>"]}]}