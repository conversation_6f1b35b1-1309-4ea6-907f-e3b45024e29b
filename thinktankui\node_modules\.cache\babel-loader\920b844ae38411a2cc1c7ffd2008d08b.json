{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!H:\\项目\\金刚\\3\\thinktankui\\src\\api\\news.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\api\\news.js", "mtime": 1749114898218}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749105971115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getNewsList", "query", "request", "url", "method", "params", "getNewsDetail", "newsId", "concat", "getLatestNews", "limit", "arguments", "length", "undefined", "getHotNews", "getNewsStatistics", "searchNewsByKeyword", "keyword", "pageNum", "pageSize", "getNewsByEntity", "entityName", "getNewsBySentiment", "sentiment", "advancedSearchNews"], "sources": ["H:/项目/金刚/3/thinktankui/src/api/news.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取新闻列表\nexport function getNewsList(query) {\n  return request({\n    url: '/news/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取新闻详情\nexport function getNewsDetail(newsId) {\n  return request({\n    url: `/news/${newsId}`,\n    method: 'get'\n  })\n}\n\n// 获取最新新闻\nexport function getLatestNews(limit = 10) {\n  return request({\n    url: '/news/latest',\n    method: 'get',\n    params: { limit }\n  })\n}\n\n// 获取热门新闻\nexport function getHotNews(limit = 10) {\n  return request({\n    url: '/news/hot',\n    method: 'get',\n    params: { limit }\n  })\n}\n\n// 获取新闻统计数据\nexport function getNewsStatistics() {\n  return request({\n    url: '/news/statistics',\n    method: 'get'\n  })\n}\n\n// 根据关键词搜索新闻\nexport function searchNewsByKeyword(keyword, pageNum = 1, pageSize = 10) {\n  return request({\n    url: '/news/search/keyword',\n    method: 'get',\n    params: {\n      keyword,\n      pageNum,\n      pageSize\n    }\n  })\n}\n\n// 根据实体名称获取相关新闻\nexport function getNewsByEntity(entityName, pageNum = 1, pageSize = 10) {\n  return request({\n    url: `/news/entity/${entityName}`,\n    method: 'get',\n    params: {\n      pageNum,\n      pageSize\n    }\n  })\n}\n\n// 根据情感倾向获取新闻\nexport function getNewsBySentiment(sentiment, pageNum = 1, pageSize = 10) {\n  return request({\n    url: `/news/sentiment/${sentiment}`,\n    method: 'get',\n    params: {\n      pageNum,\n      pageSize\n    }\n  })\n}\n\n// 新闻高级搜索\nexport function advancedSearchNews(query) {\n  return request({\n    url: '/news/list',\n    method: 'get',\n    params: query\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,WAAAK,MAAA,CAAWD,MAAM,CAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAAA,EAAa;EAAA,IAAZC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACtC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MAAEK,KAAK,EAALA;IAAM;EAClB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAa;EAAA,IAAZJ,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACnC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MAAEK,KAAK,EAALA;IAAM;EAClB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,mBAAmBA,CAACC,OAAO,EAA8B;EAAA,IAA5BC,OAAO,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEQ,QAAQ,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACrE,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MACNY,OAAO,EAAPA,OAAO;MACPC,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACC,UAAU,EAA8B;EAAA,IAA5BH,OAAO,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEQ,QAAQ,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACpE,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,kBAAAK,MAAA,CAAkBa,UAAU,CAAE;IACjCjB,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MACNa,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,kBAAkBA,CAACC,SAAS,EAA8B;EAAA,IAA5BL,OAAO,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEQ,QAAQ,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACtE,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,qBAAAK,MAAA,CAAqBe,SAAS,CAAE;IACnCnB,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MACNa,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACvB,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}