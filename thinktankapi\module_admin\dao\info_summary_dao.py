from datetime import datetime, time
from sqlalchemy import delete, select, update, func, and_, or_, case
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.info_summary_do import InfoSummary
from module_admin.entity.vo.info_summary_vo import InfoSummaryModel, InfoSummaryPageQueryModel
from utils.page_util import PageUtil


class InfoSummaryDao:
    """
    信息汇总管理模块数据库操作层
    """

    @classmethod
    async def get_info_summary_detail_by_id(cls, db: AsyncSession, info_id: int):
        """
        根据信息ID获取信息详细信息

        :param db: orm对象
        :param info_id: 信息ID
        :return: 信息对象
        """
        info = (await db.execute(select(InfoSummary).where(InfoSummary.id == info_id))).scalars().first()
        return info

    @classmethod
    async def get_info_summary_list(cls, db: AsyncSession, query_object: InfoSummaryPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取信息汇总列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 信息列表对象
        """
        # 构建基础查询
        query = select(InfoSummary)
        
        # 构建查询条件
        conditions = []
        
        # 标题关键词搜索
        if query_object.title:
            conditions.append(InfoSummary.title.like(f'%{query_object.title}%'))
        
        # 关键词搜索（在标题、内容、摘要中搜索）
        if query_object.keyword:
            keyword_condition = or_(
                InfoSummary.title.like(f'%{query_object.keyword}%'),
                InfoSummary.content.like(f'%{query_object.keyword}%'),
                InfoSummary.summary.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        
        # 平台类型筛选
        if query_object.platform_types and len(query_object.platform_types) > 0:
            conditions.append(InfoSummary.platform_type.in_(query_object.platform_types))
        elif query_object.platform_type:
            conditions.append(InfoSummary.platform_type == query_object.platform_type)
        
        # 情感类型筛选
        if query_object.sentiment_types and len(query_object.sentiment_types) > 0:
            conditions.append(InfoSummary.sentiment.in_(query_object.sentiment_types))
        elif query_object.sentiment:
            conditions.append(InfoSummary.sentiment == query_object.sentiment)
        
        # 信息属性筛选
        if query_object.info_attributes and len(query_object.info_attributes) > 0:
            conditions.append(InfoSummary.info_attribute.in_(query_object.info_attributes))
        elif query_object.info_attribute:
            conditions.append(InfoSummary.info_attribute == query_object.info_attribute)
        
        # 实体类型和名称筛选
        if query_object.entity_type:
            conditions.append(InfoSummary.entity_type == query_object.entity_type)
        if query_object.entity_name:
            conditions.append(InfoSummary.entity_name.like(f'%{query_object.entity_name}%'))
        
        # 创建者筛选
        if query_object.create_by:
            conditions.append(InfoSummary.create_by.like(f'%{query_object.create_by}%'))
        
        # 时间范围筛选
        if query_object.begin_time and query_object.end_time:
            begin_datetime = datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(0, 0, 0))
            end_datetime = datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            conditions.append(InfoSummary.create_time.between(begin_datetime, end_datetime))
        
        # 状态筛选（默认只查询正常状态）
        if query_object.status:
            conditions.append(InfoSummary.status == query_object.status)
        else:
            conditions.append(InfoSummary.status == '1')
        
        # 应用所有条件
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(InfoSummary.publish_time.desc(), InfoSummary.id.desc()).distinct()
        
        # 分页查询
        info_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)
        
        return info_list

    @classmethod
    async def add_info_summary_dao(cls, db: AsyncSession, info: InfoSummaryModel):
        """
        新增信息数据库操作

        :param db: orm对象
        :param info: 信息对象
        :return:
        """
        db_info = InfoSummary(**info.model_dump())
        db.add(db_info)
        await db.flush()
        return db_info

    @classmethod
    async def edit_info_summary_dao(cls, db: AsyncSession, info: dict):
        """
        编辑信息数据库操作

        :param db: orm对象
        :param info: 需要更新的信息字典
        :return:
        """
        await db.execute(update(InfoSummary), [info])

    @classmethod
    async def delete_info_summary_dao(cls, db: AsyncSession, info: InfoSummaryModel):
        """
        删除信息数据库操作

        :param db: orm对象
        :param info: 信息对象
        :return:
        """
        await db.execute(delete(InfoSummary).where(InfoSummary.id.in_([info.id])))

    @classmethod
    async def get_platform_statistics(cls, db: AsyncSession):
        """
        获取平台统计数据

        :param db: orm对象
        :return: 平台统计数据
        """
        # 获取各平台的统计数据
        query = select(
            InfoSummary.platform_type,
            func.count(InfoSummary.id).label('total_count'),
            func.sum(case((InfoSummary.sentiment == 'positive', 1), else_=0)).label('positive_count'),
            func.sum(case((InfoSummary.sentiment == 'neutral', 1), else_=0)).label('neutral_count'),
            func.sum(case((InfoSummary.sentiment == 'negative', 1), else_=0)).label('negative_count'),
            func.sum(case((func.date(InfoSummary.create_time) == func.curdate(), 1), else_=0)).label('today_count')
        ).where(InfoSummary.status == '1').group_by(InfoSummary.platform_type)
        
        result = await db.execute(query)
        return result.fetchall()
