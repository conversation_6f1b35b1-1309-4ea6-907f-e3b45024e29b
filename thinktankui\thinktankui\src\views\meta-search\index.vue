<template>
  <div class="meta-search-container">
    <!-- 顶部搜索区域 -->
    <div class="search-header">
      <div class="search-tabs">
        <div
          class="tab"
          :class="{ active: activeTab === 'fulltext' }"
          @click="switchTab('fulltext')"
        >
          全文检索
        </div>
        <div
          class="tab"
          :class="{ active: activeTab === 'meta' }"
          @click="switchTab('meta')"
        >
          元搜索
        </div>
      </div>

      <div class="search-box">
        <input
          type="text"
          class="search-input"
          v-model="searchKeyword"
          placeholder="请输入搜索关键词"
          @keyup.enter="handleSearch"
        />
        <el-button type="primary" class="search-btn" @click="handleSearch">搜索</el-button>
      </div>
    </div>

    <!-- 全文检索结果 -->
    <div v-if="activeTab === 'fulltext' && hasSearched" class="fulltext-results">
      <!-- 筛选条件区域 -->
      <div class="filter-section">
        <!-- 时间筛选 -->
        <div class="filter-row">
          <span class="filter-label">时间范围:</span>
          <div class="filter-options">
            <el-button
              v-for="time in timeOptions"
              :key="time.value"
              :type="selectedTime === time.value ? 'primary' : ''"
              size="small"
              @click="selectTime(time.value)"
            >
              {{ time.label }}
            </el-button>
          </div>
        </div>

        <!-- 平台筛选 -->
        <div class="filter-row">
          <span class="filter-label">平台类型:</span>
          <div class="filter-options">
            <el-button
              v-for="platform in platformOptions"
              :key="platform.value"
              :type="selectedPlatform === platform.value ? 'primary' : ''"
              size="small"
              @click="selectPlatform(platform.value)"
            >
              {{ platform.label }}
              <span v-if="platform.count" class="count">({{ platform.count }})</span>
            </el-button>
          </div>
        </div>

        <!-- 情感筛选 -->
        <div class="filter-row">
          <span class="filter-label">情感类型:</span>
          <div class="filter-options">
            <el-button
              v-for="emotion in emotionOptions"
              :key="emotion.value"
              :type="selectedEmotion === emotion.value ? 'primary' : ''"
              size="small"
              @click="selectEmotion(emotion.value)"
            >
              {{ emotion.label }}
              <span v-if="emotion.count" class="count">({{ emotion.count }})</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 结果统计 -->
      <div class="result-stats">
        <span>共{{ totalResults }}条结果</span>
        <div class="action-buttons">
          <el-button size="small">导出</el-button>
          <el-button type="primary" size="small">分析</el-button>
        </div>
      </div>

      <!-- 搜索结果列表 -->
      <div class="results-list">
        <div v-for="(item, index) in searchResults" :key="index" class="result-item">
          <div class="result-header">
            <h3 class="result-title">{{ item.title }}</h3>
            <div class="result-actions">
              <el-button type="text" icon="el-icon-view"></el-button>
            </div>
          </div>

          <div class="result-meta">
            <span class="meta-item">{{ item.source }}</span>
            <span class="meta-item">{{ item.publishTime }}</span>
            <span class="meta-item">{{ item.author }}</span>
            <span class="meta-item">{{ item.platform }}</span>
            <span class="meta-item">阅读量: {{ item.readCount }}</span>
            <span class="meta-item">{{ item.location }}</span>
            <span class="meta-item">{{ item.category }}</span>
          </div>

          <div class="result-content">
            {{ item.content }}
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalResults"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          @current-change="handlePageChange"
        ></el-pagination>
      </div>
    </div>

    <!-- 元搜索结果 -->
    <div v-if="activeTab === 'meta' && hasSearched" class="meta-results">
      <!-- 搜索引擎选项卡 -->
      <div class="search-engines">
        <div
          v-for="engine in searchEngines"
          :key="engine.id"
          class="engine-item"
          :class="{ active: engine.id === activeEngine }"
          @click="toggleEngine(engine.id)"
        >
          <div class="checkbox">
            <i class="el-icon-check" v-if="selectedEngines.includes(engine.id)"></i>
          </div>
          <img :src="engine.icon" :alt="engine.name" class="engine-icon" />
          <span class="engine-name">{{ engine.name }}</span>
        </div>
      </div>

      <!-- 搜索结果展示区 -->
      <div class="results-container">
        <!-- 左侧搜索结果 -->
        <div class="result-column" v-if="selectedEngines.includes('bing')">
          <div class="result-header">
            <img src="https://www.bing.com/favicon.ico" alt="Microsoft Bing" class="result-icon" />
            <span class="result-title">Microsoft Bing</span>
          </div>
          <div class="result-list">
            <div class="result-item" v-for="(item, index) in bingResults" :key="'bing-'+index">
              <h3 class="item-title">
                <a :href="item.link" target="_blank">{{ item.title }}</a>
              </h3>
              <div class="item-url">{{ item.url }}</div>
              <div class="item-desc">{{ item.description }}</div>
            </div>
          </div>
        </div>

        <!-- 中间搜索结果 -->
        <div class="result-column" v-if="selectedEngines.includes('baidu')">
          <div class="result-header">
            <img src="https://www.baidu.com/favicon.ico" alt="百度搜索" class="result-icon" />
            <span class="result-title">百度搜索</span>
          </div>
          <div class="result-list">
            <div class="result-item" v-for="(item, index) in baiduResults" :key="'baidu-'+index">
              <h3 class="item-title">
                <a :href="item.link" target="_blank">{{ item.title }}</a>
              </h3>
              <div class="item-url">{{ item.url }}</div>
              <div class="item-desc">{{ item.description }}</div>
            </div>
          </div>
        </div>

        <!-- 右侧搜索结果 -->
        <div class="result-column" v-if="selectedEngines.includes('360')">
          <div class="result-header">
            <img src="https://www.so.com/favicon.ico" alt="360搜索" class="result-icon" />
            <span class="result-title">360搜索</span>
          </div>
          <div class="result-list">
            <div class="result-item" v-for="(item, index) in so360Results" :key="'360-'+index">
              <h3 class="item-title">
                <a :href="item.link" target="_blank">{{ item.title }}</a>
              </h3>
              <div class="item-url">{{ item.url }}</div>
              <div class="item-desc">{{ item.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MetaSearch',
  data() {
    return {
      activeTab: 'fulltext', // 默认显示全文检索
      searchKeyword: '方太',
      hasSearched: true,

      // 全文检索相关数据
      selectedTime: '24h',
      selectedPlatform: 'all',
      selectedEmotion: 'all',
      currentPage: 1,
      pageSize: 10,
      totalResults: 10000,

      timeOptions: [
        { label: '24小时', value: '24h' },
        { label: '一周', value: '1w' },
        { label: '半年', value: '6m' },
        { label: '一年', value: '1y' },
        { label: '自定义', value: 'custom' }
      ],

      platformOptions: [
        { label: '全部', value: 'all', count: 10540 },
        { label: '微信', value: 'wechat', count: 1847 },
        { label: '微博', value: 'weibo', count: 2008 },
        { label: '客户端', value: 'app', count: 1748 },
        { label: '论坛', value: 'forum', count: 673 }
      ],

      emotionOptions: [
        { label: '全部', value: 'all' },
        { label: '正面', value: 'positive' },
        { label: '负面', value: 'negative' },
        { label: '中性', value: 'neutral' }
      ],

      searchResults: [
        {
          title: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...',
          source: '新华网',
          publishTime: '2022-06-29 20:07:04',
          author: '77人讨论',
          platform: '平台来源',
          readCount: '无',
          location: '无所在地',
          category: '新闻',
          content: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...'
        },
        {
          title: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文',
          source: '中大论文发表',
          publishTime: '2022-06-29 20:07:04',
          author: '77人讨论',
          platform: '平台来源',
          readCount: '无',
          location: '无所在地',
          category: '论文',
          content: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...'
        },
        {
          title: '转发微博#中#大学生，人情世故。',
          source: '微博',
          publishTime: '2022-06-29 20:07:04',
          author: '77人讨论',
          platform: '微博',
          readCount: '1000',
          location: '北京',
          category: '社交媒体',
          content: '转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...'
        }
      ],

      // 元搜索相关数据
      activeEngine: 'bing',
      selectedEngines: ['bing', 'baidu', '360'],
      searchEngines: [
        { id: 'bing', name: 'Microsoft Bing', icon: 'https://www.bing.com/favicon.ico' },
        { id: 'baidu', name: '百度搜索', icon: 'https://www.baidu.com/favicon.ico' },
        { id: '360', name: '360搜索', icon: 'https://www.so.com/favicon.ico' }
      ],
      bingResults: [
        {
          title: '方太官网_高端全场景厨电',
          url: 'https://www.fotile.com',
          link: 'https://www.fotile.com',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '【方太集团官网】',
          url: 'https://www.fotile.com/about',
          link: 'https://www.fotile.com/about',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '不平凡',
          url: 'https://www.fotile.com/product',
          link: 'https://www.fotile.com/product',
          description: '方太高端厨电，专注高端厨房电器20年，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        }
      ],
      baiduResults: [
        {
          title: '方太官网_高端全场景厨电',
          url: 'https://www.fotile.com',
          link: 'https://www.fotile.com',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '方太厨电 高端集成厨房电器品牌',
          url: 'https://www.fotile.com/product',
          link: 'https://www.fotile.com/product',
          description: '全国服务热线：400-315-0000 方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'
        },
        {
          title: '方太 - 百度百科',
          url: 'https://baike.baidu.com/item/方太/1830',
          link: 'https://baike.baidu.com/item/方太/1830',
          description: '方太，是中国高端厨电领导品牌，创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'
        }
      ],
      so360Results: [
        {
          title: '方太官网_高端全场景厨电',
          url: 'https://www.fotile.com',
          link: 'https://www.fotile.com',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '方太厨电旗舰店-天猫',
          url: 'https://fotile.tmall.com',
          link: 'https://fotile.tmall.com',
          description: '方太厨电旗舰店,提供方太油烟机,方太燃气灶,方太消毒柜,方太洗碗机,方太蒸箱,方太烤箱,方太微波炉,方太水槽洗碗机等产品。天猫正品保障,提供...'
        },
        {
          title: '方太集团有限公司',
          url: 'https://www.fotile.com/about',
          link: 'https://www.fotile.com/about',
          description: '方太集团有限公司创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'
        }
      ]
    }
  },
  methods: {
    // 标签页切换
    switchTab(tab) {
      this.activeTab = tab
    },

    // 搜索功能
    handleSearch() {
      this.hasSearched = true
      // 实际项目中这里应该调用API获取搜索结果
      console.log('搜索关键词:', this.searchKeyword)
      this.$message.success(`搜索: ${this.searchKeyword}`)
    },

    // 全文检索筛选方法
    selectTime(value) {
      this.selectedTime = value
      this.handleSearch()
    },

    selectPlatform(value) {
      this.selectedPlatform = value
      this.handleSearch()
    },

    selectEmotion(value) {
      this.selectedEmotion = value
      this.handleSearch()
    },

    handlePageChange(page) {
      this.currentPage = page
      // 加载对应页面数据
    },

    // 元搜索引擎切换
    toggleEngine(engineId) {
      // 切换选中状态
      if (this.selectedEngines.includes(engineId)) {
        // 如果已经选中，且不是最后一个选中的引擎，则取消选中
        if (this.selectedEngines.length > 1) {
          this.selectedEngines = this.selectedEngines.filter(id => id !== engineId)
        }
      } else {
        // 如果未选中，则添加到选中列表
        this.selectedEngines.push(engineId)
      }

      // 设置当前活动引擎
      this.activeEngine = engineId
    }
  }
}
</script>

<style scoped>
.meta-search-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.search-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-tabs {
  display: flex;
  margin-bottom: 20px;
}

.tab {
  padding: 8px 16px;
  margin-right: 10px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab.active {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  font-weight: bold;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 40px;
  padding: 0 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-right: 10px;
  font-size: 14px;
}

.search-btn {
  height: 40px;
}

.search-results {
  display: flex;
  flex-direction: column;
}

.search-engines {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.engine-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;
  background: #fff;
}

.engine-item:hover {
  border-color: #c6e2ff;
}

.engine-item.active {
  background: #ecf5ff;
  color: #409EFF;
  border-color: #409EFF;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #409EFF;
}

.engine-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.results-container {
  display: flex;
  gap: 20px;
}

.result-column {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
}

.result-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.result-title {
  font-weight: bold;
  color: #303133;
}

.result-list {
  padding: 15px;
}

.result-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.result-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-title {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.item-title a {
  color: #0366d6;
  text-decoration: none;
}

.item-title a:hover {
  text-decoration: underline;
}

.item-url {
  color: #67c23a;
  font-size: 12px;
  margin-bottom: 8px;
}

.item-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

/* 全文检索样式 */
.fulltext-results {
  margin-top: 20px;
}

.filter-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.count {
  color: #999;
  font-size: 12px;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 15px 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.results-list {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.results-list .result-item {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.results-list .result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.result-title {
  font-size: 16px;
  color: #333;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 20px;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #999;
}

.meta-item {
  white-space: nowrap;
}

.result-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
