{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\PieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\dashboard\\PieChart.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_resize", "_interopRequireDefault", "_default", "exports", "default", "mixins", "resize", "props", "className", "type", "String", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "formatter", "legend", "left", "bottom", "series", "name", "roseType", "radius", "center", "value", "animationEasing", "animationDuration"], "sources": ["src/views/dashboard/PieChart.vue"], "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\n        },\n        legend: {\n          left: 'center',\n          bottom: '10',\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\n        },\n        series: [\n          {\n            name: 'WEEKLY WRITE ARTICLES',\n            type: 'pie',\n            roseType: 'radius',\n            radius: [15, 95],\n            center: ['50%', '38%'],\n            data: [\n              { value: 320, name: 'Industries' },\n              { value: 240, name: 'Technology' },\n              { value: 149, name: 'Forex' },\n              { value: 100, name: 'Gold' },\n              { value: 59, name: 'Forecasts' }\n            ],\n            animationEasing: 'cubicInOut',\n            animationDuration: 2600\n          }\n        ]\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;AADAA,OAAA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAO,KAAA;MACAF,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAQ,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAjB,OAAA,CAAAyB,IAAA,MAAAC,GAAA;MAEA,KAAAT,KAAA,CAAAU,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,IAAA;UACAC,MAAA;UACAjB,IAAA;QACA;QACAkB,MAAA,GACA;UACAC,IAAA;UACAvB,IAAA;UACAwB,QAAA;UACAC,MAAA;UACAC,MAAA;UACAtB,IAAA,GACA;YAAAuB,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,EACA;UACAK,eAAA;UACAC,iBAAA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}