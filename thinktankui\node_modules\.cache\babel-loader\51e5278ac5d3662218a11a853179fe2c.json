{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\blots\\scroll.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\blots\\scroll.js", "mtime": 1749105929576}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireWildcard", "_emitter", "_interopRequireDefault", "_block", "_break", "_container", "isLine", "blot", "Block", "BlockEmbed", "isUpdatable", "updateContent", "<PERSON><PERSON>", "_ScrollBlot", "registry", "domNode", "_ref", "_this", "_classCallCheck2", "default", "emitter", "_callSuper2", "batch", "optimize", "enable", "addEventListener", "e", "handleDragStart", "_inherits2", "_createClass2", "key", "value", "batchStart", "Array", "isArray", "batchEnd", "mutations", "update", "emitMount", "emit", "Emitter", "events", "SCROLL_BLOT_MOUNT", "emitUnmount", "SCROLL_BLOT_UNMOUNT", "emitEmbedUpdate", "change", "SCROLL_EMBED_UPDATE", "deleteAt", "index", "length", "_this$line", "line", "_this$line2", "_slicedToArray2", "first", "offset", "_this$line3", "_this$line4", "last", "_superPropGet2", "ref", "children", "head", "Break", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove", "enabled", "arguments", "undefined", "setAttribute", "formatAt", "format", "insertAt", "def", "scroll", "query", "<PERSON><PERSON>", "BLOCK", "create", "statics", "defaultChild", "blotName", "append<PERSON><PERSON><PERSON>", "endsWith", "slice", "embed", "insertBefore", "scope", "INLINE_BLOT", "wrapper", "insertContents", "delta", "_this2", "renderBlocks", "deltaToRenderBlocks", "concat", "Delta", "insert", "pop", "shift", "shouldInsertNewlineChar", "type", "descendant", "_defineProperty2", "insertInlineContents", "newlineCharLength", "lineEndIndex", "formats", "bubbleFormats", "attributes", "AttributeMap", "diff", "Object", "keys", "for<PERSON>ach", "name", "_this$children$find", "find", "_this$children$find2", "refBlot", "refBlotOffset", "split", "renderBlock", "block", "createBlock", "blockEmbed", "isEnabled", "getAttribute", "leaf", "path", "_last", "LeafBlot", "lines", "Number", "MAX_VALUE", "getLines", "blotIndex", "blotLength", "lengthLeft", "forEachAt", "child", "childIndex", "<PERSON><PERSON><PERSON><PERSON>", "push", "ContainerBlot", "context", "SCROLL_OPTIMIZE", "_this3", "source", "sources", "USER", "observer", "takeRecords", "filter", "_ref2", "target", "SCROLL_BEFORE_UPDATE", "SCROLL_UPDATE", "updateEmbedAt", "_this$descendant", "b", "_this$descendant2", "event", "preventDefault", "_this4", "currentBlockDelta", "op", "splitted", "text", "_op$attributes", "INLINE", "_op$attributes2", "_this5", "entries", "_ref3", "_ref5", "isBlockBlot", "BLOT", "_ref4", "_ref6", "ScrollBlot", "Container", "parent", "inlineContents", "reduce", "Op", "_parent$descendant", "_parent$descendant2", "_typeof2", "isInlineEmbed", "_parent$descendant3", "_parent$descendant4", "_default", "exports"], "sources": ["../../src/blots/scroll.ts"], "sourcesContent": ["import { Container<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ScrollBlot } from 'parchment';\nimport type { Blot, Parent, EmbedBlot, ParentBlot, Registry } from 'parchment';\nimport Delta, { AttributeMap, Op } from 'quill-delta';\nimport Emitter from '../core/emitter.js';\nimport type { EmitterSource } from '../core/emitter.js';\nimport Block, { BlockEmbed, bubbleFormats } from './block.js';\nimport Break from './break.js';\nimport Container from './container.js';\n\ntype RenderBlock =\n  | {\n      type: 'blockEmbed';\n      attributes: AttributeMap;\n      key: string;\n      value: unknown;\n    }\n  | { type: 'block'; attributes: AttributeMap; delta: Delta };\n\nfunction isLine(blot: unknown): blot is Block | BlockEmbed {\n  return blot instanceof Block || blot instanceof BlockEmbed;\n}\n\ninterface UpdatableEmbed {\n  updateContent(change: unknown): void;\n}\n\nfunction isUpdatable(blot: Blot): blot is Blot & UpdatableEmbed {\n  return typeof (blot as unknown as any).updateContent === 'function';\n}\n\nclass Scroll extends ScrollBlot {\n  static blotName = 'scroll';\n  static className = 'ql-editor';\n  static tagName = 'DIV';\n  static defaultChild = Block;\n  static allowedChildren = [Block, BlockEmbed, Container];\n\n  emitter: Emitter;\n  batch: false | MutationRecord[];\n\n  constructor(\n    registry: Registry,\n    domNode: HTMLDivElement,\n    { emitter }: { emitter: Emitter },\n  ) {\n    super(registry, domNode);\n    this.emitter = emitter;\n    this.batch = false;\n    this.optimize();\n    this.enable();\n    this.domNode.addEventListener('dragstart', (e) => this.handleDragStart(e));\n  }\n\n  batchStart() {\n    if (!Array.isArray(this.batch)) {\n      this.batch = [];\n    }\n  }\n\n  batchEnd() {\n    if (!this.batch) return;\n    const mutations = this.batch;\n    this.batch = false;\n    this.update(mutations);\n  }\n\n  emitMount(blot: Blot) {\n    this.emitter.emit(Emitter.events.SCROLL_BLOT_MOUNT, blot);\n  }\n\n  emitUnmount(blot: Blot) {\n    this.emitter.emit(Emitter.events.SCROLL_BLOT_UNMOUNT, blot);\n  }\n\n  emitEmbedUpdate(blot: Blot, change: unknown) {\n    this.emitter.emit(Emitter.events.SCROLL_EMBED_UPDATE, blot, change);\n  }\n\n  deleteAt(index: number, length: number) {\n    const [first, offset] = this.line(index);\n    const [last] = this.line(index + length);\n    super.deleteAt(index, length);\n    if (last != null && first !== last && offset > 0) {\n      if (first instanceof BlockEmbed || last instanceof BlockEmbed) {\n        this.optimize();\n        return;\n      }\n      const ref =\n        last.children.head instanceof Break ? null : last.children.head;\n      // @ts-expect-error\n      first.moveChildren(last, ref);\n      // @ts-expect-error\n      first.remove();\n    }\n    this.optimize();\n  }\n\n  enable(enabled = true) {\n    this.domNode.setAttribute('contenteditable', enabled ? 'true' : 'false');\n  }\n\n  formatAt(index: number, length: number, format: string, value: unknown) {\n    super.formatAt(index, length, format, value);\n    this.optimize();\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (index >= this.length()) {\n      if (def == null || this.scroll.query(value, Scope.BLOCK) == null) {\n        const blot = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(blot);\n        if (def == null && value.endsWith('\\n')) {\n          blot.insertAt(0, value.slice(0, -1), def);\n        } else {\n          blot.insertAt(0, value, def);\n        }\n      } else {\n        const embed = this.scroll.create(value, def);\n        this.appendChild(embed);\n      }\n    } else {\n      super.insertAt(index, value, def);\n    }\n    this.optimize();\n  }\n\n  insertBefore(blot: Blot, ref?: Blot | null) {\n    if (blot.statics.scope === Scope.INLINE_BLOT) {\n      const wrapper = this.scroll.create(\n        this.statics.defaultChild.blotName,\n      ) as Parent;\n      wrapper.appendChild(blot);\n      super.insertBefore(wrapper, ref);\n    } else {\n      super.insertBefore(blot, ref);\n    }\n  }\n\n  insertContents(index: number, delta: Delta) {\n    const renderBlocks = this.deltaToRenderBlocks(\n      delta.concat(new Delta().insert('\\n')),\n    );\n    const last = renderBlocks.pop();\n    if (last == null) return;\n\n    this.batchStart();\n\n    const first = renderBlocks.shift();\n    if (first) {\n      const shouldInsertNewlineChar =\n        first.type === 'block' &&\n        (first.delta.length() === 0 ||\n          (!this.descendant(BlockEmbed, index)[0] && index < this.length()));\n      const delta =\n        first.type === 'block'\n          ? first.delta\n          : new Delta().insert({ [first.key]: first.value });\n      insertInlineContents(this, index, delta);\n      const newlineCharLength = first.type === 'block' ? 1 : 0;\n      const lineEndIndex = index + delta.length() + newlineCharLength;\n      if (shouldInsertNewlineChar) {\n        this.insertAt(lineEndIndex - 1, '\\n');\n      }\n\n      const formats = bubbleFormats(this.line(index)[0]);\n      const attributes = AttributeMap.diff(formats, first.attributes) || {};\n      Object.keys(attributes).forEach((name) => {\n        this.formatAt(lineEndIndex - 1, 1, name, attributes[name]);\n      });\n\n      index = lineEndIndex;\n    }\n\n    let [refBlot, refBlotOffset] = this.children.find(index);\n    if (renderBlocks.length) {\n      if (refBlot) {\n        refBlot = refBlot.split(refBlotOffset);\n        refBlotOffset = 0;\n      }\n\n      renderBlocks.forEach((renderBlock) => {\n        if (renderBlock.type === 'block') {\n          const block = this.createBlock(\n            renderBlock.attributes,\n            refBlot || undefined,\n          );\n          insertInlineContents(block, 0, renderBlock.delta);\n        } else {\n          const blockEmbed = this.create(\n            renderBlock.key,\n            renderBlock.value,\n          ) as EmbedBlot;\n          this.insertBefore(blockEmbed, refBlot || undefined);\n          Object.keys(renderBlock.attributes).forEach((name) => {\n            blockEmbed.format(name, renderBlock.attributes[name]);\n          });\n        }\n      });\n    }\n\n    if (last.type === 'block' && last.delta.length()) {\n      const offset = refBlot\n        ? refBlot.offset(refBlot.scroll) + refBlotOffset\n        : this.length();\n      insertInlineContents(this, offset, last.delta);\n    }\n\n    this.batchEnd();\n    this.optimize();\n  }\n\n  isEnabled() {\n    return this.domNode.getAttribute('contenteditable') === 'true';\n  }\n\n  leaf(index: number): [LeafBlot | null, number] {\n    const last = this.path(index).pop();\n    if (!last) {\n      return [null, -1];\n    }\n\n    const [blot, offset] = last;\n    return blot instanceof LeafBlot ? [blot, offset] : [null, -1];\n  }\n\n  line(index: number): [Block | BlockEmbed | null, number] {\n    if (index === this.length()) {\n      return this.line(index - 1);\n    }\n    // @ts-expect-error TODO: make descendant() generic\n    return this.descendant(isLine, index);\n  }\n\n  lines(index = 0, length = Number.MAX_VALUE): (Block | BlockEmbed)[] {\n    const getLines = (\n      blot: ParentBlot,\n      blotIndex: number,\n      blotLength: number,\n    ) => {\n      let lines: (Block | BlockEmbed)[] = [];\n      let lengthLeft = blotLength;\n      blot.children.forEachAt(\n        blotIndex,\n        blotLength,\n        (child, childIndex, childLength) => {\n          if (isLine(child)) {\n            lines.push(child);\n          } else if (child instanceof ContainerBlot) {\n            lines = lines.concat(getLines(child, childIndex, lengthLeft));\n          }\n          lengthLeft -= childLength;\n        },\n      );\n      return lines;\n    };\n    return getLines(this, index, length);\n  }\n\n  optimize(context?: { [key: string]: any }): void;\n  optimize(\n    mutations?: MutationRecord[],\n    context?: { [key: string]: any },\n  ): void;\n  optimize(mutations = [], context = {}) {\n    if (this.batch) return;\n    super.optimize(mutations, context);\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_OPTIMIZE, mutations, context);\n    }\n  }\n\n  path(index: number) {\n    return super.path(index).slice(1); // Exclude self\n  }\n\n  remove() {\n    // Never remove self\n  }\n\n  update(source?: EmitterSource): void;\n  update(mutations?: MutationRecord[]): void;\n  update(mutations?: MutationRecord[] | EmitterSource): void {\n    if (this.batch) {\n      if (Array.isArray(mutations)) {\n        this.batch = this.batch.concat(mutations);\n      }\n      return;\n    }\n    let source: EmitterSource = Emitter.sources.USER;\n    if (typeof mutations === 'string') {\n      source = mutations;\n    }\n    if (!Array.isArray(mutations)) {\n      mutations = this.observer.takeRecords();\n    }\n    mutations = mutations.filter(({ target }) => {\n      const blot = this.find(target, true);\n      return blot && !isUpdatable(blot);\n    });\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_BEFORE_UPDATE, source, mutations);\n    }\n    super.update(mutations.concat([])); // pass copy\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_UPDATE, source, mutations);\n    }\n  }\n\n  updateEmbedAt(index: number, key: string, change: unknown) {\n    // Currently it only supports top-level embeds (BlockEmbed).\n    // We can update `ParentBlot` in parchment to support inline embeds.\n    const [blot] = this.descendant((b: Blot) => b instanceof BlockEmbed, index);\n    if (blot && blot.statics.blotName === key && isUpdatable(blot)) {\n      blot.updateContent(change);\n    }\n  }\n\n  protected handleDragStart(event: DragEvent) {\n    event.preventDefault();\n  }\n\n  private deltaToRenderBlocks(delta: Delta) {\n    const renderBlocks: RenderBlock[] = [];\n\n    let currentBlockDelta = new Delta();\n    delta.forEach((op) => {\n      const insert = op?.insert;\n      if (!insert) return;\n      if (typeof insert === 'string') {\n        const splitted = insert.split('\\n');\n        splitted.slice(0, -1).forEach((text) => {\n          currentBlockDelta.insert(text, op.attributes);\n          renderBlocks.push({\n            type: 'block',\n            delta: currentBlockDelta,\n            attributes: op.attributes ?? {},\n          });\n          currentBlockDelta = new Delta();\n        });\n        const last = splitted[splitted.length - 1];\n        if (last) {\n          currentBlockDelta.insert(last, op.attributes);\n        }\n      } else {\n        const key = Object.keys(insert)[0];\n        if (!key) return;\n        if (this.query(key, Scope.INLINE)) {\n          currentBlockDelta.push(op);\n        } else {\n          if (currentBlockDelta.length()) {\n            renderBlocks.push({\n              type: 'block',\n              delta: currentBlockDelta,\n              attributes: {},\n            });\n          }\n          currentBlockDelta = new Delta();\n          renderBlocks.push({\n            type: 'blockEmbed',\n            key,\n            value: insert[key],\n            attributes: op.attributes ?? {},\n          });\n        }\n      }\n    });\n\n    if (currentBlockDelta.length()) {\n      renderBlocks.push({\n        type: 'block',\n        delta: currentBlockDelta,\n        attributes: {},\n      });\n    }\n\n    return renderBlocks;\n  }\n\n  private createBlock(attributes: AttributeMap, refBlot?: Blot) {\n    let blotName: string | undefined;\n    const formats: AttributeMap = {};\n\n    Object.entries(attributes).forEach(([key, value]) => {\n      const isBlockBlot = this.query(key, Scope.BLOCK & Scope.BLOT) != null;\n      if (isBlockBlot) {\n        blotName = key;\n      } else {\n        formats[key] = value;\n      }\n    });\n\n    const block = this.create(\n      blotName || this.statics.defaultChild.blotName,\n      blotName ? attributes[blotName] : undefined,\n    ) as ParentBlot;\n\n    this.insertBefore(block, refBlot || undefined);\n\n    const length = block.length();\n    Object.entries(formats).forEach(([key, value]) => {\n      block.formatAt(0, length, key, value);\n    });\n\n    return block;\n  }\n}\n\nfunction insertInlineContents(\n  parent: ParentBlot,\n  index: number,\n  inlineContents: Delta,\n) {\n  inlineContents.reduce((index, op) => {\n    const length = Op.length(op);\n    let attributes = op.attributes || {};\n    if (op.insert != null) {\n      if (typeof op.insert === 'string') {\n        const text = op.insert;\n        parent.insertAt(index, text);\n        const [leaf] = parent.descendant(LeafBlot, index);\n        const formats = bubbleFormats(leaf);\n        attributes = AttributeMap.diff(formats, attributes) || {};\n      } else if (typeof op.insert === 'object') {\n        const key = Object.keys(op.insert)[0]; // There should only be one key\n        if (key == null) return index;\n        parent.insertAt(index, key, op.insert[key]);\n        const isInlineEmbed = parent.scroll.query(key, Scope.INLINE) != null;\n        if (isInlineEmbed) {\n          const [leaf] = parent.descendant(LeafBlot, index);\n          const formats = bubbleFormats(leaf);\n          attributes = AttributeMap.diff(formats, attributes) || {};\n        }\n      }\n    }\n    Object.keys(attributes).forEach((key) => {\n      parent.formatAt(index, length, key, attributes[key]);\n    });\n    return index + length;\n  }, index);\n}\n\nexport default Scroll;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,WAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,QAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAEA,IAAAK,MAAA,GAAAH,uBAAA,CAAAF,OAAA;AACA,IAAAM,MAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,UAAA,GAAAH,sBAAA,CAAAJ,OAAA;AAWA,SAASQ,MAAMA,CAACC,IAAa,EAA8B;EACzD,OAAOA,IAAI,YAAYC,cAAK,IAAID,IAAI,YAAYE,iBAAU;AAC5D;AAMA,SAASC,WAAWA,CAACH,IAAU,EAAiC;EAC9D,OAAO,OAAQA,IAAI,CAAoBI,aAAa,KAAK,UAAU;AACrE;AAAA,IAEMC,MAAM,0BAAAC,WAAA;EAUV,SAAAD,OACEE,QAAkB,EAClBC,OAAuB,EAAAC,IAAA,EAEvB;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAP,MAAA;IAAA,IADEQ,OAAA,GAA+BJ,IAAA,CAA/BI,OAAA;IAEFH,KAAA,OAAAI,WAAA,CAAAF,OAAA,QAAAP,MAAA,GAAME,QAAQ,EAAEC,OAAO;IACvBE,KAAA,CAAKG,OAAO,GAAGA,OAAO;IACtBH,KAAA,CAAKK,KAAK,GAAG,KAAK;IAClBL,KAAA,CAAKM,QAAQ,CAAC,CAAC;IACfN,KAAA,CAAKO,MAAM,CAAC,CAAC;IACbP,KAAA,CAAKF,OAAO,CAACU,gBAAgB,CAAC,WAAW,EAAG,UAAAC,CAAC;MAAA,OAAKT,KAAA,CAAKU,eAAe,CAACD,CAAC,CAAC;IAAA,EAAC;IAAA,OAAAT,KAAA;EAC5E;EAAA,IAAAW,UAAA,CAAAT,OAAA,EAAAP,MAAA,EAAAC,WAAA;EAAA,WAAAgB,aAAA,CAAAV,OAAA,EAAAP,MAAA;IAAAkB,GAAA;IAAAC,KAAA,EAEA,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACZ,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACA,KAAK,GAAG,EAAE;MACjB;IACF;EAAA;IAAAQ,GAAA;IAAAC,KAAA,EAEA,SAAAI,QAAQA,CAAA,EAAG;MACT,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE;MACjB,IAAMc,SAAS,GAAG,IAAI,CAACd,KAAK;MAC5B,IAAI,CAACA,KAAK,GAAG,KAAK;MAClB,IAAI,CAACe,MAAM,CAACD,SAAS,CAAC;IACxB;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAO,SAASA,CAAC/B,IAAU,EAAE;MACpB,IAAI,CAACa,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACC,iBAAiB,EAAEnC,IAAI,CAAC;IAC3D;EAAA;IAAAuB,GAAA;IAAAC,KAAA,EAEA,SAAAY,WAAWA,CAACpC,IAAU,EAAE;MACtB,IAAI,CAACa,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACG,mBAAmB,EAAErC,IAAI,CAAC;IAC7D;EAAA;IAAAuB,GAAA;IAAAC,KAAA,EAEA,SAAAc,eAAeA,CAACtC,IAAU,EAAEuC,MAAe,EAAE;MAC3C,IAAI,CAAC1B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACM,mBAAmB,EAAExC,IAAI,EAAEuC,MAAM,CAAC;IACrE;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEA,SAAAiB,QAAQA,CAACC,KAAa,EAAEC,MAAc,EAAE;MACtC,IAAAC,UAAA,GAAwB,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;QAAAI,WAAA,OAAAC,eAAA,CAAAnC,OAAA,EAAAgC,UAAA;QAAjCI,KAAK,GAAAF,WAAA;QAAEG,MAAM,GAAAH,WAAA;MACpB,IAAAI,WAAA,GAAe,IAAI,CAACL,IAAI,CAACH,KAAK,GAAGC,MAAM,CAAC;QAAAQ,WAAA,OAAAJ,eAAA,CAAAnC,OAAA,EAAAsC,WAAA;QAAjCE,IAAI,GAAAD,WAAA;MACX,IAAAE,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAeqC,KAAK,EAAEC,MAAM;MAC5B,IAAIS,IAAI,IAAI,IAAI,IAAIJ,KAAK,KAAKI,IAAI,IAAIH,MAAM,GAAG,CAAC,EAAE;QAChD,IAAID,KAAK,YAAY9C,iBAAU,IAAIkD,IAAI,YAAYlD,iBAAU,EAAE;UAC7D,IAAI,CAACc,QAAQ,CAAC,CAAC;UACf;QACF;QACA,IAAMsC,GAAG,GACPF,IAAI,CAACG,QAAQ,CAACC,IAAI,YAAYC,cAAK,GAAG,IAAI,GAAGL,IAAI,CAACG,QAAQ,CAACC,IAAI;QACjE;QACAR,KAAK,CAACU,YAAY,CAACN,IAAI,EAAEE,GAAG,CAAC;QAC7B;QACAN,KAAK,CAACW,MAAM,CAAC,CAAC;MAChB;MACA,IAAI,CAAC3C,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAP,MAAMA,CAAA,EAAiB;MAAA,IAAhB2C,OAAO,GAAAC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MACnB,IAAI,CAACrD,OAAO,CAACuD,YAAY,CAAC,iBAAiB,EAAEH,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;IAC1E;EAAA;IAAArC,GAAA;IAAAC,KAAA,EAEA,SAAAwC,QAAQA,CAACtB,KAAa,EAAEC,MAAc,EAAEsB,MAAc,EAAEzC,KAAc,EAAE;MACtE,IAAA6B,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAeqC,KAAK,EAAEC,MAAM,EAAEsB,MAAM,EAAEzC,KAAK;MAC3C,IAAI,CAACR,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAA0C,QAAQA,CAACxB,KAAa,EAAElB,KAAa,EAAE2C,GAAa,EAAE;MACpD,IAAIzB,KAAK,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAC1B,IAAIwB,GAAG,IAAI,IAAI,IAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC7C,KAAK,EAAE8C,gBAAK,CAACC,KAAK,CAAC,IAAI,IAAI,EAAE;UAChE,IAAMvE,IAAI,GAAG,IAAI,CAACoE,MAAM,CAACI,MAAM,CAAC,IAAI,CAACC,OAAO,CAACC,YAAY,CAACC,QAAQ,CAAC;UACnE,IAAI,CAACC,WAAW,CAAC5E,IAAI,CAAC;UACtB,IAAImE,GAAG,IAAI,IAAI,IAAI3C,KAAK,CAACqD,QAAQ,CAAC,IAAI,CAAC,EAAE;YACvC7E,IAAI,CAACkE,QAAQ,CAAC,CAAC,EAAE1C,KAAK,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEX,GAAG,CAAC;UAC3C,CAAC,MAAM;YACLnE,IAAI,CAACkE,QAAQ,CAAC,CAAC,EAAE1C,KAAK,EAAE2C,GAAG,CAAC;UAC9B;QACF,CAAC,MAAM;UACL,IAAMY,KAAK,GAAG,IAAI,CAACX,MAAM,CAACI,MAAM,CAAChD,KAAK,EAAE2C,GAAG,CAAC;UAC5C,IAAI,CAACS,WAAW,CAACG,KAAK,CAAC;QACzB;MACF,CAAC,MAAM;QACL,IAAA1B,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAeqC,KAAK,EAAElB,KAAK,EAAE2C,GAAG;MAClC;MACA,IAAI,CAACnD,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAwD,YAAYA,CAAChF,IAAU,EAAEsD,GAAiB,EAAE;MAC1C,IAAItD,IAAI,CAACyE,OAAO,CAACQ,KAAK,KAAKX,gBAAK,CAACY,WAAW,EAAE;QAC5C,IAAMC,OAAO,GAAG,IAAI,CAACf,MAAM,CAACI,MAAM,CAChC,IAAI,CAACC,OAAO,CAACC,YAAY,CAACC,QAC5B,CAAW;QACXQ,OAAO,CAACP,WAAW,CAAC5E,IAAI,CAAC;QACzB,IAAAqD,cAAA,CAAAzC,OAAA,EAAAP,MAAA,4BAAmB8E,OAAO,EAAE7B,GAAG;MACjC,CAAC,MAAM;QACL,IAAAD,cAAA,CAAAzC,OAAA,EAAAP,MAAA,4BAAmBL,IAAI,EAAEsD,GAAG;MAC9B;IACF;EAAA;IAAA/B,GAAA;IAAAC,KAAA,EAEA,SAAA4D,cAAcA,CAAC1C,KAAa,EAAE2C,KAAY,EAAE;MAAA,IAAAC,MAAA;MAC1C,IAAMC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CH,KAAK,CAACI,MAAM,CAAC,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,CACvC,CAAC;MACD,IAAMvC,IAAI,GAAGmC,YAAY,CAACK,GAAG,CAAC,CAAC;MAC/B,IAAIxC,IAAI,IAAI,IAAI,EAAE;MAElB,IAAI,CAAC3B,UAAU,CAAC,CAAC;MAEjB,IAAMuB,KAAK,GAAGuC,YAAY,CAACM,KAAK,CAAC,CAAC;MAClC,IAAI7C,KAAK,EAAE;QACT,IAAM8C,uBAAuB,GAC3B9C,KAAK,CAAC+C,IAAI,KAAK,OAAO,KACrB/C,KAAK,CAACqC,KAAK,CAAC1C,MAAM,CAAC,CAAC,KAAK,CAAC,IACxB,CAAC,IAAI,CAACqD,UAAU,CAAC9F,iBAAU,EAAEwC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAE,CAAC;QACtE,IAAM0C,MAAK,GACTrC,KAAK,CAAC+C,IAAI,KAAK,OAAO,GAClB/C,KAAK,CAACqC,KAAK,GACX,IAAIK,mBAAK,CAAC,CAAC,CAACC,MAAM,KAAAM,gBAAA,CAAArF,OAAA,MAAIoC,KAAK,CAACzB,GAAG,EAAGyB,KAAK,CAACxB,KAAA,CAAO,CAAC;QACtD0E,oBAAoB,CAAC,IAAI,EAAExD,KAAK,EAAE2C,MAAK,CAAC;QACxC,IAAMc,iBAAiB,GAAGnD,KAAK,CAAC+C,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;QACxD,IAAMK,YAAY,GAAG1D,KAAK,GAAG2C,MAAK,CAAC1C,MAAM,CAAC,CAAC,GAAGwD,iBAAiB;QAC/D,IAAIL,uBAAuB,EAAE;UAC3B,IAAI,CAAC5B,QAAQ,CAACkC,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC;QACvC;QAEA,IAAMC,OAAO,GAAG,IAAAC,oBAAa,EAAC,IAAI,CAACzD,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAM6D,UAAU,GAAGC,wBAAY,CAACC,IAAI,CAACJ,OAAO,EAAErD,KAAK,CAACuD,UAAU,CAAC,IAAI,CAAC,CAAC;QACrEG,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAE,UAAAC,IAAI,EAAK;UACxCvB,MAAI,CAACtB,QAAQ,CAACoC,YAAY,GAAG,CAAC,EAAE,CAAC,EAAES,IAAI,EAAEN,UAAU,CAACM,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC;QAEFnE,KAAK,GAAG0D,YAAY;MACtB;MAEA,IAAAU,mBAAA,GAA+B,IAAI,CAACvD,QAAQ,CAACwD,IAAI,CAACrE,KAAK,CAAC;QAAAsE,oBAAA,OAAAjE,eAAA,CAAAnC,OAAA,EAAAkG,mBAAA;QAAnDG,OAAO,GAAAD,oBAAA;QAAEE,aAAa,GAAAF,oBAAA;MAC3B,IAAIzB,YAAY,CAAC5C,MAAM,EAAE;QACvB,IAAIsE,OAAO,EAAE;UACXA,OAAO,GAAGA,OAAO,CAACE,KAAK,CAACD,aAAa,CAAC;UACtCA,aAAa,GAAG,CAAC;QACnB;QAEA3B,YAAY,CAACqB,OAAO,CAAE,UAAAQ,WAAW,EAAK;UACpC,IAAIA,WAAW,CAACrB,IAAI,KAAK,OAAO,EAAE;YAChC,IAAMsB,KAAK,GAAG/B,MAAI,CAACgC,WAAW,CAC5BF,WAAW,CAACb,UAAU,EACtBU,OAAO,IAAInD,SACb,CAAC;YACDoC,oBAAoB,CAACmB,KAAK,EAAE,CAAC,EAAED,WAAW,CAAC/B,KAAK,CAAC;UACnD,CAAC,MAAM;YACL,IAAMkC,UAAU,GAAGjC,MAAI,CAACd,MAAM,CAC5B4C,WAAW,CAAC7F,GAAG,EACf6F,WAAW,CAAC5F,KACd,CAAc;YACd8D,MAAI,CAACN,YAAY,CAACuC,UAAU,EAAEN,OAAO,IAAInD,SAAS,CAAC;YACnD4C,MAAM,CAACC,IAAI,CAACS,WAAW,CAACb,UAAU,CAAC,CAACK,OAAO,CAAE,UAAAC,IAAI,EAAK;cACpDU,UAAU,CAACtD,MAAM,CAAC4C,IAAI,EAAEO,WAAW,CAACb,UAAU,CAACM,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,IAAIzD,IAAI,CAAC2C,IAAI,KAAK,OAAO,IAAI3C,IAAI,CAACiC,KAAK,CAAC1C,MAAM,CAAC,CAAC,EAAE;QAChD,IAAMM,MAAM,GAAGgE,OAAO,GAClBA,OAAO,CAAChE,MAAM,CAACgE,OAAO,CAAC7C,MAAM,CAAC,GAAG8C,aAAa,GAC9C,IAAI,CAACvE,MAAM,CAAC,CAAC;QACjBuD,oBAAoB,CAAC,IAAI,EAAEjD,MAAM,EAAEG,IAAI,CAACiC,KAAK,CAAC;MAChD;MAEA,IAAI,CAACzD,QAAQ,CAAC,CAAC;MACf,IAAI,CAACZ,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAgG,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAAChH,OAAO,CAACiH,YAAY,CAAC,iBAAiB,CAAC,KAAK,MAAM;IAChE;EAAA;IAAAlG,GAAA;IAAAC,KAAA,EAEA,SAAAkG,IAAIA,CAAChF,KAAa,EAA6B;MAC7C,IAAMU,IAAI,GAAG,IAAI,CAACuE,IAAI,CAACjF,KAAK,CAAC,CAACkD,GAAG,CAAC,CAAC;MACnC,IAAI,CAACxC,IAAI,EAAE;QACT,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;MACnB;MAEA,IAAAwE,KAAA,OAAA7E,eAAA,CAAAnC,OAAA,EAAuBwC,IAAI;QAApBpD,IAAI,GAAA4H,KAAA;QAAE3E,MAAM,GAAA2E,KAAA;MACnB,OAAO5H,IAAI,YAAY6H,mBAAQ,GAAG,CAAC7H,IAAI,EAAEiD,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC/D;EAAA;IAAA1B,GAAA;IAAAC,KAAA,EAEA,SAAAqB,IAAIA,CAACH,KAAa,EAAuC;MACvD,IAAIA,KAAK,KAAK,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAACE,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC;MAC7B;MACA;MACA,OAAO,IAAI,CAACsD,UAAU,CAACjG,MAAM,EAAE2C,KAAK,CAAC;IACvC;EAAA;IAAAnB,GAAA;IAAAC,KAAA,EAEA,SAAAsG,KAAKA,CAAA,EAA+D;MAAA,IAA9DpF,KAAK,GAAAmB,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;MAAA,IAAElB,MAAM,GAAAkB,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGkE,MAAM,CAACC,SAAS;MACxC,IAAMC,SAAQ,GAAG,SAAXA,QAAQA,CACZjI,IAAgB,EAChBkI,SAAiB,EACjBC,UAAkB,EACf;QACH,IAAIL,KAA6B,GAAG,EAAE;QACtC,IAAIM,UAAU,GAAGD,UAAU;QAC3BnI,IAAI,CAACuD,QAAQ,CAAC8E,SAAS,CACrBH,SAAS,EACTC,UAAU,EACV,UAACG,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAK;UAClC,IAAIzI,MAAM,CAACuI,KAAK,CAAC,EAAE;YACjBR,KAAK,CAACW,IAAI,CAACH,KAAK,CAAC;UACnB,CAAC,MAAM,IAAIA,KAAK,YAAYI,wBAAa,EAAE;YACzCZ,KAAK,GAAGA,KAAK,CAACrC,MAAM,CAACwC,SAAQ,CAACK,KAAK,EAAEC,UAAU,EAAEH,UAAU,CAAC,CAAC;UAC/D;UACAA,UAAU,IAAII,WAAW;QAC3B,CACF,CAAC;QACD,OAAOV,KAAK;MACd,CAAC;MACD,OAAOG,SAAQ,CAAC,IAAI,EAAEvF,KAAK,EAAEC,MAAM,CAAC;IACtC;EAAA;IAAApB,GAAA;IAAAC,KAAA,EAOA,SAAAR,QAAQA,CAAA,EAA+B;MAAA,IAA9Ba,SAAS,GAAAgC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAAA,IAAE8E,OAAO,GAAA9E,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MACnC,IAAI,IAAI,CAAC9C,KAAK,EAAE;MAChB,IAAAsC,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAewB,SAAS,EAAE8G,OAAO;MACjC,IAAI9G,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAAC0G,eAAe,EAAE/G,SAAS,EAAE8G,OAAO,CAAC;MACvE;IACF;EAAA;IAAApH,GAAA;IAAAC,KAAA,EAEA,SAAAmG,IAAIA,CAACjF,KAAa,EAAE;MAClB,OAAO,IAAAW,cAAA,CAAAzC,OAAA,EAAAP,MAAA,oBAAWqC,KAAK,GAAEoC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;EAAA;IAAAvD,GAAA;IAAAC,KAAA,EAEA,SAAAmC,MAAMA,CAAA,EAAG;MACP;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA,EAKF,SAAAM,MAAMA,CAACD,SAA4C,EAAQ;MAAA,IAAAgH,MAAA;MACzD,IAAI,IAAI,CAAC9H,KAAK,EAAE;QACd,IAAIW,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;UAC5B,IAAI,CAACd,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0E,MAAM,CAAC5D,SAAS,CAAC;QAC3C;QACA;MACF;MACA,IAAIiH,MAAqB,GAAG7G,gBAAO,CAAC8G,OAAO,CAACC,IAAI;MAChD,IAAI,OAAOnH,SAAS,KAAK,QAAQ,EAAE;QACjCiH,MAAM,GAAGjH,SAAS;MACpB;MACA,IAAI,CAACH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;QAC7BA,SAAS,GAAG,IAAI,CAACoH,QAAQ,CAACC,WAAW,CAAC,CAAC;MACzC;MACArH,SAAS,GAAGA,SAAS,CAACsH,MAAM,CAAC,UAAAC,KAAA,EAAgB;QAAA,IAAbC,MAAA,GAAQD,KAAA,CAARC,MAAA;QAC9B,IAAMrJ,IAAI,GAAG6I,MAAI,CAAC9B,IAAI,CAACsC,MAAM,EAAE,IAAI,CAAC;QACpC,OAAOrJ,IAAI,IAAI,CAACG,WAAW,CAACH,IAAI,CAAC;MACnC,CAAC,CAAC;MACF,IAAI6B,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACoH,oBAAoB,EAAER,MAAM,EAAEjH,SAAS,CAAC;MAC3E;MACA,IAAAwB,cAAA,CAAAzC,OAAA,EAAAP,MAAA,sBAAawB,SAAS,CAAC4D,MAAM,CAAC,EAAE,CAAC,GAAE,CAAC;MACpC,IAAI5D,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACqH,aAAa,EAAET,MAAM,EAAEjH,SAAS,CAAC;MACpE;IACF;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAgI,aAAaA,CAAC9G,KAAa,EAAEnB,GAAW,EAAEgB,MAAe,EAAE;MACzD;MACA;MACA,IAAAkH,gBAAA,GAAe,IAAI,CAACzD,UAAU,CAAE,UAAA0D,CAAO;UAAA,OAAKA,CAAC,YAAYxJ,iBAAU;QAAA,GAAEwC,KAAK,CAAC;QAAAiH,iBAAA,OAAA5G,eAAA,CAAAnC,OAAA,EAAA6I,gBAAA;QAApEzJ,IAAI,GAAA2J,iBAAA;MACX,IAAI3J,IAAI,IAAIA,IAAI,CAACyE,OAAO,CAACE,QAAQ,KAAKpD,GAAG,IAAIpB,WAAW,CAACH,IAAI,CAAC,EAAE;QAC9DA,IAAI,CAACI,aAAa,CAACmC,MAAM,CAAC;MAC5B;IACF;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEU,SAAAJ,eAAeA,CAACwI,KAAgB,EAAE;MAC1CA,KAAK,CAACC,cAAc,CAAC,CAAC;IACxB;EAAA;IAAAtI,GAAA;IAAAC,KAAA,EAEQ,SAAAgE,mBAAmBA,CAACH,KAAY,EAAE;MAAA,IAAAyE,MAAA;MACxC,IAAMvE,YAA2B,GAAG,EAAE;MAEtC,IAAIwE,iBAAiB,GAAG,IAAIrE,mBAAK,CAAC,CAAC;MACnCL,KAAK,CAACuB,OAAO,CAAE,UAAAoD,EAAE,EAAK;QACpB,IAAMrE,MAAM,GAAGqE,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAErE,MAAM;QACzB,IAAI,CAACA,MAAM,EAAE;QACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UAC9B,IAAMsE,QAAQ,GAAGtE,MAAM,CAACwB,KAAK,CAAC,IAAI,CAAC;UACnC8C,QAAQ,CAACnF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC8B,OAAO,CAAE,UAAAsD,IAAI,EAAK;YAAA,IAAAC,cAAA;YACtCJ,iBAAiB,CAACpE,MAAM,CAACuE,IAAI,EAAEF,EAAE,CAACzD,UAAU,CAAC;YAC7ChB,YAAY,CAACkD,IAAI,CAAC;cAChB1C,IAAI,EAAE,OAAO;cACbV,KAAK,EAAE0E,iBAAiB;cACxBxD,UAAU,GAAA4D,cAAA,GAAEH,EAAE,CAACzD,UAAU,cAAA4D,cAAA,cAAAA,cAAA,GAAI,CAAC;YAChC,CAAC,CAAC;YACFJ,iBAAiB,GAAG,IAAIrE,mBAAK,CAAC,CAAC;UACjC,CAAC,CAAC;UACF,IAAMtC,IAAI,GAAG6G,QAAQ,CAACA,QAAQ,CAACtH,MAAM,GAAG,CAAC,CAAC;UAC1C,IAAIS,IAAI,EAAE;YACR2G,iBAAiB,CAACpE,MAAM,CAACvC,IAAI,EAAE4G,EAAE,CAACzD,UAAU,CAAC;UAC/C;QACF,CAAC,MAAM;UACL,IAAMhF,GAAG,GAAGmF,MAAM,CAACC,IAAI,CAAChB,MAAM,CAAC,CAAC,CAAC,CAAC;UAClC,IAAI,CAACpE,GAAG,EAAE;UACV,IAAIuI,MAAI,CAACzF,KAAK,CAAC9C,GAAG,EAAE+C,gBAAK,CAAC8F,MAAM,CAAC,EAAE;YACjCL,iBAAiB,CAACtB,IAAI,CAACuB,EAAE,CAAC;UAC5B,CAAC,MAAM;YAAA,IAAAK,eAAA;YACL,IAAIN,iBAAiB,CAACpH,MAAM,CAAC,CAAC,EAAE;cAC9B4C,YAAY,CAACkD,IAAI,CAAC;gBAChB1C,IAAI,EAAE,OAAO;gBACbV,KAAK,EAAE0E,iBAAiB;gBACxBxD,UAAU,EAAE,CAAC;cACf,CAAC,CAAC;YACJ;YACAwD,iBAAiB,GAAG,IAAIrE,mBAAK,CAAC,CAAC;YAC/BH,YAAY,CAACkD,IAAI,CAAC;cAChB1C,IAAI,EAAE,YAAY;cAClBxE,GAAG,EAAHA,GAAG;cACHC,KAAK,EAAEmE,MAAM,CAACpE,GAAG,CAAC;cAClBgF,UAAU,GAAA8D,eAAA,GAAEL,EAAE,CAACzD,UAAU,cAAA8D,eAAA,cAAAA,eAAA,GAAI,CAAC;YAChC,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MAEF,IAAIN,iBAAiB,CAACpH,MAAM,CAAC,CAAC,EAAE;QAC9B4C,YAAY,CAACkD,IAAI,CAAC;UAChB1C,IAAI,EAAE,OAAO;UACbV,KAAK,EAAE0E,iBAAiB;UACxBxD,UAAU,EAAE,CAAC;QACf,CAAC,CAAC;MACJ;MAEA,OAAOhB,YAAY;IACrB;EAAA;IAAAhE,GAAA;IAAAC,KAAA,EAEQ,SAAA8F,WAAWA,CAACf,UAAwB,EAAEU,OAAc,EAAE;MAAA,IAAAqD,MAAA;MAC5D,IAAI3F,QAA4B;MAChC,IAAM0B,OAAqB,GAAG,CAAC,CAAC;MAEhCK,MAAM,CAAC6D,OAAO,CAAChE,UAAU,CAAC,CAACK,OAAO,CAAC,UAAA4D,KAAA,EAAkB;QAAA,IAAAC,KAAA,OAAA1H,eAAA,CAAAnC,OAAA,EAAL4J,KAAA;UAAXjJ,GAAG,GAAAkJ,KAAA;UAAEjJ,KAAK,GAAAiJ,KAAA;QAC7C,IAAMC,WAAW,GAAGJ,MAAI,CAACjG,KAAK,CAAC9C,GAAG,EAAE+C,gBAAK,CAACC,KAAK,GAAGD,gBAAK,CAACqG,IAAI,CAAC,IAAI,IAAI;QACrE,IAAID,WAAW,EAAE;UACf/F,QAAQ,GAAGpD,GAAG;QAChB,CAAC,MAAM;UACL8E,OAAO,CAAC9E,GAAG,CAAC,GAAGC,KAAK;QACtB;MACF,CAAC,CAAC;MAEF,IAAM6F,KAAK,GAAG,IAAI,CAAC7C,MAAM,CACvBG,QAAQ,IAAI,IAAI,CAACF,OAAO,CAACC,YAAY,CAACC,QAAQ,EAC9CA,QAAQ,GAAG4B,UAAU,CAAC5B,QAAQ,CAAC,GAAGb,SACpC,CAAe;MAEf,IAAI,CAACkB,YAAY,CAACqC,KAAK,EAAEJ,OAAO,IAAInD,SAAS,CAAC;MAE9C,IAAMnB,MAAM,GAAG0E,KAAK,CAAC1E,MAAM,CAAC,CAAC;MAC7B+D,MAAM,CAAC6D,OAAO,CAAClE,OAAO,CAAC,CAACO,OAAO,CAAC,UAAAgE,KAAA,EAAkB;QAAA,IAAAC,KAAA,OAAA9H,eAAA,CAAAnC,OAAA,EAALgK,KAAA;UAAXrJ,GAAG,GAAAsJ,KAAA;UAAErJ,KAAK,GAAAqJ,KAAA;QAC1CxD,KAAK,CAACrD,QAAQ,CAAC,CAAC,EAAErB,MAAM,EAAEpB,GAAG,EAAEC,KAAK,CAAC;MACvC,CAAC,CAAC;MAEF,OAAO6F,KAAK;IACd;EAAA;AAAA,EAtXmByD,qBAAU;AAAA,IAAA7E,gBAAA,CAAArF,OAAA,EAAzBP,MAAM,cACQ,QAAQ;AAAA,IAAA4F,gBAAA,CAAArF,OAAA,EADtBP,MAAM,eAES,WAAW;AAAA,IAAA4F,gBAAA,CAAArF,OAAA,EAF1BP,MAAM,aAGO,KAAK;AAAA,IAAA4F,gBAAA,CAAArF,OAAA,EAHlBP,MAAM,kBAIYJ,cAAK;AAAA,IAAAgG,gBAAA,CAAArF,OAAA,EAJvBP,MAAM,qBAKe,CAACJ,cAAK,EAAEC,iBAAU,EAAE6K,kBAAS,CAAC;AAoXzD,SAAS7E,oBAAoBA,CAC3B8E,MAAkB,EAClBtI,KAAa,EACbuI,cAAqB,EACrB;EACAA,cAAc,CAACC,MAAM,CAAC,UAACxI,KAAK,EAAEsH,EAAE,EAAK;IACnC,IAAMrH,MAAM,GAAGwI,cAAE,CAACxI,MAAM,CAACqH,EAAE,CAAC;IAC5B,IAAIzD,UAAU,GAAGyD,EAAE,CAACzD,UAAU,IAAI,CAAC,CAAC;IACpC,IAAIyD,EAAE,CAACrE,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,OAAOqE,EAAE,CAACrE,MAAM,KAAK,QAAQ,EAAE;QACjC,IAAMuE,IAAI,GAAGF,EAAE,CAACrE,MAAM;QACtBqF,MAAM,CAAC9G,QAAQ,CAACxB,KAAK,EAAEwH,IAAI,CAAC;QAC5B,IAAAkB,kBAAA,GAAeJ,MAAM,CAAChF,UAAU,CAAC6B,mBAAQ,EAAEnF,KAAK,CAAC;UAAA2I,mBAAA,OAAAtI,eAAA,CAAAnC,OAAA,EAAAwK,kBAAA;UAA1C1D,IAAI,GAAA2D,mBAAA;QACX,IAAMhF,OAAO,GAAG,IAAAC,oBAAa,EAACoB,IAAI,CAAC;QACnCnB,UAAU,GAAGC,wBAAY,CAACC,IAAI,CAACJ,OAAO,EAAEE,UAAU,CAAC,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAI,IAAA+E,QAAA,CAAA1K,OAAA,EAAOoJ,EAAE,CAACrE,MAAM,MAAK,QAAQ,EAAE;QACxC,IAAMpE,GAAG,GAAGmF,MAAM,CAACC,IAAI,CAACqD,EAAE,CAACrE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAIpE,GAAG,IAAI,IAAI,EAAE,OAAOmB,KAAK;QAC7BsI,MAAM,CAAC9G,QAAQ,CAACxB,KAAK,EAAEnB,GAAG,EAAEyI,EAAE,CAACrE,MAAM,CAACpE,GAAG,CAAC,CAAC;QAC3C,IAAMgK,aAAa,GAAGP,MAAM,CAAC5G,MAAM,CAACC,KAAK,CAAC9C,GAAG,EAAE+C,gBAAK,CAAC8F,MAAM,CAAC,IAAI,IAAI;QACpE,IAAImB,aAAa,EAAE;UACjB,IAAAC,mBAAA,GAAeR,MAAM,CAAChF,UAAU,CAAC6B,mBAAQ,EAAEnF,KAAK,CAAC;YAAA+I,mBAAA,OAAA1I,eAAA,CAAAnC,OAAA,EAAA4K,mBAAA;YAA1C9D,KAAI,GAAA+D,mBAAA;UACX,IAAMpF,QAAO,GAAG,IAAAC,oBAAa,EAACoB,KAAI,CAAC;UACnCnB,UAAU,GAAGC,wBAAY,CAACC,IAAI,CAACJ,QAAO,EAAEE,UAAU,CAAC,IAAI,CAAC,CAAC;QAC3D;MACF;IACF;IACAG,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAE,UAAArF,GAAG,EAAK;MACvCyJ,MAAM,CAAChH,QAAQ,CAACtB,KAAK,EAAEC,MAAM,EAAEpB,GAAG,EAAEgF,UAAU,CAAChF,GAAG,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,OAAOmB,KAAK,GAAGC,MAAM;EACvB,CAAC,EAAED,KAAK,CAAC;AACX;AAAA,IAAAgJ,QAAA,GAAAC,OAAA,CAAA/K,OAAA,GAEeP,MAAM", "ignoreList": []}]}