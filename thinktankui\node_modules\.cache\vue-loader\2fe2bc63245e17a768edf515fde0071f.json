{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\AppMain.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBpZnJhbWVUb2dnbGUgZnJvbSAiLi9JZnJhbWVUb2dnbGUvaW5kZXgiCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0FwcE1haW4nLAogIGNvbXBvbmVudHM6IHsgaWZyYW1lVG9nZ2xlIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNhY2hlZFZpZXdzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGFnc1ZpZXcuY2FjaGVkVmlld3MKICAgIH0sCiAgICBrZXkoKSB7CiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZS5wYXRoCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgJHJvdXRlKCkgewogICAgICB0aGlzLmFkZElmcmFtZSgpCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5hZGRJZnJhbWUoKQogIH0sCiAgbWV0aG9kczogewogICAgYWRkSWZyYW1lKCkgewogICAgICBjb25zdCB7bmFtZX0gPSB0aGlzLiRyb3V0ZQogICAgICBpZiAobmFtZSAmJiB0aGlzLiRyb3V0ZS5tZXRhLmxpbmspIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvYWRkSWZyYW1lVmlldycsIHRoaXMuJHJvdXRlKQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <keep-alive :include=\"cachedViews\">\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\n      </keep-alive>\n    </transition>\n    <iframe-toggle />\n  </section>\n</template>\n\n<script>\nimport iframeToggle from \"./IframeToggle/index\"\n\nexport default {\n  name: 'AppMain',\n  components: { iframeToggle },\n  computed: {\n    cachedViews() {\n      return this.$store.state.tagsView.cachedViews\n    },\n    key() {\n      return this.$route.path\n    }\n  },\n  watch: {\n    $route() {\n      this.addIframe()\n    }\n  },\n  mounted() {\n    this.addIframe()\n  },\n  methods: {\n    addIframe() {\n      const {name} = this.$route\n      if (name && this.$route.meta.link) {\n        this.$store.dispatch('tagsView/addIframeView', this.$route)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-main {\n  /* 50= navbar  50  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.fixed-header + .app-main {\n  padding-top: 50px;\n}\n\n.hasTagsView {\n  .app-main {\n    /* 84 = navbar + tags-view = 50 + 34 */\n    min-height: calc(100vh - 84px);\n  }\n\n  .fixed-header + .app-main {\n    padding-top: 84px;\n  }\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 6px;\n  }\n}\n\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background-color: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: #c0c0c0;\n  border-radius: 3px;\n}\n</style>\n"]}]}