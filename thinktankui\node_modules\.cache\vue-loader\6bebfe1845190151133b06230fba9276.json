{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        :style=\"activeStyle(tag)\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\"><i class=\"el-icon-refresh-right\"></i> 刷新页面</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\"><i class=\"el-icon-close\"></i> 关闭当前</li>\n      <li @click=\"closeOthersTags\"><i class=\"el-icon-circle-close\"></i> 关闭其他</li>\n      <li v-if=\"!isFirstView()\" @click=\"closeLeftTags\"><i class=\"el-icon-back\"></i> 关闭左侧</li>\n      <li v-if=\"!isLastView()\" @click=\"closeRightTags\"><i class=\"el-icon-right\"></i> 关闭右侧</li>\n      <li @click=\"closeAllTags(selectedTag)\"><i class=\"el-icon-circle-close\"></i> 全部关闭</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    },\n    routes() {\n      return this.$store.state.permission.routes\n    },\n    theme() {\n      return this.$store.state.settings.theme;\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    activeStyle(tag) {\n      if (!this.isActive(tag)) return {};\n      return {\n        \"background-color\": this.theme,\n        \"border-color\": this.theme\n      };\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    isFirstView() {\n      try {\n        return this.selectedTag.fullPath === '/index' || this.selectedTag.fullPath === this.visitedViews[1].fullPath\n      } catch (err) {\n        return false\n      }\n    },\n    isLastView() {\n      try {\n        return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath\n      } catch (err) {\n        return false\n      }\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      routes.forEach(route => {\n        if (route.meta && route.meta.affix) {\n          const tagPath = path.resolve(basePath, route.path)\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: { ...route.meta }\n          })\n        }\n        if (route.children) {\n          const tempTags = this.filterAffixTags(route.children, route.path)\n          if (tempTags.length >= 1) {\n            tags = [...tags, ...tempTags]\n          }\n        }\n      })\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      if (name) {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n    },\n    moveToCurrentTag() {\n      const tags = this.$refs.tag\n      this.$nextTick(() => {\n        for (const tag of tags) {\n          if (tag.to.path === this.$route.path) {\n            this.$refs.scrollPane.moveToTarget(tag)\n            // when query is different then update\n            if (tag.to.fullPath !== this.$route.fullPath) {\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n            }\n            break\n          }\n        }\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$tab.refreshPage(view);\n      if (this.$route.meta.link) {\n        this.$store.dispatch('tagsView/delIframeView', this.$route)\n      }\n    },\n    closeSelectedTag(view) {\n      this.$tab.closePage(view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeRightTags() {\n      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\n          this.toLastView(visitedViews)\n        }\n      })\n    },\n    closeLeftTags() {\n      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\n          this.toLastView(visitedViews)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag.fullPath).catch(()=>{});\n      this.$tab.closeOtherPage(this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$tab.closeAllPage().then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === 'Dashboard') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-view-container {\n  height: 34px;\n  width: 100%;\n  background: #fff;\n  border-bottom: 1px solid #d8dce5;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 26px;\n      line-height: 26px;\n      border: 1px solid #d8dce5;\n      color: #495060;\n      background: #fff;\n      padding: 0 8px;\n      font-size: 12px;\n      margin-left: 5px;\n      margin-top: 4px;\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n      &.active {\n        background-color: #42b983;\n        color: #fff;\n        border-color: #42b983;\n        &::before {\n          content: '';\n          background: #fff;\n          display: inline-block;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 2px;\n        }\n      }\n    }\n  }\n  .contextmenu {\n    margin: 0;\n    background: #fff;\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 5px 0;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 400;\n    color: #333;\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\n    li {\n      margin: 0;\n      padding: 7px 16px;\n      cursor: pointer;\n      &:hover {\n        background: #eee;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\n      transform-origin: 100% 50%;\n      &:before {\n        transform: scale(.6);\n        display: inline-block;\n        vertical-align: -3px;\n      }\n      &:hover {\n        background-color: #b4bccc;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n"]}]}