<template>
  <div class="app-container">
    <div class="page-layout">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar">
        <div class="user-info">
          <div class="avatar">
            <img src="@/assets/images/profile.jpg" alt="用户头像">
          </div>
          <div class="user-id">***********</div>
          <div class="register-date">2023-04-28注册</div>
        </div>

        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect"
          >
            <el-menu-item index="account">
              <i class="el-icon-user"></i>
              <span>我的账号</span>
            </el-menu-item>
            <el-menu-item index="favorite">
              <i class="el-icon-star-on"></i>
              <span>我的收藏</span>
            </el-menu-item>
            <el-menu-item index="download">
              <i class="el-icon-download"></i>
              <span>我的下载</span>
            </el-menu-item>
            <el-menu-item index="contact">
              <i class="el-icon-notebook-1"></i>
              <span>我的联系人</span>
            </el-menu-item>
            <el-menu-item index="material">
              <i class="el-icon-chat-line-round"></i>
              <span>素材库</span>
            </el-menu-item>
            <el-menu-item index="privacy">
              <i class="el-icon-setting"></i>
              <span>系统设置</span>
            </el-menu-item>
            <el-menu-item index="user-management">
              <i class="el-icon-user"></i>
              <span>用户管理</span>
            </el-menu-item>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="content">
        <!-- 账户信息内容 -->
        <div v-if="activeMenuItem === 'account'" class="account-container">
          <div class="account-header">
            <div class="title">我的账号</div>
            <div class="actions">
              <el-button size="small" @click="showAccountInfo">账号安全</el-button>
              <el-button type="primary" size="small" @click="showPasswordForm">修改密码</el-button>
            </div>
          </div>

          <!-- 账户信息内容 -->
          <div v-if="!showPasswordChange" class="account-info">
            <div class="info-item">
              <div class="label">用户账号：</div>
              <div class="value">***********</div>
            </div>

            <div class="info-item">
              <div class="label">手机号码：</div>
              <div class="value">***********</div>
            </div>

            <div class="info-item">
              <div class="label">创建时间：</div>
              <div class="value">2023-04-28</div>
            </div>

            <div class="info-item">
              <div class="label">专业认证分类：</div>
              <div class="value">互联网+</div>
            </div>

            <div class="info-item">
              <div class="label">方案：</div>
              <div class="value">【免费】+【限时】</div>
            </div>

            <div class="info-item">
              <div class="label">总积分值：</div>
              <div class="value">2000</div>
            </div>

            <div class="info-item">
              <div class="label">积分等级：</div>
              <div class="value">初级VIP</div>
            </div>

            <div class="info-item">
              <div class="label">剩余可用积分：</div>
              <div class="value">【250积分 = 600次】</div>
            </div>

            <div class="info-item">
              <div class="label">预计可用天数：</div>
              <div class="value">365</div>
            </div>

            <div class="info-item">
              <div class="label">预计到期日：</div>
              <div class="value">365</div>
            </div>

            <div class="info-item">
              <div class="label">剩余次数：</div>
              <div class="value">10000次</div>
            </div>
          </div>

          <!-- 修改密码表单 -->
          <div v-if="showPasswordChange" class="password-form">
            <div class="form-group">
              <div class="form-label">旧密码：</div>
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                placeholder="请输入旧密码"
                show-password
              ></el-input>
            </div>

            <div class="form-group">
              <div class="form-label">新密码：</div>
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="8-16 密码必须同时包含数字、大小写字母和符号"
                show-password
              ></el-input>
            </div>

            <div class="form-group">
              <div class="form-label">确认新密码：</div>
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              ></el-input>
            </div>

            <div class="form-actions">
              <el-button type="primary" @click="changePassword">确认修改</el-button>
            </div>
          </div>
        </div>

        <!-- 我的下载内容 -->
        <div v-if="activeMenuItem === 'download'" class="download-container">
          <div class="download-header">
            <div class="title">我的下载</div>
            <div class="actions">
              <el-button type="primary" size="small" @click="batchDownload">批量下载</el-button>
              <el-button type="danger" size="small" @click="batchDelete">批量删除</el-button>
            </div>
          </div>

          <div class="download-content">
            <el-table
              :data="downloadList"
              style="width: 100%"
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>
              <el-table-column
                prop="name"
                label="名称"
                width="300">
              </el-table-column>
              <el-table-column
                prop="dataSize"
                label="数据量"
                width="100">
              </el-table-column>
              <el-table-column
                prop="createTime"
                label="生成时间"
                width="180">
              </el-table-column>
              <el-table-column
                prop="status"
                label="下载状态"
                width="100">
                <template slot-scope="scope">
                  <span class="download-status">{{ scope.row.status }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                width="120">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-download"
                    @click="handleDownload(scope.row)">
                  </el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)">
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container">
              <span>共 {{ total }} 条记录</span>
              <el-pagination
                background
                layout="prev, pager, next, jumper"
                :total="total"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                @current-change="handleCurrentChange">
              </el-pagination>
              <span>前往第</span>
              <el-input
                v-model="jumpPage"
                size="mini"
                class="jump-page-input">
              </el-input>
              <span>页</span>
              <el-button
                size="mini"
                type="primary"
                @click="handleJumpPage">
                确定
              </el-button>
            </div>
          </div>
        </div>

        <!-- 我的收藏内容 -->
        <div v-if="activeMenuItem === 'favorite'" class="favorite-container">
          <div class="favorite-header">
            <div class="title">我的收藏</div>
          </div>

          <div class="favorite-toolbar">
            <div class="toolbar-left">
              <el-checkbox v-model="selectAllFavorites">全选</el-checkbox>
              <el-button size="small" type="danger" :disabled="selectedFavorites.length === 0" @click="batchCancelFavorite">批量取消收藏</el-button>
              <el-button size="small" icon="el-icon-refresh">刷新</el-button>
            </div>

            <div class="toolbar-right">
              <div class="date-filter">
                <el-date-picker
                  v-model="favoriteStartDate"
                  type="date"
                  placeholder="开始日期"
                  size="small"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 130px;"
                ></el-date-picker>
                <span class="date-separator">-</span>
                <el-date-picker
                  v-model="favoriteEndDate"
                  type="date"
                  placeholder="结束日期"
                  size="small"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 130px;"
                ></el-date-picker>
              </div>

              <div class="search-box">
                <el-input
                  v-model="favoriteSearchKeyword"
                  placeholder="搜索内容"
                  size="small"
                  prefix-icon="el-icon-search"
                  clearable
                  style="width: 200px;"
                ></el-input>
              </div>

              <el-button type="primary" size="small" icon="el-icon-download" @click="exportFavorites">全部下载</el-button>
            </div>
          </div>

          <div class="favorite-content">
            <div class="favorite-list">
              <div v-for="item in favoriteList" :key="item.id" class="favorite-item">
                <div class="item-checkbox">
                  <el-checkbox v-model="item.selected" @change="handleFavoriteSelect"></el-checkbox>
                </div>
                <div class="item-content">
                  <div class="item-title">
                    <span class="title-tag">{{ item.tag }}</span>
                    <span class="title-text">{{ item.title }}</span>
                  </div>
                  <div class="item-info">
                    <span class="info-time">收藏时间: {{ item.time }}</span>
                    <span class="info-source">来源: {{ item.source }}</span>
                  </div>
                </div>
                <div class="item-actions">
                  <el-button
                    type="text"
                    size="small"
                    icon="el-icon-delete"
                    class="cancel-favorite-btn"
                    @click="cancelFavorite(item)">
                    取消收藏
                  </el-button>
                </div>
              </div>
            </div>

            <div class="favorite-pagination">
              <div class="pagination-info">
                共 <span>{{ favoritesTotal }}</span> 条记录
              </div>
              <div class="pagination-controls">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="favoritesTotal"
                  :current-page.sync="favoritesCurrentPage"
                  :page-size="favoritesPageSize"
                  @current-change="handleFavoritePageChange">
                </el-pagination>
              </div>
              <div class="pagination-jump">
                <span>前往</span>
                <el-input
                  v-model="favoritesJumpPage"
                  size="mini"
                  class="jump-page-input">
                </el-input>
                <span>页</span>
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleFavoriteJumpPage">
                  确定
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 素材库内容 -->
        <div v-if="activeMenuItem === 'material'" class="material-container">
          <div class="material-header">
            <div class="title">素材库</div>
          </div>

          <div class="material-content">
            <div class="material-add-box" @click="showAddMaterialDialog">
              <div class="add-icon">
                <i class="el-icon-plus"></i>
              </div>
              <div class="add-text">新建素材包</div>
            </div>
          </div>

          <!-- 新建素材对话框 -->
          <el-dialog
            title="新建素材包"
            :visible.sync="addMaterialDialogVisible"
            width="400px"
            center
            :show-close="false"
            custom-class="material-dialog"
          >
            <div class="material-form">
              <div class="form-item">
                <div class="form-label">素材包名称：</div>
                <el-input v-model="newMaterialName" placeholder="请输入素材包名称"></el-input>
              </div>
            </div>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="addMaterial">确定</el-button>
              <el-button @click="addMaterialDialogVisible = false">取消</el-button>
            </div>
          </el-dialog>
        </div>

        <!-- 我的联系人内容 -->
        <div v-if="activeMenuItem === 'contact'" class="contact-container">
          <div class="contact-header">
            <div class="title">我的联系人</div>
            <div class="contact-tabs">
              <el-radio-group v-model="contactActiveTab" size="small">
                <el-radio-button label="wechat">微信</el-radio-button>
                <el-radio-button label="sms">短信</el-radio-button>
                <el-radio-button label="email">邮箱</el-radio-button>
                <el-radio-button label="qwechat">企微群</el-radio-button>
                <el-radio-button label="dingtalk">钉钉群</el-radio-button>
                <el-radio-button label="feishu">飞书群</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="contact-toolbar">
            <el-button type="primary" size="small" icon="el-icon-plus" @click="showAddContactDialog">添加/导入联系人</el-button>
          </div>

          <div class="contact-content">
            <el-table
              :data="contactList"
              style="width: 100%"
              border
              stripe
              :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
            >
              <el-table-column
                label="头像"
                width="80"
                align="center"
              >
                <template slot-scope="scope">
                  <el-avatar :size="40" icon="el-icon-user"></el-avatar>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="姓名"
                width="120"
                align="center"
              />
              <el-table-column
                prop="createTime"
                label="创建/更新"
                width="180"
                align="center"
              />
              <el-table-column
                prop="location"
                label="位置/时间"
                width="180"
                align="center"
              />
              <el-table-column
                label="操作"
                align="center"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleEditContact(scope.row)"
                  >编辑</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    class="delete-btn"
                    @click="handleDeleteContact(scope.row)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 添加联系人对话框 -->
          <el-dialog title="添加联系人" :visible.sync="addContactDialogVisible" width="500px" center>
            <el-form ref="contactForm" :model="contactForm" :rules="contactFormRules" label-width="80px">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="contactForm.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="手机号码" prop="phone">
                <el-input v-model="contactForm.phone" placeholder="请输入手机号码" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="contactForm.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="位置" prop="location">
                <el-input v-model="contactForm.location" placeholder="请输入位置" />
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="addContactDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="submitContactForm">确定</el-button>
            </div>
          </el-dialog>
        </div>

        <!-- 用户管理内容 -->
        <div v-if="activeMenuItem === 'user-management'">
          <user-management />
        </div>

        <!-- 系统设置内容 -->
        <div v-if="activeMenuItem === 'privacy'" class="settings-container">
          <div class="settings-header">
            <div class="title">系统设置</div>
          </div>

          <div class="settings-content">
            <!-- 左侧设置菜单 -->
            <div class="settings-sidebar">
              <div class="settings-menu">
                <div :class="['menu-item', currentSettingsTab === 'basic' ? 'active' : '']" @click="switchSettingsTab('basic')">
                  <i class="el-icon-setting"></i>
                  <span>基础设置</span>
                </div>
                <div :class="['menu-item', currentSettingsTab === 'updates' ? 'active' : '']" @click="switchSettingsTab('updates')">
                  <i class="el-icon-document"></i>
                  <span>更新说明</span>
                </div>
              </div>
            </div>

            <!-- 右侧设置内容 -->
            <div class="settings-main">
              <!-- 基础设置内容 -->
              <div v-if="currentSettingsTab === 'basic'" class="settings-section">
                <div class="section-title">文章管理选项：</div>
                <div class="options-group">
                  <el-checkbox v-model="settings.auto">自动</el-checkbox>
                  <el-checkbox v-model="settings.manual">手动</el-checkbox>
                  <el-checkbox v-model="settings.downloadImages">下载图片</el-checkbox>
                  <el-checkbox v-model="settings.backup">备份</el-checkbox>
                  <el-checkbox v-model="settings.realTimeSync">实时同步</el-checkbox>
                  <el-checkbox v-model="settings.humanReview">人工审核</el-checkbox>
                </div>

                <div class="options-group">
                  <el-checkbox v-model="settings.autoPublish">自动发布</el-checkbox>
                  <el-checkbox v-model="settings.keywords">关键词</el-checkbox>
                  <el-checkbox v-model="settings.highQuality">高质量</el-checkbox>
                  <el-checkbox v-model="settings.multiPlatform">多平台发布</el-checkbox>
                </div>

                <div class="options-group">
                  <el-checkbox v-model="settings.autoSave">自动保存</el-checkbox>
                  <el-checkbox v-model="settings.batchProcess">批量处理</el-checkbox>
                  <el-checkbox v-model="settings.imageProcess">图片处理</el-checkbox>
                </div>

                <div class="section-title">下载设置：</div>
                <div class="download-settings">
                  <div class="setting-item">
                    <span class="label">下载路径：</span>
                    <span class="value">自动生成文件夹</span>
                  </div>
                </div>

                <div class="section-title">高级自动化设置：</div>
                <div class="options-group">
                  <el-checkbox v-model="settings.autoOn">开启</el-checkbox>
                  <el-checkbox v-model="settings.autoOff">关闭</el-checkbox>
                </div>

                <div class="save-button">
                  <el-button type="primary" @click="saveSettings">保存</el-button>
                </div>
              </div>

              <!-- 更新说明内容 -->
              <div v-if="currentSettingsTab === 'updates'" class="updates-section">
                <div class="updates-header">
                  <div class="version-title">{{ selectedVersion.version }} 版本更新说明</div>
                  <div class="version-date">{{ selectedVersion.date }}</div>
                </div>

                <div class="updates-content">
                  <div class="version-list">
                    <div
                      v-for="version in versionList"
                      :key="version.version"
                      :class="['version-item', selectedVersion.version === version.version ? 'active' : '']"
                      @click="selectVersion(version.version)"
                    >
                      {{ version.version }}
                    </div>
                  </div>

                  <div class="update-details">
                    <div class="update-notes">
                      <div v-for="(note, index) in selectedVersion.notes" :key="index" class="note-item">
                        <div class="note-number">{{ index + 1 }}. </div>
                        <div class="note-content" v-html="note"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UserManagement from './user-management.vue';

export default {
  name: "AccountManagement",
  components: {
    UserManagement
  },
  watch: {
    selectAllFavorites(val) {
      this.watchSelectAllFavorites(val);
    }
  },
  data() {
    return {
      activeMenuItem: 'account', // 默认选中我的账号
      currentSettingsTab: 'basic', // 默认选中基础设置
      // 用户信息
      userInfo: {
        userId: '***********',
        phoneNumber: '***********',
        registerDate: '2023-04-28',
        category: '互联网+',
        plan: '【免费】+【限时】',
        totalPoints: '2000',
        pointsLevel: '初级VIP',
        availablePoints: '【250积分 = 600次】',
        estimatedDays: '365',
        expirationDate: '365',
        remainingCount: '10000次'
      },
      // 系统设置
      settings: {
        auto: true,
        manual: true,
        downloadImages: true,
        backup: true,
        realTimeSync: false,
        humanReview: true,
        autoPublish: false,
        keywords: true,
        highQuality: false,
        multiPlatform: true,
        autoSave: true,
        batchProcess: true,
        imageProcess: true,
        autoOn: true,
        autoOff: false
      },
      // 下载列表
      downloadList: [
        {
          id: 1,
          name: '方案_20240428163743_1',
          dataSize: '1323',
          createTime: '2024-04-28 16:37:57',
          status: '已完成'
        },
        {
          id: 2,
          name: '方案_20240428163743_1',
          dataSize: '2000',
          createTime: '2024-04-28 16:37:57',
          status: '已完成'
        },
        {
          id: 3,
          name: '方案_20240427173742_4',
          dataSize: '1893',
          createTime: '2024-04-27 17:37:42',
          status: '已完成'
        },
        {
          id: 4,
          name: '方案_20240427173742_3',
          dataSize: '2000',
          createTime: '2024-04-27 17:37:42',
          status: '已完成'
        },
        {
          id: 5,
          name: '方案_20240427173742_2',
          dataSize: '2000',
          createTime: '2024-04-27 17:37:42',
          status: '已完成'
        },
        {
          id: 6,
          name: '方案_20240427173742_2',
          dataSize: '2000',
          createTime: '2024-04-27 17:37:42',
          status: '已完成'
        },
        {
          id: 7,
          name: '方案_20240427173742_1',
          dataSize: '2000',
          createTime: '2024-04-27 17:37:42',
          status: '已完成'
        },
        {
          id: 8,
          name: '台账_20240427173129_5',
          dataSize: '1281',
          createTime: '2024-04-27 17:31:29',
          status: '已完成'
        },
        {
          id: 9,
          name: '台账_20240427173129_4',
          dataSize: '2000',
          createTime: '2024-04-27 17:31:29',
          status: '已完成'
        },
        {
          id: 10,
          name: '台账_20240427173129_3',
          dataSize: '2000',
          createTime: '2024-04-27 17:31:29',
          status: '已完成'
        },
        {
          id: 11,
          name: '台账_20240427173129_2',
          dataSize: '2000',
          createTime: '2024-04-27 17:31:29',
          status: '已完成'
        },
        {
          id: 12,
          name: '台账_20240427173129_1',
          dataSize: '2000',
          createTime: '2024-04-27 17:31:29',
          status: '已完成'
        }
      ],
      // 分页相关
      total: 12,
      currentPage: 1,
      pageSize: 10,
      jumpPage: '',
      // 选中的下载项
      selectedDownloads: [],
      // 版本列表
      versionList: [
        { version: '6.2.9', date: '2024.01.19' },
        { version: '6.2.8', date: '2023.12.15' },
        { version: '6.2.7', date: '2023.11.20' },
        { version: '6.2.6', date: '2023.10.18' },
        { version: '6.2.5', date: '2023.09.25' },
        { version: '6.2.4', date: '2023.08.30' },
        { version: '6.2.3', date: '2023.07.28' },
        { version: '6.2.2', date: '2023.06.22' },
        { version: '6.2.1', date: '2023.05.15' },
        { version: '6.2.0', date: '2023.04.10' }
      ],
      // 当前选中的版本
      selectedVersion: {
        version: '6.2.9',
        date: '2024.01.19',
        notes: [
          '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',
          '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',
          '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',
          '4. 【问题修复】修复了在某些情况下<b>"数据导出"</b>功能失效的问题。',
          '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'
        ]
      },
      // 素材库相关
      addMaterialDialogVisible: false,
      newMaterialName: '',
      materialList: [],
      // 修改密码相关
      showPasswordChange: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      // 我的收藏相关
      selectAllFavorites: false,
      selectedFavorites: [],
      favoriteStartDate: '',
      favoriteEndDate: '',
      favoriteSearchKeyword: '',
      favoriteList: [
        {
          id: 1,
          selected: false,
          tag: 'HOT',
          title: '新产品市场分析报告',
          time: '2023-04-29 20:37:51',
          source: '市场调查部门 A 数据库'
        },
        {
          id: 2,
          selected: false,
          tag: 'NEW',
          title: '2024年第一季度行业趋势分析',
          time: '2024-04-28 15:22:36',
          source: '行业研究中心'
        },
        {
          id: 3,
          selected: false,
          tag: 'HOT',
          title: '竞品分析与市场定位策略',
          time: '2024-04-27 09:15:42',
          source: '战略规划部'
        },
        {
          id: 4,
          selected: false,
          tag: 'TOP',
          title: '用户行为数据分析报告',
          time: '2024-04-26 14:30:18',
          source: '数据分析部门'
        },
        {
          id: 5,
          selected: false,
          tag: 'NEW',
          title: '新媒体营销策略白皮书',
          time: '2024-04-25 11:45:23',
          source: '营销部门'
        },
        {
          id: 6,
          selected: false,
          tag: 'HOT',
          title: '产品迭代计划与路线图',
          time: '2024-04-24 16:20:37',
          source: '产品部门'
        },
        {
          id: 7,
          selected: false,
          tag: 'TOP',
          title: '行业政策解读与影响分析',
          time: '2024-04-23 10:05:12',
          source: '政策研究中心'
        }
      ],
      favoritesTotal: 7,
      favoritesCurrentPage: 1,
      favoritesPageSize: 10,
      favoritesJumpPage: '',

      // 我的联系人相关
      contactActiveTab: 'wechat', // 默认选中微信标签
      contactList: [
        {
          id: 1,
          name: '张三',
          phone: '13800138001',
          email: '<EMAIL>',
          location: '北京市朝阳区',
          createTime: '2024-04-28 10:30:45'
        },
        {
          id: 2,
          name: '李四',
          phone: '13800138002',
          email: '<EMAIL>',
          location: '上海市浦东新区',
          createTime: '2024-04-27 15:20:36'
        },
        {
          id: 3,
          name: '王五',
          phone: '13800138003',
          email: '<EMAIL>',
          location: '广州市天河区',
          createTime: '2024-04-26 09:15:22'
        }
      ],
      addContactDialogVisible: false,
      contactForm: {
        id: null,
        name: '',
        phone: '',
        email: '',
        location: ''
      },
      contactFormRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    // 处理菜单项选择
    handleMenuSelect(index) {
      this.activeMenuItem = index;
    },
    // 显示账户信息
    showAccountInfo() {
      this.showPasswordChange = false;
    },
    // 显示修改密码表单
    showPasswordForm() {
      this.showPasswordChange = true;
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    },
    // 修改密码
    changePassword() {
      // 表单验证
      if (!this.passwordForm.oldPassword) {
        this.$message.warning('请输入旧密码');
        return;
      }
      if (!this.passwordForm.newPassword) {
        this.$message.warning('请输入新密码');
        return;
      }
      if (!this.passwordForm.confirmPassword) {
        this.$message.warning('请确认新密码');
        return;
      }
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.$message.warning('两次输入的新密码不一致');
        return;
      }

      // 提交修改密码请求
      this.$message.success('密码修改成功');
      this.showPasswordChange = false;
    },
    // 切换设置选项卡
    switchSettingsTab(tab) {
      this.currentSettingsTab = tab;
    },
    // 保存设置
    saveSettings() {
      this.$message.success('设置已保存');
    },
    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedDownloads = selection;
    },
    // 批量下载
    batchDownload() {
      if (this.selectedDownloads.length === 0) {
        this.$message.warning('请选择要下载的文件');
        return;
      }
      this.$message.success(`已开始下载${this.selectedDownloads.length}个文件`);
    },
    // 批量删除
    batchDelete() {
      if (this.selectedDownloads.length === 0) {
        this.$message.warning('请选择要删除的文件');
        return;
      }
      this.$confirm('确定要删除选中的文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`已删除${this.selectedDownloads.length}个文件`);
        // 实际应用中这里需要调用接口删除文件
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 下载单个文件
    handleDownload(row) {
      this.$message.success(`开始下载: ${row.name}`);
    },
    // 删除单个文件
    handleDelete(row) {
      this.$confirm('确定要删除该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`已删除: ${row.name}`);
        // 实际应用中这里需要调用接口删除文件
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      // 实际应用中这里需要调用接口获取对应页的数据
    },
    // 处理跳转页面
    handleJumpPage() {
      if (!this.jumpPage) {
        return;
      }
      const page = parseInt(this.jumpPage);
      if (isNaN(page) || page < 1 || page > Math.ceil(this.total / this.pageSize)) {
        this.$message.warning('请输入有效的页码');
        return;
      }
      this.currentPage = page;
      // 实际应用中这里需要调用接口获取对应页的数据
    },
    // 选择版本
    selectVersion(version) {
      const versionData = this.versionList.find(v => v.version === version);
      if (versionData) {
        // 根据版本号获取对应的更新说明
        let notes = [];
        if (version === '6.2.9') {
          notes = [
            '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',
            '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',
            '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',
            '4. 【问题修复】修复了在某些情况下<b>"数据导出"</b>功能失效的问题。',
            '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'
          ];
        } else if (version === '6.2.8') {
          notes = [
            '1. 【新增功能】新增<b>数据分析</b>模块，提供更全面的数据统计。',
            '2. 【功能优化】优化了<b>文件上传</b>功能，支持更多文件格式。',
            '3. 【问题修复】修复了部分用户<b>无法登录</b>的问题。'
          ];
        } else {
          notes = [
            '1. 【功能优化】优化系统性能，提升用户体验。',
            '2. 【问题修复】修复已知问题，提高系统稳定性。'
          ];
        }

        this.selectedVersion = {
          version: versionData.version,
          date: versionData.date,
          notes: notes
        };
      }
    },
    // 显示添加素材对话框
    showAddMaterialDialog() {
      this.newMaterialName = '';
      this.addMaterialDialogVisible = true;
    },
    // 添加素材
    addMaterial() {
      if (!this.newMaterialName.trim()) {
        this.$message.warning('请输入素材名称');
        return;
      }

      // 添加新素材
      this.materialList.push({
        id: Date.now(),
        name: this.newMaterialName,
        createTime: new Date().toLocaleString()
      });

      this.$message.success(`素材"${this.newMaterialName}"创建成功`);
      this.addMaterialDialogVisible = false;
    },

    // 处理收藏项选择
    handleFavoriteSelect() {
      this.selectedFavorites = this.favoriteList.filter(item => item.selected);
      // 检查是否全选
      this.selectAllFavorites = this.selectedFavorites.length === this.favoriteList.length;
    },

    // 全选/取消全选收藏项
    watchSelectAllFavorites(val) {
      this.favoriteList.forEach(item => {
        item.selected = val;
      });
      this.selectedFavorites = val ? [...this.favoriteList] : [];
    },

    // 批量取消收藏
    batchCancelFavorite() {
      if (this.selectedFavorites.length === 0) {
        this.$message.warning('请选择要取消收藏的项');
        return;
      }

      this.$confirm('确定要取消选中的收藏吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应用中这里需要调用接口取消收藏
        const selectedIds = this.selectedFavorites.map(item => item.id);
        this.favoriteList = this.favoriteList.filter(item => !selectedIds.includes(item.id));
        this.selectedFavorites = [];
        this.selectAllFavorites = false;
        this.$message.success('已取消收藏');
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    },

    // 取消单个收藏
    cancelFavorite(item) {
      this.$confirm('确定要取消收藏"' + item.title + '"吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应用中这里需要调用接口取消收藏
        this.favoriteList = this.favoriteList.filter(i => i.id !== item.id);
        // 更新选中的收藏项
        this.selectedFavorites = this.selectedFavorites.filter(i => i.id !== item.id);
        // 更新总数
        this.favoritesTotal = this.favoriteList.length;
        this.$message.success('已取消收藏');
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    },

    // 显示添加联系人对话框
    showAddContactDialog() {
      this.contactForm = {
        id: null,
        name: '',
        phone: '',
        email: '',
        location: ''
      };
      this.addContactDialogVisible = true;
    },

    // 提交联系人表单
    submitContactForm() {
      this.$refs.contactForm.validate(valid => {
        if (valid) {
          if (this.contactForm.id) {
            // 编辑联系人
            const index = this.contactList.findIndex(item => item.id === this.contactForm.id);
            if (index !== -1) {
              // 更新创建时间
              this.contactForm.createTime = new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
              }).replace(/\//g, '-');
              this.contactList.splice(index, 1, this.contactForm);
              this.$message.success('联系人修改成功');
            }
          } else {
            // 添加联系人
            const newContact = {
              ...this.contactForm,
              id: this.contactList.length > 0 ? Math.max(...this.contactList.map(item => item.id)) + 1 : 1,
              createTime: new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
              }).replace(/\//g, '-')
            };
            this.contactList.push(newContact);
            this.$message.success('联系人添加成功');
          }
          this.addContactDialogVisible = false;
        }
      });
    },

    // 编辑联系人
    handleEditContact(row) {
      this.contactForm = JSON.parse(JSON.stringify(row));
      this.addContactDialogVisible = true;
    },

    // 删除联系人
    handleDeleteContact(row) {
      this.$confirm(`确定要删除联系人"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应用中这里需要调用接口删除联系人
        this.contactList = this.contactList.filter(item => item.id !== row.id);
        this.$message.success('联系人删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 导出收藏
    exportFavorites() {
      this.$message.success('开始导出收藏');
      // 实际应用中这里需要调用接口导出收藏
    },

    // 处理收藏分页变化
    handleFavoritePageChange(val) {
      this.favoritesCurrentPage = val;
      // 实际应用中这里需要调用接口获取对应页的数据
    },

    // 处理收藏跳转页面
    handleFavoriteJumpPage() {
      if (!this.favoritesJumpPage) {
        return;
      }

      const page = parseInt(this.favoritesJumpPage);
      if (isNaN(page) || page < 1 || page > Math.ceil(this.favoritesTotal / this.favoritesPageSize)) {
        this.$message.warning('请输入有效的页码');
        return;
      }

      this.favoritesCurrentPage = page;
      // 实际应用中这里需要调用接口获取对应页的数据
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20px;
}

.page-layout {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.left-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
}

.user-info {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e6e6e6;

  .avatar {
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .user-id {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .register-date {
    font-size: 12px;
    color: #999;
  }
}

.sidebar-menu {
  .sidebar-menu-list {
    border-right: none;
  }

  .el-menu-item {
    height: 50px;
    line-height: 50px;

    i {
      margin-right: 5px;
      color: #666;
    }
  }

  .el-menu-item.is-active {
    background-color: #f0f9eb;
    color: #67c23a;

    i {
      color: #67c23a;
    }
  }
}

.content {
  flex: 1;
  padding: 20px;
}

.account-container {
  background-color: #fff;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 18px;
    font-weight: bold;
  }
}

.account-info {
  .info-item {
    display: flex;
    margin-bottom: 20px;
    line-height: 24px;

    .label {
      width: 120px;
      color: #666;
      text-align: right;
      padding-right: 10px;
    }

    .value {
      flex: 1;
      color: #333;
    }
  }
}

/* 系统设置样式 */
.settings-container {
  background-color: #fff;
}

.settings-header {
  padding-bottom: 15px;
  margin-bottom: 15px;

  .title {
    font-size: 18px;
    font-weight: bold;
  }
}

.settings-content {
  display: flex;
}

.settings-sidebar {
  width: 120px;
  border-right: 1px solid #eee;
  padding-right: 10px;
}

.settings-menu {
  .menu-item {
    padding: 10px 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;

    i {
      margin-right: 8px;
      color: #1890ff;
    }

    &.active {
      color: #1890ff;
      font-weight: bold;
    }

    &:hover {
      color: #1890ff;
    }
  }
}

.settings-main {
  flex: 1;
  padding-left: 20px;
}

.settings-section {
  .section-title {
    font-weight: bold;
    margin: 15px 0 10px;
    color: #333;
    font-size: 14px;
  }
}

.options-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;

  .el-checkbox {
    margin-right: 15px;
    margin-bottom: 10px;
    min-width: 80px;
  }
}

.download-settings {
  margin-bottom: 15px;

  .setting-item {
    display: flex;
    margin-bottom: 10px;
    font-size: 14px;

    .label {
      width: 80px;
      color: #666;
    }

    .value {
      flex: 1;
      color: #333;
    }
  }
}

.save-button {
  margin-top: 20px;

  .el-button {
    background-color: #1890ff;
    border-color: #1890ff;
    padding: 8px 20px;
  }
}

/* 我的下载样式 */
.download-container {
  background-color: #fff;
}

.download-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  margin-bottom: 15px;

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .actions {
    .el-button {
      margin-left: 10px;
    }
  }
}

.download-content {
  .el-table {
    margin-bottom: 20px;
  }

  .download-status {
    color: #67c23a;
  }
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;

  span {
    margin: 0 5px;
  }

  .jump-page-input {
    width: 50px;
    margin: 0 5px;
  }
}

/* 修改密码表单样式 */
.password-form {
  max-width: 500px;
  margin: 20px 0;

  .form-group {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .form-label {
      width: 100px;
      text-align: right;
      margin-right: 15px;
      color: #606266;
    }

    .el-input {
      width: 300px;
    }
  }

  .form-actions {
    margin-top: 30px;
    padding-left: 115px;

    .el-button {
      width: 100px;
    }
  }
}

/* 我的收藏样式 */
.favorite-container {
  background-color: #fff;
}

.favorite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 18px;
    font-weight: bold;
  }
}

.favorite-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .toolbar-left {
    display: flex;
    align-items: center;

    .el-checkbox {
      margin-right: 15px;
    }

    .el-button {
      margin-right: 10px;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;

    .date-filter {
      display: flex;
      align-items: center;
      margin-right: 15px;

      .date-separator {
        margin: 0 5px;
      }
    }

    .search-box {
      margin-right: 15px;
    }
  }
}

.favorite-content {
  margin-top: 20px;

  .favorite-list {
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .favorite-item {
      display: flex;
      padding: 15px;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .item-checkbox {
        margin-right: 15px;
        display: flex;
        align-items: center;
      }

      .item-content {
        flex: 1;

        .item-title {
          margin-bottom: 8px;

          .title-tag {
            display: inline-block;
            padding: 2px 6px;
            font-size: 12px;
            color: #fff;
            background-color: #f56c6c;
            border-radius: 2px;
            margin-right: 8px;
          }

          .title-text {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
        }

        .item-info {
          font-size: 13px;
          color: #909399;

          .info-time {
            margin-right: 15px;
          }
        }
      }

      .item-actions {
        display: flex;
        align-items: center;
        margin-left: 15px;

        .cancel-favorite-btn {
          color: #f56c6c;

          &:hover {
            color: #ff7c7c;
          }
        }
      }
    }
  }

  .favorite-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 10px 0;

    .pagination-info {
      font-size: 14px;
      color: #606266;

      span {
        font-weight: bold;
        color: #303133;
      }
    }

    .pagination-controls {
      flex: 1;
      text-align: center;
    }

    .pagination-jump {
      display: flex;
      align-items: center;

      span {
        margin: 0 5px;
        font-size: 14px;
        color: #606266;
      }

      .jump-page-input {
        width: 50px;
        margin: 0 5px;
      }

      .el-button {
        margin-left: 5px;
      }
    }
  }
}

/* 我的联系人样式 */
.contact-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .contact-tabs {
    .el-radio-group {
      .el-radio-button {
        margin-right: -1px;

        &:first-child .el-radio-button__inner {
          border-radius: 4px 0 0 4px;
        }

        &:last-child .el-radio-button__inner {
          border-radius: 0 4px 4px 0;
        }

        .el-radio-button__inner {
          padding: 8px 15px;
          font-size: 13px;
        }
      }
    }
  }
}

.contact-toolbar {
  margin-bottom: 20px;
}

.contact-content {
  .el-table {
    margin-bottom: 20px;
  }

  .delete-btn {
    color: #f56c6c;
  }
}

/* 素材库样式 */
.material-container {
  background-color: #fff;
}

.material-header {
  padding-bottom: 15px;
  margin-bottom: 15px;

  .title {
    font-size: 18px;
    font-weight: bold;
  }
}

.material-content {
  display: flex;
  flex-wrap: wrap;

  .material-add-box {
    width: 200px;
    height: 150px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-right: 20px;
    margin-bottom: 20px;

    &:hover {
      border-color: #1890ff;

      .add-icon, .add-text {
        color: #1890ff;
      }
    }

    .add-icon {
      font-size: 30px;
      color: #8c8c8c;
      margin-bottom: 10px;
    }

    .add-text {
      font-size: 14px;
      color: #8c8c8c;
    }
  }
}

.material-dialog {
  .el-dialog__header {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
  }

  .material-form {
    padding: 20px 0;

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .form-label {
        width: 80px;
        text-align: right;
        margin-right: 10px;
      }
    }
  }

  .dialog-footer {
    text-align: center;

    .el-button {
      width: 80px;
    }
  }
}

/* 更新说明样式 */
.updates-section {
  .updates-header {
    margin-bottom: 20px;

    .version-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .version-date {
      font-size: 12px;
      color: #999;
    }
  }

  .updates-content {
    display: flex;

    .version-list {
      width: 100px;
      border-right: 1px solid #eee;
      padding-right: 15px;
      margin-right: 20px;

      .version-item {
        padding: 8px 0;
        cursor: pointer;
        color: #666;
        font-size: 14px;
        text-align: center;
        border-radius: 4px;
        margin-bottom: 5px;

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          background-color: #e6f7ff;
          color: #1890ff;
          font-weight: bold;
        }
      }
    }

    .update-details {
      flex: 1;

      .update-notes {
        .note-item {
          display: flex;
          margin-bottom: 10px;
          line-height: 1.6;

          .note-number {
            margin-right: 5px;
            color: #666;
          }

          .note-content {
            flex: 1;

            b {
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}
</style>
