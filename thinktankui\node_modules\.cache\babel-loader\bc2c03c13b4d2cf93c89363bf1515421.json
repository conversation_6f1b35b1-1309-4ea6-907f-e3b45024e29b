{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core\\editor.js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\quill\\core\\editor.js", "mtime": 1749105929548}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\babel.config.js", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_parchment", "_quill<PERSON><PERSON><PERSON>", "_interopRequireWildcard", "_block", "_break", "_interopRequireDefault", "_cursor", "_text2", "_selection", "ASCII", "Editor", "scroll", "_classCallCheck2", "default", "delta", "<PERSON><PERSON><PERSON><PERSON>", "_createClass2", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "_this", "update", "<PERSON><PERSON><PERSON><PERSON>", "length", "batchStart", "normalizedDelta", "normalizeDel<PERSON>", "deleteDelta", "Delta", "normalizedOps", "splitOpLines", "ops", "slice", "reduce", "index", "op", "Op", "attributes", "isImplicitNewlinePrepended", "isImplicitNewlineAppended", "insert", "retain", "text", "endsWith", "descendant", "BlockEmbed", "insertAt", "_this$scroll$line", "line", "_this$scroll$line2", "_slicedToArray2", "offset", "formats", "merge", "bubbleFormats", "Block", "_line$descendant", "LeafBlot", "_line$descendant2", "leaf", "AttributeMap", "diff", "_typeof2", "Object", "keys", "isInlineEmbed", "query", "<PERSON><PERSON>", "INLINE", "_this$scroll$descenda", "_this$scroll$descenda2", "TextBlot", "EmbedBlot", "statics", "scope", "INLINE_BLOT", "_this$scroll$descenda3", "_this$scroll$descenda4", "push", "updateEmbedAt", "for<PERSON>ach", "name", "formatAt", "prependedLength", "<PERSON><PERSON><PERSON><PERSON>", "delete", "deleteAt", "batchEnd", "optimize", "deleteText", "formatLine", "_this2", "arguments", "undefined", "format", "lines", "Math", "max", "cloneDeep", "formatText", "_this3", "getContents", "concat", "getFormat", "leaves", "path", "_path", "blot", "descendants", "_map", "map", "blots", "shift", "combineFormats", "_map2", "lineFormats", "leafFormats", "_objectSpread2", "getHTML", "_this$scroll$line3", "_this$scroll$line4", "lineOffset", "lineLength", "isWithinLine", "convertHTML", "getText", "filter", "join", "insertContents", "contents", "change", "insertEmbed", "embed", "_defineProperty2", "insertText", "_this4", "replace", "isBlank", "children", "head", "blotName", "block", "Break", "removeFormat", "_this$scroll$line5", "_this$scroll$line6", "suffixLength", "suffix", "mutations", "selectionInfo", "<PERSON><PERSON><PERSON><PERSON>", "type", "target", "data", "match", "find", "textBlot", "oldValue", "CursorBlot", "CONTENTS", "oldText", "newText", "relativeSelectionInfo", "oldRange", "shiftRange", "newRange", "diff<PERSON><PERSON><PERSON>", "compose", "isEqual", "convertListHTML", "items", "lastIndent", "types", "_getListType", "getListType", "pop", "_getListType2", "endTag", "_items", "_toArray2", "_items$", "child", "indent", "rest", "_getListType3", "_getListType4", "tag", "attribute", "previousType", "_getListType5", "_getListType6", "isRoot", "html", "escapeText", "ParentBlot", "forEachAt", "<PERSON><PERSON><PERSON><PERSON>", "list", "parts", "_blot$domNode", "domNode", "outerHTML", "innerHTML", "_outerHTML$split", "split", "_outerHTML$split2", "start", "end", "Element", "combined", "merged", "combinedValue", "Array", "isArray", "indexOf", "_ref", "amount", "Range", "_default", "exports"], "sources": ["../../src/core/editor.ts"], "sourcesContent": ["import { cloneDeep, isEqual, merge } from 'lodash-es';\nimport { LeafBlot, EmbedBlot, Scope, ParentBlot } from 'parchment';\nimport type { Blot } from 'parchment';\nimport Delta, { AttributeMap, Op } from 'quill-delta';\nimport Block, { BlockEmbed, bubbleFormats } from '../blots/block.js';\nimport Break from '../blots/break.js';\nimport CursorBlot from '../blots/cursor.js';\nimport type Scroll from '../blots/scroll.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport { Range } from './selection.js';\n\nconst ASCII = /^[ -~]*$/;\n\ntype SelectionInfo = {\n  newRange: Range;\n  oldRange: Range;\n};\n\nclass Editor {\n  scroll: Scroll;\n  delta: Delta;\n\n  constructor(scroll: Scroll) {\n    this.scroll = scroll;\n    this.delta = this.getDelta();\n  }\n\n  applyDelta(delta: Delta): Delta {\n    this.scroll.update();\n    let scrollLength = this.scroll.length();\n    this.scroll.batchStart();\n    const normalizedDelta = normalizeDelta(delta);\n    const deleteDelta = new Delta();\n    const normalizedOps = splitOpLines(normalizedDelta.ops.slice());\n    normalizedOps.reduce((index, op) => {\n      const length = Op.length(op);\n      let attributes = op.attributes || {};\n      let isImplicitNewlinePrepended = false;\n      let isImplicitNewlineAppended = false;\n      if (op.insert != null) {\n        deleteDelta.retain(length);\n        if (typeof op.insert === 'string') {\n          const text = op.insert;\n          isImplicitNewlineAppended =\n            !text.endsWith('\\n') &&\n            (scrollLength <= index ||\n              !!this.scroll.descendant(BlockEmbed, index)[0]);\n          this.scroll.insertAt(index, text);\n          const [line, offset] = this.scroll.line(index);\n          let formats = merge({}, bubbleFormats(line));\n          if (line instanceof Block) {\n            const [leaf] = line.descendant(LeafBlot, offset);\n            if (leaf) {\n              formats = merge(formats, bubbleFormats(leaf));\n            }\n          }\n          attributes = AttributeMap.diff(formats, attributes) || {};\n        } else if (typeof op.insert === 'object') {\n          const key = Object.keys(op.insert)[0]; // There should only be one key\n          if (key == null) return index;\n          const isInlineEmbed = this.scroll.query(key, Scope.INLINE) != null;\n          if (isInlineEmbed) {\n            if (\n              scrollLength <= index ||\n              !!this.scroll.descendant(BlockEmbed, index)[0]\n            ) {\n              isImplicitNewlineAppended = true;\n            }\n          } else if (index > 0) {\n            const [leaf, offset] = this.scroll.descendant(LeafBlot, index - 1);\n            if (leaf instanceof TextBlot) {\n              const text = leaf.value();\n              if (text[offset] !== '\\n') {\n                isImplicitNewlinePrepended = true;\n              }\n            } else if (\n              leaf instanceof EmbedBlot &&\n              leaf.statics.scope === Scope.INLINE_BLOT\n            ) {\n              isImplicitNewlinePrepended = true;\n            }\n          }\n          this.scroll.insertAt(index, key, op.insert[key]);\n\n          if (isInlineEmbed) {\n            const [leaf] = this.scroll.descendant(LeafBlot, index);\n            if (leaf) {\n              const formats = merge({}, bubbleFormats(leaf));\n              attributes = AttributeMap.diff(formats, attributes) || {};\n            }\n          }\n        }\n        scrollLength += length;\n      } else {\n        deleteDelta.push(op);\n\n        if (op.retain !== null && typeof op.retain === 'object') {\n          const key = Object.keys(op.retain)[0];\n          if (key == null) return index;\n          this.scroll.updateEmbedAt(index, key, op.retain[key]);\n        }\n      }\n      Object.keys(attributes).forEach((name) => {\n        this.scroll.formatAt(index, length, name, attributes[name]);\n      });\n      const prependedLength = isImplicitNewlinePrepended ? 1 : 0;\n      const addedLength = isImplicitNewlineAppended ? 1 : 0;\n      scrollLength += prependedLength + addedLength;\n      deleteDelta.retain(prependedLength);\n      deleteDelta.delete(addedLength);\n      return index + length + prependedLength + addedLength;\n    }, 0);\n    deleteDelta.reduce((index, op) => {\n      if (typeof op.delete === 'number') {\n        this.scroll.deleteAt(index, op.delete);\n        return index;\n      }\n      return index + Op.length(op);\n    }, 0);\n    this.scroll.batchEnd();\n    this.scroll.optimize();\n    return this.update(normalizedDelta);\n  }\n\n  deleteText(index: number, length: number): Delta {\n    this.scroll.deleteAt(index, length);\n    return this.update(new Delta().retain(index).delete(length));\n  }\n\n  formatLine(\n    index: number,\n    length: number,\n    formats: Record<string, unknown> = {},\n  ): Delta {\n    this.scroll.update();\n    Object.keys(formats).forEach((format) => {\n      this.scroll.lines(index, Math.max(length, 1)).forEach((line) => {\n        line.format(format, formats[format]);\n      });\n    });\n    this.scroll.optimize();\n    const delta = new Delta().retain(index).retain(length, cloneDeep(formats));\n    return this.update(delta);\n  }\n\n  formatText(\n    index: number,\n    length: number,\n    formats: Record<string, unknown> = {},\n  ): Delta {\n    Object.keys(formats).forEach((format) => {\n      this.scroll.formatAt(index, length, format, formats[format]);\n    });\n    const delta = new Delta().retain(index).retain(length, cloneDeep(formats));\n    return this.update(delta);\n  }\n\n  getContents(index: number, length: number): Delta {\n    return this.delta.slice(index, index + length);\n  }\n\n  getDelta(): Delta {\n    return this.scroll.lines().reduce((delta, line) => {\n      return delta.concat(line.delta());\n    }, new Delta());\n  }\n\n  getFormat(index: number, length = 0): Record<string, unknown> {\n    let lines: (Block | BlockEmbed)[] = [];\n    let leaves: LeafBlot[] = [];\n    if (length === 0) {\n      this.scroll.path(index).forEach((path) => {\n        const [blot] = path;\n        if (blot instanceof Block) {\n          lines.push(blot);\n        } else if (blot instanceof LeafBlot) {\n          leaves.push(blot);\n        }\n      });\n    } else {\n      lines = this.scroll.lines(index, length);\n      leaves = this.scroll.descendants(LeafBlot, index, length);\n    }\n    const [lineFormats, leafFormats] = [lines, leaves].map((blots) => {\n      const blot = blots.shift();\n      if (blot == null) return {};\n      let formats = bubbleFormats(blot);\n      while (Object.keys(formats).length > 0) {\n        const blot = blots.shift();\n        if (blot == null) return formats;\n        formats = combineFormats(bubbleFormats(blot), formats);\n      }\n      return formats;\n    });\n    return { ...lineFormats, ...leafFormats };\n  }\n\n  getHTML(index: number, length: number): string {\n    const [line, lineOffset] = this.scroll.line(index);\n    if (line) {\n      const lineLength = line.length();\n      const isWithinLine = line.length() >= lineOffset + length;\n      if (isWithinLine && !(lineOffset === 0 && length === lineLength)) {\n        return convertHTML(line, lineOffset, length, true);\n      }\n      return convertHTML(this.scroll, index, length, true);\n    }\n    return '';\n  }\n\n  getText(index: number, length: number): string {\n    return this.getContents(index, length)\n      .filter((op) => typeof op.insert === 'string')\n      .map((op) => op.insert)\n      .join('');\n  }\n\n  insertContents(index: number, contents: Delta): Delta {\n    const normalizedDelta = normalizeDelta(contents);\n    const change = new Delta().retain(index).concat(normalizedDelta);\n    this.scroll.insertContents(index, normalizedDelta);\n    return this.update(change);\n  }\n\n  insertEmbed(index: number, embed: string, value: unknown): Delta {\n    this.scroll.insertAt(index, embed, value);\n    return this.update(new Delta().retain(index).insert({ [embed]: value }));\n  }\n\n  insertText(\n    index: number,\n    text: string,\n    formats: Record<string, unknown> = {},\n  ): Delta {\n    text = text.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n    this.scroll.insertAt(index, text);\n    Object.keys(formats).forEach((format) => {\n      this.scroll.formatAt(index, text.length, format, formats[format]);\n    });\n    return this.update(\n      new Delta().retain(index).insert(text, cloneDeep(formats)),\n    );\n  }\n\n  isBlank(): boolean {\n    if (this.scroll.children.length === 0) return true;\n    if (this.scroll.children.length > 1) return false;\n    const blot = this.scroll.children.head;\n    if (blot?.statics.blotName !== Block.blotName) return false;\n    const block = blot as Block;\n    if (block.children.length > 1) return false;\n    return block.children.head instanceof Break;\n  }\n\n  removeFormat(index: number, length: number): Delta {\n    const text = this.getText(index, length);\n    const [line, offset] = this.scroll.line(index + length);\n    let suffixLength = 0;\n    let suffix = new Delta();\n    if (line != null) {\n      suffixLength = line.length() - offset;\n      suffix = line\n        .delta()\n        .slice(offset, offset + suffixLength - 1)\n        .insert('\\n');\n    }\n    const contents = this.getContents(index, length + suffixLength);\n    const diff = contents.diff(new Delta().insert(text).concat(suffix));\n    const delta = new Delta().retain(index).concat(diff);\n    return this.applyDelta(delta);\n  }\n\n  update(\n    change: Delta | null,\n    mutations: MutationRecord[] = [],\n    selectionInfo: SelectionInfo | undefined = undefined,\n  ): Delta {\n    const oldDelta = this.delta;\n    if (\n      mutations.length === 1 &&\n      mutations[0].type === 'characterData' &&\n      // @ts-expect-error Fix me later\n      mutations[0].target.data.match(ASCII) &&\n      this.scroll.find(mutations[0].target)\n    ) {\n      // Optimization for character changes\n      const textBlot = this.scroll.find(mutations[0].target) as Blot;\n      const formats = bubbleFormats(textBlot);\n      const index = textBlot.offset(this.scroll);\n      // @ts-expect-error Fix me later\n      const oldValue = mutations[0].oldValue.replace(CursorBlot.CONTENTS, '');\n      const oldText = new Delta().insert(oldValue);\n      // @ts-expect-error\n      const newText = new Delta().insert(textBlot.value());\n      const relativeSelectionInfo = selectionInfo && {\n        oldRange: shiftRange(selectionInfo.oldRange, -index),\n        newRange: shiftRange(selectionInfo.newRange, -index),\n      };\n      const diffDelta = new Delta()\n        .retain(index)\n        .concat(oldText.diff(newText, relativeSelectionInfo));\n      change = diffDelta.reduce((delta, op) => {\n        if (op.insert) {\n          return delta.insert(op.insert, formats);\n        }\n        return delta.push(op);\n      }, new Delta());\n      this.delta = oldDelta.compose(change);\n    } else {\n      this.delta = this.getDelta();\n      if (!change || !isEqual(oldDelta.compose(change), this.delta)) {\n        change = oldDelta.diff(this.delta, selectionInfo);\n      }\n    }\n    return change;\n  }\n}\n\ninterface ListItem {\n  child: Blot;\n  offset: number;\n  length: number;\n  indent: number;\n  type: string;\n}\nfunction convertListHTML(\n  items: ListItem[],\n  lastIndent: number,\n  types: string[],\n): string {\n  if (items.length === 0) {\n    const [endTag] = getListType(types.pop());\n    if (lastIndent <= 0) {\n      return `</li></${endTag}>`;\n    }\n    return `</li></${endTag}>${convertListHTML([], lastIndent - 1, types)}`;\n  }\n  const [{ child, offset, length, indent, type }, ...rest] = items;\n  const [tag, attribute] = getListType(type);\n  if (indent > lastIndent) {\n    types.push(type);\n    if (indent === lastIndent + 1) {\n      return `<${tag}><li${attribute}>${convertHTML(\n        child,\n        offset,\n        length,\n      )}${convertListHTML(rest, indent, types)}`;\n    }\n    return `<${tag}><li>${convertListHTML(items, lastIndent + 1, types)}`;\n  }\n  const previousType = types[types.length - 1];\n  if (indent === lastIndent && type === previousType) {\n    return `</li><li${attribute}>${convertHTML(\n      child,\n      offset,\n      length,\n    )}${convertListHTML(rest, indent, types)}`;\n  }\n  const [endTag] = getListType(types.pop());\n  return `</li></${endTag}>${convertListHTML(items, lastIndent - 1, types)}`;\n}\n\nfunction convertHTML(\n  blot: Blot,\n  index: number,\n  length: number,\n  isRoot = false,\n): string {\n  if ('html' in blot && typeof blot.html === 'function') {\n    return blot.html(index, length);\n  }\n  if (blot instanceof TextBlot) {\n    return escapeText(blot.value().slice(index, index + length));\n  }\n  if (blot instanceof ParentBlot) {\n    // TODO fix API\n    if (blot.statics.blotName === 'list-container') {\n      const items: any[] = [];\n      blot.children.forEachAt(index, length, (child, offset, childLength) => {\n        const formats =\n          'formats' in child && typeof child.formats === 'function'\n            ? child.formats()\n            : {};\n        items.push({\n          child,\n          offset,\n          length: childLength,\n          indent: formats.indent || 0,\n          type: formats.list,\n        });\n      });\n      return convertListHTML(items, -1, []);\n    }\n    const parts: string[] = [];\n    blot.children.forEachAt(index, length, (child, offset, childLength) => {\n      parts.push(convertHTML(child, offset, childLength));\n    });\n    if (isRoot || blot.statics.blotName === 'list') {\n      return parts.join('');\n    }\n    const { outerHTML, innerHTML } = blot.domNode as Element;\n    const [start, end] = outerHTML.split(`>${innerHTML}<`);\n    // TODO cleanup\n    if (start === '<table') {\n      return `<table style=\"border: 1px solid #000;\">${parts.join('')}<${end}`;\n    }\n    return `${start}>${parts.join('')}<${end}`;\n  }\n  return blot.domNode instanceof Element ? blot.domNode.outerHTML : '';\n}\n\nfunction combineFormats(\n  formats: Record<string, unknown>,\n  combined: Record<string, unknown>,\n): Record<string, unknown> {\n  return Object.keys(combined).reduce(\n    (merged, name) => {\n      if (formats[name] == null) return merged;\n      const combinedValue = combined[name];\n      if (combinedValue === formats[name]) {\n        merged[name] = combinedValue;\n      } else if (Array.isArray(combinedValue)) {\n        if (combinedValue.indexOf(formats[name]) < 0) {\n          merged[name] = combinedValue.concat([formats[name]]);\n        } else {\n          // If style already exists, don't add to an array, but don't lose other styles\n          merged[name] = combinedValue;\n        }\n      } else {\n        merged[name] = [combinedValue, formats[name]];\n      }\n      return merged;\n    },\n    {} as Record<string, unknown>,\n  );\n}\n\nfunction getListType(type: string | undefined) {\n  const tag = type === 'ordered' ? 'ol' : 'ul';\n  switch (type) {\n    case 'checked':\n      return [tag, ' data-list=\"checked\"'];\n    case 'unchecked':\n      return [tag, ' data-list=\"unchecked\"'];\n    default:\n      return [tag, ''];\n  }\n}\n\nfunction normalizeDelta(delta: Delta) {\n  return delta.reduce((normalizedDelta, op) => {\n    if (typeof op.insert === 'string') {\n      const text = op.insert.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n      return normalizedDelta.insert(text, op.attributes);\n    }\n    return normalizedDelta.push(op);\n  }, new Delta());\n}\n\nfunction shiftRange({ index, length }: Range, amount: number) {\n  return new Range(index + amount, length);\n}\n\nfunction splitOpLines(ops: Op[]) {\n  const split: Op[] = [];\n  ops.forEach((op) => {\n    if (typeof op.insert === 'string') {\n      const lines = op.insert.split('\\n');\n      lines.forEach((line, index) => {\n        if (index) split.push({ insert: '\\n', attributes: op.attributes });\n        if (line) split.push({ insert: line, attributes: op.attributes });\n      });\n    } else {\n      split.push(op);\n    }\n  });\n\n  return split;\n}\n\nexport default Editor;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAEA,IAAAE,WAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAD,uBAAA,CAAAH,OAAA;AACA,IAAAK,MAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,OAAA,GAAAD,sBAAA,CAAAN,OAAA;AAEA,IAAAQ,MAAA,GAAAL,uBAAA,CAAAH,OAAA;AACA,IAAAS,UAAA,GAAAT,OAAA;AAEA,IAAMU,KAAK,GAAG,UAAU;AAAA,IAOlBC,MAAM;EAIV,SAAAA,OAAYC,MAAc,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC9B;EAAA,WAAAC,aAAA,CAAAH,OAAA,EAAAH,MAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAC,UAAUA,CAACL,KAAY,EAAS;MAAA,IAAAM,KAAA;MAC9B,IAAI,CAACT,MAAM,CAACU,MAAM,CAAC,CAAC;MACpB,IAAIC,YAAY,GAAG,IAAI,CAACX,MAAM,CAACY,MAAM,CAAC,CAAC;MACvC,IAAI,CAACZ,MAAM,CAACa,UAAU,CAAC,CAAC;MACxB,IAAMC,eAAe,GAAGC,cAAc,CAACZ,KAAK,CAAC;MAC7C,IAAMa,WAAW,GAAG,IAAIC,mBAAK,CAAC,CAAC;MAC/B,IAAMC,aAAa,GAAGC,YAAY,CAACL,eAAe,CAACM,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC;MAC/DH,aAAa,CAACI,MAAM,CAAC,UAACC,KAAK,EAAEC,EAAE,EAAK;QAClC,IAAMZ,MAAM,GAAGa,cAAE,CAACb,MAAM,CAACY,EAAE,CAAC;QAC5B,IAAIE,UAAU,GAAGF,EAAE,CAACE,UAAU,IAAI,CAAC,CAAC;QACpC,IAAIC,0BAA0B,GAAG,KAAK;QACtC,IAAIC,yBAAyB,GAAG,KAAK;QACrC,IAAIJ,EAAE,CAACK,MAAM,IAAI,IAAI,EAAE;UACrBb,WAAW,CAACc,MAAM,CAAClB,MAAM,CAAC;UAC1B,IAAI,OAAOY,EAAE,CAACK,MAAM,KAAK,QAAQ,EAAE;YACjC,IAAME,IAAI,GAAGP,EAAE,CAACK,MAAM;YACtBD,yBAAyB,GACvB,CAACG,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,KACnBrB,YAAY,IAAIY,KAAK,IACpB,CAAC,CAACd,KAAI,CAACT,MAAM,CAACiC,UAAU,CAACC,iBAAU,EAAEX,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACnDd,KAAI,CAACT,MAAM,CAACmC,QAAQ,CAACZ,KAAK,EAAEQ,IAAI,CAAC;YACjC,IAAAK,iBAAA,GAAuB3B,KAAI,CAACT,MAAM,CAACqC,IAAI,CAACd,KAAK,CAAC;cAAAe,kBAAA,OAAAC,eAAA,CAAArC,OAAA,EAAAkC,iBAAA;cAAvCC,IAAI,GAAAC,kBAAA;cAAEE,MAAM,GAAAF,kBAAA;YACnB,IAAIG,OAAO,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAE,IAAAC,oBAAa,EAACN,IAAI,CAAC,CAAC;YAC5C,IAAIA,IAAI,YAAYO,cAAK,EAAE;cACzB,IAAAC,gBAAA,GAAeR,IAAI,CAACJ,UAAU,CAACa,mBAAQ,EAAEN,MAAM,CAAC;gBAAAO,iBAAA,OAAAR,eAAA,CAAArC,OAAA,EAAA2C,gBAAA;gBAAzCG,IAAI,GAAAD,iBAAA;cACX,IAAIC,IAAI,EAAE;gBACRP,OAAO,GAAG,IAAAC,eAAK,EAACD,OAAO,EAAE,IAAAE,oBAAa,EAACK,IAAI,CAAC,CAAC;cAC/C;YACF;YACAtB,UAAU,GAAGuB,wBAAY,CAACC,IAAI,CAACT,OAAO,EAAEf,UAAU,CAAC,IAAI,CAAC,CAAC;UAC3D,CAAC,MAAM,IAAI,IAAAyB,QAAA,CAAAjD,OAAA,EAAOsB,EAAE,CAACK,MAAM,MAAK,QAAQ,EAAE;YACxC,IAAMvB,GAAG,GAAG8C,MAAM,CAACC,IAAI,CAAC7B,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,IAAIvB,GAAG,IAAI,IAAI,EAAE,OAAOiB,KAAK;YAC7B,IAAM+B,aAAa,GAAG7C,KAAI,CAACT,MAAM,CAACuD,KAAK,CAACjD,GAAG,EAAEkD,gBAAK,CAACC,MAAM,CAAC,IAAI,IAAI;YAClE,IAAIH,aAAa,EAAE;cACjB,IACE3C,YAAY,IAAIY,KAAK,IACrB,CAAC,CAACd,KAAI,CAACT,MAAM,CAACiC,UAAU,CAACC,iBAAU,EAAEX,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9C;gBACAK,yBAAyB,GAAG,IAAI;cAClC;YACF,CAAC,MAAM,IAAIL,KAAK,GAAG,CAAC,EAAE;cACpB,IAAAmC,qBAAA,GAAuBjD,KAAI,CAACT,MAAM,CAACiC,UAAU,CAACa,mBAAQ,EAAEvB,KAAK,GAAG,CAAC,CAAC;gBAAAoC,sBAAA,OAAApB,eAAA,CAAArC,OAAA,EAAAwD,qBAAA;gBAA3DV,KAAI,GAAAW,sBAAA;gBAAEnB,OAAM,GAAAmB,sBAAA;cACnB,IAAIX,KAAI,YAAYY,cAAQ,EAAE;gBAC5B,IAAM7B,KAAI,GAAGiB,KAAI,CAACzC,KAAK,CAAC,CAAC;gBACzB,IAAIwB,KAAI,CAACS,OAAM,CAAC,KAAK,IAAI,EAAE;kBACzBb,0BAA0B,GAAG,IAAI;gBACnC;cACF,CAAC,MAAM,IACLqB,KAAI,YAAYa,oBAAS,IACzBb,KAAI,CAACc,OAAO,CAACC,KAAK,KAAKP,gBAAK,CAACQ,WAAW,EACxC;gBACArC,0BAA0B,GAAG,IAAI;cACnC;YACF;YACAlB,KAAI,CAACT,MAAM,CAACmC,QAAQ,CAACZ,KAAK,EAAEjB,GAAG,EAAEkB,EAAE,CAACK,MAAM,CAACvB,GAAG,CAAC,CAAC;YAEhD,IAAIgD,aAAa,EAAE;cACjB,IAAAW,sBAAA,GAAexD,KAAI,CAACT,MAAM,CAACiC,UAAU,CAACa,mBAAQ,EAAEvB,KAAK,CAAC;gBAAA2C,sBAAA,OAAA3B,eAAA,CAAArC,OAAA,EAAA+D,sBAAA;gBAA/CjB,MAAI,GAAAkB,sBAAA;cACX,IAAIlB,MAAI,EAAE;gBACR,IAAMP,QAAO,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAE,IAAAC,oBAAa,EAACK,MAAI,CAAC,CAAC;gBAC9CtB,UAAU,GAAGuB,wBAAY,CAACC,IAAI,CAACT,QAAO,EAAEf,UAAU,CAAC,IAAI,CAAC,CAAC;cAC3D;YACF;UACF;UACAf,YAAY,IAAIC,MAAM;QACxB,CAAC,MAAM;UACLI,WAAW,CAACmD,IAAI,CAAC3C,EAAE,CAAC;UAEpB,IAAIA,EAAE,CAACM,MAAM,KAAK,IAAI,IAAI,IAAAqB,QAAA,CAAAjD,OAAA,EAAOsB,EAAE,CAACM,MAAM,MAAK,QAAQ,EAAE;YACvD,IAAMxB,IAAG,GAAG8C,MAAM,CAACC,IAAI,CAAC7B,EAAE,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;YACrC,IAAIxB,IAAG,IAAI,IAAI,EAAE,OAAOiB,KAAK;YAC7Bd,KAAI,CAACT,MAAM,CAACoE,aAAa,CAAC7C,KAAK,EAAEjB,IAAG,EAAEkB,EAAE,CAACM,MAAM,CAACxB,IAAG,CAAC,CAAC;UACvD;QACF;QACA8C,MAAM,CAACC,IAAI,CAAC3B,UAAU,CAAC,CAAC2C,OAAO,CAAE,UAAAC,IAAI,EAAK;UACxC7D,KAAI,CAACT,MAAM,CAACuE,QAAQ,CAAChD,KAAK,EAAEX,MAAM,EAAE0D,IAAI,EAAE5C,UAAU,CAAC4C,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC;QACF,IAAME,eAAe,GAAG7C,0BAA0B,GAAG,CAAC,GAAG,CAAC;QAC1D,IAAM8C,WAAW,GAAG7C,yBAAyB,GAAG,CAAC,GAAG,CAAC;QACrDjB,YAAY,IAAI6D,eAAe,GAAGC,WAAW;QAC7CzD,WAAW,CAACc,MAAM,CAAC0C,eAAe,CAAC;QACnCxD,WAAW,CAAC0D,MAAM,CAACD,WAAW,CAAC;QAC/B,OAAOlD,KAAK,GAAGX,MAAM,GAAG4D,eAAe,GAAGC,WAAW;MACvD,CAAC,EAAE,CAAC,CAAC;MACLzD,WAAW,CAACM,MAAM,CAAC,UAACC,KAAK,EAAEC,EAAE,EAAK;QAChC,IAAI,OAAOA,EAAE,CAACkD,MAAM,KAAK,QAAQ,EAAE;UACjCjE,KAAI,CAACT,MAAM,CAAC2E,QAAQ,CAACpD,KAAK,EAAEC,EAAE,CAACkD,MAAM,CAAC;UACtC,OAAOnD,KAAK;QACd;QACA,OAAOA,KAAK,GAAGE,cAAE,CAACb,MAAM,CAACY,EAAE,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;MACL,IAAI,CAACxB,MAAM,CAAC4E,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC5E,MAAM,CAAC6E,QAAQ,CAAC,CAAC;MACtB,OAAO,IAAI,CAACnE,MAAM,CAACI,eAAe,CAAC;IACrC;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAEA,SAAAuE,UAAUA,CAACvD,KAAa,EAAEX,MAAc,EAAS;MAC/C,IAAI,CAACZ,MAAM,CAAC2E,QAAQ,CAACpD,KAAK,EAAEX,MAAM,CAAC;MACnC,OAAO,IAAI,CAACF,MAAM,CAAC,IAAIO,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACmD,MAAM,CAAC9D,MAAM,CAAC,CAAC;IAC9D;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAwE,UAAUA,CACRxD,KAAa,EACbX,MAAc,EAEP;MAAA,IAAAoE,MAAA;MAAA,IADPvC,OAAgC,GAAAwC,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAErC,IAAI,CAACjF,MAAM,CAACU,MAAM,CAAC,CAAC;MACpB0C,MAAM,CAACC,IAAI,CAACZ,OAAO,CAAC,CAAC4B,OAAO,CAAE,UAAAc,MAAM,EAAK;QACvCH,MAAI,CAAChF,MAAM,CAACoF,KAAK,CAAC7D,KAAK,EAAE8D,IAAI,CAACC,GAAG,CAAC1E,MAAM,EAAE,CAAC,CAAC,CAAC,CAACyD,OAAO,CAAE,UAAAhC,IAAI,EAAK;UAC9DA,IAAI,CAAC8C,MAAM,CAACA,MAAM,EAAE1C,OAAO,CAAC0C,MAAM,CAAC,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnF,MAAM,CAAC6E,QAAQ,CAAC,CAAC;MACtB,IAAM1E,KAAK,GAAG,IAAIc,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACO,MAAM,CAAClB,MAAM,EAAE,IAAA2E,mBAAS,EAAC9C,OAAO,CAAC,CAAC;MAC1E,OAAO,IAAI,CAAC/B,MAAM,CAACP,KAAK,CAAC;IAC3B;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAiF,UAAUA,CACRjE,KAAa,EACbX,MAAc,EAEP;MAAA,IAAA6E,MAAA;MAAA,IADPhD,OAAgC,GAAAwC,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAErC7B,MAAM,CAACC,IAAI,CAACZ,OAAO,CAAC,CAAC4B,OAAO,CAAE,UAAAc,MAAM,EAAK;QACvCM,MAAI,CAACzF,MAAM,CAACuE,QAAQ,CAAChD,KAAK,EAAEX,MAAM,EAAEuE,MAAM,EAAE1C,OAAO,CAAC0C,MAAM,CAAC,CAAC;MAC9D,CAAC,CAAC;MACF,IAAMhF,KAAK,GAAG,IAAIc,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACO,MAAM,CAAClB,MAAM,EAAE,IAAA2E,mBAAS,EAAC9C,OAAO,CAAC,CAAC;MAC1E,OAAO,IAAI,CAAC/B,MAAM,CAACP,KAAK,CAAC;IAC3B;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAmF,WAAWA,CAACnE,KAAa,EAAEX,MAAc,EAAS;MAChD,OAAO,IAAI,CAACT,KAAK,CAACkB,KAAK,CAACE,KAAK,EAAEA,KAAK,GAAGX,MAAM,CAAC;IAChD;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAH,QAAQA,CAAA,EAAU;MAChB,OAAO,IAAI,CAACJ,MAAM,CAACoF,KAAK,CAAC,CAAC,CAAC9D,MAAM,CAAC,UAACnB,KAAK,EAAEkC,IAAI,EAAK;QACjD,OAAOlC,KAAK,CAACwF,MAAM,CAACtD,IAAI,CAAClC,KAAK,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,IAAIc,mBAAK,CAAC,CAAC,CAAC;IACjB;EAAA;IAAAX,GAAA;IAAAC,KAAA,EAEA,SAAAqF,SAASA,CAACrE,KAAa,EAAuC;MAAA,IAArCX,MAAM,GAAAqE,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;MACjC,IAAIG,KAA6B,GAAG,EAAE;MACtC,IAAIS,MAAkB,GAAG,EAAE;MAC3B,IAAIjF,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAACZ,MAAM,CAAC8F,IAAI,CAACvE,KAAK,CAAC,CAAC8C,OAAO,CAAE,UAAAyB,IAAI,EAAK;UACxC,IAAAC,KAAA,OAAAxD,eAAA,CAAArC,OAAA,EAAe4F,IAAI;YAAZE,IAAI,GAAAD,KAAA;UACX,IAAIC,IAAI,YAAYpD,cAAK,EAAE;YACzBwC,KAAK,CAACjB,IAAI,CAAC6B,IAAI,CAAC;UAClB,CAAC,MAAM,IAAIA,IAAI,YAAYlD,mBAAQ,EAAE;YACnC+C,MAAM,CAAC1B,IAAI,CAAC6B,IAAI,CAAC;UACnB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLZ,KAAK,GAAG,IAAI,CAACpF,MAAM,CAACoF,KAAK,CAAC7D,KAAK,EAAEX,MAAM,CAAC;QACxCiF,MAAM,GAAG,IAAI,CAAC7F,MAAM,CAACiG,WAAW,CAACnD,mBAAQ,EAAEvB,KAAK,EAAEX,MAAM,CAAC;MAC3D;MACA,IAAAsF,IAAA,GAAmC,CAACd,KAAK,EAAES,MAAM,CAAC,CAACM,GAAG,CAAE,UAAAC,KAAK,EAAK;UAChE,IAAMJ,IAAI,GAAGI,KAAK,CAACC,KAAK,CAAC,CAAC;UAC1B,IAAIL,IAAI,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;UAC3B,IAAIvD,OAAO,GAAG,IAAAE,oBAAa,EAACqD,IAAI,CAAC;UACjC,OAAO5C,MAAM,CAACC,IAAI,CAACZ,OAAO,CAAC,CAAC7B,MAAM,GAAG,CAAC,EAAE;YACtC,IAAMoF,KAAI,GAAGI,KAAK,CAACC,KAAK,CAAC,CAAC;YAC1B,IAAIL,KAAI,IAAI,IAAI,EAAE,OAAOvD,OAAO;YAChCA,OAAO,GAAG6D,cAAc,CAAC,IAAA3D,oBAAa,EAACqD,KAAI,CAAC,EAAEvD,OAAO,CAAC;UACxD;UACA,OAAOA,OAAO;QAChB,CAAC,CAAC;QAAA8D,KAAA,OAAAhE,eAAA,CAAArC,OAAA,EAAAgG,IAAA;QAVKM,WAAW,GAAAD,KAAA;QAAEE,WAAW,GAAAF,KAAA;MAW/B,WAAAG,cAAA,CAAAxG,OAAA,MAAAwG,cAAA,CAAAxG,OAAA,MAAYsG,WAAW,GAAKC,WAAA;IAC9B;EAAA;IAAAnG,GAAA;IAAAC,KAAA,EAEA,SAAAoG,OAAOA,CAACpF,KAAa,EAAEX,MAAc,EAAU;MAC7C,IAAAgG,kBAAA,GAA2B,IAAI,CAAC5G,MAAM,CAACqC,IAAI,CAACd,KAAK,CAAC;QAAAsF,kBAAA,OAAAtE,eAAA,CAAArC,OAAA,EAAA0G,kBAAA;QAA3CvE,IAAI,GAAAwE,kBAAA;QAAEC,UAAU,GAAAD,kBAAA;MACvB,IAAIxE,IAAI,EAAE;QACR,IAAM0E,UAAU,GAAG1E,IAAI,CAACzB,MAAM,CAAC,CAAC;QAChC,IAAMoG,YAAY,GAAG3E,IAAI,CAACzB,MAAM,CAAC,CAAC,IAAIkG,UAAU,GAAGlG,MAAM;QACzD,IAAIoG,YAAY,IAAI,EAAEF,UAAU,KAAK,CAAC,IAAIlG,MAAM,KAAKmG,UAAU,CAAC,EAAE;UAChE,OAAOE,WAAW,CAAC5E,IAAI,EAAEyE,UAAU,EAAElG,MAAM,EAAE,IAAI,CAAC;QACpD;QACA,OAAOqG,WAAW,CAAC,IAAI,CAACjH,MAAM,EAAEuB,KAAK,EAAEX,MAAM,EAAE,IAAI,CAAC;MACtD;MACA,OAAO,EAAE;IACX;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAA2G,OAAOA,CAAC3F,KAAa,EAAEX,MAAc,EAAU;MAC7C,OAAO,IAAI,CAAC8E,WAAW,CAACnE,KAAK,EAAEX,MAAM,CAAC,CACnCuG,MAAM,CAAE,UAAA3F,EAAE;QAAA,OAAK,OAAOA,EAAE,CAACK,MAAM,KAAK,QAAQ;MAAA,EAAC,CAC7CsE,GAAG,CAAE,UAAA3E,EAAE;QAAA,OAAKA,EAAE,CAACK,MAAM;MAAA,EAAC,CACtBuF,IAAI,CAAC,EAAE,CAAC;IACb;EAAA;IAAA9G,GAAA;IAAAC,KAAA,EAEA,SAAA8G,cAAcA,CAAC9F,KAAa,EAAE+F,QAAe,EAAS;MACpD,IAAMxG,eAAe,GAAGC,cAAc,CAACuG,QAAQ,CAAC;MAChD,IAAMC,MAAM,GAAG,IAAItG,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACoE,MAAM,CAAC7E,eAAe,CAAC;MAChE,IAAI,CAACd,MAAM,CAACqH,cAAc,CAAC9F,KAAK,EAAET,eAAe,CAAC;MAClD,OAAO,IAAI,CAACJ,MAAM,CAAC6G,MAAM,CAAC;IAC5B;EAAA;IAAAjH,GAAA;IAAAC,KAAA,EAEA,SAAAiH,WAAWA,CAACjG,KAAa,EAAEkG,KAAa,EAAElH,KAAc,EAAS;MAC/D,IAAI,CAACP,MAAM,CAACmC,QAAQ,CAACZ,KAAK,EAAEkG,KAAK,EAAElH,KAAK,CAAC;MACzC,OAAO,IAAI,CAACG,MAAM,CAAC,IAAIO,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACM,MAAM,KAAA6F,gBAAA,CAAAxH,OAAA,MAAIuH,KAAK,EAAGlH,KAAA,CAAO,CAAC,CAAC;IAC1E;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAoH,UAAUA,CACRpG,KAAa,EACbQ,IAAY,EAEL;MAAA,IAAA6F,MAAA;MAAA,IADPnF,OAAgC,GAAAwC,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAErClD,IAAI,GAAGA,IAAI,CAAC8F,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;MACvD,IAAI,CAAC7H,MAAM,CAACmC,QAAQ,CAACZ,KAAK,EAAEQ,IAAI,CAAC;MACjCqB,MAAM,CAACC,IAAI,CAACZ,OAAO,CAAC,CAAC4B,OAAO,CAAE,UAAAc,MAAM,EAAK;QACvCyC,MAAI,CAAC5H,MAAM,CAACuE,QAAQ,CAAChD,KAAK,EAAEQ,IAAI,CAACnB,MAAM,EAAEuE,MAAM,EAAE1C,OAAO,CAAC0C,MAAM,CAAC,CAAC;MACnE,CAAC,CAAC;MACF,OAAO,IAAI,CAACzE,MAAM,CAChB,IAAIO,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACM,MAAM,CAACE,IAAI,EAAE,IAAAwD,mBAAS,EAAC9C,OAAO,CAAC,CAC3D,CAAC;IACH;EAAA;IAAAnC,GAAA;IAAAC,KAAA,EAEA,SAAAuH,OAAOA,CAAA,EAAY;MACjB,IAAI,IAAI,CAAC9H,MAAM,CAAC+H,QAAQ,CAACnH,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAClD,IAAI,IAAI,CAACZ,MAAM,CAAC+H,QAAQ,CAACnH,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;MACjD,IAAMoF,IAAI,GAAG,IAAI,CAAChG,MAAM,CAAC+H,QAAQ,CAACC,IAAI;MACtC,IAAI,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAElC,OAAO,CAACmE,QAAQ,MAAKrF,cAAK,CAACqF,QAAQ,EAAE,OAAO,KAAK;MAC3D,IAAMC,KAAK,GAAGlC,IAAa;MAC3B,IAAIkC,KAAK,CAACH,QAAQ,CAACnH,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;MAC3C,OAAOsH,KAAK,CAACH,QAAQ,CAACC,IAAI,YAAYG,cAAK;IAC7C;EAAA;IAAA7H,GAAA;IAAAC,KAAA,EAEA,SAAA6H,YAAYA,CAAC7G,KAAa,EAAEX,MAAc,EAAS;MACjD,IAAMmB,IAAI,GAAG,IAAI,CAACmF,OAAO,CAAC3F,KAAK,EAAEX,MAAM,CAAC;MACxC,IAAAyH,kBAAA,GAAuB,IAAI,CAACrI,MAAM,CAACqC,IAAI,CAACd,KAAK,GAAGX,MAAM,CAAC;QAAA0H,kBAAA,OAAA/F,eAAA,CAAArC,OAAA,EAAAmI,kBAAA;QAAhDhG,IAAI,GAAAiG,kBAAA;QAAE9F,MAAM,GAAA8F,kBAAA;MACnB,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIC,MAAM,GAAG,IAAIvH,mBAAK,CAAC,CAAC;MACxB,IAAIoB,IAAI,IAAI,IAAI,EAAE;QAChBkG,YAAY,GAAGlG,IAAI,CAACzB,MAAM,CAAC,CAAC,GAAG4B,MAAM;QACrCgG,MAAM,GAAGnG,IAAI,CACVlC,KAAK,CAAC,CAAC,CACPkB,KAAK,CAACmB,MAAM,EAAEA,MAAM,GAAG+F,YAAY,GAAG,CAAC,CAAC,CACxC1G,MAAM,CAAC,IAAI,CAAC;MACjB;MACA,IAAMyF,QAAQ,GAAG,IAAI,CAAC5B,WAAW,CAACnE,KAAK,EAAEX,MAAM,GAAG2H,YAAY,CAAC;MAC/D,IAAMrF,IAAI,GAAGoE,QAAQ,CAACpE,IAAI,CAAC,IAAIjC,mBAAK,CAAC,CAAC,CAACY,MAAM,CAACE,IAAI,CAAC,CAAC4D,MAAM,CAAC6C,MAAM,CAAC,CAAC;MACnE,IAAMrI,KAAK,GAAG,IAAIc,mBAAK,CAAC,CAAC,CAACa,MAAM,CAACP,KAAK,CAAC,CAACoE,MAAM,CAACzC,IAAI,CAAC;MACpD,OAAO,IAAI,CAAC1C,UAAU,CAACL,KAAK,CAAC;IAC/B;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAG,MAAMA,CACJ6G,MAAoB,EAGb;MAAA,IAFPkB,SAA2B,GAAAxD,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAAA,IAChCyD,aAAwC,GAAAzD,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGC,SAAS;MAEpD,IAAMyD,QAAQ,GAAG,IAAI,CAACxI,KAAK;MAC3B,IACEsI,SAAS,CAAC7H,MAAM,KAAK,CAAC,IACtB6H,SAAS,CAAC,CAAC,CAAC,CAACG,IAAI,KAAK,eAAe;MACrC;MACAH,SAAS,CAAC,CAAC,CAAC,CAACI,MAAM,CAACC,IAAI,CAACC,KAAK,CAACjJ,KAAK,CAAC,IACrC,IAAI,CAACE,MAAM,CAACgJ,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,EACrC;QACA;QACA,IAAMI,QAAQ,GAAG,IAAI,CAACjJ,MAAM,CAACgJ,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC,CAACI,MAAM,CAAS;QAC9D,IAAMpG,OAAO,GAAG,IAAAE,oBAAa,EAACsG,QAAQ,CAAC;QACvC,IAAM1H,KAAK,GAAG0H,QAAQ,CAACzG,MAAM,CAAC,IAAI,CAACxC,MAAM,CAAC;QAC1C;QACA,IAAMkJ,QAAQ,GAAGT,SAAS,CAAC,CAAC,CAAC,CAACS,QAAQ,CAACrB,OAAO,CAACsB,eAAU,CAACC,QAAQ,EAAE,EAAE,CAAC;QACvE,IAAMC,OAAO,GAAG,IAAIpI,mBAAK,CAAC,CAAC,CAACY,MAAM,CAACqH,QAAQ,CAAC;QAC5C;QACA,IAAMI,OAAO,GAAG,IAAIrI,mBAAK,CAAC,CAAC,CAACY,MAAM,CAACoH,QAAQ,CAAC1I,KAAK,CAAC,CAAC,CAAC;QACpD,IAAMgJ,qBAAqB,GAAGb,aAAa,IAAI;UAC7Cc,QAAQ,EAAEC,UAAU,CAACf,aAAa,CAACc,QAAQ,EAAE,CAACjI,KAAK,CAAC;UACpDmI,QAAQ,EAAED,UAAU,CAACf,aAAa,CAACgB,QAAQ,EAAE,CAACnI,KAAK;QACrD,CAAC;QACD,IAAMoI,SAAS,GAAG,IAAI1I,mBAAK,CAAC,CAAC,CAC1Ba,MAAM,CAACP,KAAK,CAAC,CACboE,MAAM,CAAC0D,OAAO,CAACnG,IAAI,CAACoG,OAAO,EAAEC,qBAAqB,CAAC,CAAC;QACvDhC,MAAM,GAAGoC,SAAS,CAACrI,MAAM,CAAC,UAACnB,KAAK,EAAEqB,EAAE,EAAK;UACvC,IAAIA,EAAE,CAACK,MAAM,EAAE;YACb,OAAO1B,KAAK,CAAC0B,MAAM,CAACL,EAAE,CAACK,MAAM,EAAEY,OAAO,CAAC;UACzC;UACA,OAAOtC,KAAK,CAACgE,IAAI,CAAC3C,EAAE,CAAC;QACvB,CAAC,EAAE,IAAIP,mBAAK,CAAC,CAAC,CAAC;QACf,IAAI,CAACd,KAAK,GAAGwI,QAAQ,CAACiB,OAAO,CAACrC,MAAM,CAAC;MACvC,CAAC,MAAM;QACL,IAAI,CAACpH,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC5B,IAAI,CAACmH,MAAM,IAAI,CAAC,IAAAsC,iBAAO,EAAClB,QAAQ,CAACiB,OAAO,CAACrC,MAAM,CAAC,EAAE,IAAI,CAACpH,KAAK,CAAC,EAAE;UAC7DoH,MAAM,GAAGoB,QAAQ,CAACzF,IAAI,CAAC,IAAI,CAAC/C,KAAK,EAAEuI,aAAa,CAAC;QACnD;MACF;MACA,OAAOnB,MAAM;IACf;EAAA;AAAA;AAUF,SAASuC,eAAeA,CACtBC,KAAiB,EACjBC,UAAkB,EAClBC,KAAe,EACP;EACR,IAAIF,KAAK,CAACnJ,MAAM,KAAK,CAAC,EAAE;IACtB,IAAAsJ,YAAA,GAAiBC,WAAW,CAACF,KAAK,CAACG,GAAG,CAAC,CAAC,CAAC;MAAAC,aAAA,OAAA9H,eAAA,CAAArC,OAAA,EAAAgK,YAAA;MAAlCI,OAAM,GAAAD,aAAA;IACb,IAAIL,UAAU,IAAI,CAAC,EAAE;MACnB,iBAAArE,MAAA,CAAiB2E,OAAO;IAC1B;IACA,iBAAA3E,MAAA,CAAiB2E,OAAO,OAAA3E,MAAA,CAAGmE,eAAe,CAAC,EAAE,EAAEE,UAAU,GAAG,CAAC,EAAEC,KAAK,CAAE;EACxE;EACA,IAAAM,MAAA,OAAAC,SAAA,CAAAtK,OAAA,EAA2D6J,KAAK;IAAAU,OAAA,GAAAF,MAAA;IAAvDG,KAAK,GAAAD,OAAA,CAALC,KAAK;IAAElI,MAAM,GAAAiI,OAAA,CAANjI,MAAM;IAAE5B,MAAM,GAAA6J,OAAA,CAAN7J,MAAM;IAAE+J,MAAM,GAAAF,OAAA,CAANE,MAAM;IAAE/B,IAAA,GAAA6B,OAAA,CAAA7B,IAAA;IAAWgC,IAAI,GAAAL,MAAA,CAAAlJ,KAAA;EACvD,IAAAwJ,aAAA,GAAyBV,WAAW,CAACvB,IAAI,CAAC;IAAAkC,aAAA,OAAAvI,eAAA,CAAArC,OAAA,EAAA2K,aAAA;IAAnCE,GAAG,GAAAD,aAAA;IAAEE,SAAS,GAAAF,aAAA;EACrB,IAAIH,MAAM,GAAGX,UAAU,EAAE;IACvBC,KAAK,CAAC9F,IAAI,CAACyE,IAAI,CAAC;IAChB,IAAI+B,MAAM,KAAKX,UAAU,GAAG,CAAC,EAAE;MAC7B,WAAArE,MAAA,CAAWoF,GAAI,UAAApF,MAAA,CAAMqF,SAAU,OAAArF,MAAA,CAAGsB,WAAW,CAC3CyD,KAAK,EACLlI,MAAM,EACN5B,MACF,CAAE,EAAA+E,MAAA,CAAEmE,eAAe,CAACc,IAAI,EAAED,MAAM,EAAEV,KAAK,CAAE;IAC3C;IACA,WAAAtE,MAAA,CAAWoF,GAAI,WAAApF,MAAA,CAAOmE,eAAe,CAACC,KAAK,EAAEC,UAAU,GAAG,CAAC,EAAEC,KAAK,CAAE;EACtE;EACA,IAAMgB,YAAY,GAAGhB,KAAK,CAACA,KAAK,CAACrJ,MAAM,GAAG,CAAC,CAAC;EAC5C,IAAI+J,MAAM,KAAKX,UAAU,IAAIpB,IAAI,KAAKqC,YAAY,EAAE;IAClD,kBAAAtF,MAAA,CAAkBqF,SAAU,OAAArF,MAAA,CAAGsB,WAAW,CACxCyD,KAAK,EACLlI,MAAM,EACN5B,MACF,CAAE,EAAA+E,MAAA,CAAEmE,eAAe,CAACc,IAAI,EAAED,MAAM,EAAEV,KAAK,CAAE;EAC3C;EACA,IAAAiB,aAAA,GAAiBf,WAAW,CAACF,KAAK,CAACG,GAAG,CAAC,CAAC,CAAC;IAAAe,aAAA,OAAA5I,eAAA,CAAArC,OAAA,EAAAgL,aAAA;IAAlCZ,MAAM,GAAAa,aAAA;EACb,iBAAAxF,MAAA,CAAiB2E,MAAO,OAAA3E,MAAA,CAAGmE,eAAe,CAACC,KAAK,EAAEC,UAAU,GAAG,CAAC,EAAEC,KAAK,CAAE;AAC3E;AAEA,SAAShD,WAAWA,CAClBjB,IAAU,EACVzE,KAAa,EACbX,MAAc,EAEN;EAAA,IADRwK,MAAM,GAAAnG,SAAA,CAAArE,MAAA,QAAAqE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EAEd,IAAI,MAAM,IAAIe,IAAI,IAAI,OAAOA,IAAI,CAACqF,IAAI,KAAK,UAAU,EAAE;IACrD,OAAOrF,IAAI,CAACqF,IAAI,CAAC9J,KAAK,EAAEX,MAAM,CAAC;EACjC;EACA,IAAIoF,IAAI,YAAYpC,cAAQ,EAAE;IAC5B,OAAO,IAAA0H,iBAAU,EAACtF,IAAI,CAACzF,KAAK,CAAC,CAAC,CAACc,KAAK,CAACE,KAAK,EAAEA,KAAK,GAAGX,MAAM,CAAC,CAAC;EAC9D;EACA,IAAIoF,IAAI,YAAYuF,qBAAU,EAAE;IAC9B;IACA,IAAIvF,IAAI,CAAClC,OAAO,CAACmE,QAAQ,KAAK,gBAAgB,EAAE;MAC9C,IAAM8B,KAAY,GAAG,EAAE;MACvB/D,IAAI,CAAC+B,QAAQ,CAACyD,SAAS,CAACjK,KAAK,EAAEX,MAAM,EAAE,UAAC8J,KAAK,EAAElI,MAAM,EAAEiJ,WAAW,EAAK;QACrE,IAAMhJ,OAAO,GACX,SAAS,IAAIiI,KAAK,IAAI,OAAOA,KAAK,CAACjI,OAAO,KAAK,UAAU,GACrDiI,KAAK,CAACjI,OAAO,CAAC,CAAC,GACf,CAAC,CAAC;QACRsH,KAAK,CAAC5F,IAAI,CAAC;UACTuG,KAAK,EAALA,KAAK;UACLlI,MAAM,EAANA,MAAM;UACN5B,MAAM,EAAE6K,WAAW;UACnBd,MAAM,EAAElI,OAAO,CAACkI,MAAM,IAAI,CAAC;UAC3B/B,IAAI,EAAEnG,OAAO,CAACiJ;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAO5B,eAAe,CAACC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACvC;IACA,IAAM4B,KAAe,GAAG,EAAE;IAC1B3F,IAAI,CAAC+B,QAAQ,CAACyD,SAAS,CAACjK,KAAK,EAAEX,MAAM,EAAE,UAAC8J,KAAK,EAAElI,MAAM,EAAEiJ,WAAW,EAAK;MACrEE,KAAK,CAACxH,IAAI,CAAC8C,WAAW,CAACyD,KAAK,EAAElI,MAAM,EAAEiJ,WAAW,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,IAAIL,MAAM,IAAIpF,IAAI,CAAClC,OAAO,CAACmE,QAAQ,KAAK,MAAM,EAAE;MAC9C,OAAO0D,KAAK,CAACvE,IAAI,CAAC,EAAE,CAAC;IACvB;IACA,IAAAwE,aAAA,GAAiC5F,IAAI,CAAC6F,OAAkB;MAAhDC,SAAS,GAAAF,aAAA,CAATE,SAAS;MAAEC,SAAA,GAAAH,aAAA,CAAAG,SAAA;IACnB,IAAAC,gBAAA,GAAqBF,SAAS,CAACG,KAAK,KAAAtG,MAAA,CAAKoG,SAAU,MAAE,CAAC;MAAAG,iBAAA,OAAA3J,eAAA,CAAArC,OAAA,EAAA8L,gBAAA;MAA/CG,KAAK,GAAAD,iBAAA;MAAEE,GAAG,GAAAF,iBAAA;IACjB;IACA,IAAIC,KAAK,KAAK,QAAQ,EAAE;MACtB,mDAAAxG,MAAA,CAAiDgG,KAAK,CAACvE,IAAI,CAAC,EAAE,CAAE,OAAAzB,MAAA,CAAGyG,GAAI;IACzE;IACA,UAAAzG,MAAA,CAAUwG,KAAM,OAAAxG,MAAA,CAAGgG,KAAK,CAACvE,IAAI,CAAC,EAAE,CAAE,OAAAzB,MAAA,CAAGyG,GAAI;EAC3C;EACA,OAAOpG,IAAI,CAAC6F,OAAO,YAAYQ,OAAO,GAAGrG,IAAI,CAAC6F,OAAO,CAACC,SAAS,GAAG,EAAE;AACtE;AAEA,SAASxF,cAAcA,CACrB7D,OAAgC,EAChC6J,QAAiC,EACR;EACzB,OAAOlJ,MAAM,CAACC,IAAI,CAACiJ,QAAQ,CAAC,CAAChL,MAAM,CACjC,UAACiL,MAAM,EAAEjI,IAAI,EAAK;IAChB,IAAI7B,OAAO,CAAC6B,IAAI,CAAC,IAAI,IAAI,EAAE,OAAOiI,MAAM;IACxC,IAAMC,aAAa,GAAGF,QAAQ,CAAChI,IAAI,CAAC;IACpC,IAAIkI,aAAa,KAAK/J,OAAO,CAAC6B,IAAI,CAAC,EAAE;MACnCiI,MAAM,CAACjI,IAAI,CAAC,GAAGkI,aAAa;IAC9B,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;MACvC,IAAIA,aAAa,CAACG,OAAO,CAAClK,OAAO,CAAC6B,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;QAC5CiI,MAAM,CAACjI,IAAI,CAAC,GAAGkI,aAAa,CAAC7G,MAAM,CAAC,CAAClD,OAAO,CAAC6B,IAAI,CAAC,CAAC,CAAC;MACtD,CAAC,MAAM;QACL;QACAiI,MAAM,CAACjI,IAAI,CAAC,GAAGkI,aAAa;MAC9B;IACF,CAAC,MAAM;MACLD,MAAM,CAACjI,IAAI,CAAC,GAAG,CAACkI,aAAa,EAAE/J,OAAO,CAAC6B,IAAI,CAAC,CAAC;IAC/C;IACA,OAAOiI,MAAM;EACf,CAAC,EACD,CAAC,CACH,CAAC;AACH;AAEA,SAASpC,WAAWA,CAACvB,IAAwB,EAAE;EAC7C,IAAMmC,GAAG,GAAGnC,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;EAC5C,QAAQA,IAAI;IACV,KAAK,SAAS;MACZ,OAAO,CAACmC,GAAG,EAAE,sBAAsB,CAAC;IACtC,KAAK,WAAW;MACd,OAAO,CAACA,GAAG,EAAE,wBAAwB,CAAC;IACxC;MACE,OAAO,CAACA,GAAG,EAAE,EAAE,CAAC;EACpB;AACF;AAEA,SAAShK,cAAcA,CAACZ,KAAY,EAAE;EACpC,OAAOA,KAAK,CAACmB,MAAM,CAAC,UAACR,eAAe,EAAEU,EAAE,EAAK;IAC3C,IAAI,OAAOA,EAAE,CAACK,MAAM,KAAK,QAAQ,EAAE;MACjC,IAAME,IAAI,GAAGP,EAAE,CAACK,MAAM,CAACgG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;MAClE,OAAO/G,eAAe,CAACe,MAAM,CAACE,IAAI,EAAEP,EAAE,CAACE,UAAU,CAAC;IACpD;IACA,OAAOZ,eAAe,CAACqD,IAAI,CAAC3C,EAAE,CAAC;EACjC,CAAC,EAAE,IAAIP,mBAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAASwI,UAAUA,CAAAmD,IAAA,EAA2BC,MAAc,EAAE;EAAA,IAAxCtL,KAAK,GAAiBqL,IAAA,CAAtBrL,KAAK;IAAEX,MAAA,GAAegM,IAAA,CAAfhM,MAAA;EAC3B,OAAO,IAAIkM,gBAAK,CAACvL,KAAK,GAAGsL,MAAM,EAAEjM,MAAM,CAAC;AAC1C;AAEA,SAASO,YAAYA,CAACC,GAAS,EAAE;EAC/B,IAAM6K,KAAW,GAAG,EAAE;EACtB7K,GAAG,CAACiD,OAAO,CAAE,UAAA7C,EAAE,EAAK;IAClB,IAAI,OAAOA,EAAE,CAACK,MAAM,KAAK,QAAQ,EAAE;MACjC,IAAMuD,KAAK,GAAG5D,EAAE,CAACK,MAAM,CAACoK,KAAK,CAAC,IAAI,CAAC;MACnC7G,KAAK,CAACf,OAAO,CAAC,UAAChC,IAAI,EAAEd,KAAK,EAAK;QAC7B,IAAIA,KAAK,EAAE0K,KAAK,CAAC9H,IAAI,CAAC;UAAEtC,MAAM,EAAE,IAAI;UAAEH,UAAU,EAAEF,EAAE,CAACE;QAAW,CAAC,CAAC;QAClE,IAAIW,IAAI,EAAE4J,KAAK,CAAC9H,IAAI,CAAC;UAAEtC,MAAM,EAAEQ,IAAI;UAAEX,UAAU,EAAEF,EAAE,CAACE;QAAW,CAAC,CAAC;MACnE,CAAC,CAAC;IACJ,CAAC,MAAM;MACLuK,KAAK,CAAC9H,IAAI,CAAC3C,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;EAEF,OAAOyK,KAAK;AACd;AAAA,IAAAc,QAAA,GAAAC,OAAA,CAAA9M,OAAA,GAEeH,MAAM", "ignoreList": []}]}