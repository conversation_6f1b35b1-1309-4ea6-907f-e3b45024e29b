<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>信源监测</h2>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 标签页导航 -->
      <el-tabs v-model="activeTab" class="source-tabs">
        <el-tab-pane label="信源人员" name="personnel">
          <div class="tab-content">
            <!-- 搜索和筛选区域 -->
            <div class="filter-section">
              <div class="filter-row">
                <div class="filter-left">
                  <el-button type="primary" size="small" icon="el-icon-plus">接收人设置</el-button>
                  <el-button size="small" icon="el-icon-warning">预警设置</el-button>
                  <el-button size="small" icon="el-icon-key">关键词设置</el-button>
                  <el-button size="small" icon="el-icon-star-off">信源设置</el-button>
                </div>
                <div class="filter-right">
                  <span class="status-text">预警开关</span>
                  <el-switch v-model="monitorStatus" active-color="#13ce66"></el-switch>
                </div>
              </div>

              <div class="search-row">
                <div class="search-left">
                  <el-button size="small" icon="el-icon-refresh">刷新</el-button>
                  <el-button size="small" icon="el-icon-download">下载</el-button>
                </div>
                <div class="search-right">
                  <span class="total-text">共0条记录</span>
                  <el-select v-model="pageSize" size="small" style="width: 80px; margin: 0 10px;">
                    <el-option label="7条" value="7"></el-option>
                    <el-option label="10条" value="10"></el-option>
                    <el-option label="20条" value="20"></el-option>
                  </el-select>
                  <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    range-separator="~"
                    start-placeholder="2023/04/23 00:00:00"
                    end-placeholder="2023/04/25 20:47:46"
                    size="small"
                    style="width: 350px; margin-right: 10px;"
                  ></el-date-picker>
                  <el-select v-model="sortBy" size="small" style="width: 100px; margin-right: 10px;">
                    <el-option label="全部" value="all"></el-option>
                  </el-select>
                  <el-input
                    v-model="searchKeyword"
                    placeholder="请输入关键词搜索"
                    size="small"
                    style="width: 200px;"
                    suffix-icon="el-icon-search"
                  ></el-input>
                </div>
              </div>
            </div>

            <!-- 表格区域 -->
            <div class="table-section">
              <el-table
                :data="tableData"
                style="width: 100%"
                height="400"
                empty-text="暂无数据"
              >
                <el-table-column prop="media" label="媒体" width="120"></el-table-column>
                <el-table-column prop="platform" label="平台" width="100"></el-table-column>
                <el-table-column prop="publisher" label="发布人" width="120"></el-table-column>
                <el-table-column prop="publishTime" label="发布时间" width="180"></el-table-column>
                <el-table-column prop="content" label="内容" min-width="200"></el-table-column>
                <el-table-column prop="readCount" label="阅读量" width="100"></el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">查看</el-button>
                    <el-button type="text" size="small">编辑</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="信源媒体" name="media">
          <div class="tab-content">
            <div class="empty-state">
              <p>暂无数据</p>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="媒体监控" name="monitoring">
          <div class="tab-content">
            <div class="empty-state">
              <p>暂无数据</p>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="媒体反馈" name="feedback">
          <div class="tab-content">
            <div class="empty-state">
              <p>暂无数据</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SourceMonitoring',
  data() {
    return {
      activeTab: 'personnel',
      monitorStatus: true,
      pageSize: '7',
      dateRange: [],
      sortBy: 'all',
      searchKeyword: '',
      tableData: [] // 暂无数据，所以为空数组
    }
  },
  methods: {
    // 这里可以添加各种方法
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.main-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 150px);
}

.source-tabs {
  padding: 20px;

  .tab-content {
    margin-top: 20px;
  }
}

.filter-section {
  margin-bottom: 20px;

  .filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .filter-left {
      display: flex;
      gap: 10px;
    }

    .filter-right {
      display: flex;
      align-items: center;
      gap: 10px;

      .status-text {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .search-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-left {
      display: flex;
      gap: 10px;
    }

    .search-right {
      display: flex;
      align-items: center;

      .total-text {
        font-size: 14px;
        color: #606266;
        margin-right: 15px;
      }
    }
  }
}

.table-section {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #909399;
  font-size: 14px;
}
</style>
