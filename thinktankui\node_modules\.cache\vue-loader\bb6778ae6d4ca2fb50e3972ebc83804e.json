{"remainingRequest": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\项目\\金刚\\3\\thinktankui\\src\\views\\system\\menu\\index.vue", "mtime": 1746725182000}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749105926037}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749105925774}, {"path": "H:\\项目\\金刚\\3\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749105919481}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RNZW51LCBnZXRNZW51LCBkZWxNZW51LCBhZGRNZW51LCB1cGRhdGVNZW51IH0gZnJvbSAiQC9hcGkvc3lzdGVtL21lbnUiOwppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7CmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOwppbXBvcnQgSWNvblNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvSWNvblNlbGVjdCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk1lbnUiLAogIGRpY3RzOiBbJ3N5c19zaG93X2hpZGUnLCAnc3lzX25vcm1hbF9kaXNhYmxlJ10sCiAgY29tcG9uZW50czogeyBUcmVlc2VsZWN0LCBJY29uU2VsZWN0IH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g6I+c5Y2V6KGo5qC85qCR5pWw5o2uCiAgICAgIG1lbnVMaXN0OiBbXSwKICAgICAgLy8g6I+c5Y2V5qCR6YCJ6aG5CiAgICAgIG1lbnVPcHRpb25zOiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKblsZXlvIDvvIzpu5jorqTlhajpg6jmipjlj6AKICAgICAgaXNFeHBhbmRBbGw6IGZhbHNlLAogICAgICAvLyDph43mlrDmuLLmn5PooajmoLznirbmgIEKICAgICAgcmVmcmVzaFRhYmxlOiB0cnVlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBtZW51TmFtZTogdW5kZWZpbmVkLAogICAgICAgIHZpc2libGU6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIG1lbnVOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6I+c5Y2V5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIG9yZGVyTnVtOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6I+c5Y2V6aG65bqP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHBhdGg6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLot6/nlLHlnLDlnYDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOmAieaLqeWbvuaghwogICAgc2VsZWN0ZWQobmFtZSkgewogICAgICB0aGlzLmZvcm0uaWNvbiA9IG5hbWU7CiAgICB9LAogICAgLyoqIOafpeivouiPnOWNleWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdE1lbnUodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tZW51TGlzdCA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAibWVudUlkIik7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDovazmjaLoj5zljZXmlbDmja7nu5PmnoQgKi8KICAgIG5vcm1hbGl6ZXIobm9kZSkgewogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsKICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbjsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIGlkOiBub2RlLm1lbnVJZCwKICAgICAgICBsYWJlbDogbm9kZS5tZW51TmFtZSwKICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbgogICAgICB9OwogICAgfSwKICAgIC8qKiDmn6Xor6Loj5zljZXkuIvmi4nmoJHnu5PmnoQgKi8KICAgIGdldFRyZWVzZWxlY3QoKSB7CiAgICAgIGxpc3RNZW51KCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tZW51T3B0aW9ucyA9IFtdOwogICAgICAgIGNvbnN0IG1lbnUgPSB7IG1lbnVJZDogMCwgbWVudU5hbWU6ICfkuLvnsbvnm64nLCBjaGlsZHJlbjogW10gfTsKICAgICAgICBtZW51LmNoaWxkcmVuID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKTsKICAgICAgICB0aGlzLm1lbnVPcHRpb25zLnB1c2gobWVudSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBtZW51SWQ6IHVuZGVmaW5lZCwKICAgICAgICBwYXJlbnRJZDogMCwKICAgICAgICBtZW51TmFtZTogdW5kZWZpbmVkLAogICAgICAgIHJvdXRlTmFtZTogdW5kZWZpbmVkLAogICAgICAgIGljb246IHVuZGVmaW5lZCwKICAgICAgICBtZW51VHlwZTogIk0iLAogICAgICAgIG9yZGVyTnVtOiB1bmRlZmluZWQsCiAgICAgICAgaXNGcmFtZTogMSwKICAgICAgICBpc0NhY2hlOiAwLAogICAgICAgIHZpc2libGU6ICIwIiwKICAgICAgICBzdGF0dXM6ICIwIgogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5nZXRUcmVlc2VsZWN0KCk7CiAgICAgIGlmIChyb3cgIT0gbnVsbCAmJiByb3cubWVudUlkKSB7CiAgICAgICAgdGhpcy5mb3JtLnBhcmVudElkID0gcm93Lm1lbnVJZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSAwOwogICAgICB9CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6I+c5Y2VIjsKICAgIH0sCiAgICAvKiog5bGV5byAL+aKmOWPoOaTjeS9nCAqLwogICAgdG9nZ2xlRXhwYW5kQWxsKCkgewogICAgICB0aGlzLnJlZnJlc2hUYWJsZSA9IGZhbHNlOwogICAgICB0aGlzLmlzRXhwYW5kQWxsID0gIXRoaXMuaXNFeHBhbmRBbGw7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLnJlZnJlc2hUYWJsZSA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgICAgZ2V0TWVudShyb3cubWVudUlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnoj5zljZUiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbigpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0ubWVudUlkICE9IHVuZGVmaW5lZCkgewogICAgICAgICAgICB1cGRhdGVNZW51KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkTWVudSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZCN56ew5Li6IicgKyByb3cubWVudU5hbWUgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbE1lbnUocm93Lm1lbnVJZCk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+SA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/menu", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"菜单名称\" prop=\"menuName\">\n        <el-input\n          v-model=\"queryParams.menuName\"\n          placeholder=\"请输入菜单名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"菜单状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:menu:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-sort\"\n          size=\"mini\"\n          @click=\"toggleExpandAll\"\n        >展开/折叠</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-if=\"refreshTable\"\n      v-loading=\"loading\"\n      :data=\"menuList\"\n      row-key=\"menuId\"\n      :default-expand-all=\"isExpandAll\"\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column prop=\"menuName\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"160\"></el-table-column>\n      <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <svg-icon :icon-class=\"scope.row.icon\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"60\"></el-table-column>\n      <el-table-column prop=\"perms\" label=\"权限标识\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column prop=\"component\" label=\"组件路径\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:menu:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd(scope.row)\"\n            v-hasPermi=\"['system:menu:add']\"\n          >新增</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:menu:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 添加或修改菜单对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"680px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"上级菜单\" prop=\"parentId\">\n              <treeselect\n                v-model=\"form.parentId\"\n                :options=\"menuOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                placeholder=\"选择上级菜单\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\n              <el-radio-group v-model=\"form.menuType\">\n                <el-radio label=\"M\">目录</el-radio>\n                <el-radio label=\"C\">菜单</el-radio>\n                <el-radio label=\"F\">按钮</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\" v-if=\"form.menuType != 'F'\">\n            <el-form-item label=\"菜单图标\" prop=\"icon\">\n              <el-popover\n                placement=\"bottom-start\"\n                width=\"460\"\n                trigger=\"click\"\n                @show=\"$refs['iconSelect'].reset()\"\n              >\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" :active-icon=\"form.icon\" />\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\n                  <svg-icon\n                    v-if=\"form.icon\"\n                    slot=\"prefix\"\n                    :icon-class=\"form.icon\"\n                    style=\"width: 25px;\"\n                  />\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\n                </el-input>\n              </el-popover>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\n              <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item prop=\"isFrame\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择是外链则路由地址需要以`http(s)://`开头\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                是否外链\n              </span>\n              <el-radio-group v-model=\"form.isFrame\">\n                <el-radio :label=\"0\">是</el-radio>\n                <el-radio :label=\"1\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item prop=\"path\">\n              <span slot=\"label\">\n                <el-tooltip content=\"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                路由地址\n              </span>\n              <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"routeName\">\n                <span slot=\"label\">\n                    <el-tooltip content=\"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）\" placement=\"top\">\n                      <el-icon><question-filled /></el-icon>\n                    </el-tooltip>\n                    路由名称\n                </span>\n                <el-input v-model=\"form.routeName\" placeholder=\"请输入路由名称\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"component\">\n              <span slot=\"label\">\n                <el-tooltip content=\"访问的组件路径，如：`system/user/index`，默认在`views`目录下\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                组件路径\n              </span>\n              <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'M'\">\n            <el-form-item prop=\"perms\">\n              <el-input v-model=\"form.perms\" placeholder=\"请输入权限标识\" maxlength=\"100\" />\n              <span slot=\"label\">\n                <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                权限字符\n              </span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"query\">\n              <el-input v-model=\"form.query\" placeholder=\"请输入路由参数\" maxlength=\"255\" />\n              <span slot=\"label\">\n                <el-tooltip content='访问路由的默认传递参数，如：`{\"id\": 1, \"name\": \"ry\"}`' placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                路由参数\n              </span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"isCache\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                是否缓存\n              </span>\n              <el-radio-group v-model=\"form.isCache\">\n                <el-radio :label=\"0\">缓存</el-radio>\n                <el-radio :label=\"1\">不缓存</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item prop=\"visible\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                显示状态\n              </span>\n              <el-radio-group v-model=\"form.visible\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_show_hide\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"status\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择停用则路由将不会出现在侧边栏，也不能被访问\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                菜单状态\n              </span>\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from \"@/api/system/menu\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport IconSelect from \"@/components/IconSelect\";\n\nexport default {\n  name: \"Menu\",\n  dicts: ['sys_show_hide', 'sys_normal_disable'],\n  components: { Treeselect, IconSelect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 菜单表格树数据\n      menuList: [],\n      // 菜单树选项\n      menuOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部折叠\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        menuName: undefined,\n        visible: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        menuName: [\n          { required: true, message: \"菜单名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"菜单顺序不能为空\", trigger: \"blur\" }\n        ],\n        path: [\n          { required: true, message: \"路由地址不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 选择图标\n    selected(name) {\n      this.form.icon = name;\n    },\n    /** 查询菜单列表 */\n    getList() {\n      this.loading = true;\n      listMenu(this.queryParams).then(response => {\n        this.menuList = this.handleTree(response.data, \"menuId\");\n        this.loading = false;\n      });\n    },\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.menuId,\n        label: node.menuName,\n        children: node.children\n      };\n    },\n    /** 查询菜单下拉树结构 */\n    getTreeselect() {\n      listMenu().then(response => {\n        this.menuOptions = [];\n        const menu = { menuId: 0, menuName: '主类目', children: [] };\n        menu.children = this.handleTree(response.data, \"menuId\");\n        this.menuOptions.push(menu);\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        menuId: undefined,\n        parentId: 0,\n        menuName: undefined,\n        routeName: undefined,\n        icon: undefined,\n        menuType: \"M\",\n        orderNum: undefined,\n        isFrame: 1,\n        isCache: 0,\n        visible: \"0\",\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset();\n      this.getTreeselect();\n      if (row != null && row.menuId) {\n        this.form.parentId = row.menuId;\n      } else {\n        this.form.parentId = 0;\n      }\n      this.open = true;\n      this.title = \"添加菜单\";\n    },\n    /** 展开/折叠操作 */\n    toggleExpandAll() {\n      this.refreshTable = false;\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        this.refreshTable = true;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      this.getTreeselect();\n      getMenu(row.menuId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改菜单\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.menuId != undefined) {\n            updateMenu(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addMenu(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      this.$modal.confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项？').then(function() {\n        return delMenu(row.menuId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>\n"]}]}